package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/utils/array"
)

func (this *Model) ToOccupyLandCountMap() map[int32][]int32 {
	this.OccupyLandCountMapMutex.RLock()
	defer this.OccupyLandCountMapMutex.RUnlock()
	ret := map[int32][]int32{}
	for k, arr := range this.OccupyLandCountMap {
		ret[k] = array.Map(arr, func(m int32, _ int) int32 { return m })
	}
	return ret
}

func (this *Model) ToOccupyLandCountMapPb() map[int32]*pb.OccupyLandCountInfo {
	this.OccupyLandCountMapMutex.RLock()
	defer this.OccupyLandCountMapMutex.RUnlock()
	ret := map[int32]*pb.OccupyLandCountInfo{}
	for k, arr := range this.OccupyLandCountMap {
		ret[int32(k)] = &pb.OccupyLandCountInfo{Arr: array.Map(arr, func(m int32, _ int) int32 { return int32(m) })}
	}
	return ret
}

func (this *Model) ToLandScorePb() *pb.LandScoreInfo {
	wld := this.room.GetWorld()
	landScore, _, _, _ := wld.GetPlayerLandScoreAndAlliScore(this.Uid)
	return &pb.LandScoreInfo{
		LandScore:          int32(landScore),
		OccupyLandCountMap: this.ToOccupyLandCountMapPb(),
	}
}

// 添加攻占的野地数量
func (this *Model) AddOccupyLandCount(landLv, count int32) int32 {
	this.OccupyLandCountMapMutex.Lock()
	arr := this.OccupyLandCountMap[landLv]
	if arr == nil {
		arr = []int32{0, 0}
	}
	landCount := arr[1] + count
	arr[1] = landCount
	this.OccupyLandCountMap[landLv] = arr
	this.OccupyLandCountMapMutex.Unlock()
	// 大于50块领地才开始加分
	if landCount > 0 {
		if confs := constant.LAND_SCORE_CONF[landLv]; confs != nil {
			for _, m := range confs {
				if landCount <= m[0] {
					return m[1]
				}
			}
		}
	}
	return 0
}

// 获取指定等级资源地数量
func (this *Model) GetOccupyLandCount(lv int32) int32 {
	this.OccupyLandCountMapMutex.RLock()
	defer this.OccupyLandCountMapMutex.RUnlock()
	if arr, ok := this.OccupyLandCountMap[lv]; ok {
		if len(arr) >= 2 {
			return arr[1]
		}
	}
	return 0
}
