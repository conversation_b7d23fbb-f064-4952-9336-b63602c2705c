package config

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"strings"

	"github.com/huyangv/vmqant/log"
)

func GetLandAttrLvByDis(dis, landLv int32) int32 {
	var maxDis int32 = 12
	if landLv == 3 {
		maxDis = 14
	} else if landLv == 4 || landLv == 5 {
		maxDis = 16
	}
	return ut.ClampInt32(dis, 1, maxDis)
}

// 获取区域的配置信息
func GetAreaConfInfo(landId, dis, index int32) map[string]interface{} {
	// 每距离1格算一个阶段 最大16个阶段
	landLv := landId % 100
	level := GetLandAttrLvByDis(dis, landLv)
	json := GetJsonData("landAttr", landLv*1000+level)
	if json == nil {
		return nil
	}
	landType := landId / 100
	// 血量
	hp := ut.Int32(json["hp"])
	// 军队
	pawns := []map[string]interface{}{}
	armysString := ut.String(json["armys_"+ut.Itoa(landType)])
	if armysString == "" {
		armysString = ut.String(json["armys_3"])
	}
	if arr := strings.Split(armysString, "|"); len(arr) > 0 {
		for _, str := range arr {
			if str == "" {
				continue
			}
			p := strings.Split(str, ",")
			id, point, lv := ut.Int(p[0]), ut.NewVec2ByString(p[1], "_"), ut.Int(p[2])
			pawns = append(pawns, map[string]interface{}{
				"index": index,
				"uid":   ut.ID(),
				"point": point,
				"id":    id,
				"lv":    lv,
			})
		}
	}
	// 放入临时里面
	return map[string]interface{}{
		"index": index,
		"hp":    []int32{hp, hp},
		"armys": []map[string]interface{}{ //默认就一个军队
			{
				"index": index,
				"uid":   ut.ID(),
				"pawns": pawns,
			},
		},
	}
}

// 区域信息json转pb
func AreaConfInfoJsonToPb(json map[string]interface{}) *pb.AreaInfo {
	ret := &pb.AreaInfo{
		Index: pb.Int32(json["index"]),
		Hp:    pb.Int32Array(json["hp"]),
	}
	if armys, ok := json["armys"].([]map[string]interface{}); ok {
		for _, v := range armys {
			army := &pb.AreaArmyInfo{
				Index: pb.Int32(v["index"]),
				Uid:   pb.String(v["uid"]),
			}
			if pawns, ok := v["pawns"].([]map[string]interface{}); ok {
				for _, p := range pawns {
					army.Pawns = append(army.Pawns, &pb.AreaPawnInfo{
						Index: pb.Int32(p["index"]),
						Uid:   pb.String(p["uid"]),
						Point: pb.NewVec2(p["point"]),
						Id:    pb.Int32(p["id"]),
						Lv:    pb.Int32(p["lv"]),
					})
				}
			}
			ret.Armys = append(ret.Armys, army)
		}
	}
	return ret
}

// 获取古城的区域配置信息
func GetAncientCityAreaConfigPb(index, cityId int32) *pb.AreaInfo {
	cityCfg := GetJsonData("city", cityId)
	if cityCfg == nil {
		log.Error("GetAncientCityAreaConfigPb cityCfg nil index: %v, cityId: %v", index, cityId)
		return nil
	}
	// hp := ut.Int(cityCfg["hp"]) 血量在外面赋值
	ret := &pb.AreaInfo{
		Index: int32(index),
		// Hp:     []int32{int32(hp), int32(hp)},
		CityId: int32(cityId),
		Armys:  []*pb.AreaArmyInfo{},
	}
	// 获取配置军队 获取的军队配置下标为坐标对配置数量求余
	cfgLen := len(ANCIENT_ARMYS)
	cfgIndex := int(index) % cfgLen
	// cfgIndex := 4
	army := &pb.AreaArmyInfo{
		Index: pb.Int32(index),
		Uid:   ut.ID(),
		Pawns: []*pb.AreaPawnInfo{},
	}
	cfg := ANCIENT_ARMYS[cfgIndex]
	armyCfg, equipCfg := ut.MapArray(cfg["armys"]), cfg["equip"].(map[int32]string)
	for _, pawnCfg := range armyCfg {
		pawn := &pb.AreaPawnInfo{
			Index:   int32(index),
			Uid:     ut.ID(),
			ArmyUid: army.Uid,
			Id:      pb.Int32(pawnCfg["id"]),
			Lv:      pb.Int32(pawnCfg["lv"]),
		}
		// 加上石像皮肤
		pawn.SkinId = pawn.Id*1000 + 201
		// 位置
		pointStr := ut.String(pawnCfg["point"])
		pointArr := strings.Split(pointStr, ",")
		if len(pointArr) >= 2 {
			pawn.Point = &pb.Vec2{X: pb.Int32(pointArr[0]), Y: pb.Int32(pointArr[1])}
		}
		// 装备
		equip := &pb.EquipInfo{Attrs: []*pb.AttrArrayInfo{}}
		equipArr := strings.Split(equipCfg[pawn.Id], ":")
		if len(equipArr) >= 2 {
			equip.Id = pb.Int32(equipArr[0])
			equipAttrsArr := strings.Split(equipArr[1], "|")
			for _, attrStr := range equipAttrsArr {
				attrArr := strings.Split(attrStr, ",")
				equip.Attrs = append(equip.Attrs, &pb.AttrArrayInfo{
					Attr: pb.Int32Array(attrArr),
				})
			}
		}
		pawn.Equip = equip
		army.Pawns = append(army.Pawns, pawn)
	}
	ret.Armys = append(ret.Armys, army)
	return ret
}

// 获取古城升级时间
func GetAncientLvUpTime(id, lv int32) int64 {
	attrId := id*1000 + lv
	json := GetJsonData("buildAttr", attrId)
	if json == nil {
		return -1
	}
	// 配置的单位是秒
	return ut.Int64(json["bt_time"]) * 1000
}

// 获取古城名字
func GetAncientNameById(cityId int32) string {
	// 直接返回指定字符串处理多语言
	return "cityText.name_" + ut.String(cityId)
}

// 根据productId获取商品配置
func GetRechargeConfByProductId(productId string) map[string]interface{} {
	if arr := GetJson("recharge").Get("product_id", productId); len(arr) > 0 {
		return arr[0]
	}
	return nil
}
