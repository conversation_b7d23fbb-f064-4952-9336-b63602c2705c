package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type BaseTeamUserInfo struct {
	Uid      string `bson:"uid"` //玩家uid
	Nickname string `bson:"nickname"`
	HeadIcon string `bson:"headicon"`
}

func (this *BaseTeamUserInfo) GetUID() string         { return this.Uid }
func (this *BaseTeamUserInfo) GetNickname() string    { return this.Nickname }
func (this *BaseTeamUserInfo) SetNickname(val string) { this.Nickname = val }
func (this *BaseTeamUserInfo) GetHeadIcon() string    { return this.HeadIcon }
func (this *BaseTeamUserInfo) SetHeadIcon(val string) { this.HeadIcon = val }

func (this *BaseTeamUserInfo) Init(uid, nickname, headIcon string) {
	this.Uid = uid
	this.Nickname = nickname
	this.HeadIcon = headIcon
	return
}

type IBaseTeamUserInfo interface {
	GetUID() string
	GetNickname() string
	SetNickname(val string)
	GetHeadIcon() string
	SetHeadIcon(val string)
}

// 队伍玩家信息
type TeamUserInfo struct {
	BaseTeamUserInfo
	Job int `bson:"job"` // 职位 0普通 1队长 2可邀请
}

// 邀请的玩家信息
type TeamInviteUserInfo struct {
	BaseTeamUserInfo
}

// 队伍邀请信息
type TeamInviteInfo struct {
	BaseTeamUserInfo
	TeamUid string `bson:"teamUid"` //队伍uid
	Time    int64  `bson:"time"`
}

func NewTeamUser(uid, nickname, headIcon string, job int) *TeamUserInfo {
	info := &TeamUserInfo{}
	info.Init(uid, nickname, headIcon)
	info.Job = job
	return info
}

func NewTeamInviteUser(uid, nickname, headIcon string) *TeamInviteUserInfo {
	info := &TeamInviteUserInfo{}
	info.Init(uid, nickname, headIcon)
	return info
}

func NewTeamInviteInfo(teamUid, uid, nickname, headIcon string) *TeamInviteInfo {
	info := &TeamInviteInfo{TeamUid: teamUid, Time: time.Now().UnixMilli()}
	info.Init(uid, nickname, headIcon)
	return info
}

func (this *TeamUserInfo) ToPb() *pb.TeamUserInfo {
	return &pb.TeamUserInfo{
		Uid:      this.Uid,
		Nickname: this.Nickname,
		HeadIcon: this.HeadIcon,
		Job:      int32(this.Job),
	}
}

func (this *TeamInviteUserInfo) ToPb() *pb.TeamInviteUserInfo {
	return &pb.TeamInviteUserInfo{
		Uid:      this.Uid,
		Nickname: this.Nickname,
		HeadIcon: this.HeadIcon,
	}
}

func (this *TeamInviteInfo) ToPb() *pb.TeamInviteInfo {
	return &pb.TeamInviteInfo{
		TeamUid:  this.TeamUid,
		Uid:      this.Uid,
		Nickname: this.Nickname,
		HeadIcon: this.HeadIcon,
		Time:     int64(this.Time),
	}
}

var (
	teamMap                  = ut.NewMapLock[string, *TeamInfo]()
	teamDbChan               = make(chan string, 1000)
	lastUpdateTeamTime int64 = 0
)

// 队伍信息
type TeamInfo struct {
	Uid string `bson:"uid"`

	UserList   []*TeamUserInfo       `bson:"user_list"`   //玩家列表
	InviteList []*TeamInviteUserInfo `bson:"invite_list"` //邀请的玩家信息
	ChatList   []*FriendChatInfo

	userListLock   *deadlock.RWMutex
	inviteListLock *deadlock.RWMutex
	chatListLock   *deadlock.RWMutex

	ApplyTime     int64 `bson:"apply_time"`      //申请报名时间 0.表示未报名 返回给客户端时为-1
	MatchOpenTime int64 `bson:"match_open_time"` //匹配完成的开服时间
	PlaySid       int32 `bson:"play_sid"`        //当前游玩的服务器id
	RoomType      int32 `bson:"room_type"`       //区服类型
}

func NewTeam(uid string, roomType int32) *TeamInfo {
	team := &TeamInfo{
		Uid:       uid,
		RoomType:  roomType,
		UserList:  []*TeamUserInfo{},
		ApplyTime: 0,
		PlaySid:   0,
	}
	team.Init()
	teamDbChan <- uid
	return team
}

func (this *TeamInfo) ToPb() *pb.TeamInfo {
	now := time.Now().UnixMilli()
	var cancelApplySurplusTime int32 = -1
	if this.ApplyTime > 0 {
		cancelApplySurplusTime = int32(ut.MaxInt64(0, slg.SERVER_APPLY_CANCEL_CD-(now-this.ApplyTime)))
	}
	return &pb.TeamInfo{
		Uid:                    this.Uid,
		RoomType:               int32(this.RoomType),
		PlaySid:                int32(this.PlaySid),
		CancelApplySurplusTime: cancelApplySurplusTime,
		UserList:               this.ToUserLisgPb(),
		InviteList:             this.ToInviteListPb(),
		MatchOpenTime:          int64(this.GetMatchOpenTime()),
		// MatchOpenTime:          int64(ut.Max(0, this.MatchOpenTime-now)),
	}
}

// 创建队伍
func CreateTeam(uid, nickname, headIcon, lid string, roomType int32) *TeamInfo {
	team := NewTeam(uid, roomType)
	team.addUser(NewTeamUser(uid, nickname, headIcon, 1))
	teamMap.Set(team.Uid, team)
	rds.RdsHSet(rds.RDS_LOBBY_TEAM_LID_MAP_KEY, team.Uid, lid)
	return team
}

// 删除队伍
func DelTeam(team *TeamInfo) {
	if len(team.InviteList) > 0 && len(team.UserList) > 1 {
		return
	}
	teamUid := team.Uid
	teamMap.Del(teamUid)
	teamDb.DelTeamByUid(teamUid)
	rds.RdsHDel(rds.RDS_LOBBY_TEAM_LID_MAP_KEY, teamUid)
}

func (this *TeamInfo) GetMatchOpenTime() int64 {
	if this.MatchOpenTime > 0 && this.MatchOpenTime <= time.Now().UnixMilli() {
		this.MatchOpenTime = 0
	}
	return this.MatchOpenTime
}

func (this *TeamInfo) Init() {
	this.userListLock = new(deadlock.RWMutex)
	this.inviteListLock = new(deadlock.RWMutex)
	this.chatListLock = new(deadlock.RWMutex)
}

func (this *TeamInfo) ToUserLisgPb() []*pb.TeamUserInfo {
	arr := []*pb.TeamUserInfo{}
	this.userListLock.RLock()
	defer this.userListLock.RUnlock()
	for _, v := range this.UserList {
		arr = append(arr, v.ToPb())
	}
	return arr
}

func (this *TeamInfo) ToInviteListPb() []*pb.TeamInviteUserInfo {
	arr := []*pb.TeamInviteUserInfo{}
	this.inviteListLock.RLock()
	defer this.inviteListLock.RUnlock()
	for _, v := range this.InviteList {
		arr = append(arr, v.ToPb())
	}
	return arr
}

func (this *TeamInfo) ToChatListPb() []*pb.ChatInfo {
	arr := []*pb.ChatInfo{}
	this.chatListLock.RLock()
	defer this.chatListLock.RUnlock()
	for _, v := range this.ChatList {
		arr = append(arr, v.ToPb())
	}
	return arr
}

// 是否空队伍
func (this *TeamInfo) IsEqmpty() bool {
	this.userListLock.RLock()
	defer this.userListLock.RUnlock()
	this.inviteListLock.RLock()
	defer this.inviteListLock.RUnlock()
	return len(this.UserList) <= 1 && len(this.InviteList) == 0 && this.PlaySid == 0 && this.ApplyTime == 0
}

// 获取队伍人数 包含邀请中的
func (this *TeamInfo) GetUserCount() int {
	this.userListLock.RLock()
	defer this.userListLock.RUnlock()
	this.inviteListLock.RLock()
	defer this.inviteListLock.RUnlock()
	return len(this.UserList) + len(this.InviteList)
}

// 是否还有邀请
func (this *TeamInfo) IsHasInvite() bool {
	this.inviteListLock.RLock()
	defer this.inviteListLock.RUnlock()
	return len(this.InviteList) > 0
}

// 添加玩家到队伍
func (this *TeamInfo) addUser(user *TeamUserInfo) bool {
	this.userListLock.Lock()
	defer this.userListLock.Unlock()
	for _, v := range this.UserList {
		if v.Uid == user.Uid {
			return false
		}
	}
	this.UserList = append(this.UserList, user)
	log.Info("addUser team uid: %v, addUser uid: %v", this.Uid, user.Uid)
	teamDbChan <- this.Uid
	return true
}

// 队伍添加邀请信息
func (this *Lobby) addInviteUser(uid, nickname, headIcon string, team *TeamInfo) bool {
	team.inviteListLock.Lock()
	if array.Some(team.InviteList, func(m *TeamInviteUserInfo) bool { return m.Uid == uid }) {
		team.inviteListLock.Unlock()
		return false
	}
	team.InviteList = append(team.InviteList, NewTeamInviteUser(uid, nickname, headIcon))
	team.inviteListLock.Unlock()
	teamDbChan <- team.Uid
	this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
	return true
}

// 玩家回复邀请
func (this *TeamInfo) userResponseInvite(uid string, agree bool) (teamPb *pb.TeamInfo, err string) {
	var inviteUser *TeamInviteUserInfo
	this.inviteListLock.Lock()
	delIndex := array.FindIndex(this.InviteList, func(m *TeamInviteUserInfo) bool { return m.Uid == uid })
	if delIndex == -1 {
		// 没有邀请该玩家
		this.inviteListLock.Unlock()
		return nil, ecode.TEAM_NOT_EXIST.String()
	}
	inviteUser = this.InviteList[delIndex]
	// 从邀请列表中移除
	this.InviteList = append(this.InviteList[0:delIndex], this.InviteList[delIndex+1:]...)
	this.inviteListLock.Unlock()
	teamDbChan <- this.Uid
	// 同意则添加到队伍中
	if agree {
		// 人数上限
		if this.GetUserCount() >= slg.TEAM_USER_NUM_MAX {
			return nil, ecode.TEAM_USER_NUM_LIMIT.String()
		}
		this.userListLock.Lock()
		if array.Some(this.UserList, func(m *TeamUserInfo) bool { return m.Uid == uid }) {
			// 玩家已在该队伍中
			this.userListLock.Unlock()
			return nil, ecode.USER_ALREADY_IN_TEAM.String()
		}
		this.UserList = append(this.UserList, NewTeamUser(uid, inviteUser.Nickname, inviteUser.HeadIcon, 0))
		this.userListLock.Unlock()
		teamPb = this.ToPb()
	}
	return
}

// 删除队员
func (this *TeamInfo) delTeammate(uid string) (err, nickname string) {
	log.Info("delTeammate team uid: %v, delTeammate uid: %v", this.Uid, uid)
	this.userListLock.Lock()
	defer this.userListLock.Unlock()
	for i := len(this.UserList) - 1; i >= 0; i-- {
		if m := this.UserList[i]; m.Uid == uid {
			nickname = m.Nickname
			this.UserList = append(this.UserList[:i], this.UserList[i+1:]...) //删除
			teamDbChan <- this.Uid
			return
		}
	}
	return ecode.USER_NOT_IN_TEAM.String(), ""
}

// 删除邀请
func (this *TeamInfo) delInviteTeammate(uid string) (err, nickname string) {
	this.inviteListLock.Lock()
	defer this.inviteListLock.Unlock()
	for i := len(this.InviteList) - 1; i >= 0; i-- {
		if m := this.InviteList[i]; m.Uid == uid {
			nickname = m.Nickname
			this.InviteList = append(this.InviteList[:i], this.InviteList[i+1:]...) //删除
			teamDbChan <- this.Uid
			return
		}
	}
	return ecode.USER_NOT_IN_TEAM.String(), ""
}

// 改变队伍模式
func (this *Lobby) changeRoomType(roomType int32, team *TeamInfo) (err string) {
	if team.PlaySid > 0 {
		return ecode.IN_GAME.String() //是否已经游戏了
	} else if team.ApplyTime > 0 {
		return ecode.IN_APPLY.String()
	}
	team.RoomType = roomType
	// 通知队伍更新信息
	this.RcpTeamNotify(team, slg.LOBBY_TEAM_CHANGE_MODE, &pb.LOBBY_ONTEAMCHANGEMODE_NOTIFY{RoomType: int32(roomType)})
	return
}

// 更新队员信息
func (this *Lobby) UpdateUserInfo(uid, nickname, headicon string, team *TeamInfo) {
	info := team.FindUser(uid)
	if info == nil {
		return
	}
	isNotify := info.GetNickname() != nickname //只有昵称不一样才通知
	info.SetNickname(nickname)
	info.SetHeadIcon(headicon)
	// 通知队伍
	if isNotify {
		this.RcpTeamNotify(team, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: team.ToPb()})
	}
}

// 查找用户 包含邀请中的
func (this *TeamInfo) FindUser(uid string) IBaseTeamUserInfo {
	this.userListLock.RLock()
	if info := array.Find(this.UserList, func(m *TeamUserInfo) bool { return m.Uid == uid }); info != nil {
		this.userListLock.RUnlock()
		return info
	}
	this.userListLock.RUnlock()
	this.inviteListLock.RLock()
	defer this.inviteListLock.RUnlock()
	return array.Find(this.InviteList, func(m *TeamInviteUserInfo) bool { return m.Uid == uid })
}

// 检测踢人权限
func (this *TeamInfo) CheckDelAuth(uid string) string {
	this.userListLock.RLock()
	defer this.userListLock.RUnlock()
	for _, v := range this.UserList {
		if v.Uid == uid && v.Job == 1 {
			return ""
		}
	}
	return ecode.NOT_OPERATING_AUTH.String()
}

// 组队db tick
func RunTeamDbTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 10)
		for isRunning {
			<-tiker.C
			UpdateTeamDb(false)
		}
	}()
}

// 更新队伍数据到db
func UpdateTeamDb(close bool) {
	sum := len(teamDbChan)
	if sum == 0 || lastUpdateTeamTime > 0 {
		return
	}
	lastUpdateTeamTime = time.Now().UnixMilli()
	datas := []*TeamInfo{}
	uidMap := map[string]bool{}
	for len(teamDbChan) > 0 {
		uid := <-teamDbChan
		if uidMap[uid] {
			continue
		}
		uidMap[uid] = true
		if len(uidMap) >= 200 {
			log.Warning("UpdateTeamDb len over 200...")
			break
		}
	}
	for uid := range uidMap {
		teamInfo := teamMap.Get(uid)
		if teamInfo != nil {
			datas = append(datas, teamInfo)
		}
	}
	count, surplus := len(datas), len(teamDbChan)
	if close || count > 5 {
		log.Info("UpdateTeamDb sum: %v, count: %v, surplus: %v", sum, count, surplus)
	}
	teamDb.UpdateTeams(datas)
	dt := time.Now().UnixMilli() - lastUpdateTeamTime
	lastUpdateTeamTime = 0
	if close || dt >= 1000 {
		log.Info("UpdateTeamDb sum: %v, count: %v, surplus: %v, sumTime: %vms", sum, count, surplus, dt)
	}
	if close && surplus > 0 {
		UpdateTeamDb(close)
	}
}

// 获取队伍 不在内存中则从数据库获取
func (this *Lobby) GetLocalTeamOrDb(uid string) *TeamInfo {
	if uid == "" {
		return nil
	}
	team := teamMap.Get(uid)
	if team == nil {
		teamData, err := teamDb.FindTeamByUid(uid)
		if err != "" {
			return nil
		}
		team = &teamData
		team.Init()
		teamMap.Set(team.Uid, team)
		rds.RdsHSet(rds.RDS_LOBBY_TEAM_LID_MAP_KEY, team.Uid, this.GetLid())
	}
	if team.PlaySid != 0 {
		room := GetRoomById(team.PlaySid)
		if room == nil || IsGameOver(room) {
			// 兼容 区服不存在或已结算 清理游玩区服和报名信息
			team.PlaySid = 0
			team.ApplyTime = 0
			team.MatchOpenTime = 0
			teamDbChan <- team.Uid
		}
	}
	return team
}

// 获取队伍信息
func (this *Lobby) GetTeamInfoPb(user *User) *pb.TeamInfo {
	teamUid := user.TeamUid
	if teamUid == "" {
		return nil
	}
	rst, err := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_GET_TEAM_INFO))
	if err != "" {
		if err == ecode.TEAM_NOT_EXIST.String() {
			user.SetTeamUid("") //如果没有队伍 就清理
		}
		log.Info("GetTeamInfoPb nil, uid: %v, teamUid: %v, err: %v", user.UID, teamUid, err)
		return nil
	}
	teamPb := &pb.TeamInfo{}
	pb.ProtoUnMarshal(rst, teamPb)
	if array.Some(teamPb.UserList, func(m *pb.TeamUserInfo) bool { return m.Uid == user.UID }) {
		// 在队伍中就返回
		return teamPb
	}
	// 不在队伍中则清理teamUid
	user.SetTeamUid("")
	log.Info("GetTeamInfoPb user not in team, uid: %v, teamUid: %v", user.UID, teamUid)
	return nil
}

// 检测并清除玩家队伍uid
func (this *Lobby) CheckClearTeam(team *TeamInfo) bool {
	if team == nil || team.Uid == "" {
		return true
	} else if !team.IsEqmpty() {
		return false
	}
	// 通知解散队伍
	this.SendUserNotify(team.Uid, slg.LOBBY_TEAM_UPDATE, &pb.LOBBY_ONTEAMUPDATE_NOTIFY{TeamInfo: nil})
	// 通知该玩家清除队伍uid 用team.Uid 因为队长uid就是team.Uid
	this.InvokeUserFuncNR(team.Uid, slg.RPC_SET_USER_TEAMID, "")
	// 通知聊天服删除队伍频道
	this.InvokeChatRpcNR(slg.RPC_DEL_TEAM, team.Uid)
	return true
}

// 检测并删除队伍
func (this *Lobby) CheckDelTeam(uid, teamUid string) {
	if teamUid != "" {
		return
	}
	this.InvokeTeamFuncNR(uid, slg.RPC_DEL_TEAM)
}

// 发送组队系统消息
func (this *Lobby) SendTeamSysMsg(teamUid, msgType string, params ...string) {
	params = append([]string{msgType}, params...)
	chatInfo := &pb.LobbyChatInfo{
		Uid:     ut.ID(),
		Channel: "1_" + teamUid,
		Sender:  "-1",
		Time:    time.Now().UnixMilli(),
		Params:  params,
	}
	bytes, _ := pb.ProtoMarshal(chatInfo)
	this.InvokeChatRpcNR(slg.RPC_SEND_CHAT, bytes)
}
