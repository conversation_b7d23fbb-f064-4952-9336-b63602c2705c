package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 一个商人
type Merchant struct {
	State int32 `json:"state"`
}

type MerchantList struct {
	deadlock.RWMutex
	List []*Merchant
}

// 这个暂时不用加锁
func (this *MerchantList) Strip() []map[string]interface{} {
	arr := []map[string]interface{}{}
	for _, m := range this.List {
		arr = append(arr, map[string]interface{}{
			"state": m.State,
		})
	}
	return arr
}

func (this *MerchantList) ToPb() []*pb.MerchantInfo {
	arr := []*pb.MerchantInfo{}
	for _, m := range this.List {
		arr = append(arr, &pb.MerchantInfo{
			State: m.State,
		})
	}
	return arr
}

func (this *Model) ToMerchants() []map[string]interface{} {
	return this.Merchants.Strip()
}

func (this *Model) ToMerchantsPb() []*pb.MerchantInfo {
	return this.Merchants.ToPb()
}

// 获取商人运输量
func (this *Model) GetMerchantTransitCap() int32 {
	return 1000 + this.GetPolicyEffectInt(effect.MERCHANT_TRANSIT_CAP)
}

// 刷新商人数量
func (this *Model) UpdateMerchantCount(count int) {
	this.Merchants.Lock()
	defer this.Merchants.Unlock()
	for len(this.Merchants.List) < count {
		this.Merchants.List = append(this.Merchants.List, &Merchant{State: constant.MS_NONE})
	}
	l := len(this.Merchants.List)
	if count < l { //这里兼容一下 如果有多的 删除掉多余的
		log.Error("UpdateMerchantCount error count < len. count=" + ut.Itoa(count) + ", len=" + ut.Itoa(l))
		cnt := l - count
		for i := l - 1; i >= 0 && cnt > 0; i-- {
			m := this.Merchants.List[i]
			if m.State == constant.MS_NONE {
				this.Merchants.List = append(this.Merchants.List[:i], this.Merchants.List[i+1:]...)
				cnt -= 1
			}
		}
	}
	this.PutNotifyQueue(constant.NQ_UPDATE_MERCHANT, &pb.OnUpdatePlayerInfoNotify{Data_20: this.Merchants.ToPb()})
}

// 获取当前空闲的商人个数
func (this *Model) GetMerchantIdleCount() int {
	this.Merchants.RLock()
	defer this.Merchants.RUnlock()
	cnt := 0
	for _, m := range this.Merchants.List {
		if m.State == constant.MS_NONE {
			cnt += 1
		}
	}
	return cnt
}

// 改变商人状态
func (this *Model) ChangeMerchantState(c, t, count int32) {
	this.Merchants.Lock()
	defer this.Merchants.Unlock()
	for i, l := 0, len(this.Merchants.List); i < l && count > 0; i++ {
		m := this.Merchants.List[i]
		if m.State == c {
			m.State = t
			count -= 1
		}
	}
}

// 检测空闲商人状态
func (this *Model) CheckMerchants() {
	var merchantTrading, merchantTransit int32 // 处于市场和运输状态中的商人数量
	this.Merchants.RLock()
	for _, v := range this.Merchants.List {
		if v.State == constant.MS_TRADING {
			merchantTrading++
		} else if v.State == constant.MS_TRANSIT {
			merchantTransit++
		}
	}
	this.Merchants.RUnlock()

	// 获取玩家市场中商人数量
	tradingCount := this.room.GetBazaar().GetTradingMerchantCount(this.Uid)
	if diffTrading := merchantTrading - tradingCount; diffTrading > 0 {
		// 市场状态中的商人数量比实际更多 多余的转为空闲
		this.ChangeMerchantState(constant.MS_TRADING, constant.MS_NONE, diffTrading)
	}
	// 获取运输中的商人数量
	transitCount := this.room.GetWorld().GetPlayerTransitMerchantCount(this.Uid)
	if diffTransit := merchantTransit - transitCount; diffTransit > 0 {
		// 运输状态中的商人数量比实际更多 多余的转为空闲
		this.ChangeMerchantState(constant.MS_TRANSIT, constant.MS_NONE, diffTransit)
	}
}
