package http

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/mitchellh/mapstructure"
)

// Apple推送
func (this *Http) httpAppleNotify(signedPayload string) (ret map[string]interface{}, err error) {
	log.Info("httpAppleNotify signedPayload: %v", signedPayload)
	headerStr := strings.Split(signedPayload, ".")[0]
	// 解析签名标头
	publicKey, err := sdk.ParseAppleJwsHead(headerStr)
	if err != nil {
		log.Error("AppleNotify parseAppleJwsHead err: %v", err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	// 解析JWS格式的推送信息
	mapClaims, err := ut.ParseJwsToken(signedPayload, publicKey)
	// 解析推送数据
	data, ok := mapClaims["data"].(map[string]interface{})
	if !ok {
		log.Error("AppleNotify Parse data err: %v", data)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	var appleNotifyData sdk.AppleNotifyData
	err = mapstructure.Decode(data, &appleNotifyData)
	if err != nil {
		log.Error("AppleNotify Decode data err: %v", data)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	notifyType := ut.String(mapClaims["notificationType"])
	notifySubType := ut.String(mapClaims["subtype"])
	switch notifyType {
	case sdk.APPLE_NOTIFY_TYPE_DID_RENEW, sdk.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS:
		// 解析推送数据中的JWS格式的订阅信息
		renewalHeaderStr := strings.Split(appleNotifyData.SignedRenewalInfo, ".")[0]
		// 解析签名标头
		publicKey, err = sdk.ParseAppleJwsHead(renewalHeaderStr)
		if err != nil {
			log.Error("AppleNotify parseAppleRenewalJwsHead err: %v", err)
			break
		}
		// 解析JWS格式的推送信息
		renewalMapClaims, err := ut.ParseJwsToken(appleNotifyData.SignedRenewalInfo, publicKey)
		if err != nil {
			log.Error("AppleNotify parseAppleRenewalJws data err: %v", err)
			break
		}
		orderId := ut.String(renewalMapClaims["originalTransactionId"])
		endTime := ut.Int64(renewalMapClaims["renewalDate"])
		offerType := ut.String(mapClaims["offerDiscountType"])
		if notifyType == sdk.APPLE_NOTIFY_TYPE_DID_RENEW {
			// 续订
			this.handleDidRenew(orderId, slg.PAY_PLATFORM_APPLE, endTime, offerType)
		} else if notifyType == sdk.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS {
			// 更新自动续订状态
			if notifySubType == sdk.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_ENABLED {
				this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, true)
			} else if notifySubType == sdk.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_DISABLED {
				this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, false)
			}
		}
	case sdk.APPLE_NOTIFY_TYPE_REFUND:
		// 解析推送数据中的JWS格式的交易信息
		transHeaderStr := strings.Split(appleNotifyData.SignedTransactionInfo, ".")[0]
		// 解析签名标头
		publicKey, err = sdk.ParseAppleJwsHead(transHeaderStr)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJwsHead err: %v", err)
			break
		}
		// 解析JWS格式的推送信息
		transMapClaims, err := ut.ParseJwsToken(appleNotifyData.SignedTransactionInfo, publicKey)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJwsHead data err: %v", err)
			break
		}
		orderId := ut.String(transMapClaims["originalTransactionId"])
		purchaseType := ut.String(transMapClaims["type"])
		// 处理退款
		switch purchaseType {
		case sdk.APPLE_TRANSACTION_TYPE_CONSUMABLE:
			// 消耗性应用内购买
			this.handleOrderRefund(orderId, slg.PAY_PLATFORM_APPLE, appleNotifyData.ConsumptionRequestReason, "", appleNotifyData.Environment)
		case sdk.APPLE_TRANSACTION_TYPE_AUTO_RENEWABLE_SUBSCRIPTION, sdk.APPLE_TRANSACTION_TYPE_NON_RENEWING_SUBSCRIPTION:
			// 自动续订和不可续订的订阅
			this.handleSubRefund(orderId, slg.PAY_PLATFORM_APPLE)
		}

	}
	return slg.HttpResponseSuccessWithDataNoDesc(""), nil
}

// Google推送
func (this *Http) httpGoogleNotify(message string) (ret map[string]interface{}, err error) {
	log.Info("httpGoogleNotify message: %v", message)
	notify := &sdk.GoogleNotifyMessage{}
	err = json.Unmarshal([]byte(message), notify)
	if err != nil {
		log.Error("httpGoogleNotify Unmarshal message : %v, err: %v", message, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	// 解析base64编码数据
	dataBytes, err := base64.StdEncoding.DecodeString(notify.Data)
	if err != nil {
		log.Error("httpGoogleNotify base64Decode data: %v, err: %v", notify.Data, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	notifyData := &sdk.DeveloperNotification{}
	err = json.Unmarshal(dataBytes, notifyData)
	if err != nil {
		log.Error("httpGoogleNotify Unmarshal data %v, err: %v", notify.Data, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	if notifyData.Subscription != nil {
		// 处理订阅通知
		this.SubCheckByToken(notifyData.Subscription.PurchaseToken, slg.PAY_PLATFORM_GOOGLE, notifyData.Subscription.NotificationType)
	} else if notifyData.VoidedPurchase != nil {
		// 处理退款通知
		if notifyData.VoidedPurchase.ProductType == 1 {
			// 订阅退款
			this.handleSubRefund(notifyData.VoidedPurchase.OrderId, slg.PAY_PLATFORM_GOOGLE)
		} else if notifyData.VoidedPurchase.ProductType == 2 {
			// 消耗性物品退款
			this.handleOrderRefund(notifyData.VoidedPurchase.OrderId, slg.PAY_PLATFORM_GOOGLE, "", "", "")
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(""), nil
}

// 退款处理
func (this *Http) handleOrderRefund(orderId, platform, reason, source, environment string) {
	log.Info("handleOrderRefund orderId: %v, platform: %v", orderId, platform)
	orderInfo, e := recharge.RechargeDb.FindByOrderId(orderId, platform)
	if e != "" {
		// 未查询到订单
		log.Error("handleOrderRefund order not find orderId: %v, err: %v", orderId, e)
		return
	}
	if orderInfo.State == recharge.ORDER_STATE_REFUND {
		// 已经是已退款状态
		log.Error("handleOrderRefund already refund orderId: %v", orderId)
		return
	}
	if orderInfo.State == recharge.ORDER_STATE_FINISH {
		oldState := orderInfo.State
		// 更新订单状态
		recharge.RechargeDb.UpdateRefundOrder(orderInfo.UID)
		lid := rds.GetUserLid(orderInfo.UserId)
		if lid != "" {
			// 通知大厅服修改玩家数据
			this.InvokeLobbyRpcNR(lid, slg.RPC_ORDER_REFUND_BY_NOTIFY, orderInfo.UserId, orderInfo.UID, orderInfo.ProductId, orderInfo.CurrencyType, orderInfo.PayAmount, orderInfo.Quantity, oldState)
		}
		now := time.Now()
		dateTime := now.Format("2006-01-02 15:04:05")
		purchaseTime := time.UnixMilli(int64(orderInfo.PurchaseTime))
		purchaseDate := purchaseTime.Format("2006-01-02 15:04:05")
		// dh上报
		var enventData map[string]interface{}
		if platform == slg.PAY_PLATFORM_APPLE {
			// ios数据
			enventData = map[string]interface{}{
				"transaction_id":    orderId,
				"refund_reason":     reason,
				"refund_identifier": orderInfo.UserId,
				"refund_date":       dateTime,
				"purchase_date":     purchaseDate,
				"product_id":        orderInfo.ProductId,
				"order_id":          orderId,
				"environment":       environment,
			}
		} else if platform == slg.PAY_PLATFORM_GOOGLE {
			enventData = map[string]interface{}{
				"refund_order":  orderId,
				"token":         orderInfo.UID,
				"voidedSource":  source,
				"voidedReason":  reason,
				"refund_time":   dateTime,
				"purchase_time": purchaseDate,
				"kind":          "androidpublisher#voidedPurchase",
				"classType":     "pull",
			}
		}
		dh.Track(dh.DH_EVENT_NAME_REFUND, enventData)
	}
}

// 续订处理
func (this *Http) handleDidRenew(orderId, platform string, endTime int64, offerType string) {
	log.Info("handleDidRenew orderId: %v, platform: %v, endTime: %v", orderId, platform, endTime)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		//未查询到订单
		log.Error("handleDidRenew order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	// 更新订单状态
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, endTime, recharge.ORDER_SUB_STATE_ACTIVE, true)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeLobbyRpc(lid, slg.RPC_DID_RENEW_BY_NOTIFY, orderInfo.UserId, orderInfo.UID, orderId, orderInfo.ProductId, orderInfo.Type, orderInfo.CurrencyType, orderInfo.PayAmount, endTime, offerType)
	}
}

// 续订状态更新
func (this *Http) handleRenewStateChange(orderId, platform string, auto bool) {
	log.Info("handleRenewStateChange orderId: %v, platform: %v, auto: %v", orderId, platform, auto)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		//未查询到订单
		log.Error("handleRenewStateChange order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	// 更新订单状态
	state := orderInfo.State
	if !auto {
		state = recharge.ORDER_SUB_STATE_CANCEL
	}
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, orderInfo.EndTime, state, auto)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeNR("lobby@lobby"+lid, slg.RPC_RENEW_STATE_CHANGE_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
			ut.Bytes(orderInfo.PayAmount), ut.Bytes(orderInfo.EndTime), ut.Bytes(auto))
	}
}

// 订阅退款处理
func (this *Http) handleSubRefund(orderId, platform string) {
	log.Info("handleSubRefund orderId: %v, platform: %v", orderId, platform)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		//未查询到订单
		log.Error("handleSubRefund order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, 0, recharge.ORDER_SUB_STATE_REFUND, false)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeNR("lobby@lobby"+lid, slg.RPC_SUB_REFUND_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
			ut.Bytes(orderInfo.PayAmount))
	}
}

// 根据token验证订阅订单 (目前仅Google使用)
func (this *Http) SubCheckByToken(token, platform string, notifyType int) (endTime int64, auto bool, err error) {
	log.Info("SubCheckByToken token: %v, notifyType: %v", token, notifyType)
	orderInfo, e := recharge.SubscriptionDb.FindByToken(token, platform)
	offerType := slg.TA_SUB_STATE_RENEWAL
	if e != "" {
		//未查询到订单
		log.Error("SubCheckByToken order not find token: %v, platform: %v, err: %v", token, platform, e)
		err = errors.New(e)
		return
	}
	switch orderInfo.Platform {
	case slg.PAY_PLATFORM_GOOGLE:
		err, _, endTime, auto, _, _, _, offerType, _ = sdk.GoogleSubVerify(orderInfo.ProductId, orderInfo.Token)
		if err != nil {
			log.Info("SubCheckByToken GoogleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, orderInfo.UserId, err)
		}
	}
	state := orderInfo.State
	lid := rds.GetUserLid(orderInfo.UserId)
	switch notifyType {
	case sdk.GOOGLE_SUBSCRIPTION_RENEWED:
		// 续订
		state = recharge.ORDER_SUB_STATE_ACTIVE
		if lid != "" {
			this.InvokeNR("lobby@lobby"+lid, slg.RPC_DID_RENEW_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.OrderId), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
				ut.Bytes(orderInfo.PayAmount), ut.Bytes(endTime), ut.Bytes(offerType))
		}
	case sdk.GOOGLE_SUBSCRIPTION_CANCELED:
		// 取消续订
		state = recharge.ORDER_SUB_STATE_CANCEL
		if lid != "" {
			// 通知大厅服修改玩家数据
			this.InvokeNR("lobby@lobby"+lid, slg.RPC_RENEW_STATE_CHANGE_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
				ut.Bytes(orderInfo.PayAmount), ut.Bytes(orderInfo.EndTime), ut.Bytes(auto))
		}
	case sdk.GOOGLE_SUBSCRIPTION_REVOKED:
		// 退款
		state = recharge.ORDER_STATE_REFUND
		if lid != "" {
			// 通知大厅服修改玩家数据
			this.InvokeNR("lobby@lobby"+lid, slg.RPC_SUB_REFUND_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
				ut.Bytes(orderInfo.PayAmount))
		}
	}
	// 更新订单状态
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, endTime, state, auto)
	return
}

// 退款订单检测
func (this *Http) OrderRefundCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 10)
		defer tiker.Stop()
		for isRunning {
			if err, googleOrderList := sdk.GoogleVoidedOrderCheck(); err == nil && googleOrderList != nil {
				for _, info := range googleOrderList {
					// 谷歌退款处理
					_, e := recharge.RechargeDb.FindByOrderId(info.OrderId, slg.PAY_PLATFORM_GOOGLE)
					if e == "" {
						// 消耗性物品退款
						this.handleOrderRefund(info.OrderId, slg.PAY_PLATFORM_GOOGLE, ut.String(info.VoidedReason), ut.String(info.VoidedSource), "")
					} else {
						// 订阅退款
						this.handleSubRefund(info.OrderId, slg.PAY_PLATFORM_GOOGLE)
					}
				}
			}
			<-tiker.C
		}
	}()
}
