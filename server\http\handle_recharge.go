package http

import (
	"encoding/base64"
	"encoding/json"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"

	"github.com/go-redsync/redsync/v4"
	"github.com/huyangv/vmqant/log"
	"github.com/mitchellh/mapstructure"
)

// Apple推送
func (this *Http) httpAppleNotify(signedPayload string) (ret map[string]interface{}, err error) {
	log.Info("httpAppleNotify signedPayload: %v", signedPayload)
	// 解析JWS格式的推送信息
	mapClaims, err := sdk.ParseJwsToken(signedPayload)
	if err != nil {
		log.Error("AppleNotify parseAppleTransJwsHead data err: %v", err)
		return
	}
	// 解析推送数据
	data, ok := mapClaims["data"].(map[string]interface{})
	if !ok {
		log.Error("AppleNotify Parse data err: %v", data)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	var appleNotifyData sdk.AppleNotifyData
	err = mapstructure.Decode(data, &appleNotifyData)
	if err != nil {
		log.Error("AppleNotify Decode data err: %v", data)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	notifyType := ut.String(mapClaims["notificationType"])
	notifySubType := ut.String(mapClaims["subtype"])
	switch notifyType {
	case sdk.APPLE_NOTIFY_TYPE_ONE_TIME_CHARGE:
		// 解析推送数据中的JWS格式的交易信息
		transMapClaims, err := sdk.ParseJwsToken(appleNotifyData.SignedTransactionInfo)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJws data err: %v", err)
			break
		}
		orderId := ut.String(transMapClaims["originalTransactionId"])
		productId := ut.String(transMapClaims["productId"])
		appAccountToken := ut.String(transMapClaims["appAccountToken"])
		currency := ut.String(transMapClaims["currency"])
		purchaseDate := ut.Int64(transMapClaims["purchaseDate"])
		quantity := ut.Int32(transMapClaims["quantity"])
		priceInt := ut.Int64(transMapClaims["price"])
		priceStr := currency + ut.String(float64(priceInt)/1000)
		userId := ""
		if appAccountToken != "" {
			// 根据uuid获取玩家uid
			userId, _ = recharge.UuidMapDb.FindUserUuidByUuid(appAccountToken)
		}
		this.handlePayOrder(orderId, slg.PAY_PLATFORM_APPLE, "", userId, productId, priceStr, "", quantity, purchaseDate)

	case sdk.APPLE_NOTIFY_TYPE_SUBSCRIBED, sdk.APPLE_NOTIFY_TYPE_DID_RENEW, sdk.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS:
		// 解析推送数据中的JWS格式的订阅信息
		renewalMapClaims, err := sdk.ParseJwsToken(appleNotifyData.SignedRenewalInfo)
		if err != nil {
			log.Error("AppleNotify parseAppleRenewalJws data err: %v", err)
			break
		}
		orderId := ut.String(renewalMapClaims["originalTransactionId"])
		endTime := ut.Int64(renewalMapClaims["renewalDate"])
		offerType := ut.String(mapClaims["offerDiscountType"])
		productId := ut.String(renewalMapClaims["productId"])
		appAccountToken := ut.String(renewalMapClaims["appAccountToken"])
		currency := ut.String(renewalMapClaims["currency"])
		priceInt := ut.Int64(renewalMapClaims["renewalPrice"])
		switch notifyType {
		case sdk.APPLE_NOTIFY_TYPE_SUBSCRIBED:
			// 订阅
			priceStr := currency + ut.String(float64(priceInt)/1000)
			userId := ""
			if appAccountToken != "" {
				// 根据uuid获取玩家uid
				userId, _ = recharge.UuidMapDb.FindUserUuidByUuid(appAccountToken)
			}
			this.handleSubPayOrder(orderId, slg.PAY_PLATFORM_APPLE, "", userId, productId, priceStr, "", offerType, ut.Now(), endTime)
		case sdk.APPLE_NOTIFY_TYPE_DID_RENEW:
			// 续订
			this.handleDidRenew(orderId, slg.PAY_PLATFORM_APPLE, endTime, offerType)
		case sdk.APPLE_NOTIFY_TYPE_DID_CHANGE_RENEWAL_STATUS:
			// 更新自动续订状态
			switch notifySubType {
			case sdk.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_ENABLED:
				this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, true)
			case sdk.APPLE_NOTIFY_SUB_TYPE_AUTO_RENEW_DISABLED:
				this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_APPLE, false)
			}
		}
	case sdk.APPLE_NOTIFY_TYPE_REFUND:
		// 解析推送数据中的JWS格式的交易信息
		transMapClaims, err := sdk.ParseJwsToken(appleNotifyData.SignedTransactionInfo)
		if err != nil {
			log.Error("AppleNotify parseAppleTransJwsHead data err: %v", err)
			break
		}
		orderId := ut.String(transMapClaims["originalTransactionId"])
		purchaseType := ut.String(transMapClaims["type"])
		// 处理退款
		switch purchaseType {
		case sdk.APPLE_TRANSACTION_TYPE_CONSUMABLE:
			// 消耗性应用内购买
			this.handleOrderRefund(orderId, slg.PAY_PLATFORM_APPLE, appleNotifyData.ConsumptionRequestReason, "", appleNotifyData.Environment)
		case sdk.APPLE_TRANSACTION_TYPE_AUTO_RENEWABLE_SUBSCRIPTION, sdk.APPLE_TRANSACTION_TYPE_NON_RENEWING_SUBSCRIPTION:
			// 自动续订和不可续订的订阅
			this.handleSubRefund(orderId, slg.PAY_PLATFORM_APPLE)
		}

	}
	return slg.HttpResponseSuccessWithDataNoDesc(""), nil
}

// Google推送
func (this *Http) httpGoogleNotify(message string) (ret map[string]interface{}, err error) {
	log.Info("httpGoogleNotify message: %v", message)
	notify := &sdk.GoogleNotifyMessage{}
	err = json.Unmarshal([]byte(message), notify)
	if err != nil {
		log.Error("httpGoogleNotify Unmarshal message : %v, err: %v", message, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	// 解析base64编码数据
	dataBytes, err := base64.StdEncoding.DecodeString(notify.Data)
	if err != nil {
		log.Error("httpGoogleNotify base64Decode data: %v, err: %v", notify.Data, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	notifyData := &sdk.DeveloperNotification{}
	err = json.Unmarshal(dataBytes, notifyData)
	if err != nil {
		log.Error("httpGoogleNotify Unmarshal data %v, err: %v", notify.Data, err)
		return slg.HttpResponseSuccessWithDataNoDesc(""), nil
	}
	if notifyData.Subscription != nil {
		// 处理订阅通知
		e, startTime, endTime, _, currencyType, orderId, price, offerType, productId, externalStr, isApiErr := sdk.GoogleSubVerify(notifyData.Subscription.PurchaseToken)
		if e != nil && !isApiErr {
			log.Error("httpGoogleNotify GoogleSubVerify err: %v", e)
			return slg.HttpResponseSuccessWithDataNoDesc(""), nil
		}
		cpOrderId, userId := sdk.ParseGoogleExernalStr(externalStr)
		switch notifyData.Subscription.NotificationType {
		case sdk.GOOGLE_SUBSCRIPTION_PURCHASED:
			// 购买
			this.handleSubPayOrder(orderId, slg.PAY_PLATFORM_GOOGLE, cpOrderId, userId, productId, currencyType+ut.String(price), notifyData.Subscription.PurchaseToken, offerType, startTime, endTime)
		case sdk.GOOGLE_SUBSCRIPTION_RENEWED:
			// 续订
			this.handleDidRenew(orderId, slg.PAY_PLATFORM_GOOGLE, endTime, offerType)
		case sdk.GOOGLE_SUBSCRIPTION_CANCELED:
			// 取消续订
			this.handleRenewStateChange(orderId, slg.PAY_PLATFORM_GOOGLE, false)
		}
	} else if notifyData.VoidedPurchase != nil {
		// 处理退款通知
		switch notifyData.VoidedPurchase.ProductType {
		case 1:
			// 订阅退款
			this.handleSubRefund(notifyData.VoidedPurchase.OrderId, slg.PAY_PLATFORM_GOOGLE)
		case 2:
			// 消耗性物品退款
			this.handleOrderRefund(notifyData.VoidedPurchase.OrderId, slg.PAY_PLATFORM_GOOGLE, "", "", "")
		}
	} else if notifyData.OneTime != nil {
		// 处理单次购买通知
		err, quantity, orderId, externalStr, purchaseTime := sdk.GoogleOrderVerify(notifyData.OneTime.Sku, notifyData.OneTime.PurchaseToken)
		if err != nil {
			log.Error("httpGoogleNotify GoogleOrderVerify err: %v", err)
			return slg.HttpResponseSuccessWithDataNoDesc(""), nil
		}
		cpOrderId, userId := sdk.ParseGoogleExernalStr(externalStr)
		this.handlePayOrder(orderId, slg.PAY_PLATFORM_GOOGLE, cpOrderId, userId, notifyData.OneTime.Sku, "", notifyData.OneTime.PurchaseToken, quantity, purchaseTime)
	}
	return slg.HttpResponseSuccessWithDataNoDesc(""), nil
}

// 物品购买处理
func (this *Http) handlePayOrder(orderId, platform, cpOrderId, userId, productId, price, token string, quantity int32, purchaseTime int64) {
	log.Info("handlePayOrder orderId: %v, userId: %v, platform: %v", orderId, userId, platform)
	if orderId == "" {
		// orderId为空,不做处理
		log.Error("handlePayOrder orderId nil")
		return
	}

	// 验证订单使用redis分布式锁 锁orderId
	orderMutex := rds.RedsyncInst.NewMutex(
		"order_id_lock:"+orderId,
		redsync.WithExpiry(10*time.Second), // 订单验证最长10秒
	)
	lockErr := orderMutex.Lock()
	if lockErr != nil {
		log.Error("handlePayOrder orderMutex Lock err: %v", lockErr)
		return
	}
	var unlockDone bool // 防止重复解锁
	defer func() {
		if !unlockDone { // 如果中途没解锁，就这里兜底解锁
			ok, unlockErr := orderMutex.Unlock()
			if lockErr != nil || !ok {
				log.Error("handlePayOrder orderMutex Unlock err: %v", unlockErr)
			}
		}
	}()

	var orderInfo recharge.RechargeOrderData
	var e string
	if cpOrderId != "" {
		// 根据cpOrderId查询
		orderInfo, e = recharge.RechargeDb.FindByUid(cpOrderId)
		if e != "" {
			// 未查询到订单
			log.Error("handlePayOrder order not find cpOrderId: %v, err: %v", cpOrderId, e)
			return
		}
	} else {
		// 根据orderId查询
		orderInfo, e = recharge.RechargeDb.FindByOrderId(orderId, platform)
		if e != "" {
			// 未查询到订单则创建订单
			orderData, e := recharge.RechargeDb.CreateOrder(userId, productId, platform)
			if e != "" {
				// 创建订单错误
				log.Error("verifyOrder FindByUserNotVerifyOrder create err userId: %v, uid: %v, err: %v", userId, cpOrderId, e)
				return
			}
			orderInfo = *orderData
		}
	}

	if orderInfo.State != recharge.ORDER_STATE_NOT_PAY {
		// 非待验证状态
		log.Error("handlePayOrder not verify orderId: %v, state: %v", orderId, orderInfo.State)
		return
	}

	// 默认货币类型是美元
	currencyType := "USD"
	payAmount := slg.RECHARGE_PRICE_USD_CONFIG[productId]
	// 更新订单状态
	recharge.RechargeDb.UpdateVerifiedOrder(orderInfo.UID, orderId, productId, price, currencyType, purchaseTime, payAmount, quantity, token)
	// 更新完状态后解锁
	unlockDone = true

	if userId != "" {
		// 通知大厅服修改玩家数据
		_, err := this.InvokeLobbyRpc(rds.GetUserLid(userId), slg.RPC_PAY_ORDER_BY_NOTIFY, orderInfo.UID, orderId, userId, productId, price, platform, quantity)
		if err != "" {
			log.Error("handlePayOrder InvokeLobbyRpc orderId: %v, userId: %v, err: %v", orderId, userId, err)
		}
	} else {
		log.Warning("handlePayOrder userId nil, orderId: %v", orderId)
	}

}

// 订阅购买处理
func (this *Http) handleSubPayOrder(orderId, platform, cpOrderId, userId, productId, price, token, offerType string, purchaseTime, endTime int64) {
	log.Info("handleSubPayOrder orderId: %v, userId: %v, platform: %v", orderId, userId, platform)
	if orderId == "" {
		// orderId为空,不做处理
		log.Error("handleSubPayOrder orderId nil")
		return
	}

	// 订阅验证涉及到奖励发放 使用redis分布式锁
	orderMutex := rds.RedsyncInst.NewMutex(
		"sub_order_lock:"+orderId,
		redsync.WithExpiry(10*time.Second), // 订单验证最长10秒
	)
	lockErr := orderMutex.Lock()
	if lockErr != nil {
		log.Error("handleSubPayOrder orderMutex Lock err: %v", lockErr)
	}
	var unlockDone bool // 防止重复解锁
	defer func() {
		if !unlockDone { // 如果中途没解锁，就这里兜底解锁
			ok, unlockErr := orderMutex.Unlock()
			if unlockErr != nil || !ok {
				log.Error("handleSubPayOrder orderMutex Unlock err: %v", unlockErr)
			}
		}
	}()

	var orderInfo recharge.SubscriptionData
	var e string
	if cpOrderId != "" {
		// 根据cpOrderId查询
		orderInfo, e = recharge.SubscriptionDb.FindByUid(cpOrderId)
		if e != "" {
			// 未查询到订单
			log.Error("handleSubPayOrder order not find cpOrderId: %v, err: %v", cpOrderId, e)
			return
		}
	} else {
		// 根据orderId查询
		orderInfo, e = recharge.SubscriptionDb.FindByOrderId(orderId, platform)
		if e != "" {
			// 未查询到订单则创建订单
			orderData, e := recharge.SubscriptionDb.CreateOrder(userId, productId, platform, "")
			if e != "" {
				// 创建订单错误
				log.Error("handleSubPayOrder FindByUserNotVerifyOrder create err userId: %v, uid: %v, err: %v", userId, cpOrderId, e)
				return
			}
			orderInfo = *orderData
		}
	}

	if orderInfo.State != recharge.ORDER_SUB_STATE_NOT_PAY {
		// 非待验证状态
		log.Error("handleSubPayOrder not verify orderId: %v, state: %v", orderId, orderInfo.State)
		return
	}

	// 默认货币类型是美元
	currencyType := "USD"
	payAmount := slg.RECHARGE_PRICE_USD_CONFIG[productId]
	// 更新订单状态
	recharge.SubscriptionDb.UpdateVerifiedOrder(orderInfo.UID, orderId, token, userId, purchaseTime, endTime, price, currencyType, purchaseTime, payAmount, true)
	// 更新完状态后解锁
	unlockDone = true

	if userId != "" {
		// 通知大厅服修改玩家数据
		_, err := this.InvokeLobbyRpc(rds.GetUserLid(userId), slg.RPC_SUB_PAY_ORDER_BY_NOTIFY, orderInfo.UID, orderId, userId, productId, platform, offerType, endTime)
		if err != "" {
			log.Error("handleSubPayOrder InvokeLobbyRpc orderId: %v, userId: %v, err: %v", orderId, userId, err)
		}
	} else {
		log.Warning("handleSubPayOrder userId nil, orderId: %v", orderId)
	}
}

// 退款处理
func (this *Http) handleOrderRefund(orderId, platform, reason, source, environment string) {
	log.Info("handleOrderRefund orderId: %v, platform: %v", orderId, platform)
	orderInfo, e := recharge.RechargeDb.FindByOrderId(orderId, platform)
	if e != "" {
		// 未查询到订单
		log.Error("handleOrderRefund order not find orderId: %v, err: %v", orderId, e)
		return
	}
	if orderInfo.State == recharge.ORDER_STATE_REFUND {
		// 已经是已退款状态
		log.Error("handleOrderRefund already refund orderId: %v", orderId)
		return
	}
	if orderInfo.State == recharge.ORDER_STATE_FINISH {
		oldState := orderInfo.State
		// 更新订单状态
		recharge.RechargeDb.UpdateRefundOrder(orderInfo.UID)
		lid := rds.GetUserLid(orderInfo.UserId)
		if lid != "" {
			// 通知大厅服修改玩家数据
			this.InvokeLobbyRpcNR(lid, slg.RPC_ORDER_REFUND_BY_NOTIFY, orderInfo.UserId, orderInfo.UID, orderInfo.ProductId, orderInfo.CurrencyType, orderInfo.PayAmount, orderInfo.Quantity, oldState)
		}
		now := time.Now()
		dateTime := now.Format("2006-01-02 15:04:05")
		purchaseTime := time.UnixMilli(int64(orderInfo.PurchaseTime))
		purchaseDate := purchaseTime.Format("2006-01-02 15:04:05")
		// dh上报
		var enventData map[string]interface{}
		if platform == slg.PAY_PLATFORM_APPLE {
			// ios数据
			enventData = map[string]interface{}{
				"transaction_id":    orderId,
				"refund_reason":     reason,
				"refund_identifier": orderInfo.UserId,
				"refund_date":       dateTime,
				"purchase_date":     purchaseDate,
				"product_id":        orderInfo.ProductId,
				"order_id":          orderId,
				"environment":       environment,
			}
		} else if platform == slg.PAY_PLATFORM_GOOGLE {
			enventData = map[string]interface{}{
				"refund_order":  orderId,
				"token":         orderInfo.UID,
				"voidedSource":  source,
				"voidedReason":  reason,
				"refund_time":   dateTime,
				"purchase_time": purchaseDate,
				"kind":          "androidpublisher#voidedPurchase",
				"classType":     "pull",
			}
		}
		dh.Track(dh.DH_EVENT_NAME_REFUND, enventData)
	}
}

// 续订处理
func (this *Http) handleDidRenew(orderId, platform string, endTime int64, offerType string) {
	log.Info("handleDidRenew orderId: %v, platform: %v, endTime: %v", orderId, platform, endTime)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		// 未查询到订单
		log.Error("handleDidRenew order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	// 更新订单状态
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, endTime, recharge.ORDER_SUB_STATE_ACTIVE, true)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeLobbyRpc(lid, slg.RPC_DID_RENEW_BY_NOTIFY, orderInfo.UserId, orderInfo.UID, orderId, orderInfo.ProductId, orderInfo.Type, orderInfo.CurrencyType, orderInfo.PayAmount, endTime, offerType)
	}
}

// 续订状态更新
func (this *Http) handleRenewStateChange(orderId, platform string, auto bool) {
	log.Info("handleRenewStateChange orderId: %v, platform: %v, auto: %v", orderId, platform, auto)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		// 未查询到订单
		log.Error("handleRenewStateChange order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	// 更新订单状态
	state := orderInfo.State
	if !auto {
		state = recharge.ORDER_SUB_STATE_CANCEL
	}
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, orderInfo.EndTime, state, auto)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeNR("lobby@lobby"+lid, slg.RPC_RENEW_STATE_CHANGE_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
			ut.Bytes(orderInfo.PayAmount), ut.Bytes(orderInfo.EndTime), ut.Bytes(auto))
	}
}

// 订阅退款处理
func (this *Http) handleSubRefund(orderId, platform string) {
	log.Info("handleSubRefund orderId: %v, platform: %v", orderId, platform)
	// 通过订单号查找
	orderInfo, err := recharge.SubscriptionDb.FindByOrderId(orderId, platform)
	if err != "" {
		// 未查询到订单
		log.Error("handleSubRefund order not find orderId: %v, err: %v", orderId, err)
		err = ecode.DB_ERROR.String()
		return
	}
	recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, orderInfo.UserId, 0, recharge.ORDER_SUB_STATE_REFUND, false)
	lid := rds.GetUserLid(orderInfo.UserId)
	if lid != "" {
		// 通知大厅服修改玩家数据
		this.InvokeNR("lobby@lobby"+lid, slg.RPC_SUB_REFUND_BY_NOTIFY, ut.Bytes(orderInfo.UserId), ut.Bytes(orderInfo.UID), ut.Bytes(orderInfo.ProductId), ut.Bytes(orderInfo.Type), ut.Bytes(orderInfo.CurrencyType),
			ut.Bytes(orderInfo.PayAmount))
	}
}

// 退款订单检测
func (this *Http) OrderRefundCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 10)
		defer tiker.Stop()
		for isRunning {
			if err, googleOrderList := sdk.GoogleVoidedOrderCheck(); err == nil && googleOrderList != nil {
				for _, info := range googleOrderList {
					// 谷歌退款处理
					_, e := recharge.RechargeDb.FindByOrderId(info.OrderId, slg.PAY_PLATFORM_GOOGLE)
					if e == "" {
						// 消耗性物品退款
						this.handleOrderRefund(info.OrderId, slg.PAY_PLATFORM_GOOGLE, ut.String(info.VoidedReason), ut.String(info.VoidedSource), "")
					} else {
						// 订阅退款
						this.handleSubRefund(info.OrderId, slg.PAY_PLATFORM_GOOGLE)
					}
				}
			}
			<-tiker.C
		}
	}()
}
