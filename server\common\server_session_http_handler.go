package common

import (
	"fmt"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
)

// ServerSessionHTTPHandler 服务器会话HTTP处理器
type ServerSessionHTTPHandler struct {
	sessionManager *ServerSessionManager
}

// NewServerSessionHTTPHandler 创建服务器会话HTTP处理器
func NewServerSessionHTTPHandler() *ServerSessionHTTPHandler {
	return &ServerSessionHTTPHandler{
		sessionManager: NewServerSessionManager(),
	}
}

// HandleGetAllServerNodes 处理获取所有服务器节点信息的HTTP请求
func (h *ServerSessionHTTPHandler) HandleGetAllServerNodes(app module.App) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleGetAllServerNodes panic: %v", err)
		}
	}()

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	// 使用服务器会话管理器获取节点信息
	nodeInfo := h.sessionManager.GetAllServerNodes(app)

	// 检查是否有错误
	if errorMsg, exists := nodeInfo["error"]; exists {
		return HttpResponseErrorNoDataWithDesc(fmt.Sprintf("%v", errorMsg)), nil
	}

	return HttpResponseSuccessWithDataNoDesc(nodeInfo), nil
}

// HandleForceRemoveServerSession 处理强制删除服务器会话的HTTP请求
func (h *ServerSessionHTTPHandler) HandleForceRemoveServerSession(app module.App, serverID string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleForceRemoveServerSession panic: %v", err)
		}
	}()

	if serverID == "" {
		return HttpResponseErrorNoDataWithDesc("serverID不能为空"), nil
	}

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	// 使用服务器会话管理器删除会话
	removedInfo, err := h.sessionManager.ForceRemoveServerSession(app, serverID)

	if err != nil {
		return HttpResponseErrorNoDataWithDesc(fmt.Sprintf("删除失败: %v", err)), nil
	}

	if success, ok := removedInfo["success"].(bool); ok && success {
		return HttpResponseSuccessWithDataWithDesc(removedInfo, "服务器会话删除成功"), nil
	} else {
		return HttpResponseSuccessWithDataWithDesc(removedInfo, "服务器会话未找到或已删除"), nil
	}
}

// HandleGetServerSessionInfo 处理获取指定服务器会话信息的HTTP请求
func (h *ServerSessionHTTPHandler) HandleGetServerSessionInfo(app module.App, serverID string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleGetServerSessionInfo panic: %v", err)
		}
	}()

	if serverID == "" {
		return HttpResponseErrorNoDataWithDesc("serverID不能为空"), nil
	}

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	// 使用服务器会话管理器获取会话信息
	sessionInfo := h.sessionManager.GetServerSessionInfo(app, serverID)

	// 检查是否有错误
	if errorMsg, exists := sessionInfo["error"]; exists {
		return HttpResponseErrorNoDataWithDesc(fmt.Sprintf("%v", errorMsg)), nil
	}

	return HttpResponseSuccessWithDataNoDesc(sessionInfo), nil
}

// HandleCheckServerHealth 处理检查服务器健康状态的HTTP请求
func (h *ServerSessionHTTPHandler) HandleCheckServerHealth(app module.App, serverType string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleCheckServerHealth panic: %v", err)
		}
	}()

	if serverType == "" {
		return HttpResponseErrorNoDataWithDesc("serverType不能为空"), nil
	}

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	// 使用服务器会话管理器检查健康状态
	healthInfo := h.sessionManager.CheckServerHealth(app, serverType)

	// 检查是否有错误
	if errorMsg, exists := healthInfo["error"]; exists {
		return HttpResponseErrorNoDataWithDesc(fmt.Sprintf("%v", errorMsg)), nil
	}

	return HttpResponseSuccessWithDataNoDesc(healthInfo), nil
}

// RegisterHTTPHandlers 注册所有服务器会话管理相关的HTTP处理器
// 这个方法可以在各个模块中调用来注册标准的服务器会话管理接口
func (h *ServerSessionHTTPHandler) RegisterHTTPHandlers(server module.ServerSession, moduleApp module.App) {
	// 获取所有节点信息
	server.RegisterGO("/http/getAllServerNodes", func() (map[string]interface{}, error) {
		return h.HandleGetAllServerNodes(moduleApp)
	})

	// 强制删除服务器会话
	server.RegisterGO("/http/forceRemoveServerSession", func(serverID string) (map[string]interface{}, error) {
		return h.HandleForceRemoveServerSession(moduleApp, serverID)
	})

	// 获取指定服务器会话信息
	server.RegisterGO("/http/getServerSessionInfo", func(serverID string) (map[string]interface{}, error) {
		return h.HandleGetServerSessionInfo(moduleApp, serverID)
	})

	// 检查服务器健康状态
	server.RegisterGO("/http/checkServerHealth", func(serverType string) (map[string]interface{}, error) {
		return h.HandleCheckServerHealth(moduleApp, serverType)
	})

	log.Info("服务器会话管理HTTP接口注册完成")
}

// GetSessionManager 获取会话管理器实例
func (h *ServerSessionHTTPHandler) GetSessionManager() *ServerSessionManager {
	return h.sessionManager
}

// 批量操作相关方法

// HandleBatchRemoveServerSessions 批量删除服务器会话
func (h *ServerSessionHTTPHandler) HandleBatchRemoveServerSessions(app module.App, serverIDs []string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleBatchRemoveServerSessions panic: %v", err)
		}
	}()

	if len(serverIDs) == 0 {
		return HttpResponseErrorNoDataWithDesc("serverIDs不能为空"), nil
	}

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	results := make(map[string]interface{})
	successCount := 0
	failCount := 0

	for _, serverID := range serverIDs {
		if serverID == "" {
			continue
		}

		removedInfo, err := h.sessionManager.ForceRemoveServerSession(app, serverID)
		if err != nil {
			results[serverID] = map[string]interface{}{
				"success": false,
				"error":   fmt.Sprintf("%v", err),
			}
			failCount++
		} else {
			results[serverID] = removedInfo
			if success, ok := removedInfo["success"].(bool); ok && success {
				successCount++
			} else {
				failCount++
			}
		}
	}

	batchResult := map[string]interface{}{
		"total_count":   len(serverIDs),
		"success_count": successCount,
		"fail_count":    failCount,
		"results":       results,
	}

	if successCount > 0 {
		return HttpResponseSuccessWithDataWithDesc(batchResult, fmt.Sprintf("批量删除完成，成功: %d, 失败: %d", successCount, failCount)), nil
	} else {
		return HttpResponseErrorWithDataWithDesc(batchResult, "批量删除失败"), nil
	}
}

// HandleGetServersByType 获取指定类型的所有服务器
func (h *ServerSessionHTTPHandler) HandleGetServersByType(app module.App, serverType string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("HandleGetServersByType panic: %v", err)
		}
	}()

	if serverType == "" {
		return HttpResponseErrorNoDataWithDesc("serverType不能为空"), nil
	}

	if app == nil {
		return HttpResponseErrorNoDataWithDesc("应用实例为空"), nil
	}

	servers := app.GetServersByType(serverType)
	serverList := []map[string]interface{}{}

	for _, server := range servers {
		if server != nil {
			serverInfo := map[string]interface{}{
				"id":      server.GetID(),
				"session": fmt.Sprintf("%p", server),
			}
			serverList = append(serverList, serverInfo)
		}
	}

	result := map[string]interface{}{
		"server_type": serverType,
		"count":       len(serverList),
		"servers":     serverList,
	}

	return HttpResponseSuccessWithDataNoDesc(result), nil
}
