package world

import (
	"context"
	"slgsrv/server/game/common/g"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	GAME     = "game"
	ALLIANCE = "alliance"
)

type Mongodb struct {
	world string
	chat  string
	SID   int32
}

func NewMongoDB(sid int32) *Mongodb {
	sidStr := g.ServerIdToString(sid)
	return &Mongodb{world: "world_" + sidStr, SID: sid, chat: "chat_" + sidStr}
}

func (this *Mongodb) getCollection(table string) *mongo.Collection {
	return mgo.GetCollection(table)
}

type WorldTableData struct {
	CellInfo map[string]interface{} `bson:"cell_info"` //单元格信息
	AreaInfo map[string]interface{} `bson:"area_info"` //战场信息
	Index    int32                  `bson:"index"`
	LandId   int32                  `bson:"land_id"` //地面类型
}

// 创建地图数据
func (this *Mongodb) CreateWorld(datas []interface{}) {
	if err := mgo.CreateCollection(this.world); err != nil {
		return
	} else if _, e := this.getCollection(this.world).InsertMany(context.TODO(), datas); e != nil {
		log.Error(e.Error())
	}
}

// 获取地图数据
func (this *Mongodb) FindWorld() (list []WorldTableData, err string) {
	cur, e := this.getCollection(this.world).Find(context.TODO(), bson.D{})
	if e != nil {
		return []WorldTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []WorldTableData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	_ = cur.Close(context.TODO())
	return
}

// 更新一条
func (this *Mongodb) UpdateWorldOne(data WorldTableData) (err string) {
	if _, e := this.getCollection(this.world).UpdateOne(context.TODO(), bson.M{"index": data.Index}, bson.M{"$set": data}); e != nil {
		err = e.Error()
	}
	return
}

// 更新
func (this *Mongodb) UpdateWorldMap(datas []WorldTableData) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateWorldMap catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range datas {
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"index": m.Index}).SetUpdate(bson.M{"$set": m}))
	}
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection(this.world).BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateWorldMap error.", e.Error())
	} else {
		// log.Info("UpdateWorldMap Complete [" + this.world + "] " + ut.Itoa(len(datas)))
	}
}

// --------------------------------------------------------免战--------------------------------------------------------------------
type CitySkinTableData struct {
	SID  int32              `bson:"sid"`
	ID   string             `bson:"id"`
	List []map[string]int32 `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindCitySkin() (data CitySkinTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "city_skin"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateCitySkin(citySkins map[int32]int32) {
	list := []map[string]int32{}
	for index, id := range citySkins {
		list = append(list, map[string]int32{"index": index, "id": id})
	}
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "city_skin"}, bson.M{"$set": CitySkinTableData{
		SID:  this.SID,
		ID:   "city_skin",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateCitySkin error.", e.Error())
	}
}

// --------------------------------------------------------免战--------------------------------------------------------------------
type AvoidWarTableData struct {
	SID  int32            `bson:"sid"`
	ID   string           `bson:"id"`
	List []map[string]int `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindAvoidWar() (data AvoidWarTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "avoid_war"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateAvoidWar(avoidwars map[int32]int64) {
	list := []map[string]int{}
	for index, time := range avoidwars {
		list = append(list, map[string]int{"index": int(index), "time": int(time)})
	}
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "avoid_war"}, bson.M{"$set": AvoidWarTableData{
		SID:  this.SID,
		ID:   "avoid_war",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateAvoidWar error.", e.Error())
	}
}

// --------------------------------------------------------免战(战斗超过3小时的免战)--------------------------------------------------------------------
// 获取所有数据
func (this *Mongodb) FindAvoidWar2() (data AvoidWarTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "avoid_war_2"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateAvoidWar2(avoidwars map[int32]int64) {
	list := []map[string]int{}
	for index, time := range avoidwars {
		list = append(list, map[string]int{"index": int(index), "time": int(time)})
	}
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "avoid_war_2"}, bson.M{"$set": AvoidWarTableData{
		SID:  this.SID,
		ID:   "avoid_war_2",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateAvoidWar2 error.", e.Error())
	}
}

// --------------------------------------------------------修建城市--------------------------------------------------------------------
type BTCityTableData struct {
	SID  int32         `bson:"sid"`
	ID   string        `bson:"id"`
	List []*BTCityInfo `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindBTCity() (data BTCityTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "bt_city"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateBTCity(list []*BTCityInfo) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "bt_city"}, bson.M{"$set": BTCityTableData{
		SID:  this.SID,
		ID:   "bt_city",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateBTCity error.", e.Error())
	}
}

// --------------------------------------------------------屯田--------------------------------------------------------------------
type CellTondenTableData struct {
	SID  int32                 `bson:"sid"`
	ID   string                `bson:"id"`
	Data map[int32]*TondenInfo `bson:"data"`
}

// 获取所有数据
func (this *Mongodb) FindCellTonden() (data CellTondenTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "cell_tonden"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateCellTonden(cellTondens map[int32]*TondenInfo) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "cell_tonden"}, bson.M{"$set": CellTondenTableData{
		SID:  this.SID,
		ID:   "cell_tonden",
		Data: cellTondens,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateCellTonden error.", e.Error())
	}
}

// --------------------------------------------------------古城捐献信息--------------------------------------------------------------------
type AncientInfoListTableData struct {
	SID  int32                   `bson:"sid"`
	ID   string                  `bson:"id"`
	List []*AncientInfoTableData `bson:"list"`
}

type AncientInfoTableData struct {
	LvUpRes   []*g.TypeObj            `bson:"lv_up_res"`  //升级资源
	Logs      []*AncientContributeLog `bson:"logs"`       //捐献记录
	MemLogs   map[string][]*g.TypeObj `bson:"mem_logs"`   //成员累计捐献记录
	CurLogs   map[string]int32        `bson:"cur_logs"`   //当前等级当天的捐献记录
	BuildInfo *BTAncientInfo          `bson:"build_info"` //升级建造信息

	FirstOccupyTime int64 `bson:"first_occupy_time"` //首次攻占时间
	Index           int32 `bson:"index"`             //位置
	CityId          int32 `bson:"city_id"`           //城市id
	SpeedUpRes      int32 `bson:"speed_up_res"`      //加速资源
	UpdateTime      int64 `bson:"update_time"`       //当天捐献记录更新时间
}

// 获取所有数据
func (this *Mongodb) FindAncientInfo() (data AncientInfoListTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "ancient_info"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateAncientInfo(list []*AncientInfoTableData) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "ancient_info"}, bson.M{"$set": AncientInfoListTableData{
		SID:  this.SID,
		ID:   "ancient_info",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateAncientRes error.", e.Error())
	}
}

// --------------------------------------------------------训练士兵--------------------------------------------------------------------
type DrillPawnTableData struct {
	SID  int32            `bson:"sid"`
	ID   string           `bson:"id"`
	List []*DrillPawnInfo `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindDrillPawn() (data DrillPawnTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "drill_pawn"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateDrillPawn(list []*DrillPawnInfo) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "drill_pawn"}, bson.M{"$set": DrillPawnTableData{
		SID:  this.SID,
		ID:   "drill_pawn",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateDrillPawn error.", e.Error())
	}
}

// --------------------------------------------------------士兵练级--------------------------------------------------------------------
type PawnLvingTableData struct {
	SID  int32            `bson:"sid"`
	ID   string           `bson:"id"`
	List []*PawnLvingInfo `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindPawnLving() (data PawnLvingTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "pawn_lving"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdatePawnLving(obj map[int32]*AarePawnLvingQueue) {
	list := []*PawnLvingInfo{}
	for _, m := range obj {
		for _, m := range m.List {
			list = append(list, m)
		}
	}
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "pawn_lving"}, bson.M{"$set": PawnLvingTableData{
		SID:  this.SID,
		ID:   "pawn_lving",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdatePawnLving error.", e.Error())
	}
}

// --------------------------------------------------------士兵治疗--------------------------------------------------------------------
type PawnCuringTableData struct {
	SID  int32             `bson:"sid"`
	ID   string            `bson:"id"`
	List []*PawnCuringInfo `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindPawnCuring() (data PawnCuringTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "pawn_curing"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdatePawnCuring(obj map[int32]*AarePawnCuringQueue) {
	list := []*PawnCuringInfo{}
	for _, m := range obj {
		for _, m := range m.List {
			list = append(list, m)
		}
	}
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "pawn_curing"}, bson.M{"$set": PawnCuringTableData{
		SID:  this.SID,
		ID:   "pawn_curing",
		List: list,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdatePawnCuring error.", e.Error())
	}
}

// --------------------------------------------------------行军--------------------------------------------------------------------
type MarchTableData struct {
	SID  int32    `bson:"sid"`
	ID   string   `bson:"id"`
	List []*March `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindMarch() (data MarchTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "march"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateMarch(marchs []*March) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "march"}, bson.M{"$set": MarchTableData{
		SID:  this.SID,
		ID:   "march",
		List: marchs,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateMarch error.", e.Error())
	}
}

// --------------------------------------------------------运送--------------------------------------------------------------------
type TransitTableData struct {
	SID  int32      `bson:"sid"`
	ID   string     `bson:"id"`
	List []*Transit `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindTransit() (data TransitTableData, err string) {
	if e := this.getCollection(GAME).FindOne(context.TODO(), bson.M{"sid": this.SID, "id": "transit"}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 保存
func (this *Mongodb) UpdateTransit(transits []*Transit) {
	if _, e := this.getCollection(GAME).UpdateOne(context.TODO(), bson.M{"sid": this.SID, "id": "transit"}, bson.M{"$set": TransitTableData{
		SID:  this.SID,
		ID:   "transit",
		List: transits,
	}}, options.Update().SetUpsert(true)); e != nil {
		log.Error("UpdateTransit error.", e.Error())
	}
}

// --------------------------------------------------------聊天--------------------------------------------------------------------
type ChatTableData struct {
	Channel string      `bson:"channel"`
	List    []*ChatInfo `bson:"list"`
}

// 获取所有数据
func (this *Mongodb) FindChat() (list []ChatTableData, err string) {
	cur, e := this.getCollection(this.chat).Find(context.TODO(), bson.D{})
	if e != nil {
		return []ChatTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []ChatTableData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	_ = cur.Close(context.TODO())
	return
}

// 保存
func (this *Mongodb) UpdateChat(chats *ChatMap) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateChat catch error: %v", err)
		}
	}()
	count := 0
	models := []mongo.WriteModel{}
	chats.Lock()
	for k, v := range chats.DbMap {
		if count >= CHAT_MAX_DB_COUNT_PER {
			// 超过每次保存频道数量上限
			log.Warning("UpdateChat chats channel over limit")
			break
		}
		chatListInfo, ok := chats.Map[k]
		if !ok {
			log.Error("UpdateChat chats Map err key: %v", k)
			break
		}
		chatList := chatListInfo[v:]
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"channel": k}).SetUpdate(bson.M{
			"$push": bson.M{
				"list": bson.M{"$each": chatList},
			},
		}).SetUpsert(true))
		if len(chatListInfo) > CHAT_MAX_COUNT {
			// 清理每个频道中超过数量上限的消息
			startIndex := len(chatListInfo) - CHAT_MAX_COUNT
			chats.Map[k] = chatListInfo[startIndex:]
		}
		count++
		delete(chats.DbMap, k)
	}
	chats.Unlock()
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection(this.chat).BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateChat error.", e.Error())
	}
}

// 获取指定频道的聊天
func (this *Mongodb) GetChat(channel string) (data ChatTableData, err string) {
	if e := this.getCollection(this.chat).FindOne(context.TODO(), bson.M{"channel": channel}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 删除指定频道指定uid的聊天
func (this *Mongodb) DelChatByUid(channel, uid string) (data ChatTableData, err error) {
	update := bson.M{
		"$pull": bson.M{
			"list": bson.M{"uid": uid},
		},
	}
	_, err = this.getCollection(this.chat).UpdateOne(context.TODO(), bson.M{"channel": channel}, update)
	return
}

// 删除指定聊天频道的数据
func (this *Mongodb) DelChatByChannel(channel string) (err error) {
	_, err = this.getCollection(this.chat).DeleteOne(context.TODO(), bson.M{"channel": channel})
	return
}

// --------------------------------------------------------联盟--------------------------------------------------------------------
type AllianceTableData struct {
	Uid       string `bson:"uid"`
	Name      string `bson:"name"`
	Creater   string `bson:"creater"`    //创建者
	Notice    string `bson:"notice"`     //联盟公告
	ApplyDesc string `bson:"apply_desc"` //申请说明

	Members       []*AlliMember           `bson:"members"`        //成员列表
	Applys        []*AlliApply            `bson:"applys"`         //申请列表
	MapFlag       map[int32]int32         `bson:"map_flag"`       //地图标记
	Policys       map[int32]*g.PolicyInfo `bson:"policys"`        //当前政策
	SelectPolicys map[int32][]int32       `bson:"select_policys"` //当前可选择的政策
	AlliLogs      []*AlliLog              `bson:"alli_logs"`      //联盟日志
	ChatChannels  []*AlliChannel          `bson:"chat_channels"`  //聊天频道

	CreateTime         int64 `bson:"create_time"`           //创建时间
	LastEditNoticeTime int64 `bson:"last_edit_notice_time"` //最后一次编辑公告时间

	SID        int32 `bson:"sid"`
	Icon       int32 `bson:"icon"`
	NoAvoidWar bool  `bson:"no_avoid_war"` //取消免战
}

// 获取所有数据
func (this *Mongodb) FindAlliance() (list []AllianceTableData, err string) {
	cur, e := this.getCollection(ALLIANCE).Find(context.TODO(), bson.M{"sid": this.SID})
	if e != nil {
		return []AllianceTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []AllianceTableData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	_ = cur.Close(context.TODO())
	return
}

// 保存
func (this *Mongodb) UpdateAlliance(alliances map[string]*Alliance) {
	models := []mongo.WriteModel{}
	for _, m := range alliances {
		data := m.ToDB()
		data.SID = this.SID
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"sid": this.SID, "uid": m.Uid}).SetUpdate(bson.M{"$set": data}).SetUpsert(true))
	}
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection(ALLIANCE).BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateAlliance error.", e.Error())
	}
}

// 删除
func (this *Mongodb) DeleteAlliance(uid string) (err string) {
	if _, e := this.getCollection(ALLIANCE).DeleteOne(context.TODO(), bson.M{"sid": this.SID, "uid": uid}); e != nil {
		err = e.Error()
		log.Error("DeleteAlliance error.", err)
	}
	return
}
