package lc

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 检测实名
func CheckRealName(name, email, identity string) string {
	if name == "" {
		return ecode.NAME_IS_NULL.String()
	} else if slg.IsChinaArea() {
		// TODO 国服验证身份证
		if identity == "" {
		}
	} else if !ut.VerifyEmailFormat(email) {
		return ecode.EMAIL_FORMAT_ERROR.String()
	}
	return ""
}

// 检测自选英雄
func CheckHeroOptId(lv, id int32) bool {
	if lv == 3 {
		return true //自选3 表示全选
	}
	ids := HERO_OPT_GIFT[lv]
	return ids != nil && array.Has(ids, id)
}

// 随机自选英雄id
func GetRandomHeroIdByLv(lv int32) int32 {
	ids := []int32{}
	if lv == 3 {
		ids = array.Map(config.GetJson("portrayalBase").Datas, func(m map[string]interface{}, _ int) int32 { return ut.Int32(m["id"]) })
	} else {
		ids = HERO_OPT_GIFT[lv]
	}
	if ids == nil || len(ids) == 0 {
		return 0
	}
	return array.RandomOneItem(ids)
}

// 自选英雄转换碎片
func GetItemByOptHero(item *g.TypeObj, heroId int32) bool {
	if item.Type != ctype.HERO_OPT {
		// 无需转换
		return true
	}
	// 自选英雄则转化为碎片
	if heroId > 0 && CheckHeroOptId(item.Id, heroId) {
		item.Type = ctype.HERO_DEBRIS
		item.Id = heroId
		item.Count = PORTRAYAL_COMP_NEED_COUNT //固定3个碎片
		return true
	}
	return false
}
