package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDEquip() {
	this.GetServer().RegisterGO("HD_ForgeEquip", this.forgeEquip)       //打造装备
	this.GetServer().RegisterGO("HD_InDoneForge", this.inDoneForge)     //立即完成
	this.GetServer().RegisterGO("HD_RestoreForge", this.restoreForge)   //还原重铸
	this.GetServer().RegisterGO("HD_SmeltingEquip", this.smeltingEquip) //融炼装备
}

// 打造装备
func (this *Game) forgeEquip(session gate.Session, msg *pb.GAME_HD_FORGEEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsForgeEquiping() {
		return nil, ecode.FORGE_EQUIPING.String()
	}
	id := msg.GetId()
	var recastCount, lockEffect int32 //重铸次数, 锁定效果id
	isFreeForge := false
	cost := []*g.TypeObj{}
	equip := wld.GetPlayerEquip(plr.Uid, id)
	var json map[string]interface{} = nil
	isRecast := false
	if equip != nil { //表示重铸
		json = equip.GetJson()
		recastCount = equip.RecastCount
		isRecast = true
		isFreeForge = equip.NextForgeFree
		if lockEffect = msg.GetLockEffect(); lockEffect > 0 && equip.GetExclusivePawnId() > 0 && equip.GetEquipEffectByType(lockEffect) != nil {
			cost = append(cost, g.NewTypeObj(ctype.FIXATOR, 0, 1))
		} else {
			lockEffect = 0
		}
	} else if json = config.GetJsonData("equipBase", id); json == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if !plr.CheckCTypes(g.StringToTypeObjs(json["forge_cond"])) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	}
	if json == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if ut.Int(json["need_unlock"]) == 1 && !plr.IsUnlockEquip(id) { //是否需要解锁
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	time := ut.Int32(json["forge_time"])
	s2c := &pb.GAME_HD_FORGEEQUIP_S2C{}
	if !isFreeForge {
		cost = append(cost, g.StringToTypeObjs(json["forge_cost"])...)
	} else {
		time = 3 //这里将时间直接缩短
	}
	if len(cost) == 0 {
	} else if !plr.CheckAndDeductCostByTypeObjs(cost) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	} else {
		s2c.Cost = plr.ToItemByTypeObjsPb(cost)
	}
	// 开始打造
	plr.ForgeEquip(id, time, lockEffect, isRecast)
	s2c.CurrForgeEquip = plr.ToForgeEquipDataPb()
	// 下次是否免费
	if equip != nil {
		equip.NextForgeFree = ut.ChanceInt32(plr.GetPolicyEffectInt(effect.FREE_RECAST))
		s2c.NextForgeFree = equip.NextForgeFree
	}
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_forgeEquip", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": id,
		},
		"forge_resetCount": recastCount,
		"forge_isFree":     isFreeForge,
	})
	return pb.ProtoMarshal(s2c)
}

// 立即完成
func (this *Game) inDoneForge(session gate.Session, msg *pb.GAME_HD_INDONEFORGE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if !plr.IsForgeEquiping() {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	gold := this.ChangePlayerGold(plr, -constant.IN_DONE_FORGE_GOLD, constant.GOLD_CHANGE_IN_DONE_FORGE)
	if gold == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
	}
	equipId := plr.CurrForgeEquip.ID
	// 完成
	plr.CompleteForge()
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_inDoneEq", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": equipId,
		},
		"gold_cost": constant.IN_DONE_FORGE_GOLD,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_INDONEFORGE_S2C{
		Gold: gold,
	})
}

// 还原重铸
func (this *Game) restoreForge(session gate.Session, msg *pb.GAME_HD_RESTOREFORGE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsForgeEquiping() {
		return nil, ecode.FORGE_EQUIPING.String()
	}
	equip := wld.GetPlayerEquip(plr.Uid, msg.GetId())
	if equip == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if len(equip.LastAttrs) == 0 {
		return nil, ecode.NOT_CAN_RESTORE_ATTR.String()
	}
	json := equip.GetJson()
	if json == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	iron := ut.Int32(json["restore_cost"])
	if !plr.CheckAndDeductCostByTypeObjOne(g.NewTypeObj(ctype.IRON, 0, iron)) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 还原
	equip.RestoreAttr()
	// 更新玩家装备到临时记录列表
	wld.UpdatePlayerPawnEquipInfo(plr.Uid, equip.ID, equip.Attrs)
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_restoreForge", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": equip.ID,
		},
		"iron_cost": iron,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_RESTOREFORGE_S2C{
		Equip: equip.ToPb(),
		Iron:  plr.Iron,
	})
}

// 熔炼装备
func (this *Game) smeltingEquip(session gate.Session, msg *pb.GAME_HD_SMELTINGEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsInSmeltingEquiping() {
		return nil, ecode.IN_SMELTING.String()
	} else if wld.GetPlayerBuildLvById(plr.Uid, constant.SMITHY_BUILD_ID) < 18 {
		return nil, ecode.COND_NOT_ENOUGH.String()
	}
	mainId, viceId := msg.GetMainId(), msg.GetViceId()
	if mainId == viceId {
		return nil, ecode.CANNOT_SMELTING.String()
	}
	mainJson, viceJson := config.GetJsonData("equipBase", mainId), config.GetJsonData("equipBase", viceId)
	if mainJson == nil || viceJson == nil {
		return nil, ecode.NOT_SELECT_EQUIP.String()
	} else if smelt_type := ut.Int(mainJson["smelt_type"]); smelt_type != 0 && smelt_type != 1 {
		return nil, ecode.CANNOT_SMELTING.String()
	} else if smelt_type := ut.Int(viceJson["smelt_type"]); smelt_type != 0 && smelt_type != 2 {
		return nil, ecode.CANNOT_SMELTING.String()
	}
	mainEquip, viceEquip := wld.GetPlayerEquip(plr.Uid, mainId), wld.GetPlayerEquip(plr.Uid, viceId)
	if mainEquip == nil || viceEquip == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if !array.Some(viceEquip.Attrs, func(m []int32) bool { return m[0] == 2 }) { //副装备没有效果
		return nil, ecode.CANNOT_SMELTING.String()
	} else if mainEquip.IsSmelt() || viceEquip.IsSmelt() {
		return nil, ecode.CANNOT_SMELTING.String()
	} else if viceEquip.IsExclusive() { //副装备不能是专属
		return nil, ecode.CANNOT_SMELTING.String()
	} else if mainEquip.IsExclusive() && mainEquip.HasAttrEffect(viceEquip.Attrs) { //如果主装备是 专属那么副装备就不能有专属装备的属性
		return nil, ecode.CANNOT_SMELTING.String()
	} else if !plr.CheckAndDeductCostByTypeObjOne(g.NewTypeObj(ctype.FIXATOR, 0, constant.SMELTING_FIXATOR_COST+plr.SmelCount)) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 开始融炼
	info := plr.SmeltEquip(mainEquip, viceEquip)
	// 增加融炼次数
	plr.SmelCount += 1
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_smeltEquip", map[string]interface{}{
		"main_id":    mainId,
		"vice_id":    viceId,
		"smel_count": plr.SmelCount,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_SMELTINGEQUIP_S2C{
		CurrSmeltEquip: info,
		Fixator:        plr.Fixator,
		SmelCount:      plr.SmelCount,
	})
}
