package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDEquip() {
	this.GetServer().RegisterGO("HD_ForgeEquip", this.forgeEquip)               //打造装备
	this.GetServer().RegisterGO("HD_InDoneForge", this.inDoneForge)             //立即完成
	this.GetServer().RegisterGO("HD_RestoreForge", this.restoreForge)           //还原重铸
	this.GetServer().RegisterGO("HD_SmeltingEquip", this.smeltingEquip)         //融炼装备
	this.GetServer().RegisterGO("HD_RestoreSmeltEquip", this.restoreSmeltEquip) //还原融炼装备
}

// 打造装备
func (this *Game) forgeEquip(session gate.Session, msg *pb.GAME_HD_FORGEEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsForgeEquiping() {
		return nil, ecode.FORGE_EQUIPING.String()
	} else if plr.IsInSmeltingEquiping() {
		return nil, ecode.IN_SMELTING.String()
	}
	uid, id := msg.GetUid(), int32(0)
	var recastCount, lockEffect int32 //重铸次数, 锁定效果id
	var json map[string]interface{} = nil
	isFreeForge := false
	isRecast := false
	fixator, cost := 0, []*g.TypeObj{}
	equip := wld.GetPlayerEquipByUid(plr.Uid, uid)
	if equip != nil { //表示重铸
		id = equip.ID
		json = equip.GetJson()
		recastCount = equip.RecastCount
		isRecast = true
		isFreeForge = equip.NextForgeFree
		// 已融炼数量
		smeltAttrs := equip.GetSmeltAttrs()
		semltAttrEffectMap := map[int32]bool{}
		for _, attr := range smeltAttrs {
			if len(attr) >= 2 && attr[0] == 2 {
				semltAttrEffectMap[attr[1]] = true
			}
		}
		if randomEffects := wld.ExclusiveEffectMap[id]; randomEffects != nil {
			for _, effect := range randomEffects {
				// 如果熔炼的属性包含全局专武待随机属性 要算钉子
				if semltAttrEffectMap[effect] {
					fixator += 1
				}
			}
		}
		// 锁定
		if lockEffect = msg.GetLockEffect(); lockEffect > 0 && equip.GetExclusivePawnId() > 0 && equip.GetEquipEffectByType(lockEffect) != nil {
			fixator += 1
		} else {
			lockEffect = 0
		}
		if fixator > 0 {
			cost = append(cost, g.NewTypeObj(ctype.FIXATOR, 0, int32(fixator)))
		}
	} else {
		id = int32(ut.Atoi(strings.Split(uid, "_")[0]))
		json = config.GetJsonData("equipBase", id)
	}
	if json == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if equip == nil && !plr.IsUnlockEquip(uid) { //是否需要解锁
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	if !isFreeForge && wld.GetPlayerPolicyEffectIntByUid(plr.Uid, effect.FREE_RECAST_COUNT) > plr.FreeForgeCount {
		// 使用免费打造次数
		plr.FreeForgeCount++
		isFreeForge = true
	}
	time := ut.Int32(json["forge_time"])
	s2c := &pb.GAME_HD_FORGEEQUIP_S2C{FreeForgeCount: plr.FreeForgeCount}
	if !isFreeForge {
		cost = append(cost, g.StringToTypeObjs(json["forge_cost"])...)
	} else {
		time = constant.FREE_TIME_MIN //这里将时间直接缩短
	}
	if len(cost) == 0 {
	} else if !plr.CheckAndDeductCostByTypeObjs(cost) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	} else {
		s2c.Cost = plr.ToItemByTypeObjsPb(cost)
	}
	// 开始打造
	plr.ForgeEquip(uid, time, lockEffect, isRecast)
	s2c.CurrForgeEquip = plr.ToForgeEquipDataPb()
	// 下次是否免费
	if equip != nil {
		equip.NextForgeFree = ut.ChanceInt32(plr.GetPolicyEffectInt(effect.FREE_RECAST))
		s2c.NextForgeFree = equip.NextForgeFree
	}
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_forgeEquip", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": id,
		},
		"forge_resetCount": recastCount,
		"forge_isFree":     isFreeForge,
	})
	return pb.ProtoMarshal(s2c)
}

// 立即完成
func (this *Game) inDoneForge(session gate.Session, msg *pb.GAME_HD_INDONEFORGE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if !plr.IsForgeEquiping() {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	gold := this.ChangePlayerGold(plr, -constant.IN_DONE_FORGE_GOLD, constant.GOLD_CHANGE_IN_DONE_FORGE)
	if gold == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
	}
	equipId := plr.CurrForgeEquip.GetEquipID()
	// 完成
	plr.CompleteForge()
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_inDoneEq", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": equipId,
		},
		"gold_cost": constant.IN_DONE_FORGE_GOLD,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_INDONEFORGE_S2C{
		Gold: gold,
	})
}

// 还原重铸
func (this *Game) restoreForge(session gate.Session, msg *pb.GAME_HD_RESTOREFORGE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsForgeEquiping() {
		return nil, ecode.FORGE_EQUIPING.String()
	}
	equip := wld.GetPlayerEquipByUid(plr.Uid, msg.GetUid())
	if equip == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if len(equip.LastAttrs) == 0 {
		return nil, ecode.NOT_CAN_RESTORE_ATTR.String()
	}
	json := equip.GetJson()
	if json == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	}
	iron := ut.Int32(json["restore_cost"])
	if iron == 0 {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if !plr.CheckAndDeductCostByTypeObjOne(g.NewTypeObj(ctype.IRON, 0, iron)) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 还原
	equip.RestoreAttr()
	// 更新玩家装备到临时记录列表
	wld.UpdatePlayerPawnEquipInfo(plr.Uid, equip.UID, equip.Attrs)
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_restoreForge", map[string]interface{}{
		"equip": map[string]interface{}{
			"equip_id": equip.ID,
		},
		"iron_cost": iron,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_RESTOREFORGE_S2C{
		Equip: equip.ToPb(),
		Iron:  plr.Iron,
	})
}

// 融炼装备
func (this *Game) smeltingEquip(session gate.Session, msg *pb.GAME_HD_SMELTINGEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsInSmeltingEquiping() {
		return nil, ecode.IN_SMELTING.String()
	} else if plr.IsForgeEquiping() {
		return nil, ecode.FORGE_EQUIPING.String()
	}

	mainUid, viceIdList := msg.GetMainUid(), msg.GetViceIds()
	if len(viceIdList) == 0 {
		return nil, ecode.CANNOT_SMELTING.String()
	}
	needLv := constant.SMELT_EQUIP_LV_MAP[len(viceIdList)]
	if needLv == 0 {
		return nil, ecode.CANNOT_SMELTING.String()
	} else if wld.GetPlayerBuildLvById(plr.Uid, constant.SMITHY_BUILD_ID) < needLv { // 建筑等级不足
		return nil, ecode.COND_NOT_ENOUGH.String()
	}

	// 获取主装备
	mainEquip := wld.GetPlayerEquipByUid(plr.Uid, mainUid)
	if mainEquip == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if !mainEquip.IsExclusive() {
		return nil, ecode.CANNOT_SMELTING.String() //只有专属才可融炼
	}
	mainId := mainEquip.ID
	mainJson := config.GetJsonData("equipBase", mainId)
	if mainJson == nil {
		return nil, ecode.NOT_SELECT_EQUIP.String()
	}

	// 获取副装备
	viceList := [][]int32{}
	for _, viceId := range viceIdList {
		if mainId == viceId {
			return nil, ecode.CANNOT_SMELTING.String()
		}
		viceJson := config.GetJsonData("equipBase", viceId)
		if viceJson == nil {
			return nil, ecode.NOT_SELECT_EQUIP.String()
		} else if smelt_type := ut.Int(viceJson["smelt_type"]); smelt_type != 1 {
			return nil, ecode.CANNOT_SMELTING.String()
		} else if wld.IsPlayerEquipSmelted(plr.Uid, mainEquip.UID, viceId) {
			return nil, ecode.CANNOT_SMELTING.String() // 该装备已熔炼过
		}

		viceEquip := wld.GetPlayerEquip(plr.Uid, viceId)
		if viceEquip == nil {
			return nil, ecode.EQUIP_NOT_EXIST.String()
		} else if !array.Some(viceEquip.Attrs, func(m []int32) bool { return m[0] == 2 }) { //副装备没有效果
			return nil, ecode.CANNOT_SMELTING.String()
		} else if viceEquip.IsSmelt() {
			return nil, ecode.CANNOT_SMELTING.String()
		} else if viceEquip.IsExclusive() { //副装备不能是专属
			return nil, ecode.CANNOT_SMELTING.String()
		} else if mainEquip.HasAttrEffect(viceEquip.Attrs) { //如果主装备是专属 那么副装备就不能有专属装备的属性
			return nil, ecode.CANNOT_SMELTING.String()
		}
		viceList = append(viceList, viceEquip.ToViceAttrs()...)
	}

	// 扣除费用
	if !plr.CheckAndDeductCostByTypeObjOne(g.NewTypeObj(ctype.FIXATOR, 0, constant.SMELTING_FIXATOR_COST*int32(len(viceIdList)))) {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 开始融炼
	info := plr.SmeltEquip(mainEquip, viceList)
	// 增加融炼次数
	plr.SmelCount += 1
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_smeltEquip", map[string]interface{}{
		"main_id":    mainId,
		"vice_id":    viceIdList,
		"smel_count": plr.SmelCount,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_SMELTINGEQUIP_S2C{
		CurrSmeltEquip: info,
		Fixator:        plr.Fixator,
	})
}

// 还原融炼装备
func (this *Game) restoreSmeltEquip(session gate.Session, msg *pb.GAME_HD_RESTORESMELTEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	mainUid := msg.GetMainUid()
	// 获取主装备
	mainEquip := wld.GetPlayerEquipByUid(plr.Uid, mainUid)
	if mainEquip == nil {
		return nil, ecode.EQUIP_NOT_EXIST.String()
	} else if !mainEquip.IsSmelt() { // 装备未融炼
		return nil, ecode.CANNOT_SMELTING.String()
	}
	// 还原融炼
	mainEquip.RestoreEquipSmelt()
	return pb.ProtoMarshal(&pb.GAME_HD_RESTORESMELTEQUIP_S2C{
		Equip: mainEquip.ToPb(),
	})
}
