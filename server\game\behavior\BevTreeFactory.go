package behavior

import (
	"fmt"
	"reflect"
)

// 定义注册结构map
type RegisterStructMaps struct {
	maps map[string]reflect.Type
}

// 根据name初始化结构
// 在这里根据结构的成员注解进行DI注入，这里没有实现，只是简单都初始化
func (this *RegisterStructMaps) New(name string) (interface{}, error) {
	//fmt.Println("New ", name)
	var c interface{}
	var err error
	if v, ok := this.maps[name]; ok {
		c = reflect.New(v).Interface()
		//fmt.Println("found ", name, "  ", reflect.TypeOf(c))
		return c, nil
	} else {
		err = fmt.Errorf("not found %s struct", name)
		//fmt.Println("New no found", name, "  ", len(this.maps))
	}
	return nil, err
}

// 查询是否存在
func (this *RegisterStructMaps) CheckElem(name string) bool {
	if _, ok := this.maps[name]; ok {
		return true
	}
	return false
}

// 根据名字注册实例
func (this *RegisterStructMaps) Register(name string, c interface{}) {
	this.maps[name] = reflect.TypeOf(c).Elem()
}

func (this *RegisterStructMaps) Init() *RegisterStructMaps {
	this.maps = make(map[string]reflect.Type)
	this.Register("Attack", &Attack{})
	this.Register("EndRound", &EndRound{})
	this.Register("Move", &Move{})
	this.Register("SearchTarget", &SearchTarget{})
	this.Register("Parallel", &Parallel{})
	this.Register("Priority", &Priority{})
	this.Register("Sequence", &Sequence{})
	this.Register("CanMove", &CanMove{})
	this.Register("HasAttackTarget", &HasAttackTarget{})
	this.Register("InAttackRange", &InAttackRange{})
	this.Register("Probability", &Probability{})
	this.Register("SearchCanAttackTarget", &SearchCanAttackTarget{})
	this.Register("CheckBeginBlood", &CheckBeginBlood{})
	this.Register("CheckBeginDeductHp", &CheckBeginDeductHp{})
	this.Register("CheckRoundBegin", &CheckRoundBegin{})
	this.Register("CheckUseSkillAttack", &CheckUseSkillAttack{})
	return this
}

var bevTreeFactory = new(RegisterStructMaps).Init()
