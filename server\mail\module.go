package mail

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Mail)
}

type Mail struct {
	basemodule.BaseModule
}

func (this *Mail) GetType() string {
	return "mail" //很关键,需要与配置文件中的Module配置对应
}

func (this *Mail) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Mail) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "mail" || serverType == "development" {
		// sdk.InitAppleRootCerts()
	}
}

func (this *Mail) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	this.InitMailManager()
	this.InitRpc()
	this.GetServer().RegisterGO("HD_GetMails", this.getMails)                   //获取邮件列表
	this.GetServer().RegisterGO("HD_ReadMail", this.readMail)                   //读邮件
	this.GetServer().RegisterGO("HD_ClaimMailItem", this.claimMailItem)         //领取道具
	this.GetServer().RegisterGO("HD_ClaimMailItemOne", this.claimMailItemOne)   //领取道具单个
	this.GetServer().RegisterGO("HD_RemoveMail", this.removeMail)               //删除邮件
	this.GetServer().RegisterGO("HD_RemoveAllReadMail", this.removeAllReadMail) //删除已读邮件

	this.InitHttp()
}

func (this *Mail) Run(closeSig chan bool) {
	RunTick(this)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	StopTick()
}

func (this *Mail) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 获取邮件列表
func (this *Mail) getMails(session gate.Session, msg *pb.MAIL_HD_GETMAILS_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	sid := msg.GetSid()
	return pb.ProtoMarshal(&pb.MAIL_HD_GETMAILS_S2C{
		List: GetUserMailList(uid, sid),
	})
}

// 读邮件
func (this *Mail) readMail(session gate.Session, msg *pb.MAIL_HD_READMAIL_C2S) (ret map[string]interface{}, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	err = this.ReadMail(uid, msg.GetUid())
	return
}

// 领取道具
func (this *Mail) claimMailItem(session gate.Session, msg *pb.MAIL_HD_CLAIMMAILITEM_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	items, err := this.ClaimMailItem(uid, msg.GetUid())
	if err != "" {
		return nil, err
	}
	lid := rds.GetUserLid(uid)
	// 通知大厅服添加道具
	rst, err := this.InvokeLobbyRpc(lid, slg.RPC_ADD_USER_ITEMS, uid, items, constant.GOLD_CHANGE_MAIL_GET)
	if err != "" {
		log.Error("claimMailItem err: %v, userId : %v, uid: %v", err, uid, msg.GetUid())
		return
	}
	rstBytes := ut.Bytes(rst)
	rewards := &pb.UpdateOutPut{}
	pb.ProtoUnMarshal(rstBytes, rewards)
	// 返回
	return pb.ProtoMarshal(&pb.MAIL_HD_CLAIMMAILITEM_S2C{
		Rewards: rewards,
	})
}

// 领取道具单个
func (this *Mail) claimMailItemOne(session gate.Session, msg *pb.MAIL_HD_CLAIMMAILITEMONE_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	item, err := this.ClaimMailItemOne(uid, msg.GetUid(), msg.GetIndex(), msg.GetHeroId())
	if err != "" {
		return nil, err
	}

	lid := rds.GetUserLid(uid)
	// 通知大厅服添加道具
	rst, err := this.InvokeLobbyRpc(lid, slg.RPC_ADD_USER_ITEMS, uid, []*g.TypeObj{item}, constant.GOLD_CHANGE_MAIL_GET)
	if err == "" {
		rstBytes := ut.Bytes(rst)
		rewards := &pb.UpdateOutPut{}
		pb.ProtoUnMarshal(rstBytes, rewards)
		// 返回
		return pb.ProtoMarshal(&pb.MAIL_HD_CLAIMMAILITEM_S2C{
			Rewards: rewards,
		})
	} else {
		log.Error("claimMailItem err: %v, userId : %v, uid: %v", err, err, uid, msg.GetUid())
		return
	}
}

// 删除邮件
func (this *Mail) removeMail(session gate.Session, msg *pb.MAIL_HD_REMOVEMAIL_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	err = this.RemoveMail(uid, msg.GetUid())
	return
}

// 删除已读邮件
func (this *Mail) removeAllReadMail(session gate.Session, msg *pb.MAIL_HD_REMOVEALLREADMAIL_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	err = this.RemoveAllReadMail(uid, msg.GetUids())
	return
}

func checkSession(session gate.Session) string {
	if session == nil {
		return ""
	} else {
		return session.GetUserID()
	}
}

func getInvokeRpcInfo(serverType, id string, params []interface{}) (string, []interface{}) {
	moduleType := serverType
	if serverType == slg.MACH_SERVER_TYPE_LOBBY || serverType == slg.MACH_SERVER_TYPE_GAME {
		moduleType = serverType + "@" + serverType + id
	}
	return moduleType, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
}

// 发送Rpc到lobby
func (this *Mail) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	return this.Invoke(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Mail) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 广播Rpc到所有lobby
func (this *Mail) BroadCastAllLobbyNR(_func string, params ...interface{}) {
	rdsData, e := rds.RdsHGetAll(rds.RDS_LOBBY_LOAD_MAP_KEY)
	if e != nil {
		return
	}
	for lid := range rdsData {
		moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, lid, params)
		this.InvokeNR(moduleType, _func, paramsBytesArr...)
	}
	return
}
