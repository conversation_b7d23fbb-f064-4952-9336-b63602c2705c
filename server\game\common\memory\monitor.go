package memory

import (
	"os"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/huyangv/vmqant/log"
)

// 内存监控器
type Monitor struct {
	isRunning      bool
	stopChan       chan bool
	interval       time.Duration
	alertThreshold int64 // MB
	gcThreshold    int64 // MB
	mutex          sync.RWMutex

	// 统计数据
	lastMemStats runtime.MemStats
	peakMemUsage uint64
	gcCount      uint32
	lastGCTime   time.Time
}

// 内存使用统计
type MemoryStats struct {
	AllocMB       float64 `json:"alloc_mb"`        // 当前分配的内存(MB)
	TotalAllocMB  float64 `json:"total_alloc_mb"`  // 累计分配的内存(MB)
	SysMB         float64 `json:"sys_mb"`          // 系统内存(MB)
	NumGC         uint32  `json:"num_gc"`          // GC次数
	PauseTotalNs  uint64  `json:"pause_total_ns"`  // GC暂停总时间(纳秒)
	HeapObjects   uint64  `json:"heap_objects"`    // 堆对象数量
	StackInuse    uint64  `json:"stack_inuse"`     // 栈使用量
	PeakMemMB     float64 `json:"peak_mem_mb"`     // 峰值内存(MB)
	RSSMB         float64 `json:"rss_mb"`          // 物理内存占用(MB)
	VSZMB         float64 `json:"vsz_mb"`          // 虚拟内存占用(MB)
	TempVec2Count int     `json:"temp_vec2_count"` // 临时Vec2对象数量
}

// 创建内存监控器
func NewMonitor(interval time.Duration, alertThreshold, gcThreshold int64) *Monitor {
	return &Monitor{
		isRunning:      false,
		stopChan:       make(chan bool),
		interval:       interval,
		alertThreshold: alertThreshold,
		gcThreshold:    gcThreshold,
		lastGCTime:     time.Now(),
	}
}

// 启动监控
func (m *Monitor) Start() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isRunning {
		return
	}

	m.isRunning = true
	go m.run()
	log.Info("Memory monitor started, interval: %v, alert threshold: %dMB, GC threshold: %dMB",
		m.interval, m.alertThreshold, m.gcThreshold)
}

// 停止监控
func (m *Monitor) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isRunning {
		return
	}

	m.isRunning = false
	close(m.stopChan)
	log.Info("Memory monitor stopped")
}

// 主运行循环
func (m *Monitor) run() {
	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkMemory()
		case <-m.stopChan:
			return
		}
	}
}

// 检查内存使用情况
func (m *Monitor) checkMemory() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	currentAllocMB := float64(memStats.Alloc) / 1024 / 1024

	// 更新峰值内存
	if memStats.Alloc > m.peakMemUsage {
		m.peakMemUsage = memStats.Alloc
	}

	// 检查是否需要告警
	if currentAllocMB > float64(m.alertThreshold) {
		log.Warning("Memory usage high: %.2fMB (threshold: %dMB)", currentAllocMB, m.alertThreshold)
		m.logDetailedMemStats(&memStats)

		// 分析内存碎片
		fragStats := m.AnalyzeMemoryFragmentation()
		m.logFragmentationStats(fragStats)
	}

	// 检查是否需要强制GC
	if currentAllocMB > float64(m.gcThreshold) && time.Since(m.lastGCTime) > time.Minute {
		log.Info("Triggering GC due to high memory usage: %.2fMB", currentAllocMB)
		runtime.GC()
		m.lastGCTime = time.Now()
	}

	// 检查GC频率
	if memStats.NumGC > m.gcCount {
		gcDiff := memStats.NumGC - m.gcCount
		if gcDiff > 10 { // 如果1分钟内GC超过10次
			log.Warning("High GC frequency: %d GCs in last interval", gcDiff)
		}
		m.gcCount = memStats.NumGC
	}

	// 定期输出内存统计
	if int(time.Now().Unix())%300 == 0 { // 每5分钟输出一次
		m.logMemoryStats(&memStats)
	}

	m.lastMemStats = memStats
}

// 获取当前内存统计
func (m *Monitor) GetMemoryStats() *MemoryStats {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 获取系统内存使用情况
	rssMB, vszMB := getProcessMemoryUsage()

	return &MemoryStats{
		AllocMB:       float64(memStats.Alloc) / 1024 / 1024,
		TotalAllocMB:  float64(memStats.TotalAlloc) / 1024 / 1024,
		SysMB:         float64(memStats.Sys) / 1024 / 1024,
		NumGC:         memStats.NumGC,
		PauseTotalNs:  memStats.PauseTotalNs,
		HeapObjects:   memStats.HeapObjects,
		StackInuse:    memStats.StackInuse,
		PeakMemMB:     float64(m.peakMemUsage) / 1024 / 1024,
		RSSMB:         rssMB,
		VSZMB:         vszMB,
		TempVec2Count: 0, // TODO: 添加Vec2对象池统计
	}
}

// 输出内存统计信息
func (m *Monitor) logMemoryStats(memStats *runtime.MemStats) {
	log.Info("Memory Stats - Alloc: %.2fMB, TotalAlloc: %.2fMB, Sys: %.2fMB, NumGC: %d, HeapObjects: %d",
		float64(memStats.Alloc)/1024/1024,
		float64(memStats.TotalAlloc)/1024/1024,
		float64(memStats.Sys)/1024/1024,
		memStats.NumGC,
		memStats.HeapObjects)
}

// 输出详细内存统计信息
func (m *Monitor) logDetailedMemStats(memStats *runtime.MemStats) {
	rssMB, vszMB := getProcessMemoryUsage()

	log.Warning("Detailed Memory Stats:")
	log.Warning("  Go Heap Alloc: %.2fMB", float64(memStats.Alloc)/1024/1024)
	log.Warning("  Go TotalAlloc: %.2fMB", float64(memStats.TotalAlloc)/1024/1024)
	log.Warning("  Go Sys: %.2fMB", float64(memStats.Sys)/1024/1024)
	log.Warning("  Process RSS: %.2fMB", rssMB)
	log.Warning("  Process VSZ: %.2fMB", vszMB)
	log.Warning("  Memory Gap: %.2fMB (RSS - Go Heap)", rssMB-float64(memStats.Alloc)/1024/1024)
	log.Warning("  Lookups: %d", memStats.Lookups)
	log.Warning("  Mallocs: %d", memStats.Mallocs)
	log.Warning("  Frees: %d", memStats.Frees)
	log.Warning("  HeapAlloc: %.2fMB", float64(memStats.HeapAlloc)/1024/1024)
	log.Warning("  HeapSys: %.2fMB", float64(memStats.HeapSys)/1024/1024)
	log.Warning("  HeapIdle: %.2fMB", float64(memStats.HeapIdle)/1024/1024)
	log.Warning("  HeapInuse: %.2fMB", float64(memStats.HeapInuse)/1024/1024)
	log.Warning("  HeapReleased: %.2fMB", float64(memStats.HeapReleased)/1024/1024)
	log.Warning("  HeapObjects: %d", memStats.HeapObjects)
	log.Warning("  StackInuse: %.2fMB", float64(memStats.StackInuse)/1024/1024)
	log.Warning("  StackSys: %.2fMB", float64(memStats.StackSys)/1024/1024)
	log.Warning("  NumGC: %d", memStats.NumGC)
	log.Warning("  PauseTotalNs: %d", memStats.PauseTotalNs)

	if memStats.NumGC > 0 {
		avgPause := memStats.PauseTotalNs / uint64(memStats.NumGC)
		log.Warning("  AvgGCPause: %.2fms", float64(avgPause)/1000000)
	}
}

// 输出内存碎片统计信息
func (m *Monitor) logFragmentationStats(stats *FragmentationStats) {
	log.Warning("=== Memory Fragmentation Analysis ===")
	log.Warning("  Heap System: %.2fMB", stats.HeapSysMB)
	log.Warning("  Heap InUse: %.2fMB", stats.HeapInUseMB)
	log.Warning("  Heap Idle: %.2fMB", stats.HeapIdleMB)
	log.Warning("  Heap Released: %.2fMB", stats.HeapReleasedMB)
	log.Warning("  Fragmentation Rate: %.2f%%", stats.FragmentationRate)
	log.Warning("  Utilization Rate: %.2f%%", stats.UtilizationRate)
	log.Warning("  Release Rate: %.2f%%", stats.ReleaseRate)
	log.Warning("  GC Count: %d", stats.NumGC)

	// 碎片分析建议
	if stats.FragmentationRate > 30 {
		log.Warning("  HIGH FRAGMENTATION detected! Consider triggering GC or optimizing allocation patterns")
	} else if stats.FragmentationRate > 15 {
		log.Warning("  MODERATE FRAGMENTATION detected")
	} else {
		log.Info("  Low fragmentation - memory usage is efficient")
	}

	if stats.UtilizationRate < 50 {
		log.Warning("  LOW UTILIZATION detected! %.2f%% of allocated heap is unused", 100-stats.UtilizationRate)
	}
}

// 强制执行GC并输出前后对比
func (m *Monitor) ForceGCWithStats() {
	var before, after runtime.MemStats

	runtime.ReadMemStats(&before)
	log.Info("Before GC - Alloc: %.2fMB, HeapObjects: %d",
		float64(before.Alloc)/1024/1024, before.HeapObjects)

	runtime.GC()
	runtime.ReadMemStats(&after)

	log.Info("After GC - Alloc: %.2fMB, HeapObjects: %d, Freed: %.2fMB",
		float64(after.Alloc)/1024/1024,
		after.HeapObjects,
		float64(before.Alloc-after.Alloc)/1024/1024)

	m.lastGCTime = time.Now()
}

// 检查内存泄漏
func (m *Monitor) CheckMemoryLeak() bool {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 简单的内存泄漏检测：如果堆对象数量持续增长且GC后仍然很高
	if memStats.HeapObjects > 1000000 { // 超过100万个对象
		log.Warning("Potential memory leak detected: %d heap objects", memStats.HeapObjects)
		return true
	}

	// 检查内存增长趋势
	if m.lastMemStats.Alloc > 0 {
		growthRatio := float64(memStats.Alloc) / float64(m.lastMemStats.Alloc)
		if growthRatio > 1.5 { // 内存增长超过50%
			log.Warning("Rapid memory growth detected: %.2f%% increase", (growthRatio-1)*100)
			return true
		}
	}

	return false
}

// 分析内存碎片情况
func (m *Monitor) AnalyzeMemoryFragmentation() *FragmentationStats {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 计算碎片率
	heapInUse := memStats.HeapInuse
	heapSys := memStats.HeapSys
	heapIdle := memStats.HeapIdle
	heapReleased := memStats.HeapReleased

	// 碎片率 = (系统分配的堆内存 - 正在使用的堆内存) / 系统分配的堆内存
	var fragmentationRate float64
	if heapSys > 0 {
		fragmentationRate = float64(heapSys-heapInUse) / float64(heapSys) * 100
	}

	// 内存利用率 = 正在使用的内存 / 系统分配的内存
	var utilizationRate float64
	if heapSys > 0 {
		utilizationRate = float64(heapInUse) / float64(heapSys) * 100
	}

	// 释放率 = 已释放给OS的内存 / 空闲内存
	var releaseRate float64
	if heapIdle > 0 {
		releaseRate = float64(heapReleased) / float64(heapIdle) * 100
	}

	return &FragmentationStats{
		HeapSysMB:         float64(heapSys) / 1024 / 1024,
		HeapInUseMB:       float64(heapInUse) / 1024 / 1024,
		HeapIdleMB:        float64(heapIdle) / 1024 / 1024,
		HeapReleasedMB:    float64(heapReleased) / 1024 / 1024,
		FragmentationRate: fragmentationRate,
		UtilizationRate:   utilizationRate,
		ReleaseRate:       releaseRate,
		NumGC:             memStats.NumGC,
		LastGCTime:        time.Unix(0, int64(memStats.LastGC)),
	}
}

// 内存碎片统计
type FragmentationStats struct {
	HeapSysMB         float64   `json:"heap_sys_mb"`        // 系统分配的堆内存(MB)
	HeapInUseMB       float64   `json:"heap_inuse_mb"`      // 正在使用的堆内存(MB)
	HeapIdleMB        float64   `json:"heap_idle_mb"`       // 空闲堆内存(MB)
	HeapReleasedMB    float64   `json:"heap_released_mb"`   // 已释放给OS的内存(MB)
	FragmentationRate float64   `json:"fragmentation_rate"` // 碎片率(%)
	UtilizationRate   float64   `json:"utilization_rate"`   // 内存利用率(%)
	ReleaseRate       float64   `json:"release_rate"`       // 内存释放率(%)
	NumGC             uint32    `json:"num_gc"`             // GC次数
	LastGCTime        time.Time `json:"last_gc_time"`       // 最后GC时间
}

// 获取进程的RSS和VSZ内存使用情况（Linux系统）
func getProcessMemoryUsage() (float64, float64) {
	// 读取/proc/self/status文件
	data, err := os.ReadFile("/proc/self/status")
	if err != nil {
		return 0, 0
	}

	var rssKB, vszKB float64
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "VmRSS:") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				if val, err := strconv.ParseFloat(fields[1], 64); err == nil {
					rssKB = val
				}
			}
		} else if strings.HasPrefix(line, "VmSize:") {
			fields := strings.Fields(line)
			if len(fields) >= 2 {
				if val, err := strconv.ParseFloat(fields[1], 64); err == nil {
					vszKB = val
				}
			}
		}
	}

	// 转换为MB
	return rssKB / 1024, vszKB / 1024
}

// 全局内存监控器实例
var globalMonitor *Monitor

// 初始化全局内存监控器
func InitGlobalMonitor(interval time.Duration, alertThreshold, gcThreshold int64) {
	globalMonitor = NewMonitor(interval, alertThreshold, gcThreshold)
	globalMonitor.Start()
}

// 获取全局内存监控器
func GetGlobalMonitor() *Monitor {
	return globalMonitor
}

// 停止全局内存监控器
func StopGlobalMonitor() {
	if globalMonitor != nil {
		globalMonitor.Stop()
	}
}
