package slg

// 通知类型
const (
	LOBBY_UPDATE_USER_INFO = "lobby/OnUpdateUserInfo" //用户信息更新

	LOBBY_FRIEND_UPDATE    = "lobby/OnFriendUpdate"   //好友信息更新 FriendInfo
	LOBBY_FRIEND_APPLY     = "lobby/OnFriendApply"    //好友申请通知 FriendApplyInfo
	LOBBY_FRIEND_ADD       = "lobby/OnFriendAdd"      //好友添加通知 FriendInfo
	LOBBY_FRIEND_CHAT      = "lobby/OnFriendChat"     //好友聊天通知 ChatInfo
	LOBBY_FRIEND_DEL       = "lobby/OnFriendDel"      //删除好友通知 FriendInfo
	LOBBY_FRIEND_SEND_GIFT = "lobby/OnFriendSendGift" //好友赠送通知 FriendInfo

	LOBBY_TEAM_UPDATE          = "lobby/OnTeamUpdate"         //队伍信息更新
	LOBBY_TEAM_INVITE          = "lobby/OnTeamInvite"         //队伍邀请通知
	LOBBY_TEAM_DEL_TEAMMATE    = "lobby/OnTeamDelTeammate"    //队伍删除队员通知
	LOBBY_TEAM_CHANGE_MODE     = "lobby/OnTeamChangeMode"     //队伍改变模式
	LOBBY_TEAM_CANCEL_APPLY    = "lobby/OnTeamCancelApply"    //队伍取消报名
	LOBBY_TEAMMATE_INFO_CHANGE = "lobby/OnTeammateInfoChange" //队员信息变更

	LOBBY_TRANSLATE_TEXT = "lobby/OnTranslateText" //文本翻译通知

	CHAT_LOBBY_CHAT = "chat/OnLobbyChat" //聊天新消息通知
)
