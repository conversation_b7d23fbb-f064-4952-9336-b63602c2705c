package lobby

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

func InitPortrayalsFormDB(list []*g.PortrayalInfo) []*g.PortrayalInfo {
	for _, m := range list {
		m.UpdateInfoByDB()
	}
	return list
}

func (this *User) ToPortrayalsPb() []*pb.PortrayalInfo {
	this.PopularityLock.RLock()
	defer this.PopularityLock.RUnlock()
	return array.Map(this.Portrayals, func(m *g.PortrayalInfo, _ int) *pb.PortrayalInfo { return m.ToPb() })
}

func (this *User) PortrayalsClone() []*g.PortrayalInfo {
	this.PopularityLock.RLock()
	defer this.PopularityLock.RUnlock()
	return array.Map(this.Portrayals, func(m *g.PortrayalInfo, _ int) *g.PortrayalInfo { return m })
}

// 点将
func (this *User) Pointsets(count int32) []int32 {
	datas := config.GetJson("portrayalBase").Datas
	totalWeight := 0
	for _, m := range datas {
		totalWeight += ut.Int(m["weight"])
	}
	ids := []int32{}
	for i := int32(0); i < count; i++ {
		index := ut.RandomIndexByWeightHasTotal(datas, totalWeight)
		json := datas[index]
		id := ut.Int32(json["id"])
		if ut.Chance(lc.POINTSETS_PORTRAYAL_ODDS) { //3%几率 获得画像
			id = id*10 + 1
		}
		ids = append(ids, id)
	}
	return ids
}

// 获取画像信息
func (this *User) GetPortrayalInfo(id int32) *g.PortrayalInfo {
	this.PopularityLock.RLock()
	defer this.PopularityLock.RUnlock()
	if info := array.Find(this.Portrayals, func(m *g.PortrayalInfo) bool { return m.ID == id }); info != nil {
		return info
	}
	return nil
}

// 删除画像
func (this *User) RemovePortrayal(id int32) {
	this.PopularityLock.Lock()
	defer this.PopularityLock.Unlock()
	this.Portrayals = array.RemoveItem(this.Portrayals, func(m *g.PortrayalInfo) bool { return m.ID == id })
}

// 添加残卷
func (this *User) AddPortrayalDebris(id int32) {
	var count int32 = 1
	if id > 1000000 { //画像直接添加3个残卷
		id /= 10
		count = lc.PORTRAYAL_COMP_NEED_COUNT
	}
	this.AddPortrayalDebrisCount(id, count)
}

// 添加残卷
func (this *User) AddPortrayalDebrisCount(id, count int32) {
	var after int32
	if count == 0 {
		return
	} else if info := this.GetPortrayalInfo(id); info != nil {
		info.Debris += count
		after = info.Debris
	} else {
		this.PopularityLock.Lock()
		this.Portrayals = append(this.Portrayals, g.NewPortrayal(id, count))
		this.PopularityLock.Unlock()
		after = count
	}
	AddItemRecord(this.UID, ctype.HERO_DEBRIS, id, count, after, 0, "")
}
