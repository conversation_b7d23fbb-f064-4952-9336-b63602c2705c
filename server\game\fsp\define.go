package fsp

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

// 区域
type Area interface {
	GetIndex() int32
	GetOwner() string
	GetCurHP() int32
	GetMaxHP() int32
	SetCurHP(val int32)
	SetMaxHP(val int32)
	GetCityID() int32
	GetAreaSize() *ut.Vec2
	RecordLastAttackMainPlayer()
	GetMainPoints() []*ut.Vec2
	GetWorld() g.World
	GetBuildPawnInfo() (id, lv int32)
	CheckIsBattleArea(x, y int32) bool //检测是否在战斗区域内
	CheckIsBuildArea(x, y int32) bool  //检测是否在建筑区域内
	CheckBattleEndTime() bool          //检测是否战斗时间到了
	GetBattleTime() int32              //获取交战时间
	BattleEnd(fighters []string)
	RemoveArmyPawn(auid, uid string, isDead bool) (bool, map[int32]int32) //删除士兵
	NotifyCheckFrame(msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY)
	ToBuildsStrip() []*g.BuildStrip
	ToArmysStrip2() []*g.ArmyStrip2
	GetAreaIBuildById(id int32) g.Build
	GetBattleCurrentFrameIndex() int32 //获取当前战斗帧数
	AddFightersToBattle()
	CreateBuildFlagPawn(uid string, hp []int32, point *ut.Vec2, owner string) g.Pawn
	CreatePetPawn(uid string, id, lv int32, point *ut.Vec2, owner string) g.Pawn
	CreateNoncombatPawn(uid string, id, lv int32, point *ut.Vec2, owner string, enterDir int32) g.Pawn
}

// 帧同步参数
type FSPParam struct {
	Attacker        string
	Pawns           []g.Pawn
	CampMap         map[int32]string
	FspMul          int32
	AlliUidMap      map[string]string
	AttackerArmyNum int32
	DefenderArmyNum int32
}
