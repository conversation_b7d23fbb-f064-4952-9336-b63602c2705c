package ut

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"slgsrv/utils/array"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	isSetRandSeed         = false
	unique_id       int64 = 0
	gen_id_last_now int64 = 0
)

type DataWeight interface {
	GetWeight() int
}

const (
	TIME_DAY    = 24 * 60 * 60 * 1000 // 天
	TIME_HOUR   = 60 * 60 * 1000      // 时
	TIME_MINUTE = 60 * 1000           // 分
	TIME_SECOND = 1000                // 秒
)

// 当前的时间戳 毫秒
func Now() int64 {
	return time.Now().UnixMilli()
}

// 根据毫秒获取零点毫秒
func DateZeroTime(msec int64) int64 {
	date := time.UnixMilli(msec)
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return addTime.UnixMilli()
}

// 当前的零点
func NowZeroTime() int64 {
	date := time.Now()
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return addTime.UnixMilli()
}

// 当天的指定小时的时间戳
func TodayHourTime(hour int) int64 {
	date := time.Now()
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, hour, 0, 0, 0, date.Location())
	return addTime.UnixMilli()
}

// 当天的凌晨4点
func NowFourTime() int64 {
	return TodayHourTime(4)
}

// 时间字符串转换为时间戳
func ParseDateStr2TimeStamps(dateStr string) (int, error) {
	timeLayout := "2006-01-02 15:04:05" // 转化所需模板
	loc, _ := time.LoadLocation("Local")
	timeDate, err := time.ParseInLocation(timeLayout, dateStr, loc)
	if err != nil {
		return 0, err
	}
	return int(timeDate.UnixMilli()), nil
}

// 获取当天日期
func GetTodayDate() string {
	t := time.Now()
	return t.Format("2006-01-02")
}

// 获取当天凌晨4点为界限的日期
func GetToday4Date() string {
	t := time.Now().Add(-time.Hour * 4)
	return t.Format("2006-01-02")
}

// 格式化时间为字符串 2006-01-02 15:04:05
func DateFormat(msec int64, layout string) string {
	return time.UnixMilli(msec).Format(layout)
}

// 生成随机数包括min和max
func Random(min int, max int) int {
	if min >= max {
		return min
	} else if !isSetRandSeed {
		isSetRandSeed = true
		rand.Seed(time.Now().Unix())
	}
	return rand.Intn(int(math.Max(float64(max-min), 0))+1) + min
}

// 生成随机数包括min和max
func RandomInt32(min int32, max int32) int32 {
	if min >= max {
		return min
	} else if !isSetRandSeed {
		isSetRandSeed = true
		rand.Seed(time.Now().Unix())
	}
	return int32(rand.Intn(int(math.Max(float64(max-min), 0))+1) + int(min))
}

// 四舍五入
func Round(val float64) int {
	return int(math.Round(val))
}

// 四舍五入
func RoundInt32(val float64) int32 {
	return int32(math.Round(val))
}

// 概率
func Chance(odds int) bool {
	mul := 100
	return odds > 0 && Random(0, 100*mul-1) < odds*mul
}

// 概率
func ChanceInt32(odds int32) bool {
	var mul int32 = 100
	return odds > 0 && RandomInt32(0, 100*mul-1) < odds*mul
}

// 根据权重随机一个下标
func RandomIndexByWeight(datas []map[string]interface{}) int {
	totalWeight, cnt := 0, len(datas)
	for _, m := range datas {
		totalWeight += Int(m["weight"])
	}
	offset := Random(0, totalWeight-1)
	for i := 0; i < cnt; i++ {
		val := Int(datas[i]["weight"])
		if val == 0 {
			continue
		} else if offset < val {
			return i
		} else {
			offset -= val
		}
	}
	return Random(0, cnt-1)
}

func RandomIndexByWeightHasTotal(datas []map[string]interface{}, totalWeight int) int {
	cnt := len(datas)
	offset := Random(0, totalWeight-1)
	for i := 0; i < cnt; i++ {
		val := Int(datas[i]["weight"])
		if val == 0 {
			continue
		} else if offset < val {
			return i
		} else {
			offset -= val
		}
	}
	return Random(0, cnt-1)
}

func RandomIndexByDataWeight(datas []DataWeight) int {
	cnt := len(datas)
	if cnt == 0 {
		return -1
	}
	totalWeight := 0
	for _, m := range datas {
		totalWeight += m.GetWeight()
	}
	offset := Random(0, totalWeight-1)
	for i := 0; i < cnt; i++ {
		val := datas[i].GetWeight()
		if val == 0 {
			continue
		} else if offset < val {
			return i
		} else {
			offset -= val
		}
	}
	return Random(0, cnt-1)
}

// 64位状态变量: 高48位存储时间戳(gen_id_last_now), 低16位存储计数器(unique_id)
var idGenState uint64

// 唯一ID生成函数
func ID() string {
	for { // CAS 重试循环
		oldState := atomic.LoadUint64(&idGenState)
		lastTime := uint64(oldState >> 16)   // 高48位：上次时间戳
		counter := uint32(oldState & 0xFFFF) // 提取低16位计数器
		now := uint64(Now())                 // 获取当前时间戳（假设Now()返回int）

		var newTime uint64
		var newCounter uint32

		// 逻辑判断
		if now != lastTime {
			// 时间戳变化：重置计数器
			newTime, newCounter = now, 0
		} else if counter >= 999 {
			// 计数器耗尽：强制进位到下一时间戳
			newTime, newCounter = now+1, 0
		} else {
			// 常规情况：计数器自增
			newTime, newCounter = now, counter+1
		}

		// 打包新状态
		newState := (uint64(newTime) << 16) | uint64(newCounter)

		// CAS 原子更新
		if atomic.CompareAndSwapUint64(&idGenState, oldState, newState) {
			// 计算最终ID
			if newCounter == 0 && newTime != now {
				// 处理进位后的ID（时间戳+1，计数器归零）
				return strconv.Itoa(int(newTime)*1000 + 1)
			}
			return strconv.Itoa(int(now)*1000 + int(newCounter) + 1)
		}
		// CAS 失败则重试
	}
}

// // 唯一ID
// func ID() string {
// 	now := Int64(Now())
// 	id := now*1000 + 1
// 	currentGenId := atomic.LoadInt64(&gen_id_last_now)
// 	if now != currentGenId {
// 		atomic.StoreInt64(&gen_id_last_now, now)
// 		atomic.StoreInt64(&unique_id, 0)
// 	} else if atomic.LoadInt64(&unique_id) >= 999 {
// 		newTime := now + 1
// 		atomic.StoreInt64(&gen_id_last_now, newTime)
// 		atomic.StoreInt64(&unique_id, 0)
// 		id = newTime*1000 + 1
// 	} else {
// 		atomic.AddInt64(&unique_id, 1)
// 		id += atomic.LoadInt64(&unique_id)
// 	}
// 	return String(id)
// }

// 生成一个随机7位UID
func UID7() string {
	return strconv.Itoa(Random(1000000, 9999999))
}

func UID8() string {
	return strconv.Itoa(Random(10000000, 99999999))
}

func UID6() string {
	return strconv.Itoa(Random(100000, 999999))
}

// 生成md5
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// 三元判断
func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func Byte(val interface{}) byte {
	switch reply := val.(type) {
	case byte:
		return reply
	case float32:
		return byte(reply)
	case float64:
		return byte(reply)
	case int32:
		return byte(reply)
	case int64:
		return byte(reply)
	case int:
		return byte(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return byte(s)
	case nil:
		return 0
	}
	return 0
}

func Int(val interface{}) int {
	switch reply := val.(type) {
	case float32:
		return int(reply)
	case float64:
		return int(reply)
	case int32:
		return int(reply)
	case int64:
		return int(reply)
	case int:
		return reply
	case string:
		s, _ := strconv.Atoi(reply)
		return s
	case nil:
		return 0
	}
	return 0
}

func Int32(val interface{}) int32 {
	switch reply := val.(type) {
	case float32:
		return int32(reply)
	case float64:
		return int32(reply)
	case int32:
		return reply
	case int64:
		return int32(reply)
	case int:
		return int32(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return int32(s)
	case nil:
		return 0
	}
	return 0
}

func Int64(val interface{}) int64 {
	switch reply := val.(type) {
	case float32:
		return int64(reply)
	case float64:
		return int64(reply)
	case int32:
		return int64(reply)
	case int64:
		return reply
	case int:
		return int64(reply)
	case string:
		s, _ := strconv.ParseInt(reply, 10, 64)
		return s
	case nil:
		return 0
	}
	return 0
}

func Float32(val interface{}) float32 {
	switch reply := val.(type) {
	case float32:
		return reply
	case float64:
		return float32(reply)
	case int:
		return float32(reply)
	case int32:
		return float32(reply)
	case int64:
		return float32(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 32)
		return float32(s)
	case nil:
		return 0
	}
	return 0
}

func Float64(val interface{}) float64 {
	switch reply := val.(type) {
	case float64:
		return reply
	case float32:
		return float64(reply)
	case int:
		return float64(reply)
	case int32:
		return float64(reply)
	case int64:
		return float64(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 64)
		return s
	case nil:
		return 0
	}
	return 0
}

func String(val interface{}) string {
	switch reply := val.(type) {
	case string:
		return reply
	case int:
		return strconv.Itoa(reply)
	case int32:
		return strconv.Itoa(int(reply))
	case int64:
		return strconv.FormatInt(reply, 10)
	case int8:
		return strconv.Itoa(int(reply))
	case uint8:
		return strconv.Itoa(int(reply))
	case float32:
		return strconv.FormatFloat(float64(reply), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(reply, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(reply)
	case []interface{}:
		return ArrayJoin(reply, "|")
	case []map[string]interface{}:
		return ArrayJoin(reply, "|")
	case map[string]interface{}:
		data, _ := json.Marshal(reply)
		return string(data)
	case nil:
		return ""
	}
	return ""
}

// 数组拼接
func ArrayJoin[T any](arr []T, separator string) string {
	if len(arr) == 0 {
		return ""
	}
	str := ""
	for _, v := range arr {
		if str != "" {
			str += separator
		}
		str += String(v)
	}
	return str
}

// 字符串拼接
func StringJoin(separator string, arr ...interface{}) string {
	return ArrayJoin(arr, separator)
}

func Bool(val interface{}) bool {
	switch reply := val.(type) {
	case bool:
		return reply
	case nil:
		return false
	}
	return false
}

func IntArray(val interface{}) []int {
	switch reply := val.(type) {
	case []int:
		return reply
	case []float64:
		return array.Map(reply, func(m float64, _ int) int { return int(m) })
	case []int32:
		return array.Map(reply, func(m int32, _ int) int { return int(m) })
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) int { return Int(m) })
	case primitive.A:
		arr := []int{}
		for _, val := range reply {
			arr = append(arr, Int(val))
		}
		return arr
	case nil:
		return []int{}
	}
	return []int{}
}

func Int32Array(val interface{}) []int32 {
	switch reply := val.(type) {
	case []int:
		return array.Map(reply, func(m int, _ int) int32 { return int32(m) })
	case []float64:
		return array.Map(reply, func(m float64, _ int) int32 { return int32(m) })
	case []int32:
		return reply
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) int32 { return Int32(m) })
	case primitive.A:
		arr := []int32{}
		for _, val := range reply {
			arr = append(arr, Int32(val))
		}
		return arr
	case nil:
		return []int32{}
	}
	return []int32{}
}

func Int32Arrays(val interface{}) [][]int32 {
	switch reply := val.(type) {
	case [][]int:
		return array.Map(reply, func(m []int, _ int) []int32 { return Int32Array(m) })
	case [][]float64:
		return array.Map(reply, func(m []float64, _ int) []int32 { return Int32Array(m) })
	case [][]int32:
		return reply
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) []int32 { return Int32Array(m) })
	case primitive.A:
		arr := [][]int32{}
		for _, val := range reply {
			arr = append(arr, Int32Array(val))
		}
		return arr
	case nil:
		return [][]int32{}
	}
	return [][]int32{}
}

func StringArray(val interface{}) []string {
	switch reply := val.(type) {
	case []string:
		return reply
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) string { return String(m) })
	case nil:
		return []string{}
	}
	return []string{}
}

func Bytes(val any) []byte {
	switch reply := val.(type) {
	case []byte:
		return reply
	}
	body, _ := json.Marshal(val)
	return body
}

func MapArray(data interface{}) []map[string]interface{} {
	arr := []map[string]interface{}{}
	if data == nil {
		return arr
	}
	switch reply := data.(type) {
	case []interface{}:
		for _, v := range reply {
			arr = append(arr, v.(map[string]interface{}))
		}
		return arr
	case []map[string]interface{}:
		return reply
	case primitive.A:
		for _, v := range reply {
			arr = append(arr, MapInterface(v))
		}
		return arr
	}
	return arr
}

func MapInterface(data interface{}) map[string]interface{} {
	switch reply := data.(type) {
	case map[string]interface{}:
		return reply
	case nil:
	}
	return map[string]interface{}{}
}

func MapIntInt(data interface{}) map[int]int {
	switch reply := data.(type) {
	case map[int]int:
		return reply
	case map[int32]int32:
		ret := map[int]int{}
		for k, v := range reply {
			ret[Int(k)] = Int(v)
		}
		return ret
	case map[string]interface{}:
		ret := map[int]int{}
		for k, v := range reply {
			ret[Int(k)] = Int(v)
		}
		return ret
	case nil:
	}
	return map[int]int{}
}

func MapInt32Int32(data interface{}) map[int32]int32 {
	switch reply := data.(type) {
	case map[int32]int32:
		return reply
	case map[int]int:
		ret := map[int32]int32{}
		for k, v := range reply {
			ret[Int32(k)] = Int32(v)
		}
		return ret
	case map[string]interface{}:
		ret := map[int32]int32{}
		for k, v := range reply {
			ret[Int32(k)] = Int32(v)
		}
		return ret
	case nil:
	}
	return map[int32]int32{}
}

func Map[K comparable, V any](reply interface{}) map[K]V {
	switch reply := reply.(type) {
	case map[K]V:
		return reply
	}
	return map[K]V{}
}

func MapStringBool(data interface{}) map[string]bool {
	switch reply := data.(type) {
	case map[string]bool:
		return reply
	case map[string]interface{}:
		ret := map[string]bool{}
		for k, v := range reply {
			ret[String(k)] = Bool(v)
		}
		return ret
	case nil:
	}
	return map[string]bool{}
}

func MapStringInt(data interface{}) map[string]int {
	switch reply := data.(type) {
	case map[string]int:
		return reply
	case map[string]interface{}:
		ret := map[string]int{}
		for k, v := range reply {
			ret[String(k)] = Int(v)
		}
		return ret
	case nil:
	}
	return map[string]int{}
}

func MapIntBool(data interface{}) map[int]bool {
	switch reply := data.(type) {
	case map[int]bool:
		return reply
	case map[int]interface{}:
		ret := map[int]bool{}
		for k, v := range reply {
			ret[Int(k)] = Bool(v)
		}
		return ret
	case map[string]interface{}:
		ret := map[int]bool{}
		for k, v := range reply {
			ret[Int(k)] = Bool(v)
		}
		return ret
	case nil:
	}
	return map[int]bool{}
}

func MapInt32Bool(data interface{}) map[int32]bool {
	switch reply := data.(type) {
	case map[int32]bool:
		return reply
	case map[int32]interface{}:
		ret := map[int32]bool{}
		for k, v := range reply {
			ret[Int32(k)] = Bool(v)
		}
		return ret
	case map[string]interface{}:
		ret := map[int32]bool{}
		for k, v := range reply {
			ret[Int32(k)] = Bool(v)
		}
		return ret
	case nil:
	}
	return map[int32]bool{}
}

func RpcInterfaceMap(reply interface{}, _err interface{}) (ret map[string]interface{}, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case map[string]interface{}:
		ret = reply
	}
	return
}

func RpcMap[K comparable, V any](reply interface{}, _err interface{}) (ret map[K]V, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case map[K]V:
		ret = reply
	}
	return
}

func RpcInt(reply interface{}, _err interface{}) (ret int, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Int(reply)
	return
}

func RpcBool(reply interface{}, _err interface{}) (ret bool, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Bool(reply)
	return
}

func RpcBytes(reply interface{}, _err interface{}) (ret []byte, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case []byte:
		ret = reply
	}
	return
}

// int转字符串
func Itoa(val interface{}) string {
	return strconv.Itoa(Int(val))
}

// string转int
func Atoi(val interface{}) int {
	r, _ := strconv.Atoi(String(val))
	return r
}

// string转Float
func Atof(val interface{}) float64 {
	r, _ := strconv.ParseFloat(String(val), 64)
	return r
}

// 将一个字符串拆分为数组
func StringToInts(val string, separator string) []int {
	if val == "" {
		return []int{}
	}
	arr := strings.Split(val, separator)
	ret := []int{}
	for _, s := range arr {
		ret = append(ret, Atoi(s))
	}
	return ret
}

// 将一个字符串拆分为数组
func StringToInt32s(val string, separator string) []int32 {
	if val == "" {
		return []int32{}
	}
	arr := strings.Split(val, separator)
	ret := []int32{}
	for _, s := range arr {
		ret = append(ret, int32(Atoi(s)))
	}
	return ret
}

func StringToFloats(val string, separator string) []float64 {
	if val == "" {
		return []float64{}
	}
	arr := strings.Split(val, separator)
	ret := []float64{}
	for _, s := range arr {
		ret = append(ret, Atof(s))
	}
	return ret
}

// 获取字符串长度 一个汉字2个长度
func GetStringLen(str string) int {
	if str == "" {
		return 0
	}
	count := 0
	for _, ch := range str {
		count += If((ch >= 0x0001 && ch <= 0x007e) || (0xff60 <= ch && ch <= 0xff9f), 1, 2)
	}
	return count
}

// 根据字符长度截取字符串，中文占用2个长度，英文占用1个长度
func TruncateString(s string, maxChars int) string {
	if maxChars <= 0 {
		return ""
	}
	count := 0
	result := ""
	for _, ch := range s {
		charLength := If((ch >= 0x0001 && ch <= 0x007e) || (0xff60 <= ch && ch <= 0xff9f), 1, 2)
		// fmt.Println(ch, charLength)
		if count+charLength > maxChars {
			break
		}
		result += string(ch)
		count += charLength
	}
	return result
}

// 获取当前的项目路径
func WorkDir() (dir string) {
	var err error
	dir, err = os.Getwd()
	if err != nil {
		file, _ := exec.LookPath(os.Args[0])
		ApplicationPath, _ := filepath.Abs(file)
		dir, _ = filepath.Split(ApplicationPath)
	}
	return
}

func SetTimeout(duration time.Duration, cb func()) {
	go func() {
		timer := time.NewTimer(duration)
		defer timer.Stop()
		<-timer.C
		cb()
	}()
}

// 加载json文件
func LoadJson(filename string, v interface{}) string {
	return LoadJsonByPath(filename, "/bin/conf/json/", v)
}

func LoadJsonByPath(filename string, path string, v interface{}) string {
	file, err := os.Open(WorkDir() + path + filename + ".json")
	if err != nil {
		return err.Error()
	}
	dec := json.NewDecoder(file)
	if err = dec.Decode(&v); err != nil {
		return "解析配置出错 err=" + err.Error()
	}
	if err = file.Close(); err != nil {
		return "file.Close() err=" + err.Error()
	}
	return ""
}

func Max(a, b int) int {
	return If(a > b, a, b)
}

func Min(a, b int) int {
	return If(a < b, a, b)
}

func MaxInt32(a, b int32) int32 {
	return If(a > b, a, b)
}

func MinInt32(a, b int32) int32 {
	return If(a < b, a, b)
}

func MaxInt64(a, b int64) int64 {
	return If(a > b, a, b)
}

func MinInt64(a, b int64) int64 {
	return If(a < b, a, b)
}

// 最大最小值
func Clamp(val, min, max int) int {
	if val < min {
		return min
	} else if val > max {
		return max
	}
	return val
}

// 最大最小值
func ClampInt32(val, min, max int32) int32 {
	if val < min {
		return min
	} else if val > max {
		return max
	}
	return val
}

func Abs(x int) int {
	return int(math.Abs(float64(x)))
}

func AbsInt32(x int32) int32 {
	return int32(math.Abs(float64(x)))
}

func Ceil(x float64) int {
	return int(math.Ceil(x))
}

func Floor(x float64) int {
	return int(math.Floor(x))
}

// 循环值
func LoopValue(index int, len int) int {
	index = index % len
	if index < 0 {
		return index + len
	}
	return index
}

// IsEmpty 字符串是否为空 忽略空格
func IsEmpty(str string) bool {
	return len(strings.ReplaceAll(str, " ", "")) == 0
}

func TraceMemStats() {
	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)
}

// Trim 字符串去掉所有空格
func Trim(str string) string {
	return strings.Join(strings.Fields(str), "")
}

// InitialIsChar 判断首个字符是不是字母 upper=true时区分大小写
func InitialIsChar(line string, upper bool) bool {
	line = Trim(line)
	if len(line) == 0 {
		return false
	}
	reg := "^[A-Za-z]"
	if upper {
		reg = "^[A-Z]"
	}
	matched, _ := regexp.Match(reg, []byte(line))
	return matched
}

// 检测版本 a >= b 为true，反之则为false
func CheckVersion(a, b string) bool {
	vA := StringToInts(a, ".")
	vB := StringToInts(b, ".")
	vALen := len(vA)
	vBLen := len(vB)
	for i := 0; i < vALen; i++ {
		a, b := vA[i], 0
		if i < vBLen {
			b = vB[i]
		}
		if a < b {
			return false
		} else if a > b {
			break
		}
	}
	return true
}

// 验证邮箱格式
func VerifyEmailFormat(email string) bool {
	pattern := `\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*` // 匹配电子邮箱
	reg := regexp.MustCompile(pattern)
	return reg.MatchString(email)
}

// uint32数组转为指定比特长度的字节数组
func IntCompressToBytes(data []uint32, sizes ...int) []byte {
	ret := []byte{}
	lastBitCount := 0 // 上个字节剩余位数
	lastIndex := -1   // 上个字节下标
	curSize := 0      // 当前数据所占位数
	curSizeIndex := 0 // 当前数据所占位数下标
	for _, v := range data {
		curSize = sizes[curSizeIndex]
		// byteCount := curSize / 8
		if lastBitCount > 0 {
			// 之前的字节仍有空位没用完 当前字节的高位填充其地位
			lastByte := ret[lastIndex]
			mv := curSize - lastBitCount
			lastByte |= byte(v >> uint32(mv))
			ret[lastIndex] = lastByte
			if lastBitCount >= curSize {
				// 上个字节剩余的比特位足够当前数据使用
				lastBitCount -= curSize
				continue
			}
		}
		// 添加新的字节
		newBytes, leftBitCount := uint32ToBytes(v, 32-curSize+lastBitCount)
		ret = append(ret, newBytes...)
		lastBitCount = leftBitCount
		lastIndex = Max(0, lastIndex+len(newBytes))
		curSizeIndex = (curSizeIndex + 1) % len(sizes)
	}
	return ret
}

// uint32从指定位数开始转为指定长度的字节数组
func uint32ToBytes(data uint32, start int) ([]byte, int) {
	leftSize := 32 - start
	byteCount := leftSize / 8
	mod := leftSize % 8
	if mod > 0 {
		byteCount++
	}
	ret := []byte{}
	validData := data << uint32(start%8)
	for i := byteCount - 1; i >= 0; i-- {
		mv := 8 * i
		ret = append(ret, uint8(validData>>mv))
	}
	return ret, If(mod == 0, 0, 8-mod)
}

// float32四舍五入并保留n位小数
func Float32Round(num float32, n int) float32 {
	rounded := float32(math.Round(float64(num)*100) / 100)
	str := strconv.FormatFloat(float64(rounded), 'f', n, 32)
	f64, _ := strconv.ParseFloat(str, 32)
	return float32(f64)
}

// float64四舍五入并保留n位小数
func Float64Round(num float64, n int) float64 {
	rounded := float32(math.Round(float64(num)*100) / 100)
	str := strconv.FormatFloat(float64(rounded), 'f', n, 64)
	f64, _ := strconv.ParseFloat(str, 64)
	return f64
}

// 切片乱序
func ShuffleSlice[T any](slice []T) {
	sort.Slice(slice, func(i, j int) bool { return Chance(50) })
}

// 计算数字的位数
func GetNumberLen(val int32) int32 {
	var ret int32 = 1
	for val >= 10 {
		val /= 10
		ret *= 10
	}
	return ret
}

// 获取本周几的时间戳
func GetCurrentWeekDayTime(weekday int32) int64 {
	// 检查输入是否有效
	if weekday < 0 || weekday > 6 {
		return 0
	}
	// 获取当前时间
	now := time.Now()
	// 计算目标星期几的偏移
	week := int32(now.Weekday())
	if week == 0 {
		week = 7
	}
	offset := weekday - week
	// 获取目标日期并将时间截断到零点
	targetDate := now.AddDate(0, 0, int(offset)).Truncate(24 * time.Hour)
	// 返回毫秒级时间戳
	return targetDate.UnixMilli()
}

// 获取下周几的时间戳
func GetNextWeekDayTime(weekday int32) int64 {
	// 检查输入是否有效
	if weekday < 0 || weekday > 6 {
		return 0
	}
	// 获取当前时间
	now := time.Now()
	// 计算目标星期几的偏移
	week := int32(now.Weekday())
	if week == 0 {
		week = 7
	}
	offset := weekday - week + 7 // 加 7 表示下周
	// 获取目标日期并将时间截断到零点
	targetDate := now.AddDate(0, 0, int(offset)).Truncate(24 * time.Hour)
	// 返回毫秒级时间戳
	return targetDate.UnixMilli()
}

// 获取本月几日的时间戳
func GetCurrentMonthDayTime(day int) int {
	now := time.Now()
	// Get the current year and month
	currentYear, currentMonth, _ := now.Date()
	// Create a new time for the first day of the current month
	currentMonthFirstDay := time.Date(currentYear, currentMonth, day, 0, 0, 0, 0, now.Location())
	// Return the Unix timestamp
	return int(currentMonthFirstDay.UnixMilli())
}

// 获取下月几日的时间戳
func GetNextMonthDayTime(day int) int {
	now := time.Now()
	// Get the current year and month
	currentYear, currentMonth, _ := now.Date()

	// Calculate the first day of the next month
	nextMonth := currentMonth + 1
	nextYear := currentYear

	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}
	// Create a new time for the first day of the next month
	nextMonthFirstDay := time.Date(nextYear, nextMonth, day, 0, 0, 0, 0, now.Location())
	// Return the Unix timestamp
	return int(nextMonthFirstDay.UnixMilli())
}

// 将一个数字转换为带正负符号的字符串
func NormalizeNumber(val int) int {
	if val == 0 {
		return 0
	}
	return val / Abs(val)
}

const namespace = "6ba7b811-9dad-11d1-80b4-00c04fd430c8" // 固定命名空间

// 将字符串转换为UUID
func StringToUUID(uid string) string {
	uuid := uuid.NewSHA1(uuid.MustParse(namespace), []byte(uid))
	return uuid.String()
}
