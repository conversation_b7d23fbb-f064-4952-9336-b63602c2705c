package behavior

import (
	"math"
	"sort"

	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 检测回合开始时
type CheckRoundBegin struct {
	BaseAction

	flamingArmorEffect *g.EquipEffectObj // 烈焰铠甲效果
}

func (this *CheckRoundBegin) OnInit(conf map[string]interface{}) {
}

func (this *CheckRoundBegin) OnOpen() {
	// 烈焰铠甲
	this.flamingArmorEffect = this.target.GetEquipEffectByType(eeffect.FLAMING_ARMOR)
	this.SetBlackboardData("isHasArmorEffect", this.flamingArmorEffect != nil)
	// 定心盔 每5回合恢复
	if CHECK_CENTERING_HELMET := this.target.GetBuff(bufftype.CHECK_CENTERING_HELMET); CHECK_CENTERING_HELMET != nil {
		CHECK_CENTERING_HELMET.Value -= 1
		if CHECK_CENTERING_HELMET.Value <= 0 {
			CHECK_CENTERING_HELMET.Value = 5
			this.SetBlackboardData("isHasCenteringHelmet", true)
		}
	}
	// 赤金盾 每3回合获得一个护盾
	if CHECK_CRIMSONGOLD_SHIELD := this.target.GetBuff(bufftype.CHECK_CRIMSONGOLD_SHIELD); CHECK_CRIMSONGOLD_SHIELD != nil {
		CHECK_CRIMSONGOLD_SHIELD.Value -= 1
		if CHECK_CRIMSONGOLD_SHIELD.Value <= 0 {
			CHECK_CRIMSONGOLD_SHIELD.Value = 3
			this.SetBlackboardData("isHasCrimsongoldShield", true)
		}
	}
}

func (this *CheckRoundBegin) OnLeave(state Status) {
	attackTarget := this.target.GetAttackTarget()
	if attackTarget != nil && attackTarget.IsDie() {
		this.target.ChangeAttackTarget(nil)
	}
}

func (this *CheckRoundBegin) OnTick(dt int32) Status {
	skill := this.target.GetEntity().GetPortrayalSkill()
	if waitRound := this.target.GetWaitRound(); waitRound > 0 {
		this.target.SetWaitRound(waitRound - 1)
		return FAILURE // 是否需要等待一回合
	} else if ut.Bool(this.GetTreeBlackboardData("isPlayJJShieldEnd")) {
		return this.playJJShieldEnd(dt) // 重盾机甲结束动画
	} else if ut.Bool(this.GetBlackboardData("isHasArmorEffect")) {
		return this.checkFlamingArmor(dt) // 焱阳铠
	} else if ut.Bool(this.GetTreeBlackboardData("isPoisonedWineEnd")) {
		return this.checkPoisonedWineEnd(dt) // 鸩毒爆炸
	} else if ut.Bool(this.GetBlackboardData("isHasCenteringHelmet")) {
		return this.checkCenteringHelmet(dt) // 定心盔
	} else if ut.Bool(this.GetBlackboardData("isHasCrimsongoldShield")) {
		return this.checkCrimsongoldShield(dt) // 赤金盾
	} else if this.target.IsHasBuffs(
		bufftype.DIZZINESS,
		bufftype.STAND_SHIELD,
		bufftype.PARALYSIS,
		bufftype.PARALYSIS_UP,
		bufftype.FEAR,
		bufftype.ANTICIPATION_DEFENSE,
	) {
		return FAILURE // 是否有眩晕buff 或者 立盾状态
	} else if skill != nil && skill.Id == hero.HUANG_GAI && !this.target.IsHasBuff(bufftype.CHECK_KUROU) {
		return this.checkKuRou(dt, skill) // 黄盖 检测是否释放苦肉
	} else if skill != nil && skill.Id == hero.DENG_AI && !this.target.IsHasBuff(bufftype.CHECK_TONDEN) {
		return this.checkTonden(dt, skill) // 邓艾 检测是否释放屯垦令
	} else if skill != nil && skill.Id == hero.SUN_SHANGXIANG && !ut.Bool(this.GetBlackboardData("isCheckLittleGirl")) {
		return this.checkLittleGirl(dt, skill) // 孙尚香 每5回合释放技能
	} else if this.target.CheckTriggerBuff(bufftype.CHECK_JADE_PENDANT) != nil {
		return this.checkJadePendant() // 检测玉佩
	}
	entity := this.target.GetEntity()
	if entity.GetID() == constant.PAWN_CROSSBOW_ID {
		return this.checkPullString(dt) // 检测拉弦
	} else if entity.GetID() == constant.AX_CAVALRY_ID {
		this.target.SetAnger(1) // 给斧骑兵加1怒
	}
	return SUCCESS
}

// 播放重盾机甲的立盾结束
func (this *CheckRoundBegin) playJJShieldEnd(dt int32) Status {
	currTime := ut.Int32(this.GetBlackboardData("currTimeJJShieldEnd"))
	if currTime >= 770 {
		this.SetTreeBlackboardData("isPlayJJShieldEnd", false)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTimeJJShieldEnd", currTime+dt)
	if currTime == 0 {
		this.target.RemoveBuff(bufftype.STAND_SHIELD)
	}
	return RUNNING
}

// 检测是否有烈焰铠甲 对1格内造成伤害
func (this *CheckRoundBegin) checkFlamingArmor(dt int32) Status {
	if this.flamingArmorEffect == nil {
		this.flamingArmorEffect = this.target.GetEquipEffectByType(eeffect.FLAMING_ARMOR)
	}
	currTime := ut.Int32(this.GetBlackboardData("currTimeFlamingArmor"))
	if currTime >= 600 {
		this.SetBlackboardData("isHasArmorEffect", false)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTimeFlamingArmor", currTime+dt)
	if currTime == 0 {
		trueDamage := ut.RoundInt32(float64(this.target.GetMaxHp()) * 0.04)
		if trueDamage <= 0 || !this.GetCtrl().GetRandom().ChanceInt32(this.flamingArmorEffect.Odds) {
			this.SetBlackboardData("isHasArmorEffect", false)
			return RUNNING
		}
		// 对1格内造成伤害
		arr := this.target.GetCanAttackPawnByRange(this.target.GetCanAttackFighters(), 1, 4, "")
		if len(arr) == 0 {
			this.SetBlackboardData("isHasArmorEffect", false)
			return RUNNING
		}
		hasDie := false
		uid := this.target.GetUID()
		for _, m := range arr {
			_, damage := m.HitPrepDamageHandle(0, trueDamage)
			v, _, _ := m.OnHit(damage, damage)
			if m.IsDie() {
				hasDie = true
			}
			// 记录数据
			this.GetCtrl().AddFighterBattleDamageInfo(uid, m, v)
		}
		// 如果有目标死了 需要重新获得攻击目标
		if hasDie {
			this.target.CleanCanAttackTargets()
		}
	}
	return RUNNING
}

// 检测鸩毒爆炸
func (this *CheckRoundBegin) checkPoisonedWineEnd(dt int32) Status {
	currTime := ut.Int32(this.GetBlackboardData("currPoisonedWineEnd"))
	if currTime >= 770 {
		this.SetTreeBlackboardData("isPoisonedWineEnd", false)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currPoisonedWineEnd", currTime+dt)
	if currTime == 0 {
		POISONED_WINE := this.target.GetBuff(bufftype.POISONED_WINE)
		if POISONED_WINE == nil {
			this.SetTreeBlackboardData("isPoisonedWineEnd", false)
			return RUNNING
		}
		this.target.RemoveBuff(bufftype.POISONED_WINE)
		uid, camp := this.target.GetUID(), this.target.GetCamp()
		arr := this.target.GetCanAttackRangeFighter(this.GetCtrl().GetFighters(), 2, 7, uid, func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
		arr = append(arr, this.target)
		damage := ut.RoundInt32(POISONED_WINE.Value * float64(POISONED_WINE.Lv+9) * 0.01)
		for _, m := range arr {
			_, val := m.HitPrepDamageHandle(0, damage)
			v, _, _ := m.OnHit(val, val)
			// 记录数据
			this.GetCtrl().AddFighterBattleDamageInfo(POISONED_WINE.Provider, m, v)
		}
	}
	return RUNNING
}

// 检测是否有定心盔 对1格内友方回血
func (this *CheckRoundBegin) checkCenteringHelmet(dt int32) Status {
	currTime := ut.Int32(this.GetBlackboardData("currTimeCenteringHelmet"))
	if currTime >= 600 {
		this.SetBlackboardData("isHasCenteringHelmet", false)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTimeCenteringHelmet", currTime+dt)
	if currTime == 0 {
		// 对1格内至多4个友方恢复生命
		uid, point, camp, rang, count := this.target.GetUID(), this.target.GetPoint(), this.target.GetCamp(), int32(1), 4
		fighters := array.Filter(this.GetCtrl().GetFighters(), func(m g.IFighter, _ int) bool {
			return m.GetCamp() == camp && m.GetUID() != uid && m.IsPawn() && helper.GetPointToPointDis(m.GetPoint(), point) == rang && !m.IsFullHP()
		})
		sort.Slice(fighters, func(i, j int) bool {
			a, b := fighters[i], fighters[j]
			aw := ut.RoundInt32((1-a.GetHPRatio())*100)*1000 + (999 - a.GetAttackIndex())
			bw := ut.RoundInt32((1-b.GetHPRatio())*100)*1000 + (999 - b.GetAttackIndex())
			return aw > bw
		})
		friends := fighters[0:ut.Min(len(fighters), count)]
		if !this.target.IsFullHP() {
			friends = append(friends, this.target)
		}
		if len(friends) == 0 {
			this.SetBlackboardData("isHasCenteringHelmet", false)
			return RUNNING
		}
		for _, m := range friends {
			val := ut.RoundInt32(float64(m.GetMaxHp()-m.GetCurHp()) * 0.25)
			m.OnHeal(val, false)
			m.ChangeState(constant.PAWN_STATE_HEAL)
		}
	}
	return RUNNING
}

// 检测是否有赤金盾 获得护盾
func (this *CheckRoundBegin) checkCrimsongoldShield(dt int32) Status {
	currTime := ut.Int32(this.GetBlackboardData("currTimeCrimsongoldShield"))
	if currTime >= 600 {
		this.SetBlackboardData("isHasCrimsongoldShield", false)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTimeCrimsongoldShield", currTime+dt)
	if currTime == 0 {
		shieldVal := math.Round(float64(this.target.GetMaxHp()) * 0.04)
		this.target.AddBuffValue(bufftype.CRIMSONGOLD_SHIELD, this.target.GetUID(), shieldVal)
	}
	return RUNNING
}

// 检测苦肉
func (this *CheckRoundBegin) checkKuRou(dt int32, skill *g.PortrayalSkill) Status {
	if this.target.GetHPRatio() <= skill.GetParamsFloat64() {
		this.target.AddBuff(bufftype.CHECK_KUROU, this.target.GetUID(), 1)
		return RUNNING // 血量低于20% 不苦肉
	}
	currTime := ut.Int32(this.GetBlackboardData("currTime"))
	if currTime >= 850 {
		this.target.AddBuff(bufftype.CHECK_KUROU, this.target.GetUID(), 1)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTime", currTime+dt)
	if currTime >= 600 {
		if !ut.Bool(this.GetBlackboardData("isHit")) {
			this.SetBlackboardData("isHit", true)
			damage := ut.RoundInt32(float64(this.target.GetMaxHp()) * skill.GetParamsFloat64())
			camp, point, uid := this.target.GetCamp(), this.target.GetPoint(), this.target.GetUID()
			// 扣除血量
			this.target.OnHit(damage, damage)
			// 给队友加护盾
			shield := math.Round(float64(damage) * skill.GetValue() * 0.01)
			arr := array.Filter(this.GetCtrl().GetFighters(), func(m g.IFighter, _ int) bool {
				return m.GetCamp() == camp && m.IsPawn() && m.GetUID() != uid && !m.IsHasBuff(bufftype.KUROU_SHIELD)
			})
			sort.Slice(arr, func(i, j int) bool {
				a, b := arr[i], arr[j]
				aw := 99 - helper.GetPointToPointDis(a.GetPoint(), point)
				bw := 99 - helper.GetPointToPointDis(b.GetPoint(), point)
				aw = aw*1000 + int32((1.0-a.GetHPRatio())*100.0)
				bw = bw*1000 + int32((1.0-b.GetHPRatio())*100.0)
				aw = aw*1000 + (999 - a.GetAttackIndex())
				bw = bw*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			arr = arr[:ut.MinInt32(int32(len(arr)), skill.Target)]
			arr = append(arr, this.target)
			for _, m := range arr {
				m.AddBuffValue(bufftype.KUROU_SHIELD, uid, shield)
				m.AddBuff(bufftype.KUROU_ADD_ATTACK, uid, 1)
			}
		}
	}
	return RUNNING
}

// 检测屯田
func (this *CheckRoundBegin) checkTonden(dt int32, skill *g.PortrayalSkill) Status {
	currTime := ut.Int32(this.GetBlackboardData("currTime"))
	if currTime >= 1500 {
		this.target.AddBuff(bufftype.CHECK_TONDEN, this.target.GetUID(), 1)
		return RUNNING
	}
	// 增加时间
	this.SetBlackboardData("currTime", currTime+dt)
	if currTime >= 1000 {
		if !ut.Bool(this.GetBlackboardData("isHit")) {
			this.SetBlackboardData("isHit", true)
			camp, point, uid := this.target.GetCamp(), this.target.GetPoint(), this.target.GetUID()
			arr := array.Filter(this.GetCtrl().GetFighters(), func(m g.IFighter, _ int) bool {
				return m.GetCamp() == camp && m.IsPawn() && m.GetUID() != uid && !m.IsHasBuffs(bufftype.TONDEN_BEGIN, bufftype.TONDEN_RECOVER)
			})
			sort.Slice(arr, func(i, j int) bool {
				a, b := arr[i], arr[j]
				aw := 99 - helper.GetPointToPointDis(a.GetPoint(), point)
				bw := 99 - helper.GetPointToPointDis(b.GetPoint(), point)
				aw = aw*1000 + (999 - a.GetAttackIndex())
				bw = bw*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			arr = arr[:ut.MinInt32(int32(len(arr)), skill.Target)]
			arr = append(arr, this.target)
			for _, m := range arr {
				m.AddBuffValue(bufftype.TONDEN_BEGIN, uid, skill.GetValue())
			}
		}
	}
	return RUNNING
}

// 孙尚香 每5回合释放技能
func (this *CheckRoundBegin) checkLittleGirl(dt int32, skill *g.PortrayalSkill) Status {
	currTime := ut.Int32(this.GetBlackboardData("currTime"))
	if currTime == 0 {
		round := skill.GetParamsFloat64()
		buff := this.target.GetBuff(bufftype.CHECK_LITTLE_GIRL)
		if buff == nil {
			buff = this.target.AddBuffValue(bufftype.CHECK_LITTLE_GIRL, this.target.GetUID(), round)
		}
		if buff.Value > 1 {
			buff.Value -= 1
			this.SetBlackboardData("isCheckLittleGirl", true)
			return RUNNING
		}
		buff.Value = round
	}
	// 增加时间
	this.SetBlackboardData("currTime", currTime+dt)
	if currTime >= 300 {
		camp, uid, point := this.target.GetCamp(), this.target.GetUID(), this.target.GetPoint()
		fTargets, eTargets := []g.IFighter{}, []g.IFighter{}
		fighters := this.GetCtrl().GetFighters()
		for _, m := range fighters {
			if !m.IsPawn() && m.GetUID() == uid {
				continue
			} else if m.GetCamp() == camp {
				fTargets = append(fTargets, m)
			} else if !m.IsHasBuff(bufftype.DIZZINESS) {
				eTargets = append(eTargets, m)
			}
		}
		cnt := ut.If(this.target.GetEntity().IsMaxLv(), 2, 1)
		// // 恢复生命比最低的友方
		if len(fTargets) > 0 {
			sort.Slice(fTargets, func(i, j int) bool {
				a, b := fTargets[i], fTargets[j]
				aw := int32((1 - a.GetHPRatio()) * 100)
				bw := int32((1 - b.GetHPRatio()) * 100)
				aw = aw*100 + (99 - helper.GetPointToPointDis(point, a.GetPoint()))
				bw = bw*100 + (99 - helper.GetPointToPointDis(point, b.GetPoint()))
				aw = aw*1000 + (999 - a.GetAttackIndex())
				bw = bw*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			arr := fTargets[:ut.Min(len(fTargets), cnt)]
			for _, m := range arr {
				val := ut.RoundInt32(float64(m.GetMaxHp()) * skill.GetValue() * 0.01)
				m.OnHeal(val, false)
			}
		}
		// 眩晕敌方攻击力最高的
		if len(eTargets) > 0 {
			sort.Slice(eTargets, func(i, j int) bool {
				a, b := eTargets[i], eTargets[j]
				aw := a.GetActAttack()*100 + (99 - helper.GetPointToPointDis(point, a.GetPoint()))
				bw := b.GetActAttack()*100 + (99 - helper.GetPointToPointDis(point, b.GetPoint()))
				aw = aw*1000 + (999 - a.GetAttackIndex())
				bw = bw*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			arr := eTargets[:ut.Min(len(eTargets), cnt)]
			for _, m := range arr {
				m.AddBuff(bufftype.DIZZINESS, uid, 3)
			}
		}
		this.SetBlackboardData("isCheckLittleGirl", true)
	}
	return RUNNING
}

// 检测玉佩
func (this *CheckRoundBegin) checkJadePendant() Status {
	this.target.RemoveBuff(bufftype.CHECK_JADE_PENDANT)
	if effect := this.target.GetEquipEffectByType(eeffect.BATTLE_BEGIN_SHIELD); effect != nil {
		camp, point, uid := this.target.GetCamp(), this.target.GetPoint(), this.target.GetUID()
		shield := ut.RoundInt32(float64(this.target.GetMaxHp()) * effect.Value * 0.01)
		arr := array.Filter(this.GetCtrl().GetFighters(), func(m g.IFighter, _ int) bool {
			return m.GetCamp() == camp && m.IsPawn() && !m.IsHasBuff(bufftype.BATTLE_BEGIN_SHIELD)
		})
		sort.Slice(arr, func(i, j int) bool {
			a, b := arr[i], arr[j]
			aw := 99 - helper.GetPointToPointDis(a.GetPoint(), point)
			bw := 99 - helper.GetPointToPointDis(b.GetPoint(), point)
			aw = aw*1000 + int32((1.0-a.GetHPRatio())*100.0)
			bw = bw*1000 + int32((1.0-b.GetHPRatio())*100.0)
			aw = aw*1000 + (999 - a.GetAttackIndex())
			bw = bw*1000 + (999 - b.GetAttackIndex())
			return aw > bw
		})
		arr = arr[:ut.Min(len(arr), 3)]
		for _, m := range arr {
			m.AddBuffValue(bufftype.BATTLE_BEGIN_SHIELD, uid, float64(shield)) // 添加护盾buff
			m.AddBuff(bufftype.WITHSTAND, uid, 1)                              // 抵挡一次技能攻击
		}
	}
	return RUNNING
}

// 检测是否需要拉弦 如果还没拉弦 那么就播放拉弦的动画
func (this *CheckRoundBegin) checkPullString(dt int32) Status {
	curAnger := this.target.GetEntity().GetCurAnger()
	if curAnger > 0 {
		return SUCCESS
	} else if curAnger == 0 {
		this.target.SetAnger(-1) // 让状态变成拉弦状态
	}
	animTimes := this.target.GetAttackAnimTimes()
	if animTimes != nil && len(animTimes) > 0 && len(animTimes[0]) > 0 {
		needAttackTime := int32(animTimes[0][0] * 1000)
		currTime := ut.Int32(this.GetBlackboardData("currTime"))
		if currTime >= needAttackTime {
			var val int32 = 1
			if this.target.GetStrategyValue(31402) > 0 {
				val = 2 // 韬略
			}
			this.target.SetAnger(val)
			this.target.ChangeState(constant.PAWN_STATE_STAND)
			return FAILURE
		}
		// 增加时间
		this.SetBlackboardData("currTime", currTime+dt)
		return RUNNING
	}
	return SUCCESS
}
