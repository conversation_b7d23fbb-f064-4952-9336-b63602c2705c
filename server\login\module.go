package login

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Login)
}

type Login struct {
	basemodule.BaseModule
}

func (this *Login) GetType() string {
	return "login" //很关键,需要与配置文件中的Module配置对应
}

func (this *Login) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Login) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "login" || serverType == "development" {
		sdk.InitAppleRootCerts()
	}
}

func (this *Login) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	this.GetServer().RegisterGO("HD_GuestLogin", this.guestLogin)       //游客登录
	this.GetServer().RegisterGO("HD_WxLogin", this.wxLogin)             //微信登录
	this.GetServer().RegisterGO("HD_FacebookLogin", this.faceBookLogin) //facebook登录
	this.GetServer().RegisterGO("HD_AppleLogin", this.appleLogin)       //apple登录
	this.GetServer().RegisterGO("HD_GoogleLogin", this.googleLogin)     //google登录

	dh.InitDhLog()
	sdk.InitAppleTransfer()
}

func (this *Login) Run(closeSig chan bool) {
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
}

func (this *Login) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 游客登录
func (this *Login) guestLogin(session gate.Session, msg *pb.LOGIN_HD_GUESTLOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("guestLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	guestId, distinctId, platform, os, nickName, inviteUid, lang := msg.GetGuestId(), msg.GetDistinctId(), msg.GetPlatform(), msg.GetOs(), msg.GetNickname(), msg.GetInviteUid(), msg.GetLang()
	needCreate := false
	if guestId != "" {
		//从数据库查找是否有这个用户
		data, err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_GUEST, guestId)
		if err != "" {
			needCreate = true
		} else {
			uid = data.Uid
			loginTrack(slg.LOGIN_TYPE_GUEST, uid, distinctId, platform, lang, os)
		}
	}
	if guestId == "" || needCreate {
		guestId = ut.ID()
		headicon := slg.FREE_HEAD_ICONS[ut.Random(0, len(slg.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(slg.LOGIN_TYPE_GUEST, "", "", guestId, distinctId, platform, os, nickName, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_GUESTLOGIN_S2C{AccountToken: getGameToken(uid), GuestId: guestId})
}

// 微信登录
func (this *Login) wxLogin(session gate.Session, msg *pb.LOGIN_HD_WXLOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("wxLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	code, distinctId, platform, os, nickName, inviteUid, lang := msg.GetCode(), msg.GetDistinctId(), msg.GetPlatform(), msg.GetOs(), msg.GetNickname(), msg.GetInviteUid(), msg.GetLang()
	// 先获取openid
	e, res := sdk.GetOpenIdByCode(code)
	if e != 0 {
		err = ecode.LOGIN_CHECK_FAIL.String()
		return
	} else if data, _err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_WX, res.Openid); _err == "" { //从数据库查找是否有这个用户
		uid = data.Uid
		db.UpdateOne(uid, "session_key", res.SessionKey)
		loginTrack(slg.LOGIN_TYPE_WX, uid, distinctId, platform, lang, os)
	} else {
		headicon := slg.FREE_HEAD_ICONS[ut.Random(0, len(slg.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(slg.LOGIN_TYPE_WX, res.Openid, res.SessionKey, "", distinctId, platform, os, nickName, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_WXLOGIN_S2C{AccountToken: getGameToken(uid)})
}

// facebook登录
func (this *Login) faceBookLogin(session gate.Session, msg *pb.LOGIN_HD_FACEBOOKLOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("faceBookLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	token, jwtToken, userId, distinctId, platform, os, inviteUid, lang := msg.GetToken(), msg.GetJwtToken(), msg.GetUserId(), msg.GetDistinctId(), msg.GetPlatform(), msg.GetOs(), msg.GetInviteUid(), msg.GetLang()
	// 验证并获取用户数据
	res, e := sdk.FacebookLoginVerify(token, jwtToken, userId)
	if e != nil {
		err = ecode.LOGIN_CHECK_FAIL.String()
		return
	} else if data, _err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_FACEBOOK, res.Id); _err == "" { //从数据库查找是否有这个用户
		uid = data.Uid
		db.UpdateOne(uid, "session_key", token)
		loginTrack(slg.LOGIN_TYPE_FACEBOOK, uid, distinctId, platform, lang, os)
	} else {
		headicon := slg.FREE_HEAD_ICONS[ut.Random(0, len(slg.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(slg.LOGIN_TYPE_FACEBOOK, res.Id, token, "", distinctId, platform, os, res.Name, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_FACEBOOKLOGIN_S2C{AccountToken: getGameToken(uid)})
}

// apple登录
func (this *Login) appleLogin(session gate.Session, msg *pb.LOGIN_HD_APPLELOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("appleLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	code, token, openId, distinctId, platform, os, nickName, inviteUid, lang := msg.GetCode(), msg.GetToken(), msg.GetUserId(), msg.GetDistinctId(), msg.GetPlatform(), msg.GetOs(), msg.GetNickname(), msg.GetInviteUid(), msg.GetLang()
	var accesToken string
	var e error
	if platform == slg.LOGIN_PLATFORM_GOOGLE || platform == slg.LOGIN_PLATFORM_TAPTAP {
		// 安卓使用apple账号登录
		accesToken, e = sdk.AppleLoginCheckByAndroid(token, openId)
	} else {
		// apple登录
		accesToken, e = sdk.AppleLoginCheck(code, token, openId)
	}
	if e != nil {
		err = ecode.LOGIN_CHECK_FAIL.String()
		return
	} else if data, _err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_APPLE, openId); _err == "" { //从数据库查找是否有这个用户
		uid = data.Uid
		db.UpdateOne(uid, "session_key", token)
		loginTrack(slg.LOGIN_TYPE_APPLE, uid, distinctId, platform, lang, os)
	} else {
		headicon := slg.FREE_HEAD_ICONS[ut.Random(0, len(slg.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(slg.LOGIN_TYPE_APPLE, openId, accesToken, "", distinctId, platform, os, nickName, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_APPLELOGIN_S2C{AccountToken: getGameToken(uid)})
}

// google登录
func (this *Login) googleLogin(session gate.Session, msg *pb.LOGIN_HD_GOOGLELOGIN_C2S) (bytes []byte, err string) {
	uid := session.GetUserID()
	if uid != "" {
		log.Warning("googleLogin repeat sessionId: %v, ip: %v", session.GetSessionID(), session.GetIP())
	}
	token, distinctId, platform, os, nickName, inviteUid, lang := msg.GetIdToken(), msg.GetDistinctId(), msg.GetPlatform(), msg.GetOs(), msg.GetNickname(), msg.GetInviteUid(), msg.GetLang()
	// 先获取openid
	res, e := sdk.GoogleLoginCheck(token)
	if e != nil {
		err = ecode.LOGIN_CHECK_FAIL.String()
		return
	} else if data, _err := db.FindByLoginTypeOpenid(slg.LOGIN_ID_TYPE_GOOGLE, res.UserId); _err == "" { //从数据库查找是否有这个用户
		uid = data.Uid
		db.UpdateOne(uid, "session_key", token)
		loginTrack(slg.LOGIN_TYPE_GOOGLE, uid, distinctId, platform, lang, os)
	} else {
		headicon := slg.FREE_HEAD_ICONS[ut.Random(0, len(slg.FREE_HEAD_ICONS)-1)]
		err, uid = createUserAccount(slg.LOGIN_TYPE_GOOGLE, res.UserId, token, "", distinctId, platform, os, nickName, headicon, inviteUid, lang, session.GetIP())
		if err != "" {
			err = ecode.DB_ERROR.String()
			return
		}
	}
	return pb.ProtoMarshal(&pb.LOGIN_HD_GOOGLELOGIN_S2C{AccountToken: getGameToken(uid), Email: res.Email})
}
