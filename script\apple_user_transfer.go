package main

// import (
// 	"context"
// 	slg "slgsrv/server/common"
// 	"slgsrv/server/common/sdk"
// 	"slgsrv/server/lobby"
// 	ut "slgsrv/utils"
// 	"sync"
// 	"time"

// 	"github.com/huyangv/vmqant/log"
// 	"go.mongodb.org/mongo-driver/bson"
// 	"go.mongodb.org/mongo-driver/mongo"
// 	"go.mongodb.org/mongo-driver/mongo/options"
// )

// const (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slgsrv"                    //大厅服数据库名
// )

// const (
// 	maxConcurrentGoroutines = 10            // 最大并发数量
// 	pageSize                = 100           // 每页处理的玩家数量
// 	lastTransTime           = 1727164752000 // 上一次生成transfer_sub时间戳
// )

// var (
// 	curPage = 0
// )

// func processPlayer(uid, openId string, wg *sync.WaitGroup, lobbyDb *mongo.Database) string {
// 	defer wg.Done()

// 	log.Info("uid: %v, start get transferSub", uid)
// 	transferSub, e := sdk.GetTransferSub(openId)
// 	if transferSub == "" {
// 		log.Info("processPlayer GetTransferSub nil, uid: %v", uid)
// 	}
// 	info := &sdk.TransferInfo{
// 		Uid:         uid,
// 		OpenId:      openId,
// 		TransferSub: transferSub,
// 		Error:       e,
// 	}
// 	// 写入数据库
// 	_, err := lobbyDb.Collection(slg.DB_COLLECTION_NAME_APPLE_TRANSFER).UpdateOne(context.TODO(),
// 		bson.M{"uid": uid}, bson.M{"$set": info}, options.Update().SetUpsert(true))
// 	if err != nil {
// 		log.Error("insert transfer db err: %v, uid: %v", err, uid)
// 		return err.Error()
// 	}

// 	// 输出结果
// 	log.Info("transfer ok uid: %v, transferID: %v", uid, transferSub)
// 	return ""
// }

// func main() {
// 	// 初始化
// 	sdk.InitAppleTransfer()
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)
// 	playerCollection := lobbyDb.Collection(slg.DB_COLLECTION_NAME_USER)
// 	filter := bson.M{
// 		"apple_openid": bson.M{
// 			"$ne": "",
// 		},
// 		"$and": bson.A{
// 			bson.M{"create_time": bson.M{"$gte": lastTransTime}},
// 			bson.M{"create_time": bson.M{"$lt": ut.Now()}},
// 		},
// 	}
// 	var stopProcessing bool         // 用来表示是否应该停止处理
// 	errChan := make(chan string, 1) // 错误通道
// 	for !stopProcessing {
// 		findOptions := options.Find()
// 		findOptions.SetSort(bson.M{"create_time": 1})
// 		findOptions.SetSkip(int64(curPage * pageSize))
// 		findOptions.SetLimit(int64(pageSize))
// 		cur, err := playerCollection.Find(context.TODO(), filter, findOptions)
// 		if err != nil {
// 			log.Error("find user err: %v", err)
// 			return
// 		}

// 		var wg sync.WaitGroup
// 		semaphore := make(chan struct{}, maxConcurrentGoroutines) // 控制并发数量
// 		hasMore := false

// 		for cur.Next(context.TODO()) {
// 			var player lobby.TableData
// 			err := cur.Decode(&player)
// 			if err != nil {
// 				log.Error("decode user err: %v", err)
// 				continue
// 			}

// 			wg.Add(1)
// 			semaphore <- struct{}{} // 获取信号量

// 			go func(uid, openId string) {
// 				defer func() { <-semaphore }() // 释放信号量
// 				err := processPlayer(uid, openId, &wg, lobbyDb)
// 				if err != "" {
// 					errChan <- err // 将错误发送到通道
// 				}
// 			}(player.Uid, player.AppleOpenid)

// 			hasMore = true
// 		}

// 		cur.Close(context.TODO())

// 		wg.Wait() // 等待所有 goroutine 完成

// 		// 监听错误通道，若有错误则停止后续处理
// 		select {
// 		case err := <-errChan:
// 			log.Info("Stopping due to error: %v, curPage: %v", err, curPage)
// 			stopProcessing = true // 标记停止处理
// 		default:
// 			// 没有错误，继续处理
// 		}

// 		// 如果当前页没有更多玩家，结束循环
// 		if !hasMore {
// 			break
// 		}
// 		curPage++ // 处理下一页
// 	}
// }
