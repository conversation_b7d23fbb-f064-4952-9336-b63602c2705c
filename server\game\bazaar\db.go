package bazaar

import (
	"context"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 获取数据总数
func (this *Mongodb) Find() (arr []*TRes, err string) {
	cur, e := this.getCollection().Find(context.TODO(), bson.D{})
	if e != nil {
		return []*TRes{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []*TRes{}, e.Error()
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*TRes{}
	for cur.Next(context.TODO()) {
		var elem TRes
		if e = cur.Decode(&elem); e == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// 插入多个
func (this *Mongodb) InsertMany(datas []interface{}) (err string) {
	if _, e := this.getCollection().InsertMany(context.TODO(), datas); e != nil {
		log.Error("InsertMany error.", err)
		err = e.Error()
	}
	return
}

// 删除多个
func (this *Mongodb) DeleteMany(uids []string) (err string) {
	if _, e := this.getCollection().DeleteMany(context.TODO(), bson.M{"uid": bson.M{"$in": uids}}); e != nil {
		err = e.Error()
	}
	return
}

// 获取所有交易记录
func (this *Mongodb) FindRecords() (arr []*TradRecordList, err string) {
	cur, e := this.getCollection().Find(context.TODO(), bson.D{})
	if e != nil {
		return []*TradRecordList{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []*TradRecordList{}, e.Error()
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*TradRecordList{}
	for cur.Next(context.TODO()) {
		var elem TradRecordList
		if e = cur.Decode(&elem); e == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// 更新交易记录
func (this *Mongodb) UpdateTresRecord(record *TradRecordList) (err error) {
	opt := &options.UpdateOptions{}
	opt.SetUpsert(true)
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"buy_type": record.BuyType, "sell_type": record.SellType}, bson.M{"$set": bson.M{
		"cur_price": record.CurPrice,
		"list":      record.List,
		"acc_time":  record.AccTime,
	}}, opt)
	if err != nil {
		log.Error("UpdateTresRecord db err: %v", err)
	}
	return
}
