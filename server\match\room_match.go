package match

import (
	"context"
	"encoding/csv"
	"math"
	"math/rand"
	"os"
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"sort"
	"strconv"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 匹配池状态
const (
	MATCH_POOL_STATE_MATCHING    = iota //匹配中
	MATCH_POOL_STATE_CREATE_ROOM        //创建房间中
	MATCH_POOL_STATE_FINISH      = 100
)

// 匹配中的队伍
type MatchTeam struct {
	deadlock.RWMutex
	Uid           string
	UserList      []string //玩家列表
	RankScoreAvg  float64  //平均胜点
	GameTotalAvg  float64  //平均场数
	CreateTimeAvg float64  //平均注册时间戳
	RoomType      int32    //区服类型
}

// 报名信息
type ApplyInfo struct {
	teamMap    *ut.MapLock[string, *MatchTeam] //已报名的队伍map
	MatchTime  int64                           `bson:"match_time"` //下次匹配时间
	RoomType   uint8                           `bson:"room_type"`  //区服类型
	Close      bool                            `bson:"close"`      //是否关闭报名
	isMatching bool                            //是否匹配中
}

// 匹配池信息
type MatchPool struct {
	Uid       string  `bson:"uid"`
	SidList   []int32 `bson:"sid_list"` //开启的区服id列表
	TeamList  []*MatchTeam
	OpenTime  int64 `bson:"open_time"`  //开启时间
	SidStart  int32 `bson:"sid_start"`  //开服的起始sid
	roomCount int32 `bson:"room_count"` //开服数量
	RoomType  uint8 `bson:"room_type"`  //区服类型
	SubType   uint8 `bson:"sub_type"`   //子类型
}

var (
	applyInfoMap      = map[uint8]*ApplyInfo{}              // 匹配信息map
	matchPoolMap      = ut.NewMapLock[string, *MatchPool]() // 匹配池map
	initFinish        = false                               // 匹配信息初始化是否完成
	matchPause        = false                               // 是否暂停匹配
	matchIntervalTime = slg.INIT_ROOM_MATCH_TIME            // 匹配间隔时间
)

// 初始化所有区服的匹配信息
func (this *Match) InitMatchInfo() {
	now := time.Now().UnixMilli()
	// 从数据库获取开服间隔
	itervalTime, err := gmMapDb.GetMatchIntervalTime()
	if err == nil && itervalTime > 0 {
		matchIntervalTime = int64(itervalTime)
	}
	// 从redis缓存获取匹配池信息 用于中断恢复
	rdsMatchPoolMap, _ := rds.RdsHGetAll(rds.RDS_MATCH_POOL_MAP)
	// 数据库获取报名信息
	for _, roomType := range slg.MATCH_ROOM_TYPE_LIST {
		var applyInfo *ApplyInfo
		// 获取报名信息
		data, err := applyDb.FindApplyInfo(roomType)
		if err == nil {
			applyInfo = &data
		} else {
			// 数据库没有则初始化
			applyInfo = &ApplyInfo{
				RoomType:  roomType,
				MatchTime: ut.NowZeroTime() + int64(slg.SERVER_APPLY_OPEN_TIME*ut.TIME_HOUR) + slg.INIT_ROOM_MATCH_TIME, // 初始化匹配时间为3天后10点
			}
			applyDb.UpdateApplyInfo(applyInfo)
		}
		// 获取报名中的队伍
		applyInfo.teamMap = ut.NewMapLock[string, *MatchTeam]()
		teamList, err := teamDb.FindAllMatchTeam(roomType)
		if err == nil {
			for _, team := range teamList {
				applyInfo.teamMap.Set(team.Uid, team)
			}
		}
		applyInfoMap[roomType] = applyInfo
		// 匹配池中断恢复
		if rdsMatchPoolMap != nil {
			for matchPoolUid, matchPoolRoomType := range rdsMatchPoolMap {
				if uint8(ut.Int32(matchPoolRoomType)) != roomType {
					continue
				}
				// 获取该匹配池中的队伍
				rdsMatchTeamMap, err := rds.RdsHGetAll(rds.RDS_MATCH_TEAM_MAP_KEY + matchPoolUid)
				if err != nil {
					log.Error("InitMatchInfo get team map uid: %v, err: %v")
					continue
				}
				subType := getSubType(roomType)
				// 创建匹配池
				matchPool := &MatchPool{
					Uid:      matchPoolUid,
					RoomType: roomType,
					SubType:  subType,
					OpenTime: now + slg.SERVER_APPLY_TO_OPEN_TIME,
					TeamList: []*MatchTeam{},
					SidList:  []int32{},
				}
				var userNum int32
				for teamUid := range rdsMatchTeamMap {
					if teamInfo := applyInfo.teamMap.Get(teamUid); teamInfo != nil {
						matchPool.TeamList = append(matchPool.TeamList, teamInfo)
						applyInfo.teamMap.Del(teamUid)
						userNum += int32(len(teamInfo.UserList))
					}
				}
				matchPoolMap.Set(matchPool.Uid, matchPool)
				// 开始匹配
				this.DoMatch(matchPool, userNum)
			}
		}
	}
	// 检测并恢复中断的开服
	checkRecoverOpenRoom()
	initFinish = true
}

// 队伍报名
func TeamApply(roomType uint8, teamUid string, userList []string, rankScoreAvg, gameTotalAvg, creatTimeAvg float64) (err string) {
	log.Info("TeamApply roomType: %v, teamUid: %v", roomType, teamUid)
	if !initFinish {
		return ecode.ROOM_CLOSE.String()
	}
	// 先取消所有区服的报名
	TeamCancelApply(teamUid)
	applyInfo, ok := applyInfoMap[roomType]
	if !ok {
		// 区服类型错误
		return ecode.ROOM_TYPE_ERR.String()
	}
	teamInfo := applyInfo.teamMap.Get(teamUid)
	if teamInfo != nil {
		// 重复报名
		return ecode.SERVER_APPLY_REPEAT.String()
	} else {
		teamInfo = &MatchTeam{
			Uid:           teamUid,
			UserList:      userList,
			RankScoreAvg:  rankScoreAvg + float64(len(userList)*slg.MATCH_TEAM_NUM_AVG_SCORE),
			GameTotalAvg:  gameTotalAvg,
			CreateTimeAvg: creatTimeAvg,
		}
		applyInfo.teamMap.Set(teamUid, teamInfo)
	}
	log.Info("TeamApply finish roomType: %v, teamUid: %v", roomType, teamUid)
	return
}

// 取消报名
func TeamCancelApply(teamUid string) (err string) {
	log.Info("TeamCancelApply teamUid: %v", teamUid)
	if !initFinish {
		return ecode.ROOM_CLOSE.String()
	}
	// 遍历所有模式 取消该队伍报名
	for _, roomType := range slg.MATCH_ROOM_TYPE_LIST {
		applyInfo, ok := applyInfoMap[roomType]
		if !ok {
			continue
		}
		if applyInfo.isMatching {
			// 匹配中不能取消
			return ecode.IN_MATCH.String()
		}
		applyInfo.teamMap.Del(teamUid)
	}
	log.Info("TeamCancelApply finish teamUid: %v", teamUid)
	return
}

// 个人取消报名
func UserCancelApply(roomType uint8, teamUid, uid string) (err string) {
	log.Info("UserCancelApply teamUid: %v, uid: %v", teamUid, uid)
	if !initFinish {
		return ecode.ROOM_CLOSE.String()
	}
	applyInfo, ok := applyInfoMap[roomType]
	if !ok {
		// 区服类型错误
		return ecode.ROOM_TYPE_ERR.String()
	}
	if applyInfo.isMatching {
		// 匹配中不能取消
		return ecode.IN_MATCH.String()
	}
	team := applyInfo.teamMap.Get(teamUid)
	if team == nil {
		// 队伍不存在
		return ecode.TEAM_NOT_EXIST.String()
	}
	team.Lock()
	team.UserList = array.Delete(team.UserList, func(m string) bool { return m == uid })
	team.Unlock()
	return
}

// 匹配tick
func (this *Match) MatchTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		for isRunning {
			<-tiker.C
			if matchPause {
				continue
			}
			now := time.Now().UnixMilli()
			for _, applyInfo := range applyInfoMap {
				if now >= applyInfo.MatchTime {
					applyInfo.isMatching = true
					// 检测报名人数
					sum := 0
					applyInfo.teamMap.ForEach(func(v *MatchTeam, k string) bool {
						sum += len(v.UserList)
						return true
					})
					if sum < slg.MATCH_NEW_ROOM_MIN_PLAYER_NUM {
						// 人数不够创建新区 延迟创建匹配池 继续等待报名
						applyInfo.MatchTime += slg.ROOM_MATCH_OPEN_FAIL_DELAY_TIME
						applyDb.UpdateApplyInfo(applyInfo)
						applyInfo.teamMap.ForEach(func(v *MatchTeam, k string) bool {
							// 通知队伍延迟匹配
							this.InvokeTeamFuncNR(k, slg.RPC_TEAM_DELAY_MATCH, applyInfo.MatchTime)
							return true
						})
						applyInfo.isMatching = false
						continue
					}

					// 分层匹配 每层单独一个匹配池
					teamArr := []*MatchTeam{}
					applyInfo.teamMap.ForEach(func(v *MatchTeam, k string) bool {
						teamArr = append(teamArr, v)
						return true
					})
					// 按平均胜点从大到小排序
					sort.Slice(teamArr, func(i, j int) bool {
						if teamArr[i].RankScoreAvg == teamArr[j].RankScoreAvg {
							if teamArr[i].GameTotalAvg == teamArr[j].GameTotalAvg {
								// 对局数相同按注册时间从小到大
								return teamArr[i].CreateTimeAvg < teamArr[j].CreateTimeAvg
							}
							// 胜点相同按对局数从大到小
							return teamArr[i].GameTotalAvg > teamArr[j].GameTotalAvg
						}
						return teamArr[i].RankScoreAvg > teamArr[j].RankScoreAvg
					})
					// // 分层处理
					// teamLayers := distributeTeamsBalanced(teamArr, slg.MATCH_LAYER_USER_NUM)
					// 创建匹配池 目前不分层 按一层处理
					this.CreateMatchPools(applyInfo.RoomType, [][]*MatchTeam{teamArr})
					applyInfo.teamMap.Clean()
					// TODO 根据开服间隔优化下次开服时间
					applyInfo.MatchTime = now + slg.INIT_ROOM_MATCH_TIME
					applyDb.UpdateApplyInfo(applyInfo)
					applyInfo.isMatching = false
				}
			}
		}
	}()
}

// 创建匹配池
func (this *Match) CreateMatchPools(roomType uint8, layers [][]*MatchTeam) {
	// 获取开服id
	subType := getSubType(roomType)
	sidStart := getGenSid(roomType, subType)
	now := time.Now().UnixMilli()
	// 遍历每层 计算每个区服的人数和每层新开的区服数量
	for _, layer := range layers {
		userNum := getLayerUserNum(layer)
		// // 每个区的人数按报名人数浮动
		// stepMax := (slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM - slg.MATCH_NEW_ROOM_PLAYER_NUM) / slg.MATCH_NEW_ROOM_PLAYER_NUM_STEP
		// roomUserNum := slg.MATCH_NEW_ROOM_PLAYER_NUM // 每服人数上限
		// for i := 0; i < stepMax; i++ {
		// 	if userNum <= roomUserNum {
		// 		// 报名人数不足 则不继续增长区服人数
		// 		break
		// 	}
		// 	if roomUserNum+stepMax > slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM {
		// 		// 达到上限 停止增长
		// 		break
		// 	}
		// 	roomUserNum += slg.MATCH_NEW_ROOM_PLAYER_NUM_STEP
		// }
		// roomUserNum = ut.Min(roomUserNum, slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM)
		roomUserNum := slg.MATCH_BALLENCE_PLAYER_NUM_MAX
		roomCount := userNum / roomUserNum // 新开区服数量
		if userNum%roomUserNum > 0 {
			roomCount++
		}

		// 创建匹配池
		matchPool := &MatchPool{
			Uid:       ut.ID(),
			RoomType:  roomType,
			SubType:   subType,
			OpenTime:  now + slg.SERVER_APPLY_TO_OPEN_TIME,
			TeamList:  []*MatchTeam{},
			SidList:   []int32{},
			SidStart:  sidStart,
			roomCount: roomCount,
		}
		sidStart += roomCount
		teamUidMap := map[string]interface{}{}
		for _, team := range layer {
			// 添加队伍到匹配池
			matchPool.TeamList = append(matchPool.TeamList, team)
			teamUidMap[team.Uid] = true
			// 通知队伍
			this.InvokeTeamFuncNR(team.Uid, slg.RPC_TEAM_START_MATCH, matchPool.OpenTime)
		}
		// 将匹配池的队伍map缓存到redis 避免匹配中宕机或重启
		rdsPipline := rds.RdsPipeline()
		rdsKey := rds.RDS_MATCH_TEAM_MAP_KEY + matchPool.Uid
		rdsPipline.HSet(context.TODO(), rds.RDS_MATCH_POOL_MAP, matchPool.Uid, matchPool.RoomType)
		rdsPipline.HSet(context.TODO(), rdsKey, teamUidMap)
		rdsPipline.Exec(context.TODO())
		matchPoolMap.Set(matchPool.Uid, matchPool)
		this.DoMatch(matchPool, userNum)
	}
}

// 队伍分层处理
func distributeTeamsBalanced(teams []*MatchTeam, minPlayersPerLayer int32) [][]*MatchTeam {
	result := [][]*MatchTeam{}
	currentLayer := []*MatchTeam{}
	var userNum, currentPlayerCount int32

	// 首先将队伍分层
	for _, team := range teams {
		teamSize := int32(len(team.UserList))
		userNum += teamSize
		currentPlayerCount += teamSize
		currentLayer = append(currentLayer, team)

		// 当当前层人数大于或等于 minPlayersPerLayer 时，可以分层
		if currentPlayerCount >= minPlayersPerLayer {
			result = append(result, currentLayer)
			currentLayer = []*MatchTeam{}
			currentPlayerCount = 0
		}
	}

	// 处理最后一层
	curLayerNum := int32(len(currentLayer))
	if curLayerNum > minPlayersPerLayer {
		// 人数足够单独作为最后一层
		result = append(result, currentLayer)
	} else if curLayerNum > 0 {
		// 剩余的人数不够
		result[len(result)-1] = append(result[len(result)-1], currentLayer...)
		avgUserNum := userNum / int32(len(result))
		// 尝试往前移动队伍
		for i := len(result) - 1; i > 0; i-- {
			layerUserNum := getLayerUserNum(result[i])
			for layerUserNum > avgUserNum && layerUserNum > minPlayersPerLayer {
				firstTeam := result[i][0]
				leftNum := layerUserNum - int32(len(firstTeam.UserList))
				if leftNum < avgUserNum || leftNum <= minPlayersPerLayer {
					break
				}
				// 移动队伍到前一层
				result[i-1] = append(result[i-1], firstTeam)
				result[i] = result[i][1:] // 从当前层移除该队伍
				layerUserNum = leftNum
			}
		}

	}

	// 输出日志
	teamCount := 0
	for _, v := range result {
		teamCount += len(v)
		userNum := 0
		for _, team := range v {
			userNum += len(team.UserList)
		}
		log.Info("match layerUserNum: %v", userNum)
	}
	log.Info("match layerCount: %v, teamCount: %v", len(result), teamCount)

	return result
}

// 计算给定层的总人数
func getLayerUserNum(layer []*MatchTeam) int32 {
	var total int32
	for _, team := range layer {
		total += int32(len(team.UserList))
	}
	return total
}

// 队伍分层算法 动态规划 保证顺序 满足每层人数minPlayersPerLayer尽可能分多的层
// func dpDistributeTeams(teams []*MatchTeam, minPlayersPerLayer int) [][]*MatchTeam {
// 	log.Info("dpDistributeTeams start teamCount: %v", len(teams))
// 	n := len(teams)
// 	dp := make([]int, n+1)
// 	layer := make([]int, n+1)

// 	// 初始化 dp 和 layer 数组
// 	for i := 0; i <= n; i++ {
// 		dp[i] = math.MaxInt32 // 初始为无穷大，表示越小越好
// 		layer[i] = -1         // 初始化为无效状态
// 	}
// 	dp[0] = 0

// 	// 填充 dp 数组，寻找人数差最小的分层方案
// 	for i := 1; i <= n; i++ {
// 		playerCount := 0
// 		minLayerPlayers := math.MaxInt32
// 		maxLayerPlayers := 0

// 		for j := i; j >= 1; j-- {
// 			playerCount += len(teams[j-1].UserList)

// 			// 更新当前层的最小和最大人数
// 			minLayerPlayers = ut.Min(minLayerPlayers, playerCount)
// 			maxLayerPlayers = ut.Max(maxLayerPlayers, playerCount)

// 			if playerCount >= minPlayersPerLayer {
// 				// 计算人数差
// 				diff := maxLayerPlayers - minLayerPlayers
// 				if dp[j-1]+diff < dp[i] { // 更新 dp 数组
// 					dp[i] = dp[j-1] + diff
// 					layer[i] = j - 1 // 记录层的起始点
// 				}
// 			}
// 		}
// 	}

// 	// 回溯找到层次划分
// 	result := [][]*MatchTeam{}
// 	i := n
// 	for i > 0 {
// 		start := layer[i]
// 		if start == -1 {
// 			result = append([][]*MatchTeam{teams[:i]}, result...) // 确保第一个队伍被加入
// 			break
// 		}
// 		result = append([][]*MatchTeam{teams[start:i]}, result...) // 逆序加入每层
// 		i = start                                                  // 回溯到上一个层的起始位置
// 	}

// 	return result
// }

//
/*
开始匹配
1.根据人数确定开服的数量并划分队伍到区服
2.更新缓存
3.开启区服
*/
func (this *Match) DoMatch(matchPool *MatchPool, userNum int32) {
	go func() {
		log.Info("DoMatch start roomType: %v, uid: %v", matchPool.RoomType, matchPool.Uid)
		roomTeamMap := map[int32][]string{} //待创建的区服id对应的队伍uid列表map
		log.Info("DoMatch userNum: %v, roomCount: %v, sidStart: %v", userNum, matchPool.roomCount, matchPool.SidStart)
		roomUserNumMap := map[int32]int32{}
		MalocRoomUserNumMap := map[int32]int32{}
		if matchPool.roomCount == 0 {
			log.Warning("DoMatch sidList nil roomType: %v, teamList: %v", matchPool.RoomType, matchPool.TeamList)
			return
		}
		roomUserNumAvg := userNum / matchPool.roomCount // 每服平均人数
		// 初始化每服人数
		sidStart := matchPool.SidStart
		for i := int32(0); i < matchPool.roomCount; i++ {
			roomUserNumMap[sidStart+i] = 0
			MalocRoomUserNumMap[i] = roomUserNumAvg
		}

		// 区服阶梯人数分配
		splitCount := matchPool.roomCount / 2
		// 区服是按匹配分降序排列 将区服分为两部分 前半部分的人数按配置移到后半部分
		for i := int32(0); i < splitCount; i++ {
			leftNum := MalocRoomUserNumMap[i]
			rightNum := MalocRoomUserNumMap[matchPool.roomCount-1-i]
			moveNum := float64(leftNum-slg.MATCH_BALLENCE_PLAYER_NUM_MIN) * slg.MATCH_BALLENCE_PLAYER_NUM_INIT_PARAM
			if i > 0 {
				// 每往后一个服 转移的人数按指数衰减
				moveNum *= math.Pow(slg.MATCH_BALLENCE_PLAYER_NUM_STEP_PARAM, float64(i))
			}
			moveNumInt := int32(ut.Floor(moveNum))
			if rightNum+moveNumInt > slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM {
				// 转移后右半部的区服超过上限 修正转移人数
				moveNumInt = slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM - rightNum
				rightNum = slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM
			} else {
				rightNum += moveNumInt
			}
			MalocRoomUserNumMap[i] = leftNum - moveNumInt
			MalocRoomUserNumMap[matchPool.roomCount-1-i] = rightNum
		}
		log.Info("DoMatch MalocRoomUserNumMap: %v", MalocRoomUserNumMap)
		var curDiff int32 // 上一个服实际人数和分配的人数差
		// 随机队伍顺序 暂时不用随机 开启分层再随机
		// ut.ShuffleSlice(matchPool.TeamList)
		curSid := sidStart
		// 根据排序顺序将队伍放入区服中
		malocUserNum := MalocRoomUserNumMap[curSid-sidStart]
		for _, v := range matchPool.TeamList {
			curRoomUserNum := roomUserNumMap[curSid]
			curTeamUserNum := int32(len(v.UserList))
			roomUserNum := curRoomUserNum + curTeamUserNum
			if roomUserNum > malocUserNum {
				// 如果加上该队伍后该区服人数超过分配的人数
				if curSid-sidStart+1 < matchPool.roomCount && curDiff > 0 {
					// 当前服不是最后一个服并且上一个服已经超过分配的人数 则该队伍分配到下一个服
					curSid++
					malocUserNum = MalocRoomUserNumMap[curSid-sidStart]
					roomUserNum = curTeamUserNum
					curDiff = curRoomUserNum - malocUserNum
				}
			}
			roomTeamMap[curSid] = append(roomTeamMap[curSid], v.Uid)
			roomUserNumMap[curSid] = roomUserNum
			if roomUserNum >= malocUserNum && curSid-sidStart+1 < matchPool.roomCount {
				// 当前区服队伍超过分配的人数且不是最后一个区服 开始分配下个区服
				curSid++
				malocUserNum = MalocRoomUserNumMap[curSid-sidStart]
				curDiff = roomUserNum - malocUserNum
			}
		}
		log.Info("DoMatch roomUserNumMap: %v", roomUserNumMap)

		// 更新到redis缓存
		rdsPipline := rds.RdsPipeline()
		for sid, teamList := range roomTeamMap {
			for _, teamUid := range teamList {
				rdsPipline.HSet(context.TODO(), rds.RDS_CREATE_ROOM_TEAM_MAP_KEY+ut.String(sid), teamUid, true)
			}
		}
		rdsPipline.Del(context.TODO(), rds.RDS_MATCH_TEAM_MAP_KEY+matchPool.Uid)
		rdsPipline.HDel(context.TODO(), rds.RDS_MATCH_POOL_MAP, matchPool.Uid)
		rdsPipline.Exec(context.TODO())
		log.Info("DoMatch start create Room")
		// 创建区服
		for i := int32(0); i < matchPool.roomCount; i++ {
			sid := this.PreCreateRoom(matchPool.RoomType, matchPool.SubType)
			matchPool.SidList = append(matchPool.SidList, sid)
			rds.RdsHSet(rds.RDS_MATCH_WAIT_OPEN_MAP_KEY, ut.String(sid), matchPool.OpenTime)
		}
		matchPoolOpenRoomTick(matchPool)
	}()
}

// 开服tick
func matchPoolOpenRoomTick(matchPool *MatchPool) {
	log.Info("DoMatch start matchPoolOpenRoomTick")
	tiker := time.NewTicker(time.Second * 1)
	for isRunning && len(matchPool.SidList) > 0 {
		<-tiker.C
		if matchPool.OpenTime-time.Now().UnixMilli() <= ut.TIME_MINUTE*5 {
			// 到开服时间 (提前5分钟开服)
			for i := len(matchPool.SidList) - 1; i >= 0; i-- {
				sid := matchPool.SidList[i]
				room := GetRoomById(sid)
				if room == nil || room.state == slg.SERVER_STATUS_CREATING {
					// 区服数据未创建完成 继续等待
					log.Warning("DoMatch create room nil sid: %v", sid)
					continue
				}
				openRoomChan <- sid
				// 缓存中删除待开服信息
				rds.RdsHDel(rds.RDS_MATCH_WAIT_OPEN_MAP_KEY, ut.String(sid))
				matchPool.SidList = append(matchPool.SidList[:i], matchPool.SidList[i+1:]...)
				log.Info("DoMatch openRoomChan sid: %v", sid)
			}
		}
	}
	// 开服完成后移除匹配池
	matchPoolMap.Del(matchPool.Uid)
}

// 检测并恢复中断的开服
func checkRecoverOpenRoom() {
	// 从缓存中获取是否有匹配待开服的区服
	rdsWaitOpenRoomMap, err := rds.RdsHGetAll(rds.RDS_MATCH_WAIT_OPEN_MAP_KEY)
	if err == nil && rdsWaitOpenRoomMap != nil && len(rdsWaitOpenRoomMap) > 0 {
		openList := [][]int{}
		for sid, openTime := range rdsWaitOpenRoomMap {
			openInfo := []int{ut.Int(sid), ut.Int(openTime)}
			openList = append(openList, openInfo)
		}
		sort.Slice(openList, func(i, j int) bool {
			return openList[i][1] > openList[j][1]
		})
		recoverOpenRoomTick(openList)
	}
}

// 恢复开服tick
func recoverOpenRoomTick(openList [][]int) {
	go func() {
		log.Info("start recoverOpenRoomTick openList: %v", openList)
		tiker := time.NewTicker(time.Second * 1)
		for isRunning {
			<-tiker.C
			if len(openList) <= 0 {
				break
			}
			curOpenInfo := openList[0]
			sid := curOpenInfo[0]
			openTime := curOpenInfo[1]
			if time.Now().UnixMilli() >= int64(openTime) {
				// 到开服时间
				openRoomChan <- int32(sid)
				openList = append(openList[:0], openList[1:]...)
				// 缓存中删除待开服信息
				rds.RdsHDel(rds.RDS_MATCH_WAIT_OPEN_MAP_KEY, ut.String(sid))
			}
		}
	}()
}

// 设置开服间隔
func SetMatchIntervalTime(time int64) {
	matchIntervalTime = time
	gmMapDb.SetMatchIntervalTime(int(time))
}

// 获取子类型
func getSubType(roomType uint8) uint8 {
	var subType uint8
	if roomType == slg.NORMAL_SERVER_TYPE {
		// 普通区的子类型默认为自由区
		subType = slg.NORMAL_SERVER_SUB_TYPE_FREE
	}
	return subType
}

func (this *Match) TestMatch() {
	count := rand.Intn(50) + 50

	// 将结果输出到CSV文件
	file, err := os.OpenFile("output.csv", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		log.Error("无法打开文件")
		return
	}
	defer file.Close()
	writer := csv.NewWriter(file)
	defer writer.Flush()

	for i := 0; i < count; i++ {

		// 写入列名
		if fileStat, err := file.Stat(); err == nil && fileStat.Size() == 0 {
			// 写入BOM头以确保文件以UTF-8编码打开
			_, err = file.WriteString("\xEF\xBB\xBF")
			if err != nil {
				log.Error("无法写入BOM: %v", err)
				return
			}
			writer.Write([]string{"队伍数量", "玩家总数", "每服人数"})
		}

		// 随机队伍数量
		teamNum := rand.Intn(100) + 50
		roomUserMap, userAcc := this.TestMatchPool(teamNum)
		// 构建每行数据
		roomUserNums := ""
		roomUserNumArr := []map[string]int{}
		for sid, userNum := range roomUserMap {
			roomUserNumArr = append(roomUserNumArr, map[string]int{"sid": sid, "num": userNum})
		}
		// 按sid升序
		sort.Slice(roomUserNumArr, func(i, j int) bool {
			return roomUserNumArr[i]["sid"] < roomUserNumArr[j]["sid"]
		})
		for _, v := range roomUserNumArr {
			roomUserNums += strconv.Itoa(v["num"]) + " | "
		}
		// 去掉最后一个逗号
		if len(roomUserNums) > 0 {
			roomUserNums = roomUserNums[:len(roomUserNums)-1]
		}

		record := []string{strconv.Itoa(teamNum), strconv.Itoa(userAcc), roomUserNums}
		writer.Write(record)
	}
}

func (this *Match) TestMatchPool(teamNum int) (roomUserMap map[int]int, userAcc int) {
	if !slg.DEBUG {
		return
	}
	// // 创建匹配池
	// matchPool := &MatchPool{
	// 	Uid:      ut.ID(),
	// 	RoomType: 0,
	// 	OpenTime: time.Now().UnixMilli() + slg.SERVER_APPLY_TO_OPEN_TIME,
	// 	TeamMap:  ut.NewMapLock[string, *MatchTeam](),
	// }
	// userNumArr := []int{1, 4, 4, 3, 1, 3, 2, 4, 3, 2, 2, 1, 3, 1, 4, 1, 2, 3, 1, 2, 2, 1, 1, 1}
	// teamNum = len(userNumArr)
	teamArr := []*MatchTeam{}
	// 创建匹配池
	matchPool := &MatchPool{
		Uid:      ut.ID(),
		RoomType: 2,
		SubType:  0,
		OpenTime: time.Now().UnixMilli() + slg.SERVER_APPLY_TO_OPEN_TIME,
		TeamList: []*MatchTeam{},
		SidList:  []int32{},
		SidStart: 2000001,
	}
	for i := 0; i < teamNum; i++ {
		userNum := rand.Intn(slg.TEAM_USER_NUM_MAX) + 1
		userAcc += userNum
		rank := rand.Intn(500)
		team := &MatchTeam{
			Uid:          ut.ID(),
			RoomType:     0,
			UserList:     []string{},
			RankScoreAvg: float64(rank),
		}
		// userNum := userNumArr[i]
		for j := 0; j < userNum; j++ {
			team.UserList = append(team.UserList, ut.ID())
		}
		// matchPool.TeamMap.Set(team.Uid, team)
		teamArr = append(teamArr, team)
		// 添加队伍到匹配池
		matchPool.TeamList = append(matchPool.TeamList, team)
	}
	// layers := distributeTeamsBalanced(teamArr, 2000)
	roomUserNum := int(slg.MATCH_BALLENCE_PLAYER_NUM_MAX)
	roomCount := userAcc / roomUserNum // 新开区服数量
	if userAcc%roomUserNum > 0 {
		roomCount++
	}
	// matchPool.roomCount = roomCount
	// roomUserMap = this.DoMatch(matchPool, userAcc)

	// this.CreateMatchPools(2, [][]*MatchTeam{teamArr})
	return
}
