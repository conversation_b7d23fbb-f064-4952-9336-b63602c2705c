package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 任务条件
type TaskCondInfo struct {
	Type  int32 `json:"type"`
	ID    int32 `json:"id"`
	Count int32 `json:"count"` //目标值
}

// 一个任务信息
type TaskInfo struct {
	json            map[string]interface{}
	condInfo        *TaskCondInfo
	rewards         []*TypeObj
	TreasureRewards []*TreasureInfo `json:"treasure_rewards" bson:"treasure_rewards"`

	ID           int32 `json:"id" bson:"id"`
	Progress     int32 `json:"progress" bson:"progress"`
	ServerRunDay int32 `json:"serverRunDay" bson:"server_run_day"`
}

func NewTaskCondByString(str interface{}) *TaskCondInfo {
	arr := ut.StringToInts(ut.String(str), ",")
	return &TaskCondInfo{Type: int32(arr[0]), ID: int32(arr[1]), Count: int32(arr[2])}
}

func NewTaskInfo(json map[string]interface{}) *TaskInfo {
	return &TaskInfo{
		ID:       ut.Int32(json["id"]),
		json:     json,
		condInfo: NewTaskCondByString(json["cond"]),
		rewards:  StringToTypeObjs(json["reward"]),
	}
}

func NewTaskInfoByReCreate(json map[string]interface{}) *TaskInfo {
	task := &TaskInfo{
		ID:       ut.Int32(json["id"]),
		json:     json,
		condInfo: NewTaskCondByString(json["cond"]),
	}
	if str := ut.String(json["recreate_reward"]); str != "" {
		task.rewards = StringToTypeObjs(str)
	} else {
		task.rewards = StringToTypeObjs(json["reward"])
	}
	return task
}

func (this *TaskInfo) GetJson() map[string]interface{} { return this.json }
func (this *TaskInfo) GetCondInfo() *TaskCondInfo      { return this.condInfo }
func (this *TaskInfo) GetRewards() []*TypeObj          { return this.rewards }
func (this *TaskInfo) GetPrevID() int32                { return ut.Int32(this.json["prev_id"]) }
func (this *TaskInfo) GetType() int32                  { return ut.Int32(this.json["type"]) }     //获取任务类型
func (this *TaskInfo) GetTitleID() int32               { return ut.Int32(this.json["title_id"]) } //获取成就关联的称号id

func (this *TaskInfo) InitJson(jsonName string, runDay int32) (ok bool) {
	json := config.GetJsonData(jsonName, this.ID)
	if ok = json != nil; ok {
		this.json = json
		this.condInfo = NewTaskCondByString(json["cond"])
		this.rewards = StringToTypeObjs(json["reward"])
	}
	if jsonName == "todayTask" && this.ServerRunDay == 0 {
		this.ServerRunDay = runDay
	}
	return
}

func (this *TaskInfo) ToPb() *pb.TaskInfo {
	return &pb.TaskInfo{
		Id:              int32(this.ID),
		Progress:        int32(this.Progress),
		TreasureRewards: this.ToTreasureRewardsPb(),
		ServerRunDay:    int32(this.ServerRunDay),
	}
}

func (this *TaskInfo) ToTreasureRewardsPb() []*pb.TreasureInfo {
	return array.Map(this.TreasureRewards, func(m *TreasureInfo, _ int) *pb.TreasureInfo { return m.ToPb() })
}

// 获取宝箱奖励
func (this *TaskInfo) GetTreasureRewards(index int) []*TypeObj {
	if this.TreasureRewards == nil {
		this.TreasureRewards = []*TreasureInfo{}
		rewards := StringToTypeObjs(this.json["reward"])
		rewards = append(rewards, StringToTypeObjs(this.json["reward_"+ut.String(ut.Clamp(int(this.ServerRunDay), 1, 4))])...)
		tos := array.Filter(rewards, func(m *TypeObj, _ int) bool { return m.Type == ctype.TREASURE })
		for _, m := range tos {
			for i := int32(0); i < m.Count; i++ {
				this.TreasureRewards = append(this.TreasureRewards, NewTreasure(m.Id))
			}
		}
	}
	if index < 0 || index >= len(this.TreasureRewards) {
		return []*TypeObj{}
	}
	treasure := this.TreasureRewards[index]
	if len(treasure.Rewards) == 0 {
		treasure.RandomRewards(1.0)
		return []*TypeObj{}
	}
	this.TreasureRewards = array.RemoveByIndex(this.TreasureRewards, index)
	return treasure.Rewards
}

// 获取选择奖励
func (this *TaskInfo) GetSelectRewards(index int) []*TypeObj {
	rewards := StringToTypeObjs(this.json["reward_"+ut.String(ut.Clamp(int(this.ServerRunDay), 1, 4))])
	if index < 0 || index >= len(rewards) {
		return []*TypeObj{}
	}
	return []*TypeObj{rewards[index]}
}

// 任务进度更新
func (this *TaskInfo) AddProgress(count int32) bool {
	if _, ok := tctype.TASK_PROGRESS_DATA_TYPE_MAP[this.GetCondInfo().Type]; ok {
		// 数值作为进度 新的数值更大则覆盖
		if count > this.Progress {
			this.Progress = count
			return true
		}
	} else if this.Progress < this.condInfo.Count {
		// 累计进度
		this.Progress += count
		return true
	}
	return false
}
