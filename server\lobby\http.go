package lobby

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/record"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/pkg/errors"
)

const (
	secret = "twomiles@2020|Slg"
	limit  = 86400
)

func (this *Lobby) InitHttp() {
	// web 登录 移动至http模块
	// this.GetServer().RegisterGO("/http/login", this.httpLogin)
	// admin个人信息 移动至http模块
	// this.GetServer().RegisterGO("/http/info", this.httpInfo)

	// 获取用户列表(非角色列表)
	this.GetServer().RegisterGO("/http/getUserList", this.httpGetUserList)
	// 删除用户
	this.GetServer().RegisterGO("/http/deleteUser", this.httpDeleteUser)
	// 动态执行
	this.GetServer().RegisterGO("/http/lobbyExecute", this.httpLobbyExecute)
	// 修改游客id
	this.GetServer().RegisterGO("/http/changeGuestId", this.changeGuestId)
	// 修改登录类型
	this.GetServer().RegisterGO("/http/changeLoginType", this.changeLoginType)
	// 获取用户金币记录列表
	this.GetServer().RegisterGO("/http/getUserGoldRecordList", this.getUserGoldRecordList)
	// 获取用户元宝记录列表
	this.GetServer().RegisterGO("/http/getUserIngotRecordList", this.getUserIngotRecordList)
	// 获取用户道具记录列表
	this.GetServer().RegisterGO("/http/getUserItemRecordList", this.getUserItemRecordList)
	// 获取战斗记录
	this.GetServer().RegisterGO("/http/getBattleRecord", this.getBattleRecord)
	// 设置玩家最大地块数
	this.GetServer().RegisterGO("/http/setUserMaxLandCount", this.setUserMaxLandCount)
	// 禁言
	this.GetServer().RegisterGO("/http/bannedChat", this.httpBannedChat)
	// 封禁
	this.GetServer().RegisterGO("/http/bannedAccount", this.httpBannedAccount)
	// 获取禁言/封禁记录
	this.GetServer().RegisterGO("/http/getBanRecordList", this.httpGetBanRecordList)
	// 创建兑换码
	this.GetServer().RegisterGO("/http/genCdk", this.httpGenCdk)
	// 兑换码列表
	this.GetServer().RegisterGO("/http/cdkList", this.httpGetCdkList)
	// 删除兑换码
	this.GetServer().RegisterGO("/http/delGift", this.httpDelGift)
	// 查询兑换码领取人数
	this.GetServer().RegisterGO("/http/getCdkClaimNum", this.httpGetCdkClaimNum)
	// 查询指定玩家兑换数据
	this.GetServer().RegisterGO("/http/getUserCdkClaim", this.httpGetUserCdkClaim)
	// 修改玩家排位分
	this.GetServer().RegisterGO("/http/modifyUserRankScore", this.modifyUserRankScore)
	// 从内存删除用户并保存数据库
	this.GetServer().RegisterGO("/http/userSaveDb", this.userSaveDb)
	// 强制下线
	this.GetServer().RegisterGO("/http/userOffline", this.httpUserOffline)
	// 获取玩家队伍信息
	this.GetServer().RegisterGO("/http/getUserTeamInfo", this.httpGetUserTeamInfo)
	// 重置队伍状态
	this.GetServer().RegisterGO("/http/resetTeamState", this.httpResetTeamState)
	// 删除队伍
	this.GetServer().RegisterGO("/http/delTeam", this.httpDelTeam)
	// 删除队员
	this.GetServer().RegisterGO("/http/delTeamUser", this.httpDelTeamUser)
	// 重置玩家队伍uid
	this.GetServer().RegisterGO("/http/resetUserTeamUid", this.httpResetUserTeamUid)
	// 查询玩家累计充值
	this.GetServer().RegisterGO("/http/getUserRechargeSum", this.httpGetUserRechargeSum)
	// 设置玩家连续登录天数
	this.GetServer().RegisterGO("/http/setUserCloginDays", this.setUserCloginDays)
	// 设置玩家登录天数
	this.GetServer().RegisterGO("/http/setUserloginDays", this.setUserloginDays)
	// 设置玩家所在区服
	this.GetServer().RegisterGO("/http/setUserSid", this.httpSetUserSid)
	// 设置玩家游玩区服
	this.GetServer().RegisterGO("/http/setUserPlaySid", this.httpSetUserPlaySid)
	// 设置新手区次数
	this.GetServer().RegisterGO("/http/setUserNewbieCount", this.httpSetUserNewbieCount)
	// 获取用户画像列表
	this.GetServer().RegisterGO("/http/getUserPortrayalList", this.httpGetUserPortrayalList)
	// 设置画像属性
	this.GetServer().RegisterGO("/http/setUserPortrayalAttr", this.httpSetUserPortrayalAttr)
	// 查询创意工坊物品
	this.GetServer().RegisterGO("/http/getWorkshopItems", this.httpGetWorkshopItems)
	// 创意工坊审核
	this.GetServer().RegisterGO("/http/workshopAudit", this.httpWorkshopAudit)
	// 创建队伍并报名
	this.GetServer().RegisterGO("/http/createTeamApply", this.httpCreateTeamApply)
	// 获取创建队伍信息
	this.GetServer().RegisterGO("/http/getCreateTeamInfo", this.httpGetCreateTeamInfo)
}

func (this *Lobby) httpGetUserList(lastId, size, sortType, sTime, eTime, uid, nickName, onlineType string) (ret map[string]interface{}, err error) {
	_size, _ := strconv.Atoi(size)
	_sortType, _ := strconv.Atoi(sortType)
	_sTime, _ := strconv.ParseInt(sTime, 10, 64)
	_eTime, _ := strconv.ParseInt(eTime, 10, 64)
	_onlineType := ut.Int(onlineType)
	docs := []map[string]interface{}{}
	uidList := []string{}
	switch _onlineType {
	case 0, 4, 5: // 全部, 禁言中, 封禁中
		list, err := db.GetUserList(lastId, _sTime, _eTime, uid, nickName, _sortType, _size, _onlineType)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("err:%s", err.Error())), nil
		}
		for _, m := range list {
			if user := GetUserByRpc(ut.String(m["uid"])); user != nil {
				userData := user.ToWeb(ut.String(m["_id"]))
				userData["incache"] = true
				userData["online"] = user.IsOnline()
				userData["team_uid"] = user.TeamUid
				docs = append(docs, userData)
			} else {
				m["online"] = false
				docs = append(docs, m)
			}
			uidList = append(uidList, ut.String(m["uid"]))
		}
	case 1, 2: // 在线或离线且在内存
		arr := GetOnlineSortedUsers(_size, _onlineType, lastId)
		// 只获取内存中的
		for _, user := range arr {
			userData := user.ToWeb("")
			userData["incache"] = true
			userData["online"] = user.IsOnline()
			userData["team_uid"] = user.TeamUid
			docs = append(docs, userData)
		}
	case 3: // 离线
		// 只从数据库获取
		list, err := db.GetUserList(lastId, _sTime, _eTime, uid, nickName, _sortType, _size, _onlineType)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("err:%s", err.Error())), nil
		}
		for _, m := range list {
			m["online"] = false
			docs = append(docs, m)
		}
	}
	onlineSum, total := GetOnlineSum()
	// 获取其他lobby数据
	rstList, errList := this.BroadCastOtherLobby(slg.RPC_GET_USER_BY_WEB, uidList)
	if len(rstList) > 0 {
		otherLobbyUserMap := map[string]map[string]interface{}{}
		for i, rst := range rstList {
			if errList[i] != "" {
				continue
			}
			rstData := ut.MapInterface(rst)
			if mapData := rstData["map"]; mapData != nil {
				userMapData := ut.MapInterface(mapData)
				for uid, v := range userMapData {
					userData := ut.MapInterface(v)
					otherLobbyUserMap[uid] = userData
				}
			}
			onlineSum += ut.Int(rstData["onlineSum"])
			total += ut.Int(rstData["cacheTotal"])
		}
	}
	data := make(map[string]interface{})
	data["size"] = _size
	data["sortType"] = _sortType
	data["docs"] = docs
	data["onlineSum"] = onlineSum
	data["cacheTotal"] = total
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Lobby) httpDeleteUser(id string) (ret map[string]interface{}, err error) {
	err = db.DeleteUser(id)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("删除失败:%s", err.Error())), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
}

func (this *Lobby) httpLobbyExecute(lid, code, fun string) (map[string]interface{}, error) {
	defer func() {
		err := recover()
		if err != nil {
			log.Error("httpGameExecute err:%s", err)
		}
	}()
	context := interp.New(interp.Options{})
	context.Use(stdlib.Symbols)
	context.Use(game.WorldSymbols)
	context.Use(game.UtilSymbols)
	context.Use(game.RoomSymbols)
	context.Use(game.RoomSymbols)
	context.Use(game.CommonSymbols)
	context.Use(game.CommonHelperSymbols)
	// context.Use(http.RecordSymbols)
	// context.Use(http.MailSymbols)
	context.Use(game.PlayerSymbols)
	// context.Use(http.FspSymbols)
	// context.Use(http.BehaviorSymbols)
	// context.Use(http.BazaarSymbols)
	// context.Use(http.BazaarSymbols)
	context.Use(Symbols)
	code, err := url.QueryUnescape(code)
	if err != nil {
		return nil, err
	}
	// 实际测试，部分字符需要转URI编码 ，所以code通过http发上来之前最好通过encodeURIComponent编码一次
	if strings.ReplaceAll(code, " ", "") == "" {
		return nil, errors.New("code is empty")
	}
	if strings.Contains(code, "go") {
		return nil, errors.New("can not use go in customer code")
	}
	_, err = context.Eval(code)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	v, err := context.Eval(fun)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	return slg.HttpResponseSuccessWithDataNoDesc(v.Interface()), nil
}

func (this *Lobby) changeGuestId(uid, guestId string) (map[string]interface{}, error) {
	if ut.IsEmpty(guestId) || ut.IsEmpty(uid) {
		return slg.HttpResponseErrorNoDataWithDesc("参数值为空."), nil
	}
	if err := db.ChangeGuestId(uid, guestId); err != nil {
		return slg.HttpResponseSuccessNoDataWithDesc(err.Error()), err
	}
	return slg.HttpResponseSuccessNoDataWithDesc("修改游客id成功!"), nil
}

func (this *Lobby) changeLoginType(uid, loginType string) (map[string]interface{}, error) {
	if ut.IsEmpty(loginType) || ut.IsEmpty(uid) {
		return slg.HttpResponseErrorNoDataWithDesc("参数值为空."), nil
	} else if user := this.GetUserByDB(uid); user != nil {
		user.LoginType = loginType
		user.FlagUpdateDB()
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

func (this *Lobby) getUserGoldRecordList(size, page string, sortType, sTime, eTime string, uid string) (ret map[string]interface{}, err error) {
	_size, _ := strconv.Atoi(size)
	_sortType, _ := strconv.Atoi(sortType)
	_sTime, _ := strconv.ParseInt(sTime, 10, 64)
	_eTime, _ := strconv.ParseInt(eTime, 10, 64)
	_page := ut.Int(page)
	list, err := goldRecordDb.GetGoldRecordList(_sTime, _eTime, uid, _sortType, _size)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("err:%s", err.Error())), nil
	}
	res := make(map[string]interface{})
	res["size"] = _size
	res["sortType"] = _sortType
	res["uid"] = uid
	if len(list) > 0 {
		arr := list[0].List
		total := len(arr)
		start := ut.Max(0, (_page-1)*_size)
		tail := ut.Min(total, start+_size)
		if tail > start {
			res["list"] = arr[start:tail]
			res["total"] = total
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(res), nil
}

// 查询元宝记录
func (this *Lobby) getUserIngotRecordList(size, sortType, sTime, eTime, uid, skip string) (ret map[string]interface{}, err error) {
	_size, _sortType, _skip := ut.Int(size), ut.Int(sortType), ut.Int(skip)
	_sTime, _ := strconv.ParseInt(sTime, 10, 64)
	_eTime, _ := strconv.ParseInt(eTime, 10, 64)
	list, err := ingotRecordDb.GetIngotRecordList(_sTime, _eTime, uid, _sortType, _size, _skip)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据库错误"), nil
	}
	res := make(map[string]interface{})
	res["list"] = list
	return slg.HttpResponseSuccessWithDataNoDesc(res), nil
}

// 查询道具记录
func (this *Lobby) getUserItemRecordList(tp, id, size, sortType, sTime, eTime, uid, skip string) (ret map[string]interface{}, err error) {
	_tp, _id, _size, _skip := ut.Int(tp), ut.Int(id), ut.Int(size), ut.Int(skip)
	_sTime, _ := strconv.ParseInt(sTime, 10, 64)
	_eTime, _ := strconv.ParseInt(eTime, 10, 64)
	list, err := GetItemRecordList(_sTime, _eTime, uid, _tp, _id, _size, _skip)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据库错误"), nil
	}
	res := make(map[string]interface{})
	res["list"] = list
	return slg.HttpResponseSuccessWithDataNoDesc(res), nil
}

// 获取战斗记录
func (this *Lobby) getBattleRecord(sid, uid string) (ret map[string]interface{}, err error) {
	var data record.BattleRecordData
	_sid := ut.Int(sid)
	if err = battleRecordDb.getCollectionBySid(sid).FindOne(context.TODO(), bson.M{"sid": _sid, "uid": uid}).Decode(&data); err != nil {
		log.Error("getBattleRecord: %v", err)
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"data": data.ToPb(),
	}), nil
}

// 设置玩家最大地块数
func (this *Lobby) setUserMaxLandCount(uid, landCount string) (ret map[string]interface{}, err error) {
	if user := this.GetUserByDB(uid); user != nil {
		user.MaxLandCount = ut.Int32(landCount)
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 设置玩家连续登录天数
func (this *Lobby) setUserCloginDays(uid, cloginDays string) (ret map[string]interface{}, err error) {
	if user := this.GetUserByDB(uid); user != nil {
		user.CLoginDayCount = ut.Int32(cloginDays)
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 设置玩家登录天数
func (this *Lobby) setUserloginDays(uid, loginDays string) (ret map[string]interface{}, err error) {
	if user := this.GetUserByDB(uid); user != nil {
		user.LoginDayCount = ut.Int32(loginDays)
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 设置玩家所在区服
func (this *Lobby) httpSetUserSid(uid, curSid string) (ret map[string]interface{}, err error) {
	_sid := ut.Int32(curSid)
	if user := this.GetUserByDB(uid); user != nil {
		user.SID = _sid
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 设置玩家游玩区服
func (this *Lobby) httpSetUserPlaySid(uid, playSid string) (ret map[string]interface{}, err error) {
	_playSid := ut.Int32(playSid)
	if user := this.GetUserByDB(uid); user != nil {
		user.SetPlaySid(_playSid)
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 设置新手区次数
func (this *Lobby) httpSetUserNewbieCount(uid, count string) (ret map[string]interface{}, err error) {
	cnt := ut.Int32(count)
	if user := this.GetUserByDB(uid); user != nil {
		user.AccTotalNewbieCounts[1] = cnt
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功"), nil
}

// 获取用户画像列表
func (this *Lobby) httpGetUserPortrayalList(uid string) (ret map[string]interface{}, err error) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return slg.HttpResponseSuccessNoDataWithDesc("玩家不存在"), nil
	}
	user.PopularityLock.RLock()
	list := array.Map(user.Portrayals, func(m *g.PortrayalInfo, _ int) map[string]interface{} {
		return map[string]interface{}{
			"id":          m.ID,
			"strategys":   array.Map(m.GetStrategys(), func(m *g.StrategyObj, _ int) int32 { return m.Type }),
			"isChosenOne": m.IsChosenOne(),
			"recompCount": m.RecompCount,
		}
	})
	user.PopularityLock.RUnlock()
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{"list": list}), nil
}

// 设置画像属性
func (this *Lobby) httpSetUserPortrayalAttr(uid, id, strategys, isChosenOne string) (ret map[string]interface{}, err error) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return slg.HttpResponseSuccessNoDataWithDesc("玩家不存在"), nil
	} else if !slg.IsDebug() {
		return slg.HttpResponseSuccessNoDataWithDesc("只有调试模式可设置"), nil
	}
	portrayal := user.GetPortrayalInfo(ut.Int32(id))
	if portrayal == nil {
		return slg.HttpResponseSuccessNoDataWithDesc("画像不存在"), nil
	} else if !portrayal.IsUnlock() {
		return slg.HttpResponseSuccessNoDataWithDesc("画像还未修复"), nil
	}
	portrayal.ChangeStrategys(ut.StringToInt32s(strategys, ","))
	if isChosenOne != "" && !portrayal.IsChosenOne() {
		portrayal.SetChosenOne()
	}
	// 通知游戏服
	this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_HERO_ATTR, user.UID, portrayal.ID, portrayal.Attrs)
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功"), nil
}

// 禁言
func (this *Lobby) httpBannedChat(uid string, time string) (map[string]interface{}, error) {
	if user := this.GetUserByDB(uid); user != nil {
		user.BanUserChat(ut.Int64(time))
	}
	return slg.HttpResponseSuccessNoDataWithDesc("禁言成功!"), nil
}

// 封禁
func (this *Lobby) httpBannedAccount(uid string, time, banDevice, banType string) (map[string]interface{}, error) {
	banTime := ut.Int64(time)
	_banDevice := false
	if banDevice == "1" {
		_banDevice = true
	}
	if user := this.GetUserByDB(uid); user != nil {
		this.BanUserAccount(user, banTime, ut.Int32(banType), _banDevice)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("封禁成功!"), nil
}

// 获取禁言/封禁记录
func (this *Lobby) httpGetBanRecordList(uid string) (map[string]interface{}, error) {
	list, err := banRecordDb.FindBanRecords(uid)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("获取禁言/封禁记录失败!"), err
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 生成兑换码
func (this *Lobby) httpGenCdk(sid, code, receiver, items string, isClaim, endTime string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	_isClaim, _ := strconv.ParseBool(isClaim)
	var item []*g.TypeObj
	err := json.Unmarshal([]byte(items), &item)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("处理奖励数据失败!!"), nil
	}
	_endTime, _ := ut.ParseDateStr2TimeStamps(endTime)
	temp := giftDb.CreateGift(_sid, code, receiver, item, _isClaim, int64(_endTime))
	// 用户可能没有自定义兑换码,那么需要保证生成多个区服的兑换码时，兑换码要一样，并且把这个兑换码返回给前端展示。
	if code == "" {
		code = temp
	} else if temp == "" {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("兑换码已存在:%v!", code)), nil
	}

	return slg.HttpResponseSuccessWithDataWithDesc(map[string]string{
		"code": code,
	}, "操作成功!"), nil
}

// 获取兑换码列表
func (this *Lobby) httpGetCdkList(sid, page, size, sortType string) (map[string]interface{}, error) {
	_sid := ut.Int(sid)
	_page := ut.Int(page)
	_size := ut.Int(size)
	_sortType := ut.Int(sortType)

	list := giftDb.GetCdkList(_sid)
	if _sortType == 1 {
		// 按照创建时间降序
		sort.Slice(list, func(i, j int) bool {
			return list[i].CreateTime > list[j].CreateTime
		})
	}

	if _sortType == 2 {
		// 按照创建时间升序
		sort.Slice(list, func(i, j int) bool {
			return list[i].CreateTime < list[j].CreateTime
		})
	}

	total := len(list)
	start := _page * _size
	end := int(math.Min(float64((_page+1)*_size), float64(total)))
	if end < start {
		start, _page = 0, 0
	}
	data := make(map[string]interface{})
	data["total"] = total
	data["page"] = _page
	data["size"] = _size
	data["sortType"] = _sortType
	data["docs"] = list[start:end]
	return slg.HttpResponseSuccessWithDataWithDesc(data, "兑换码列表获取成功!"), nil
}

// 删除兑换码
func (this *Lobby) httpDelGift(sid string, uid string) (map[string]interface{}, error) {
	if err := giftDb.DeleteCodeByUID(uid); err != "" {
		return slg.HttpResponseErrorNoDataWithDesc(err), nil
	}
	// 删除领取记录
	if err := giftClaimDb.DeleteGiftClaims(uid); err != "" {
		return slg.HttpResponseErrorNoDataWithDesc(err), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
}

// 查询兑换码领取人数
func (this *Lobby) httpGetCdkClaimNum(uid string) (map[string]interface{}, error) {
	data := make(map[string]interface{})
	num, _ := giftClaimDb.FindGiftClaimNum(uid)
	data["num"] = num
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

// 查询指定玩家兑换数据
func (this *Lobby) httpGetUserCdkClaim(code, userId string) (map[string]interface{}, error) {
	data, err := giftClaimDb.FindUserClaimGift(code, userId)
	if err != "" {
		return slg.HttpResponseSuccessNoDataWithDesc("未领取!"), nil
	}
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

// 修改玩家排位分
func (this *Lobby) modifyUserRankScore(uid, score string) (map[string]interface{}, error) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在!"), nil
	}
	user.RankScore = ut.Int32(score)
	user.FlagUpdateDB()
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

// 从内存删除用户并保存数据库
func (this *Lobby) userSaveDb(uid string) (map[string]interface{}, error) {
	user := GetUserByRpc(uid)
	if user == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不在内存中!"), nil
	}
	if user.IsOnline() {
		return slg.HttpResponseErrorNoDataWithDesc("在线玩家不能保存!"), nil
	}
	user.SetDbUpdaateFlag(2)
	rds.DelUserMallocLidLock(user.UID)
	_, _, _, err := SaveUserDb(user)
	if err != "" {
		return slg.HttpResponseErrorNoDataWithDesc("保存失败!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("保存成功!"), nil
}

// 强制下线
func (this *Lobby) httpUserOffline(uid string) (map[string]interface{}, error) {
	user := GetUserByRpc(uid)
	if user == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不在内存中!"), nil
	}
	if !user.IsOnline() {
		return slg.HttpResponseErrorNoDataWithDesc("玩家已离线!"), nil
	}
	this.UserForceOffline(user, time.Now().UnixMilli(), slg.KICK_NOTIFY_TYPE_GM, 0)
	return slg.HttpResponseSuccessNoDataWithDesc("离线成功!"), nil
}

func (this *Lobby) httpGetUserTeamInfo(uid string) (map[string]interface{}, error) {
	if uid == "" {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在!"), nil
	}
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在!"), nil
	}
	if user.TeamUid == "" {
		return slg.HttpResponseErrorNoDataWithDesc("玩家未加入队伍!"), nil
	}
	rst, err := ut.RpcBytes(this.InvokeTeamFunc(user.TeamUid, slg.RPC_GET_TEAM_INFO))
	if err != "" {
		return slg.HttpResponseErrorNoDataWithDesc("队伍不存在!"), nil
	}
	teamPb := &pb.TeamInfo{}
	pb.ProtoUnMarshal(rst, teamPb)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"team": teamPb,
	}), nil
}

// 重置队伍状态
func (this *Lobby) httpResetTeamState(teamUid, roomType, playSid string) (map[string]interface{}, error) {
	_roomType, _playSid := ut.Int32(roomType), ut.Int32(playSid)
	_, err := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_TEAM_RESET_STATE, _roomType, _playSid))
	if err != "" {
		return slg.HttpResponseErrorNoDataWithDesc("队伍不存在!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 删除队伍
func (this *Lobby) httpDelTeam(teamUid string) (map[string]interface{}, error) {
	_, err := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_TEAM_GM_DEL_TEAM))
	if err != "" {
		return slg.HttpResponseErrorNoDataWithDesc("队伍不存在!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
}

// 删除队员
func (this *Lobby) httpDelTeamUser(teamUid, uid string) (map[string]interface{}, error) {
	_, err := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_TEAM_GM_DEL_TEAM_USER, uid))
	if err != "" {
		return slg.HttpResponseErrorNoDataWithDesc("队伍不存在!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
}

// 重置队伍uid
func (this *Lobby) httpResetUserTeamUid(uid string) (map[string]interface{}, error) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在!"), nil
	}
	rst, err := ut.RpcBytes(this.InvokeTeamFunc(user.TeamUid, slg.RPC_GET_TEAM_INFO))
	if err != "" {
		// 队伍不存在 直接重置玩家队伍uid
		user.TeamUid = ""
		user.FlagUpdateDB()
	} else {
		teamPb := &pb.TeamInfo{}
		pb.ProtoUnMarshal(rst, teamPb)
		if teamPb.UserList != nil {
			for _, v := range teamPb.UserList {
				if v.Uid == user.UID {
					// 玩家在该队伍中 无法重置
					return slg.HttpResponseErrorNoDataWithDesc("玩家在该队伍中 无法重置!"), nil
				}
			}
		}
		user.TeamUid = ""
		user.FlagUpdateDB()
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 查询玩家累计充值
func (this *Lobby) httpGetUserRechargeSum(uid string) (map[string]interface{}, error) {
	orders, err := recharge.RechargeDb.GetUserRechargeOrders(uid)
	if err != nil {
		return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
			"sum": "未充值",
		}), nil
	}
	sumMap := map[string]float32{}
	for _, v := range orders {
		if v.CurrencyType == "" {
			continue
		}
		sumMap[v.CurrencyType] += v.PayAmount
	}
	sumStr := ""
	for k, v := range sumMap {
		if sumStr != "" {
			sumStr += ", "
		}
		sumStr += ut.String(v) + k
	}
	if sumStr == "" {
		sumStr = "未充值"
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"sum": sumStr,
	}), nil
}

// 查询创意工坊物品
func (this *Lobby) httpGetWorkshopItems(uid, userId, page, size, itemType, state, sortField, sortType string) (map[string]interface{}, error) {
	_page, _size, _itemType, _state, _decs := ut.Int(page), ut.Int(size), ut.Int(itemType), ut.Int(state), ut.Int(sortType)
	skip := _page * _size
	list, err := workshopDb.FindWorkshopItems(uid, userId, skip, _size, _itemType, _state, _decs, sortField)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据库错误!"), nil
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 创意工坊审核
func (this *Lobby) httpWorkshopAudit(uid, state string) (map[string]interface{}, error) {
	_state := ut.Int(state)
	err := workshopDb.UpdateWorkshopItemState(uid, _state)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据库错误!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 创建队伍并报名
func (this *Lobby) httpCreateTeamApply(playerNum, teamNum, roomType, teamData string) (map[string]interface{}, error) {
	if !slg.IsDebug() {
		return slg.HttpResponseSuccessNoDataWithDesc("只有测试环境开启"), nil
	}
	pNum, tNum := ut.Int32(playerNum), ut.Int32(teamNum)
	teamStrArr := strings.Split(teamData, "|")
	teamList := [][]string{}
	for _, v := range teamStrArr {
		if v != "" {
			teamList = append(teamList, strings.Split(v, ","))
		}
	}
	if httpTeamCreate == nil {
		httpTeamCreate = &HttpTeamCreate{}
	} else if httpTeamCreate.State == 1 {
		str := fmt.Sprintf("上一次队伍创建还未完成: %v/%v", httpTeamCreate.CurPlyNum, httpTeamCreate.PlayerNum)
		return slg.HttpResponseSuccessNoDataWithDesc(str), nil
	}
	httpTeamCreate.TeamList = teamList
	httpTeamCreate.PlayerNum = pNum
	httpTeamCreate.TeamNum = tNum
	httpTeamCreate.RoomType = ut.Int32(roomType)
	go httpTeamCreate.createTeams()
	return slg.HttpResponseSuccessNoDataWithDesc("开始创建队伍!"), nil
}

// 查询队伍创建状态
func (this *Lobby) httpGetCreateTeamInfo() (map[string]interface{}, error) {
	if !slg.IsDebug() {
		return slg.HttpResponseSuccessNoDataWithDesc("只有测试环境开启"), nil
	}
	rst := map[string]interface{}{}
	if httpTeamCreate != nil && httpTeamCreate.State == 1 {
		// 创建中
		rst["playerNum"] = httpTeamCreate.PlayerNum
		rst["curPlyNum"] = httpTeamCreate.CurPlyNum
	}

	return slg.HttpResponseSuccessWithDataNoDesc(rst), nil
}

var httpTeamCreate *HttpTeamCreate

type HttpTeamCreate struct {
	TeamList       [][]string
	handledUserMap map[string]bool
	PlayerNum      int32
	TeamNum        int32
	RoomType       int32
	CurPlyNum      int32 // 已处理玩家数量
	CurTeamNum     int32 // 已处理队伍数量
	State          int32 // 0未开启 1处理中 2已结束
}

// 处理创建队伍和报名
func (this *HttpTeamCreate) createTeams() {
	if this.State == 1 {
		return
	}
	this.State = 1
	this.CurPlyNum = 0
	this.CurTeamNum = 0
	this.handledUserMap = map[string]bool{}
	// 处理已确定的队伍
	for _, uidList := range this.TeamList {
		// 创建队伍
		this.HandleCreateTeam(uidList)
	}
	if this.CurPlyNum < this.PlayerNum {
		// 已报名的玩家人数不满足设置人数 从数据库中取出
		leftPlyNum := this.PlayerNum - this.CurPlyNum
		leftTeamNum := this.TeamNum - this.CurTeamNum
		avgTeamUserNum := slg.TEAM_USER_NUM_MAX
		if leftTeamNum > 0 {
			avgTeamUserNum = ut.Min(avgTeamUserNum, int(leftPlyNum/leftTeamNum))
		}
		dbLastId := ""
		curTeamUsers := []string{}
		for leftPlyNum > 0 {
			// 每次从数据库最多取100人
			size := ut.Min(100, int(leftPlyNum))
			userDatas, err := db.GetUserList(dbLastId, 0, 0, "", "", 1, size, 0)
			if err != nil || len(userDatas) == 0 {
				log.Warning("handCreateTeam GetUserList err: %v", err)
				break
			}
			objId := userDatas[len(userDatas)-1]["_id"].(primitive.ObjectID)
			dbLastId = objId.Hex()
			for _, v := range userDatas {
				uid := ut.String(v["uid"])
				if this.handledUserMap[uid] {
					continue
				}
				curTeamUsers = append(curTeamUsers, uid)
				leftPlyNum--
				if len(curTeamUsers) >= avgTeamUserNum || leftPlyNum <= 0 {
					// 已达到队伍人数上限或者总人数上限 则创建队伍
					this.HandleCreateTeam(curTeamUsers)
					curTeamUsers = []string{}
				}
				if leftPlyNum <= 0 {
					break
				}
			}
		}

	}
	this.State = 2
}

// 创建队伍
func (this *HttpTeamCreate) HandleCreateTeam(uidList []string) {
	var teamUid string
	var team *TeamInfo
	for _, uid := range uidList {
		if this.handledUserMap[uid] { // 该玩家已处理过
			continue
		}
		this.handledUserMap[uid] = true
		if teamUid == "" {
			teamUid = uid
		}
		if info, e := lobbyModule.InvokeUserFunc(uid, slg.RPC_SET_USER_TEAM_FORCE, teamUid); e == "" {
			userInfo := ut.MapInterface(info)
			nickname := ut.String(userInfo["nickname"])
			headIcon := ut.String(userInfo["headIcon"])
			if team == nil {
				// 创建队伍
				team = CreateTeam(uid, nickname, headIcon, lobbyModule.LID, this.RoomType, int32(0), int32(0))
			} else {
				team.userListLock.Lock()
				team.UserList = append(team.UserList, NewTeamUser(uid, nickname, headIcon, 0, 0, 0))
				team.userListLock.Unlock()
			}
			this.CurPlyNum++
		} else if teamUid == uid {
			teamUid = ""
		}
	}
	if team == nil {
		return
	}
	// 队伍报名
	lobbyModule.InvokeTeamFunc(teamUid, slg.RPC_TEAM_APPLY_SERVER, teamUid, "", "", this.RoomType, int32(0), int32(0))
	this.CurTeamNum++
}
