package game

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/player"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	"sort"
	"strconv"
	"strings"
	"time"

	"math"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/world"

	"slgsrv/server/game/common/g"

	"github.com/huyangv/vmqant/log"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
	"go.mongodb.org/mongo-driver/bson"
)

func (this *Game) InitHttp() {
	this.GetServer().RegisterGO("/http/hello", this.httpHello)
	this.GetServer().RegisterGO("/http/addArmys", this.httpAddArmys)
	this.GetServer().RegisterGO("/http/changeEquipAttr", this.httpChangeEquipAttr)
	// 玩家列表
	this.GetServer().RegisterGO("/http/getPlayers", this.httpGetPlayers)
	// 删除玩家
	this.GetServer().RegisterGO("/http/delPlayer", this.httpDelPlayer)
	// 获取聊天记录
	this.GetServer().RegisterGO("/http/getChats", this.httpGetChatList)
	// 获取私聊列表
	this.GetServer().RegisterGO("/http/getPlayerChatList", this.httpGetPlayerChatListWeb)
	// 获取联盟列表
	this.GetServer().RegisterGO("/http/getAlliances", this.httpGetAlliancesWeb)
	// 获取区服列表信息
	this.GetServer().RegisterGO("/http/getAreaInfo", this.httpGetAreaInfo)
	// 获取军队信息
	this.GetServer().RegisterGO("/http/getPlayerArmys", this.httpGetPlayerArmys)
	// 动态执行
	this.GetServer().RegisterGO("/http/gameExecute", this.httpGameExecute)
	// 删除聊天
	this.GetServer().RegisterGO("/http/delGameChat", this.httpDelGameChat)
	// 修改区服版本限制
	this.GetServer().RegisterGO("/http/changeAreaVersionLimit", this.httpChangeAreaVersionLimit)
	// 获取Gm参数
	this.GetServer().RegisterGO("/http/getGmParams", this.httpGetGmParams)
	// 设置Gm参数
	this.GetServer().RegisterGO("/http/setGmParams", this.httpSetGmParams)
	// 设置获胜领地数
	this.GetServer().RegisterGO("/http/setWinLandCount", this.setWinLandCount)
	// 设置开服时间
	this.GetServer().RegisterGO("/http/setCreateTime", this.setCreateTime)
	// 删除地块免战
	this.GetServer().RegisterGO("/http/delAvoidWar", this.delAvoidWar)
	// 删除玩家所有免战
	this.GetServer().RegisterGO("/http/delPlayerAvoidWar", this.delPlayerAvoidWar)
	// 直接占领土地
	this.GetServer().RegisterGO("/http/addPlayerCell", this.httpAddPlayerCell)
	// 按数量占领土地
	this.GetServer().RegisterGO("/http/addPlayerCellByNum", this.httpAddPlayerCellByNum)
	// 修改联盟公告
	this.GetServer().RegisterGO("/http/changeAlliNotice", this.httpChangeAlliNotice)
	// 获取市场汇率
	this.GetServer().RegisterGO("/http/getBazaarPrices", this.httpGetBazaarPrices)
	// 修改市场汇率
	this.GetServer().RegisterGO("/http/modifyTradePrice", this.httpModifyTradePrice)
	// 获取联盟科技槽位参数
	this.GetServer().RegisterGO("/http/getAlliCeriParams", this.httpGetAlliCeriParams)
	// 设置联盟科技槽位参数
	this.GetServer().RegisterGO("/http/setAlliCeriParams", this.httpSetAlliCeriParams)
	// 重置英雄供奉
	this.GetServer().RegisterGO("/http/resetHeroSlots", this.httpResetHeroSlots)
	// 获取地块军队
	this.GetServer().RegisterGO("/http/getAreaArmys", this.httGetAreaArmys)
}

func (this *Game) httpHello(sid string, name string) (ret map[string]interface{}, err error) {
	fmt.Println(sid, name)
	log.Info("收到 = " + name)
	return map[string]interface{}{
		"sid":  sid,
		"name": name,
	}, nil
}

func (this *Game) httpGetPlayers(sid string, page string, size string, sortType string, uid string, nickName, alliUid string) (ret map[string]interface{}, err error) {
	_sid := ut.Int32(sid)
	_page, _ := strconv.Atoi(page)
	_size, _ := strconv.Atoi(size)
	_sortType, _ := strconv.Atoi(sortType)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("没有发现房间:%d", _sid)), nil
	}
	// 根据昵称/uid索引玩家
	nickName = strings.ReplaceAll(nickName, " ", "")
	uid = strings.ReplaceAll(uid, " ", "")
	filter := bson.M{}
	if !ut.IsEmpty(uid) {
		filter["uid"] = uid
	}
	if !ut.IsEmpty(nickName) {
		filter["nickname"] = nickName
	}
	if !ut.IsEmpty(alliUid) {
		filter["alliance_uid"] = alliUid
	}
	manager := room.GetPlayerManager()
	wld := room.GetWorld()
	cur, err := manager.GetDbCollection().Find(context.TODO(), filter)
	var allPlayers []player.TableData
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("db err:%s", err)), nil
	}
	cur.All(context.TODO(), &allPlayers)
	cur.Close(context.TODO())
	// 装在线的
	var onlinePlayer []map[string]interface{}
	// 装离线的
	var offlinePlayer []map[string]interface{}

	for _, playerObj := range allPlayers {
		plr := room.GetPlayer(playerObj.Uid)
		if plr != nil {
			plrMod := plr.(*player.Model)
			onlinePlayer = append(onlinePlayer, plrMod.ToWebData())
			continue
		}
		offlinePlayer = append(offlinePlayer, playerObj.ToWebData(wld))
	}
	// 排序 默认降序
	sort.Slice(onlinePlayer, func(i, j int) bool {
		modelA := onlinePlayer[i]
		modelB := onlinePlayer[j]
		if _sortType == 2 {
			return modelA["lastLoginTime"].(int64) < modelB["lastLoginTime"].(int64)
		}
		return modelA["lastLoginTime"].(int64) > modelB["lastLoginTime"].(int64)
	})
	sort.Slice(offlinePlayer, func(i, j int) bool {
		modelA := offlinePlayer[i]
		modelB := offlinePlayer[j]
		if _sortType == 2 {
			return modelA["offlineTime"].(int64) < modelB["offlineTime"].(int64)
		}
		return modelA["offlineTime"].(int64) > modelB["offlineTime"].(int64)
	})

	all := []map[string]interface{}{}
	all = append(all, onlinePlayer...)
	all = append(all, offlinePlayer...)

	//players := room.GetPlayers()
	//if len(players) == 0 {
	//	return HttpResponseErrorNoDataWithDesc(fmt.Sprintf("没有任何玩家数据:%d", _sid)), nil
	//}
	//if _sortType != 0 {
	//	sort.Slice(players, func(i, j int) bool {
	//		modelA := players[i]
	//		modelB := players[j]
	//		if _sortType == 2 {
	//			// 登录时间升序
	//			return modelA.LastLoginTime < modelB.LastLoginTime
	//		}
	//		if _sortType == 3 {
	//			// 总在线时长降序
	//			return modelA.SumOnlineTime > modelB.SumOnlineTime
	//		}
	//		if _sortType == 4 {
	//			// 总在线时长升序
	//			return modelA.SumOnlineTime < modelB.SumOnlineTime
	//		}
	//		if _sortType == 5 {
	//			// 连续登录天数降序
	//			return modelA.CLoginDayCount > modelB.CLoginDayCount
	//		}
	//		if _sortType == 6 {
	//			// 连续登录天数升序
	//			return modelA.CLoginDayCount < modelB.CLoginDayCount
	//		}
	//		// 默认 登录时间降序
	//		return modelA.LastLoginTime > modelB.LastLoginTime
	//	})
	//}

	total := len(all)
	start := _page * _size
	end := int(math.Min(float64((_page+1)*_size), float64(len(all))))
	if end < start {
		start, _page = 0, 0
	}
	data := make(map[string]interface{})
	data["total"] = total
	data["page"] = _page
	data["size"] = _size
	data["sortType"] = _sortType
	//var docs []map[string]interface{}
	//for _, player := range players[start:end] {
	//	docs = append(docs, player.Strip())
	//}
	data["docs"] = all[start:end]
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

// 删除玩家
func (this *Game) httpDelPlayer(sid string, uid string) (map[string]interface{}, error) {
	err, _, wld := checkHttpError(sid)
	if err != nil {
		return nil, err
	}
	plr := wld.GetTempPlayer(uid)
	if plr == nil {
		return slg.HttpResponseErrorNoDataWithDesc("玩家不存在"), nil
	} else if ok := wld.DeletePlayerByGame(plr); ok {
		return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
	}
	return slg.HttpResponseErrorNoDataWithDesc("删除失败!"), nil
}

func (this *Game) httpAddArmys(sid string, owner string, index string, parames string) (ret map[string]interface{}, err error) {
	err, _, wld := checkHttpError(sid)
	if err != nil {
		return nil, err
	}
	area := wld.GetArea(ut.Int32(index))
	if area == nil {
		return nil, fmt.Errorf("土地位置不存在")
	}
	armys := strings.Split(parames, "|")
	for _, str := range armys {
		wld.AddAreaArmy(area, area.CreateArmyByString(str, owner), 2)
		wld.NotifyPlayerArmyDistInfo(owner)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("添加成功!"), nil
}

func (this *Game) httpChangeEquipAttr(sid string, owner string, equipId string, equipAttrs string) (ret map[string]interface{}, err error) {
	err, _, wld := checkHttpError(sid)
	if err != nil {
		return nil, err
	}
	attrs := [][]int32{}
	arr := strings.Split(equipAttrs, "|")
	for _, m := range arr {
		attrs = append(attrs, ut.StringToInt32s(m, ","))
	}
	if wld.ChangePlayerEquipAttrs(owner, ut.Int32(equipId), attrs) {
		return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
	}
	return slg.HttpResponseErrorNoDataWithDesc("修改失败!"), nil
}

func (this *Game) httpGetChatList(sid, _type, alli, uids, index, size, startTime, endTime, filterUid string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	chatType := ut.Int32(_type)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	model := room.GetWorld().(*world.Model)
	_index, _size := ut.Int(index), ut.Int(size)
	_startTime, _endTime := ut.Int64(startTime), ut.Int64(endTime)
	// 世界聊天
	if chatType == 0 {
		data := model.GetChatsByAlliUID("", "", _index, _size, _startTime, _endTime, filterUid)
		return slg.HttpResponseSuccessWithDataNoDesc(data), nil
	}
	// 联盟聊天
	if chatType == 1 {
		data := model.GetChatsByAlliUID("", alli, _index, _size, _startTime, _endTime, filterUid)
		return slg.HttpResponseSuccessWithDataNoDesc(data), nil
	}
	// 私聊
	if chatType == 2 {
		data := model.GetChatsByAlliUID(uids, "", _index, _size, _startTime, _endTime, filterUid)
		return slg.HttpResponseSuccessWithDataNoDesc(data), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("没有数据!"), nil
}

func (this *Game) httpGetPlayerChatListWeb(sid, uid string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	model := room.GetWorld().(*world.Model)
	data := model.GetPlayerChatList(uid)
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Game) httpGetAlliancesWeb(sid string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	model := room.GetWorld().(*world.Model)
	var data []map[string]interface{}
	model.Alliances.RLock()
	for _, a := range model.Alliances.Map {
		alliMap := a.Strip2()
		landCount, _, _, _ := model.GetAlliRankInfo(a)
		alliMap["landCount"] = landCount
		creator := model.GetTempPlayer(a.Creater)
		if creator != nil {
			creatorName := creator.Nickname
			alliMap["creatorName"] = creatorName
		}
		data = append(data, alliMap)
	}
	model.Alliances.RUnlock()
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Game) httpChangeAreaVersionLimit(sid, clientVersion, clientBigVersion string) (map[string]interface{}, error) {
	_sid := this.GetServer().Options().Metadata["sid"]
	if _sid != sid {
		return slg.HttpResponseErrorNoDataWithDesc("sid错误,不符合当前服务器。"), nil
	}
	room := r.GetRoomById(ut.Int32(_sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("服务器不存在,无法修改"), nil
	} else if room.IsClose() {
		return slg.HttpResponseErrorNoDataWithDesc("服务器已关闭,无法修改"), nil
	}
	clientVersion = ut.Trim(clientVersion)
	clientBigVersion = ut.Trim(clientBigVersion)
	if room.GetClientVersion() != clientVersion {
		room.SetClientVersion(clientVersion)
	}
	if room.GetClientBigVersion() != clientBigVersion {
		room.SetClientBigVersion(clientBigVersion)
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}
func (this *Game) httpGetAreaInfo(sid string) (map[string]interface{}, error) {
	_sid := this.GetServer().Options().Metadata["sid"]
	if _sid != sid {
		return slg.HttpResponseErrorNoDataWithDesc("sid错误,不符合当前服务器。"), nil
	}
	__sid := ut.Int32(_sid)
	room := r.GetRoomById(__sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("服务器不存在"), nil
	} else if room.IsClose() {
		return slg.HttpResponseErrorNoDataWithDesc("服务器已关闭"), nil
	}
	data := room.Strip()
	// get在线人数
	data["online"] = room.GetOnlinePlayerCount()
	// 当前最多领地玩家和联盟
	model := room.GetWorld().(*world.Model)
	playerTop, alllTop, alliTopBeAddCount := model.GetPlayerAndAlliTop()
	data["playerTopLandCount"] = playerTop
	data["alliTopLandCount"] = alllTop
	data["alliTopBeAddCount"] = alliTopBeAddCount
	// 进程运行时间
	data["processRunTime"] = room.GetProcessRunTime()
	data["highestAncientLv"] = room.GetWorld().GetAncientHighestLv()
	data["season"] = room.GetWorld().GetSeason().GetType()
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Game) httpGetPlayerArmys(sid string, uid string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	wld := room.GetWorld().(*world.Model)
	data := wld.GetPlayerArmysPb(uid, 0, -1)
	list := []map[string]interface{}{}
	for _, army := range data {
		armyInfo := map[string]interface{}{}
		armyInfo["index"] = army.Index
		armyInfo["uid"] = army.Uid
		armyInfo["name"] = army.Name
		armyInfo["pawns"] = army.Pawns
		// 获取行军
		march := wld.GetMarchByArmyUID(army.Uid)
		if march != nil {
			armyInfo["target"] = march.TargetIndex
		}
		list = append(list, armyInfo)
	}
	return slg.HttpResponseSuccessWithDataNoDesc(list), nil
}

func (this *Game) httGetAreaArmys(sid string, index string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	wld := room.GetWorld().(*world.Model)
	_index := ut.Int32(index)
	area := wld.GetArea(_index)
	if area == nil {
		return slg.HttpResponseErrorNoDataWithDesc("坐标错误"), nil
	}
	arr := area.GetArmysClone()
	list := []map[string]interface{}{}
	for _, v := range arr {
		army := v.ToShortDataPb(area.ToArmyState(v))
		armyInfo := map[string]interface{}{}
		armyInfo["index"] = army.Index
		armyInfo["uid"] = army.Uid
		armyInfo["name"] = army.Name
		armyInfo["pawns"] = army.Pawns
		// 获取行军
		march := wld.GetMarchByArmyUID(army.Uid)
		if march != nil {
			armyInfo["target"] = march.TargetIndex
		}
		list = append(list, armyInfo)
	}
	return slg.HttpResponseSuccessWithDataNoDesc(list), nil
}

// 动态执行
func (this *Game) httpGameExecute(sid, code, fun string) (map[string]interface{}, error) {
	defer func() {
		err := recover()
		if err != nil {
			log.Error("httpGameExecute err:%s", err)
		}
	}()
	context := interp.New(interp.Options{})
	context.Use(stdlib.Symbols)
	context.Use(WorldSymbols)
	context.Use(UtilSymbols)
	context.Use(RoomSymbols)
	context.Use(RecordSymbols)
	context.Use(MailSymbols)
	context.Use(PlayerSymbols)
	context.Use(FspSymbols)
	context.Use(BehaviorSymbols)
	context.Use(BazaarSymbols)
	context.Use(BazaarSymbols)
	context.Use(CommonSymbols)
	context.Use(CommonHelperSymbols)
	//context.Use(http.LoginSymbols)

	code, err := url.QueryUnescape(code)
	if err != nil {
		return nil, err
	}
	if strings.ReplaceAll(code, " ", "") == "" {
		return nil, errors.New("code is empty")
	}
	if strings.Index(code, "go") > -1 {
		return nil, errors.New("can not use go in customer code")
	}
	_, err = context.Eval(code)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	v, err := context.Eval(fun)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	return slg.HttpResponseSuccessWithDataNoDesc(v.Interface()), nil
}

// 删除聊天
func (this *Game) httpDelGameChat(uid, sid, channel string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	model := room.GetWorld().(*world.Model)
	model.DoDelGameChat(channel, uid)
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

func (this *Game) httpGetGmParams(sid string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"march":              slg.GetMarchUpSpeed(),
		"ceri":               slg.GetCeriSpeedUp(),
		"drill":              slg.GetDrillSpeedUp(),
		"canOccupyTime":      slg.ToCanOccupyTime[int](),
		"alliSwtich":         slg.GetAlliSwitch(),
		"build":              slg.GetBuildSpeedUp(),
		"guestCreateAlli":    slg.GetGuestCreateAlli(),
		"ancientCtbLimit":    slg.GetAncientCtbLimit(),
		"ancientSpeedUpTime": constant.ANCIENT_CITY_SPEED_UP_TIME / ut.TIME_MINUTE,
		"transit":            slg.GetTransitSpeedUp(),
		"ancientCtbCount":    constant.ANCIENT_CONTRIBUTE_COUNT_MAX,
		"heroRevivesTime":    g.HERO_REVIVES_TIME / 1000,
		"cellTondenCount":    constant.CELL_TONDEN_DAILY_COUNT,
	}), nil
}

func (this *Game) httpSetGmParams(sid string, march, ceri, drill, canOccupyTime, restTime, alliSwitch, build, guestCreateAlli, ancientCtbLimit, ancientSpeedUpTime, transit, ancientCtbCount, heroRevivesTime,
	cellTondenCount string) (map[string]interface{}, error) {
	if !slg.DEBUG {
		return slg.HttpResponseErrorNoDataNoDesc(), nil
	}
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	_march, _ceri, _drill, _build, _transit, _heroRevivesTime, _cellTondenCount := ut.Int(march), ut.Int(ceri), ut.Int(drill), ut.Int(build), ut.Int(transit), ut.Int(heroRevivesTime),
		ut.Int32(cellTondenCount)
	if _march > 0 {
		slg.SetMarchUpSpeed(_march)
	}
	if _ceri > 0 {
		slg.SetCeriSpeedUp(_ceri)
	}
	if _drill > 0 {
		slg.SetDrillSpeedUp(_drill)
	}
	if _build > 0 {
		slg.SetBuildSpeedUp(_build)
	}
	if _transit > 0 {
		slg.SetTransitSpeedUp(_transit)
	}
	if _heroRevivesTime >= 0 {
		g.HERO_REVIVES_TIME = ut.TIME_SECOND * _heroRevivesTime
	}
	if _cellTondenCount > 0 {
		constant.CELL_TONDEN_DAILY_COUNT = _cellTondenCount
	}
	slg.SetCanOccupyTime(ut.StringToInts(canOccupyTime, ","))
	slg.SetAlliSwitch(alliSwitch == "1")
	slg.SetGuestCreateAlli(guestCreateAlli == "1")
	slg.SetAncientCtbLimit(ancientCtbLimit == "1")
	constant.ANCIENT_CITY_SPEED_UP_TIME = ut.Int64(ancientSpeedUpTime) * ut.TIME_MINUTE
	constant.ANCIENT_CONTRIBUTE_COUNT_MAX = ut.Int32(ancientCtbCount)
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

func (this *Game) setWinLandCount(sid string, landCount string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	cond := room.GetWinCond()
	if len(cond) >= 3 {
		_landCount := ut.Int32(landCount)
		if _landCount > 0 {
			cond[1] = _landCount
			room.SetWinCond(cond)
		}
	} else {
		log.Error("setWinLandCount winCond err: %v", cond)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

func (this *Game) setCreateTime(sid string, time string) (map[string]interface{}, error) {
	if !slg.DEBUG {
		return slg.HttpResponseErrorNoDataNoDesc(), nil
	}
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	crateTime := ut.Int64(time)
	room.SetCreateTime(crateTime)
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

func (this *Game) delAvoidWar(sid string, index string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	room.GetWorld().RemoveAvoidWarArea(ut.Int32(index))
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

func (this *Game) delPlayerAvoidWar(sid string, uid string) (map[string]interface{}, error) {
	_sid := ut.Int32(sid)
	room := r.GetRoomById(_sid)
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	room.GetWorld().DelPlayerAvoidWar(uid)
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 添加玩家领地
func (this *Game) httpAddPlayerCell(sid string, uid string, indexStr string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	indexArr := strings.Split(indexStr, "|")
	if len(indexArr) == 0 {
		room.GetWorld().DirectOccupyCell(ut.Int32(indexStr), uid, true)
	} else {
		for _, index := range indexArr {
			room.GetWorld().DirectOccupyCell(ut.Int32(index), uid, true)
		}
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 添加玩家领地 按数量添加
func (this *Game) httpAddPlayerCellByNum(sid string, uid string, mainCity, num string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	wld := room.GetWorld().(*world.Model)
	mainCityIndex, cellNum := ut.Int32(mainCity), ut.Int32(num)
	playerCellNum := len(wld.GetPlayerOwnCells(uid))
	if playerCellNum >= int(cellNum) {
		return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
	}
	size := ut.MinInt32(ut.Int32(math.Ceil(math.Sqrt(float64(cellNum)))), 20)
	indexList := helper.GetManhattanPoints(mainCityIndex, size, room.GetMapSize())
	var curNum int32
	for _, index := range indexList {
		if curNum >= cellNum {
			break
		}
		cell := wld.GetCell(index)
		if cell != nil && cell.Owner != "" {
			continue
		}
		wld.DirectOccupyCell(index, uid, true)
		curNum++
	}

	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 修改联盟公告
func (this *Game) httpChangeAlliNotice(sid, uid, notice string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	world := room.GetWorld().(*world.Model)
	alli := world.GetAlliance(uid)
	if alli == nil {
		return slg.HttpResponseErrorNoDataWithDesc("联盟不存在!"), nil
	}
	alli.Notice = notice
	alli.LastEditNoticeTime = time.Now().UnixMilli()
	// 通知玩家公告变更
	room.PutNotifyAllPlayersQueue("game/OnAllianceChangeNotice", pb.Bytes(&pb.GAME_ONALLIANCECHANGENOTICE_NOTIFY{Notice: alli.Notice}), alli.GetMemberUids())
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 获取市场汇率
func (this *Game) httpGetBazaarPrices(sid string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	list := room.GetBazaar().TradePriceToPb()
	return slg.HttpResponseSuccessWithDataNoDesc(list), nil
}

// 修改市场汇率
func (this *Game) httpModifyTradePrice(sid, sellType, buyType, price string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	_price := ut.Float64(price)
	if _price <= 0 {
		return slg.HttpResponseErrorNoDataWithDesc("汇率错误!"), nil
	}
	room.GetBazaar().ModifyTradePrice(ut.Int32(sellType), ut.Int32(buyType), _price)
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 获取联盟科技槽位参数
func (this *Game) httpGetAlliCeriParams(sid string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	params := []int32{constant.ALLI_POLICY_SLOT_CONF[0], constant.ALLI_POLICY_SLOT_CONF[1], constant.ALLI_POLICY_SLOT_CONF[2]}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"params": params,
	}), nil
}

// 设置联盟科技槽位参数
func (this *Game) httpSetAlliCeriParams(sid, params string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	paramsArr := strings.Split(params, ",")
	for i, v := range paramsArr {
		constant.ALLI_POLICY_SLOT_CONF[int32(i)] = ut.Int32(v)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 重置英雄供奉槽位
func (this *Game) httpResetHeroSlots(sid, uid, index string) (map[string]interface{}, error) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil || room.IsClose() {
		return slg.HttpResponseErrorNoDataWithDesc("区服不存在!"), nil
	}
	resetIndex := ut.Int32(index)
	heroSlots := room.GetWorld().GetPlayerHeroSlots(uid)
	for i, m := range heroSlots {
		if int32(i) == resetIndex || resetIndex == -1 {
			m.Hero = nil
			m.AvatarArmyUID = ""
			m.DieTime = 0
		}
	}
	return slg.HttpResponseSuccessNoDataWithDesc("重置成功!"), nil
}
