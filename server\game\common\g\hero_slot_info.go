package g

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
)

var HERO_REVIVES_TIME = ut.TIME_HOUR * 5 // 英雄复活剩余时间

// 英雄一个槽位信息
type HeroSlotInfo struct {
	Lv            int            // 需要等级
	Hero          *PortrayalInfo // 英雄
	DieTime       int64          // 阵亡时间
	AvatarArmyUID string         // 化身的军队uid
}

func (this *HeroSlotInfo) FromHeroJson(data interface{}) {
	if data == nil {
		return
	} else if json, ok := data.(map[string]interface{}); ok {
		this.Hero = NewPortrayalByJson(json)
	}
}

func (this *HeroSlotInfo) GetHeroJson() map[string]interface{} {
	if this.Hero == nil {
		return nil
	}
	return this.Hero.ToJson()
}

func (this *HeroSlotInfo) GetHeroPb() *pb.PortrayalInfo {
	if this.Hero == nil {
		return nil
	}
	return this.Hero.ToPbForPawn()
}

func (this *HeroSlotInfo) ToPb() *pb.HeroSlotInfo {
	return &pb.HeroSlotInfo{
		Lv:                int32(this.Lv),
		Hero:              this.GetHeroPb(),
		ReviveSurplusTime: int64(this.GetReviveSurplusTime()),
		AvatarArmyUID:     this.AvatarArmyUID,
	}
}

func (this *HeroSlotInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"hero":          this.GetHeroJson(),
		"dieTime":       this.DieTime,
		"avatarArmyUID": this.AvatarArmyUID,
	}
}

// 重置
func (this *HeroSlotInfo) Reset() {
	this.Hero = nil
	this.AvatarArmyUID = ""
	this.DieTime = 0
}

// 获取英雄id
func (this *HeroSlotInfo) GetHeroID() int32 {
	if this.Hero != nil {
		return this.Hero.ID
	}
	return 0
}

// 获取复活剩余时间
func (this *HeroSlotInfo) GetReviveSurplusTime() int {
	if this.DieTime > 0 {
		return ut.Max(0, HERO_REVIVES_TIME-int(ut.Now()-this.DieTime))
	}
	return 0
}

// 是否阵亡
func (this *HeroSlotInfo) IsDie() bool {
	return this.GetReviveSurplusTime() > 0
}
