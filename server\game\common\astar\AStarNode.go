package astar

import (
	ut "slgsrv/utils"
	"sync"
)

// 一个节点
type AStarNode struct {
	uid    string
	point  *ut.Vec2
	parent *AStarNode
	dir    *ut.Vec2 //相对上一个格子的方向
	F      int32
	G      int32
	H      int32
	T      int32 //拐弯次数
	P      int32 //是否有人
	SP     int32 //总人数
	CP     int32 //连续的人数
}

// AStarNode对象池
var aStarNodePool = sync.Pool{
	New: func() interface{} { return &AStarNode{} },
}

func NewAStartNode() *AStarNode {
	return aStarNodePool.Get().(*AStarNode)
}

// 清理对象 并放回对象池
func (this *AStarNode) Clean() {
	this.uid = ""
	this.point = nil
	this.parent = nil
	this.dir = nil
	this.F = 0
	this.G = 0
	this.H = 0
	this.T = 0
	this.P = 0
	this.SP = 0
	this.CP = 0
	aStarNodePool.Put(this)
}

func (this *AStarNode) ID() string {
	return this.uid
}

func (this *AStarNode) Init(x, y int32) *AStarNode {
	this.uid = ut.Itoa(x) + "_" + ut.Itoa(y)
	this.point = ut.NewVec2(x, y)
	this.dir = ut.NewVec2(0, 0)
	this.parent = nil
	this.F = 0
	this.G = 0
	this.H = 0
	this.T = 0
	this.P = 0
	this.CP = 0
	return this
}

func (this *AStarNode) Has(x, y int32) bool {
	return this.point.X == x && this.point.Y == y
}

func (this *AStarNode) UpdateParent(node *AStarNode, tag int32, p int32) {
	this.parent = node
	this.dir = this.point.Sub(node.point)
	this.T = node.T
	if !node.dir.Equal2(0, 0) && !node.dir.Equals(this.dir) {
		this.T += 1 //说明转弯了
	}
	if p > 0 {
		this.CP += p
	} else {
		this.CP = 0
	}
	this.P = p
	this.SP = node.SP + p
	this.G = node.G + tag
	this.F = this.H + this.G + this.T + this.SP
}
