package db

import (
	"reflect"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"
	"sync"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// MongoTable  mongo 表定义
type MongoTable interface {
	GetTableDef() *TableDef // 获取表名定义
}

var newTableManagerOnceLock sync.Once
var tableManager *TableManager

// GlobalGetTableManager 初始化单例tableManager
func GlobalGetTableManager() *TableManager {
	newTableManagerOnceLock.Do(func() {
		tableManager = &TableManager{
			mod: make(map[string]MongoTable),
			ok:  true,
		}
	})
	return tableManager
}

type TableManager struct {
	mod map[string]MongoTable
	//client   *mongo.Client
	//database string
	ok bool //是否初始化完成
}

// GetCollection 获取db连接对象 千万要记得必须实现MongoTable接口  不然会引起panic
func (this *TableManager) GetCollection(table MongoTable) *mongo.Collection {
	def := table.GetTableDef()
	if def == nil || !this.ok {
		log.Error("GetCollection Error, maybe table name err or manager is not init end.[%s|%t]", reflect.TypeOf(table), this.ok)
		// 这里必须要panic
		panic("GetCollection Error")
	}
	collection := mgo.GetCollection(def.Get())
	// 创建索引
	def.CreateIndex(collection)
	return collection
}

// Init 初始化连接 这是列车的db中心初始化
//func (this *TableManager) Init(url, dbname string) {
//	if this.ok {
//		return
//	}
//	log.Info("Try to connect mongo db, wait for response.")
//	this.database = dbname
//	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
//	defer cancel()
//	opt := options.Client().ApplyURI(url)
//	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
//	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
//	opt.SetMaxPoolSize(1024)                //使用最大的连接数
//	var err error
//	if this.client, err = mongo.Connect(ctx, opt); err == nil {
//		err = this.client.Ping(context.Background(), nil)
//		if err != nil {
//			log.Error("Connect mongo db err :%s , System exit.", err.Error())
//			os.Exit(-1)
//		}
//		log.Info("mongodb init success! " + url + "[" + this.database + "]")
//		tableManager.ok = true
//	} else if err == mongo.ErrNoDocuments {
//		log.Error("mongodb init error! ErrNoDocuments")
//	} else {
//		log.Error(err.Error())
//	}
//
//}

// FieldToBsonAuto 获取一个基于obj对象生成的bson.M用来做db保存,需要设置tag:`bson:xxx`
func FieldToBsonAuto(obj interface{}) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}
	t := v.Type()
	r := bson.M{}
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("bson")
		if ut.IsEmpty(tag) {
			tag = field.Tag.Get("json")
		}
		if ut.IsEmpty(tag) || tag == "-" {
			continue // 跳过
		}
		val := reflect.ValueOf(v.Interface()).Field(i)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Debug("Save field :%s, instance :% s", field, t)
			if !val.Elem().IsValid() {
				continue
			}
			r[tag] = val.Elem().Interface()
			continue
		}
		r[tag] = val.Interface()
	}
	return r
}

// FieldToBson 获取一个基于传入的fields列表生成的bson.M，用于文档操作,所有fields列表的字段必须都存在于obj上,需要设置tag:`bson:xxx`
func FieldToBson(obj interface{}, fields ...string) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}

	t := v.Type()
	r := bson.M{}
	for _, field := range fields {
		rf, b := t.FieldByName(field)
		if !b {
			log.Warning("Skip.none field :%s, instance :% s", field, t)
			continue
		}
		bson := rf.Tag.Get("bson")
		if bson == "" {
			log.Warning("Skip.field has no bson tag:%s, instance :% s", field, t)
			continue
		}
		val := reflect.ValueOf(v.Interface()).FieldByName(field)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Warning("Skip.field type error:%s, instance :% s", field, t)
			r[bson] = val.Elem().Interface()
			continue
		}
		r[bson] = val.Interface()
	}
	return r
}
