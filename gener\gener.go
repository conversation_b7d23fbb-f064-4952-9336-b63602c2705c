package gener

import (
	"context"
	"fmt"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/constant"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"
	"time"

	"github.com/huyangv/vmqant/log"
)

func GenerRoom(sid int32, roomType, subType uint8, mapId int32, state int8, filePath string, winCond []int32) error {
	oldClient := mgo.Client
	oldDatabase := mgo.Database
	defer func() {
		// 还原数据库连接
		if oldClient != nil && oldDatabase != "" {
			mgo.Client = oldClient
			mgo.Database = oldDatabase
		}
	}()
	fmt.Println("Init Mongodb")
	switch slg.SERVER_AREA {
	case slg.SERVER_AREA_HK:
		mgo.Init("************************************************************************************", "slgsrv", "game") //香港
	case slg.SERVER_AREA_CHINA:
		mgo.Init("*************************************************************************************", "slgsrv", "game") //国内
	case slg.SERVER_AREA_US:
		mgo.Init("************************************************************************************", "slgsrv", "game") //美服
	case "test":
		mgo.Init("mongodb://slg_test:<EMAIL>:27017/slgsrv_test?w=majority", "slgsrv_test", "game") //测试服
	case "singapore":
		mgo.Init("******************************************************************", "slgsrv_test", "development") //测试服-新加坡
	case "local":
		mgo.Init("mongodb://127.0.0.1:27017/slgsrv?w=majority", "slgsrv", "game") //本地
	}
	now := time.Now().UnixMilli()
	id := ut.String(mapId)
	mainCitys := LoadMainCitys(id, filePath)
	size := ut.NewVec2(600, 600)
	// windCond := []int{1, 30000, 12, ut.TIME_DAY * 1} //领地争夺
	// windCond := []int{2, 20, 12, ut.TIME_DAY * 1} //修建遗迹
	staminas := []int32{100, 50}
	if roomType == 0 && subType == 1 {
		staminas = []int32{200, 150} //自由区
	}
	farmType := constant.FARM_TYPE_NORMAL // 默认正常模式
	if roomType == slg.ROOKIE_SERVER_TYPE {
		// 新手区开荒默认保护模式
		farmType = constant.FARM_TYPE_PROTECTION
	}
	fmt.Println("Create room id=", sid)
	roomData := r.RoomTableData{
		Id:        sid,
		Type:      roomType,
		SubType:   subType,
		InitTime:  now,
		MainCitys: mainCitys,
		MapSize:   size,
		WinCond:   winCond,
		State:     state,
		ApplyUids: [][]string{},
		Staminas:  staminas,
		MapId:     mapId,
		FarmType:  int32(farmType),
	}
	if state == slg.SERVER_STATUS_APPLYING {
		roomData.ApplyUids = [][]string{}
		for i := 0; i < 4; i++ {
			roomData.ApplyUids = append(roomData.ApplyUids, []string{})
		}
	}
	if _, e := mgo.GetCollection("room").InsertOne(context.TODO(), roomData); e != nil {
		fmt.Println(e.Error())
		return e
	}
	log.Info("Create world_%v path: %v, mapId: %v", sid, filePath, mapId)
	maps := LoadMapsConf(id, filePath)
	r.InitRoom(sid, roomType, subType, now, size, winCond, staminas, mainCitys, maps, mapId)
	log.Info("Create room done !!!")
	return nil
}

func LoadMapsConf(id, filePath string) []int32 {
	v := []interface{}{}
	datas := []int32{}
	if err := ut.LoadJsonByPath("maps_"+id, filePath, &v); err == "" {
		for _, data := range v {
			datas = append(datas, ut.Int32(data))
		}
	}
	return datas
}

func LoadMainCitys(id, filePath string) [][]int32 {
	v := []interface{}{}
	datas := [][]int32{}
	if err := ut.LoadJsonByPath("mainCitys_"+id, filePath, &v); err == "" {
		for _, data := range v {
			datas = append(datas, ut.Int32Array(data))
		}
	} else {
		fmt.Println(err)
	}
	return datas
}
