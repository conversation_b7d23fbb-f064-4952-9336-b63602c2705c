package match

import (
	"context"
	slg "slgsrv/server/common"
	"slgsrv/server/lobby"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 队伍表
var teamDb = &Mongodb{slg.DB_COLLECTION_NAME_TEAM}

// 获取所有该区服类型在报名中的队伍
func (this *Mongodb) FindAllMatchTeam(roomType uint8) (arr []*MatchTeam, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{
		"room_type":       roomType,
		"apply_time":      bson.M{"$gt": 0},
		"play_sid":        bson.M{"$eq": 0},
		"match_open_time": bson.M{"$eq": 0},
	})
	if err != nil {
		return []*MatchTeam{}, err
	} else if err = cur.Err(); err != nil {
		return []*MatchTeam{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*MatchTeam{}
	for cur.Next(context.TODO()) {
		var elem lobby.TeamInfo
		if err = cur.Decode(&elem); err == nil {
			team := &MatchTeam{
				Uid:      elem.Uid,
				RoomType: elem.RoomType,
				UserList: array.Map(elem.UserList, func(m *lobby.TeamUserInfo, i int) string { return m.Uid }),
			}
			arr = append(arr, team)
		}
	}
	return
}

// 获取指定自定义房间的队伍
func (this *Mongodb) FindTeamsByCustomRoomId(sid int32) (datas []*lobby.TeamInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"custom_room_id": sid})
	defer func() {
		cur.Close(context.TODO())
	}()
	if err = cur.All(context.TODO(), &datas); err != nil {
		log.Error("FindTeamsByCustomRoomId error! err: %v", err)
	}
	return
}

// 房间报名信息表
var applyDb = &Mongodb{slg.DB_COLLECTION_NAME_APPLY_INFO}

// 查询报名信息
func (this *Mongodb) FindApplyInfo(roomType uint8) (data ApplyInfo, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{"room_type": roomType}).Decode(&data)
	return
}

// 更新报名信息
func (this *Mongodb) UpdateApplyInfo(applyInfo *ApplyInfo) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"room_type": applyInfo.RoomType}, bson.M{"$set": applyInfo}, options.Update().SetUpsert(true))
	return
}

// 获取所有房间报名信息
func (this *Mongodb) FindAllApplyInfo() (arr []*ApplyInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{})
	if err != nil {
		return []*ApplyInfo{}, err
	} else if err = cur.Err(); err != nil {
		return []*ApplyInfo{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*ApplyInfo{}
	for cur.Next(context.TODO()) {
		var elem ApplyInfo
		if err = cur.Decode(&elem); err == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// GM参数记录表
var gmMapDb = &Mongodb{slg.DB_COLLECTION_NAME_GM_MAP}

type GmMapCol[V string | int | int32 | int64] struct {
	Key string `bson:"key"`
	Val V      `bson:"val"`
}

// 根据区服类型获取自增sid
func (this *Mongodb) GetGenSidByRoomType(roomType, subType uint8, isPre bool) (sid int32, err error) {
	data := &GmMapCol[int32]{}
	keyType := subType*(slg.ROOM_TYPE_FLAG/slg.ROOM_SUB_TYPE_FLAT) + roomType
	key := "genSid" + ut.String(keyType)
	if isPre {
		key = "preGenSid" + ut.String(keyType)
	}
	if err = this.getCollection().FindOne(context.TODO(), bson.M{"key": key}).Decode(&data); err != nil {
		return int32(roomType)*slg.ROOM_TYPE_FLAG + int32(subType)*slg.ROOM_SUB_TYPE_FLAT + 1, err
	}
	sid = data.Val
	return
}

// 更新自增id
func (this *Mongodb) UpdateGenSid(keyType, sid int32, isPre bool) (err error) {
	data := &GmMapCol[int32]{}
	key := "genSid" + ut.String(keyType)
	if isPre {
		key = "preGenSid" + ut.String(keyType)
	}
	data.Key = key
	data.Val = sid
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"key": key}, bson.M{"$set": data}, options.Update().SetUpsert(true))
	if err != nil {
		log.Warning("UpdateGenSid sid: %v, err: %v", sid, err)
	}
	return
}

// 获取开服间隔时间
func (this *Mongodb) GetMatchIntervalTime() (time int, err error) {
	key := "matchIntervalTime"
	data := &GmMapCol[int]{}
	err = this.getCollection().FindOne(context.TODO(), bson.M{"key": key}).Decode(&data)
	time = data.Val
	return
}

// 设置开服间隔时间
func (this *Mongodb) SetMatchIntervalTime(time int) (err error) {
	key := "matchIntervalTime"
	data := &GmMapCol[int]{
		Key: key,
		Val: time,
	}
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"key": key}, bson.M{"$set": data}, options.Update().SetUpsert(true))
	return
}

// 自定义房间信息表
var customRoomDb = &Mongodb{slg.DB_COLLECTION_NAME_CUSTOM_ROOM}

// 获取所有未开服的自定义房间
func (this *Mongodb) FindAllReadyCustomRooms() (datas []*CustomRoom, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"opened": false})
	defer func() {
		cur.Close(context.TODO())
	}()
	if err = cur.All(context.TODO(), &datas); err != nil {
		log.Error("FindAllReadyCustomRooms error! err: %v", err)
	}
	return
}

// 更新自定义房间
func (this *Mongodb) UpdateCustomRoom(customRoom *CustomRoom) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), bson.M{"sid": customRoom.Sid}, customRoom, options.Update().SetUpsert(true))
	return
}
