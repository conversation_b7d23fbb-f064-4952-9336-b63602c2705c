package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	cs "slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type DrillPawnList struct {
	deadlock.RWMutex
	List []int32
}

type CuringPawnList struct {
	deadlock.RWMutex
	List []*pb.InjuryPawnInfo
}

type PawnList struct {
	deadlock.RWMutex
	List []*AreaPawn
}

// 军队
type AreaArmy struct {
	Uid         string
	Name        string          //名字
	Owner       string          //拥有者
	Pawns       *PawnList       //士兵列表
	DrillPawns  *DrillPawnList  //招募的士兵列表
	CuringPawns *CuringPawnList //治疗中的士兵列表

	AutoBackIndex      int32 //战斗结束后自动返回位置
	AIndex             int32 //所在区域位置
	EnterDir           int32 //进入方向
	MarchSpeed         int32 //行军速度
	state              int8  //状态
	ModifiedMarchSpeed bool  //是否修改过行军速度
}

func NewAreaArmy(index int32, uid string, owner string) *AreaArmy {
	return &AreaArmy{
		AIndex:        index,
		EnterDir:      -1,
		Uid:           uid,
		Owner:         owner,
		DrillPawns:    &DrillPawnList{List: []int32{}},
		CuringPawns:   &CuringPawnList{List: []*pb.InjuryPawnInfo{}},
		AutoBackIndex: -1,
	}
}

func (this *AreaArmy) Init(name string) *AreaArmy {
	this.Name = name
	this.Pawns = &PawnList{List: []*AreaPawn{}}
	this.state = 0
	return this
}

func (this *AreaArmy) FromData(data map[string]interface{}, wld g.World) *AreaArmy {
	this.Pawns = &PawnList{List: []*AreaPawn{}}
	arr := data["pawns"].([]map[string]interface{})
	for _, m := range arr {
		this.Pawns.List = append(this.Pawns.List, NewAreaPawn(this.AIndex, this.EnterDir, ut.String(m["uid"]), ut.NewVec2ByObj(m["point"]), ut.Int32(m["id"]), this.Owner, this.Uid, wld).FromData(m))
	}
	return this
}

func (this *AreaArmy) FromPbData(data *pb.AreaArmyInfo, wld g.World) *AreaArmy {
	this.Pawns = &PawnList{List: []*AreaPawn{}}
	for _, m := range data.Pawns {
		point := &ut.Vec2{X: m.Point.X, Y: m.Point.Y}
		this.Pawns.List = append(this.Pawns.List, NewAreaPawn(this.AIndex, this.EnterDir, m.Uid, point, m.Id, this.Owner, this.Uid, wld).FromPbData(m))
	}
	return this
}

func (this *AreaArmy) FromDB(data map[string]interface{}, wld g.World) *AreaArmy {
	this.Name = ut.String(data["name"])
	this.EnterDir = ut.Int32(data["enterDir"])
	this.AutoBackIndex = ut.If(data["autoBackIndex"] == nil, -1, ut.Int32(data["autoBackIndex"]))
	this.Pawns = &PawnList{List: this.FromPawnsDB(data["pawns"], wld)}
	this.MarchSpeed = ut.Int32(data["marchSpeed"])
	this.ModifiedMarchSpeed = ut.Bool(data["modifiedMarchSpeed"])
	return this
}

func (this *AreaArmy) ToDB() map[string]interface{} {
	data := map[string]interface{}{
		"enterDir":   this.EnterDir,
		"uid":        this.Uid,
		"name":       this.Name,
		"owner":      this.Owner,
		"pawns":      this.ToPawnsDB(),
		"marchSpeed": this.MarchSpeed,
	}
	if this.ModifiedMarchSpeed {
		data["modifiedMarchSpeed"] = this.ModifiedMarchSpeed
	}
	if this.AutoBackIndex != -1 {
		data["autoBackIndex"] = this.AutoBackIndex
	}
	return data
}

func (this *AreaArmy) ToPb(state int) *pb.AreaArmyInfo {
	ret := &pb.AreaArmyInfo{
		Index:      int32(this.AIndex),
		EnterDir:   int32(this.EnterDir),
		Uid:        this.Uid,
		Name:       this.Name,
		Owner:      this.Owner,
		Pawns:      this.ToPawnsDataPb(),
		State:      int32(state),
		MarchSpeed: int32(this.MarchSpeed),
	}
	this.DrillPawns.RLock()
	ret.DrillPawns = array.Clone(this.DrillPawns.List)
	this.DrillPawns.RUnlock()
	this.CuringPawns.RLock()
	ret.CuringPawns = array.Clone(this.CuringPawns.List)
	this.CuringPawns.RUnlock()
	return ret
}

func (this *AreaArmy) Strip2(state int) *g.ArmyStrip2 {
	return &g.ArmyStrip2{
		Index:    this.AIndex,
		Uid:      this.Uid,
		Name:     this.Name,
		Owner:    this.Owner,
		Pawns:    this.ToPawnsStrip2(),
		State:    int32(state),
		EnterDir: this.EnterDir,
	}
}

// func (this *AreaArmy) Strip2(state int) map[string]interface{} {
// 	return map[string]interface{}{
// 		"index":    this.AIndex,
// 		"uid":      this.Uid,
// 		"name":     this.Name,
// 		"owner":    this.Owner,
// 		"pawns":    this.ToPawnsStrip2(),
// 		"state":    state,
// 		"enterDir": this.EnterDir,
// 	}
// }

func (this *AreaArmy) ToBaseDataPb(state int) *pb.AreaArmyInfo {
	return &pb.AreaArmyInfo{
		Uid:   this.Uid,
		Name:  this.Name,
		State: int32(state),
		Pawns: this.ToPawnsBaseDataPb(),
	}
}

func (this *AreaArmy) ToShortDataPb(state int) *pb.AreaArmyInfo {
	ret := &pb.AreaArmyInfo{
		Index:      int32(this.AIndex),
		Uid:        this.Uid,
		Name:       this.Name,
		Pawns:      this.ToPawnsShortDataPb(),
		State:      int32(state),
		MarchSpeed: int32(this.MarchSpeed),
	}
	this.DrillPawns.RLock()
	ret.DrillPawns = array.Clone(this.DrillPawns.List)
	this.DrillPawns.RUnlock()
	this.CuringPawns.RLock()
	ret.CuringPawns = array.Clone(this.CuringPawns.List)
	this.CuringPawns.RUnlock()
	return ret
}

func (this *AreaArmy) FromPawnsDB(data interface{}, wld g.World) []*AreaPawn {
	arr := []*AreaPawn{}
	uids := map[string]bool{}
	if armys, ok := data.(primitive.A); ok {
		for _, val := range armys {
			m := val.(map[string]interface{})
			uid := ut.String(m["uid"])
			if !uids[uid] {
				uids[uid] = true
				arr = append(arr, NewAreaPawn(this.AIndex, this.EnterDir, uid, ut.NewVec2ByObj(m["point"]), ut.Int32(m["id"]), this.Owner, this.Uid, wld).FromDB(m))
			}
		}
	}
	return arr
}

func (this *AreaArmy) ToPawnsDB() []map[string]interface{} {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return array.Map(this.Pawns.List, func(m *AreaPawn, _ int) map[string]interface{} { return m.ToDB() })
}

func (this *AreaArmy) GetPawnsClone() []*AreaPawn {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return this.Pawns.List[:]
}

func (this *AreaArmy) ToPawnsStrip2() []*g.PawnStrip2 {
	return array.Map(this.GetPawnsClone(), func(m *AreaPawn, _ int) *g.PawnStrip2 { return m.Strip2() })
}

func (this *AreaArmy) ToPawnsBaseDataPb() []*pb.AreaPawnInfo {
	return array.Map(this.GetPawnsClone(), func(m *AreaPawn, _ int) *pb.AreaPawnInfo { return m.ToBaseDataPb() })
}

func (this *AreaArmy) ToPawnsShortDataPb() []*pb.AreaPawnInfo {
	return array.Map(this.GetPawnsClone(), func(m *AreaPawn, _ int) *pb.AreaPawnInfo { return m.ToShortDataPb() })
}

func (this *AreaArmy) ToPawnsDataPb() []*pb.AreaPawnInfo {
	return array.Map(this.GetPawnsClone(), func(m *AreaPawn, _ int) *pb.AreaPawnInfo { return m.ToPb() })
}

// 改变所在区域
func (this *AreaArmy) ChangeAIndex(val, dir int32) {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	this.AIndex = val
	this.EnterDir = dir
	this.state = constant.AS_NONE
	for _, m := range this.Pawns.List {
		m.AIndex = val
		m.EnterDir = dir
		m.CleanAllBuffs()
		m.InitAnger()
	}
}

func (this *AreaArmy) GetPawnCount() int32 {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return int32(len(this.Pawns.List))
}

func (this *AreaArmy) GetDrillPawnCount() int32 {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return int32(len(this.DrillPawns.List))
}

func (this *AreaArmy) GetCuringawnCount() int32 {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return int32(len(this.CuringPawns.List))
}

// 当前实际士兵数量 加上招募和治疗中的
func (this *AreaArmy) GetActPawnCount() int32 {
	return this.GetPawnCount() + this.GetDrillPawnCount() + this.GetCuringawnCount()
}

// 队伍是否已满
func (this *AreaArmy) IsFull() bool {
	return this.GetActPawnCount() >= constant.ARMY_PAWN_MAX_COUNT
}

// 是否招募中
func (this *AreaArmy) IsPawnDrilling() bool {
	return this.GetDrillPawnCount() > 0
}

// 是否治疗中
func (this *AreaArmy) IsPawnCuring() bool {
	return this.GetCuringawnCount() > 0
}

// 是否战斗中
func (this *AreaArmy) IsFighting() bool {
	return this.state == constant.AS_FIGHT
}

// 准备战斗
func (this *AreaArmy) ReadyBattle() {
	this.state = constant.AS_FIGHT
}

// 结束战斗
func (this *AreaArmy) EndBattle() {
	this.state = constant.AS_NONE
}

// 开始屯田
func (this *AreaArmy) StartCellTonden() {
	this.state = constant.AS_TONDEN
}

// 是否屯田中
func (this *AreaArmy) IsCellTonden() bool {
	return this.state == constant.AS_TONDEN
}

// 结束屯田
func (this *AreaArmy) EndCellTonden() {
	this.state = constant.AS_NONE
}

// 获取士兵
func (this *AreaArmy) GetPawnByUID(uid string) *AreaPawn {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	if pawn := array.Find(this.Pawns.List, func(m *AreaPawn) bool { return m.Uid == uid }); pawn != nil {
		return pawn
	}
	return nil
}

func (this *AreaArmy) GetPawnByPoint(point *ut.Vec2) *AreaPawn {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	if pawn := array.Find(this.Pawns.List, func(m *AreaPawn) bool { return m.Point.Equals(point) }); pawn != nil {
		return pawn
	}
	return nil
}

// 添加士兵
func (this *AreaArmy) AddPawn(pawn *AreaPawn) {
	pawn.ArmyUid = this.Uid
	pawn.AIndex = this.AIndex
	pawn.EnterDir = this.EnterDir
	this.Pawns.Lock()
	if !array.Some(this.Pawns.List, func(m *AreaPawn) bool { return m.Uid == pawn.Uid }) {
		this.Pawns.List = append(this.Pawns.List, pawn)
	}
	this.Pawns.Unlock()
	if pawn.IsHero() {
		pawn.wld.UpdatePlayerHeroArmy(this.Owner, pawn.Portrayal.ID, this.Uid)
	}
	// 刷新行军速度
	this.UpdateAmryMarchSpeed()
}

// 删除士兵
func (this *AreaArmy) RemovePawn(uid string) *AreaPawn {
	var pawn *AreaPawn = nil
	this.Pawns.Lock()
	for i := len(this.Pawns.List) - 1; i >= 0; i-- {
		if this.Pawns.List[i].Uid == uid {
			pawn = this.Pawns.List[i]
			this.Pawns.List = append(this.Pawns.List[:i], this.Pawns.List[i+1:]...)
			break
		}
	}
	this.Pawns.Unlock()
	// 刷新行军速度
	this.UpdateAmryMarchSpeed()
	return pawn
}

// 添加招募士兵
func (this *AreaArmy) AddDrillPawn(id int32) {
	this.DrillPawns.Lock()
	defer this.DrillPawns.Unlock()
	this.DrillPawns.List = append(this.DrillPawns.List, id)
}

func (this *AreaArmy) RemoveDrillPawn(id int32) {
	this.DrillPawns.Lock()
	defer this.DrillPawns.Unlock()
	this.DrillPawns.List = array.Remove(this.DrillPawns.List, id)
}

// 添加治疗士兵
func (this *AreaArmy) AddCuringPawn(info *pb.InjuryPawnInfo) {
	this.CuringPawns.Lock()
	defer this.CuringPawns.Unlock()
	this.CuringPawns.List = append(this.CuringPawns.List, info)
}

func (this *AreaArmy) RemoveCuringPawn(uid string) {
	this.CuringPawns.Lock()
	defer this.CuringPawns.Unlock()
	this.CuringPawns.List = array.RemoveItem(this.CuringPawns.List, func(m *pb.InjuryPawnInfo) bool { return m.Uid == uid })
}

func (this *AreaArmy) CleanDrillAndCuringPawn() {
	this.DrillPawns.Lock()
	this.DrillPawns.List = []int32{}
	this.DrillPawns.Unlock()
	this.CuringPawns.Lock()
	this.CuringPawns.List = []*pb.InjuryPawnInfo{}
	this.CuringPawns.Unlock()
}

// 获取所有士兵的粮耗
func (this *AreaArmy) GetAllPawnCerealConsume() float64 {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	count := 0.0
	for _, m := range this.Pawns.List {
		count += m.GetCerealCost()
	}
	return count
}

// 刷新行军速度
func (this *AreaArmy) UpdateAmryMarchSpeed() {
	pawnSpeed, _ := this.GetPawnByMinMarchSpeed()
	if this.MarchSpeed > pawnSpeed {
		this.MarchSpeed = pawnSpeed
		this.ModifiedMarchSpeed = false
	} else if !this.ModifiedMarchSpeed {
		this.MarchSpeed = pawnSpeed
	}
}

// 获取行军速度最慢的 速度
func (this *AreaArmy) GetPawnByMinMarchSpeed() (pawnSpeed, roleId int32) {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	var heroId int32
	for _, m := range this.Pawns.List {
		if m.IsHero() {
			heroId = m.Portrayal.ID
			roleId = heroId
		}
		if pawnSpeed == 0 || m.marchSpeed < pawnSpeed || (m.marchSpeed == pawnSpeed && m.SkinId > 0) {
			pawnSpeed = m.marchSpeed
			if heroId == 0 {
				roleId = ut.If(m.SkinId > 0, m.SkinId, m.Id)
			}
		}
	}
	return
}

// 获取行军速度和roleid
func (this *AreaArmy) GetMarchSpeedAndRoleID() (int32, int32) {
	pawnSpeed, roleId := this.GetPawnByMinMarchSpeed()
	if this.MarchSpeed <= 0 {
		this.MarchSpeed = pawnSpeed
	}
	armySpeed := ut.ClampInt32(this.MarchSpeed, constant.CAN_MIN_MARCH_SPEED, pawnSpeed)
	if armySpeed != this.MarchSpeed {
		this.MarchSpeed = armySpeed
	}
	return armySpeed, roleId
}

// 设置士兵位置 根据顺序
func (this *AreaArmy) SetPawnPoints(points []*ut.Vec2) {
	pLen := len(points)
	if pLen == 0 {
		return
	}
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	for i, pawn := range this.Pawns.List {
		if i < pLen {
			pawn.Point.Set(points[i])
		} else {
			pawn.Point.Set(points[0])
		}
	}
}

// 刷新士兵的装备属性
func (this *AreaArmy) UpdatePawnEquipAttr(euid string, attrs [][]int32, recover bool) bool {
	if this.state == cs.AS_FIGHT {
		return false
	}
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	has := false
	for _, p := range this.Pawns.List {
		if p.UpdateEquipAttr(euid, attrs, recover) {
			has = true
		}
	}
	return has
}

// 刷新士兵的英雄信息
func (this *AreaArmy) UpdatePawnHeroAttr(id int32, attrs [][]int32, recover bool) bool {
	if this.state == cs.AS_FIGHT {
		return false
	}
	has := false
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	for _, p := range this.Pawns.List {
		if p.UpdateHeroAttr(id, attrs, recover) {
			has = true
		}
	}
	return has
}

// 恢复所有士兵血量
func (this *AreaArmy) RecoverAllPawn() {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	for _, p := range this.Pawns.List {
		p.RecoverHP()
	}
}

// 获取所有宝箱
func (this *AreaArmy) GetTreasures() []*g.TreasureInfo {
	treasures := []*g.TreasureInfo{}
	list := this.GetPawnsClone()
	for _, p := range list {
		p.treasureMutex.RLock()
		treasures = append(treasures, p.Treasures...)
		p.treasureMutex.RUnlock()
	}
	return treasures
}

// 是否有英雄
func (this *AreaArmy) HasHero() bool {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	return array.Some(this.Pawns.List, func(m *AreaPawn) bool { return m.IsHero() })
}

// 获取英雄
func (this *AreaArmy) GetHero() *g.PortrayalInfo {
	this.Pawns.RLock()
	defer this.Pawns.RUnlock()
	if pawn := array.Find(this.Pawns.List, func(m *AreaPawn) bool { return m.IsHero() }); pawn != nil {
		return pawn.Portrayal
	}
	return nil
}
