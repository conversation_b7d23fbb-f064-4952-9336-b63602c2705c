package world

import (
	"fmt"
	"maps"
	"strconv"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

/**
维护玩家的临时信息
*/

type TempPlayer struct {
	Uid          string
	Nickname     string
	HeadIcon     string
	PersonalDesc string // 个人简介
	DistinctId   string // 访客id
	AllianceUid  string // 所在联盟
	language     string // 使用语言
	FCMToken     string // FCM通知令牌

	PolicySlots        *g.CeriSlotMap                   // 当前政策槽位map
	ConfigPawnMap      map[int32]*g.PawnConfigInfo      // 士兵配置信息
	LastUseCitySkinMap *ut.MapLock[int32, int32]        // 最后一次使用的城市皮肤信息
	FortAutoSupports   map[int32]bool                   // 要塞自动支援配置
	TowerLvMap         deadlock.Map                     // 当前玩家的箭塔等级对应士兵id
	Armys              map[string]int32                 // 军队分布列表
	PawnDrillMap       map[int32]int32                  // 累计士兵招募数量map
	PawnMaxLvMap       map[int32]int32                  // 满级士兵数量map
	PolicyUseMap       map[int32]bool                   // 本局使用过政策map
	EquipUseMap        map[int32]bool                   // 本局使用过装备map
	KillRecordMap      *ut.MapLock[int32, int32]        // 记录击杀数量map
	BattleRecordInfo   map[int32]int32                  // 战斗记录信息
	CityOutputMap      *ut.MapLock[int32, []*g.TypeObj] // 城市产出
	InjuryPawns        *InjuryPawnList                  // 医馆受伤士兵列表

	OwnCells      []int32           // 拥有的地块
	ApplyAlliUids []string          // 已经申请的联盟列表
	Equips        []*g.EquipInfo    // 装备列表
	HeroSlots     []*g.HeroSlotInfo // 英雄列表
	offlineOpt    []int32           // 玩家离线通知设置

	armyMutex         *deadlock.RWMutex
	battleRecordMutex *deadlock.RWMutex
	configPawnMutex   *deadlock.RWMutex
	pawnDrillMutex    *deadlock.RWMutex
	policyUseMutex    *deadlock.RWMutex
	equipUseMutex     *deadlock.RWMutex

	CreateTime           int64 // 创建时间
	OfflineTime          int64 // 离线时间 0表示在线
	AttackLastNotifyTime int64 // 上次被攻击通知时间
	ResAcc               int64 // 总经济

	MainCityIndex     int32 // 主城位置
	Title             int32 // 当前佩戴的称号
	MaxLandCount      int32 // 最大拥有的地块数量
	RodeleroCadetLv   int32 // 剑盾兵 见习勇者 层数
	LandScore         int32 // 玩家领地积分
	LandScoreTop      int32 // 玩家领地积分 历史最高
	AlliScore         int32 // 玩家联盟积分
	AlliScoreTop      int32 // 玩家联盟积分 历史最高
	ExtraScore        int32 // 玩家额外积分
	AvoidWarReduce    int32 // 主城免战减少信息
	TreasureLostCount int32 // 宝箱损失补偿次数
	AttackNotifyCount int32 // 被攻击通知次数
	FarmType          int32 // 开荒模式

	IsGiveupGame   bool // 是否放弃本次对局
	IsCapture      bool // 是否沦陷
	IsSettled      bool // 是否结算
	IsSpectate     bool // 是否观战
	NoAvoidWar     bool // 取消免战
	IsNeedUpdateDB bool // 是否更新到数据库
}

func (this *TempPlayer) AddApplyAlli(uid string) {
	if !array.Has(this.ApplyAlliUids, uid) {
		this.ApplyAlliUids = append(this.ApplyAlliUids, uid)
	}
}

func (this *TempPlayer) RemoveApplyAlli(uid string) {
	this.ApplyAlliUids = array.Remove(this.ApplyAlliUids, uid)
}

func (this *TempPlayer) ToTowerLvMap() map[int32]int32 {
	obj := map[int32]int32{}
	this.TowerLvMap.Range(func(key, value any) bool {
		obj[key.(int32)] = value.(int32)
		return true
	})
	return obj
}

func (this *TempPlayer) ToTowerLvMapPb() map[int32]int32 {
	obj := map[int32]int32{}
	this.TowerLvMap.Range(func(key, value any) bool {
		obj[pb.Int32(key)] = pb.Int32(value)
		return true
	})
	return obj
}

func (this *TempPlayer) ToStatistics() map[int32]int32 {
	this.battleRecordMutex.RLock()
	ret := make(map[int32]int32, len(this.BattleRecordInfo))
	for k, v := range this.BattleRecordInfo {
		ret[k] = v
	}
	this.battleRecordMutex.RUnlock()

	this.pawnDrillMutex.RLock()
	// 招募数量
	for k, v := range this.PawnDrillMap {
		ret[bdtype.ACC_RECRUIT_PAWN_COUNT*10000+k] = v
	}
	// 满级士兵数量
	for k, v := range this.PawnMaxLvMap {
		ret[bdtype.MAXLV_PAWN_COUNT*10000+k] = v
	}
	this.pawnDrillMutex.RUnlock()
	ret[bdtype.LAND_COUNT] = int32(len(this.OwnCells))
	ret[bdtype.MAX_LAND_COUNT] = this.MaxLandCount
	return ret
}

// 返回玩家的城市产出
func (this *TempPlayer) ToCityOutputPb() map[int32]*pb.TypeObjList {
	obj := map[int32]*pb.TypeObjList{}
	this.CityOutputMap.ForEach(func(v []*g.TypeObj, k int32) bool {
		obj[k] = &pb.TypeObjList{List: array.Map(v, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() })}
		return true
	})
	return obj
}

func (this *Model) createTempPlayer(plr player.TableData) *TempPlayer {
	tempPly := &TempPlayer{
		Uid:               plr.Uid,
		Nickname:          plr.Nickname,
		HeadIcon:          plr.HeadIcon,
		PersonalDesc:      plr.PersonalDesc,
		CreateTime:        plr.CreateTime,
		OwnCells:          []int32{},
		MainCityIndex:     plr.MainCityIndex,
		Title:             plr.Title,
		AllianceUid:       plr.AllianceUid,
		OfflineTime:       plr.OfflineTime,
		ApplyAlliUids:     []string{},
		PolicySlots:       &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_POLICY, Map: plr.PolicySlots},
		Equips:            initEquips(plr.Equips),
		HeroSlots:         initHeroSlots(plr.HeroSlots),
		ConfigPawnMap:     ut.If(plr.ConfigPawnMap == nil, map[int32]*g.PawnConfigInfo{}, plr.ConfigPawnMap),
		FortAutoSupports:  ut.If(plr.FortAutoSupports == nil, map[int32]bool{}, plr.FortAutoSupports),
		TowerLvMap:        deadlock.Map{},
		RodeleroCadetLv:   plr.RodeleroCadetLv,
		LandScore:         plr.LandScore,
		LandScoreTop:      ut.MaxInt32(plr.LandScoreTop, plr.LandScore),
		AlliScore:         plr.AlliScore,
		AlliScoreTop:      ut.MaxInt32(plr.AlliScoreTop, plr.AlliScore),
		Armys:             map[string]int32{},
		PawnDrillMap:      ut.If(plr.PawnDrillMap == nil, map[int32]int32{}, plr.PawnDrillMap),
		PawnMaxLvMap:      ut.If(plr.PawnMaxLvMap == nil, map[int32]int32{}, plr.PawnMaxLvMap),
		PolicyUseMap:      ut.If(plr.PolicyUseMap == nil, map[int32]bool{}, plr.PolicyUseMap),
		EquipUseMap:       ut.If(plr.EquipUseMap == nil, map[int32]bool{}, plr.EquipUseMap),
		KillRecordMap:     ut.FromMapLock(plr.KillRecordMap),
		MaxLandCount:      plr.MaxLandCount,
		BattleRecordInfo:  ut.If(plr.BattleRecordInfo == nil, map[int32]int32{}, plr.BattleRecordInfo),
		AvoidWarReduce:    plr.AvoidWarReduce,
		IsGiveupGame:      plr.IsGiveupGame,
		FCMToken:          plr.FCMToken,
		language:          plr.Language,
		offlineOpt:        ut.If(plr.OfflineNotifyOtp == nil, []int32{0, 1, 2, 3, 4, 5}, plr.OfflineNotifyOtp),
		CityOutputMap:     initCityOutput(plr.CityOutputMap),
		armyMutex:         new(deadlock.RWMutex),
		battleRecordMutex: new(deadlock.RWMutex),
		configPawnMutex:   new(deadlock.RWMutex),
		pawnDrillMutex:    new(deadlock.RWMutex),
		policyUseMutex:    new(deadlock.RWMutex),
		equipUseMutex:     new(deadlock.RWMutex),
		IsCapture:         plr.CaptureInfo != nil,
		IsSpectate:        false,
		TreasureLostCount: plr.TreasureLostCount,
		NoAvoidWar:        plr.NoAvoidWar,
		InjuryPawns:       &InjuryPawnList{List: plr.InjuryPawnList, AbandomAcc: plr.HospitalAbandomAcc},
		FarmType:          plr.FarmType,
		ResAcc:            plr.ResAcc,
		IsSettled:         plr.IsSettled,
	}
	if tempPly.FarmType == constant.FARM_TYPE_NONE {
		// 未选择开荒方式则使用区服默认开荒方式
		tempPly.FarmType = this.room.GetFarmType()
	}
	if tempPly.PolicySlots.Map == nil {
		tempPly.PolicySlots.Map = map[int32]*g.CeriSlotInfo{}
	}
	this.allTempPlayers.Map[plr.Uid] = tempPly
	// 低于4.0.0版本的区服兼容数据
	if this.room.CheckRoomVersionLower("4.0.0") {
		// 装备数据兼容
		tempPly.EquipInfoFix()
	}
	if tempPly.IsSettled || (this.room.IsConquer() && tempPly.IsGiveupGame) {
		this.settlePlayerNum++ // 已结算人数
	}
	if this.room.IsConquer() && ((tempPly.IsSettled && tempPly.AllianceUid == "") || tempPly.IsGiveupGame) {
		this.FailPlrMap.Set(tempPly.Uid, true) // 已出局玩家map
	}
	return tempPly
}

func (this *Model) CreateSpectateTempPlayer(uid, nickname, headIcon, personalDesc, lang string, title, mainCity int32) {
	this.allTempPlayers.Lock()
	plr := &TempPlayer{
		Uid:               uid,
		Nickname:          nickname,
		HeadIcon:          headIcon,
		PersonalDesc:      personalDesc,
		Title:             title,
		language:          lang,
		OwnCells:          []int32{},
		ApplyAlliUids:     []string{},
		PolicySlots:       &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_POLICY, Map: map[int32]*g.CeriSlotInfo{}},
		Equips:            []*g.EquipInfo{},
		HeroSlots:         []*g.HeroSlotInfo{},
		ConfigPawnMap:     map[int32]*g.PawnConfigInfo{},
		FortAutoSupports:  map[int32]bool{},
		TowerLvMap:        deadlock.Map{},
		Armys:             map[string]int32{},
		PawnDrillMap:      map[int32]int32{},
		PawnMaxLvMap:      map[int32]int32{},
		PolicyUseMap:      map[int32]bool{},
		EquipUseMap:       map[int32]bool{},
		KillRecordMap:     ut.NewMapLock[int32, int32](),
		BattleRecordInfo:  map[int32]int32{},
		offlineOpt:        []int32{0, 1, 2, 3, 4, 5},
		CityOutputMap:     ut.NewMapLock[int32, []*g.TypeObj](),
		armyMutex:         new(deadlock.RWMutex),
		battleRecordMutex: new(deadlock.RWMutex),
		configPawnMutex:   new(deadlock.RWMutex),
		pawnDrillMutex:    new(deadlock.RWMutex),
		policyUseMutex:    new(deadlock.RWMutex),
		equipUseMutex:     new(deadlock.RWMutex),
		IsSpectate:        true,
	}
	this.allTempPlayers.Map[uid] = plr
	this.allTempPlayers.Unlock()
	tempPlr := &pb.TempPlayerInfo{
		Uid:           plr.Uid,
		Nickname:      plr.Nickname,
		HeadIcon:      plr.HeadIcon,
		PersonalDesc:  plr.PersonalDesc,
		Title:         pb.Int32(plr.Title),
		SpectateIndex: mainCity,
	}
	this.SetWorldPbPlayer(plr.Uid, tempPlr)
	// 通知
	this.PutNotifyQueue(constant.NQ_ADD_PLAYER, &pb.OnUpdateWorldInfoNotify{
		Data_71: tempPlr,
	})
}

// 从worldPb中删除tempPlr
func (this *Model) DelWorldPbTempPlayer(uid string) {
	this.allTempPlayers.Lock()
	delete(this.allTempPlayers.Map, uid)
	this.allTempPlayers.Unlock()
	this.DelWorldPbPlayer(uid)
	// 通知
	this.PutNotifyQueue(constant.NQ_DELETE_PLAYER, &pb.OnUpdateWorldInfoNotify{Data_44: uid})
}

func (this *Model) SetPlayerNeedUpdateDB(uid string, val bool) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = val
	}
}

func (this *Model) GetPlayerDataToDB(uid string) (string, string, string, int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Nickname, plr.HeadIcon, plr.PersonalDesc, plr.Title
	}
	return "", "", "", 0
}

func (this *Model) ToHttpTempPlayerStrip(uid string) map[string]interface{} {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return map[string]interface{}{}
	}
	return map[string]interface{}{
		"uid":              plr.Uid,
		"nickname":         plr.Nickname,
		"headIcon":         plr.HeadIcon,
		"personalDesc":     plr.PersonalDesc,
		"distinctId":       plr.DistinctId,
		"ownCells":         plr.OwnCells,
		"mainCityIndex":    plr.MainCityIndex,
		"title":            plr.Title,
		"allianceUid":      plr.AllianceUid,
		"applyAlliUids":    plr.ApplyAlliUids,
		"policys":          plr.PolicySlots.Map,
		"equips":           plr.Equips,
		"configPawnMap":    plr.ConfigPawnMap,
		"fortAutoSupports": plr.FortAutoSupports,
		"towerLvMap":       plr.ToTowerLvMap(),
		"armys":            plr.Armys,
		"IsGiveupGame":     plr.IsGiveupGame,
	}
}

func (this *Model) ToTempPlayerPb(plr *TempPlayer, alli *AllianceBaseData) *pb.TempPlayerInfo {
	ret := &pb.TempPlayerInfo{
		Uid:             plr.Uid,
		MainCityIndex:   pb.Int32(plr.MainCityIndex),
		Nickname:        plr.Nickname,
		HeadIcon:        plr.HeadIcon,
		PersonalDesc:    plr.PersonalDesc,
		AllianceUid:     plr.AllianceUid,
		TowerLvMap:      plr.ToTowerLvMapPb(),
		Policys:         plr.PolicySlots.ToPolicys(),
		Title:           pb.Int32(plr.Title),
		RodeleroCadetLv: plr.RodeleroCadetLv,
		MaxLandCount:    plr.MaxLandCount,
		IsGiveupGame:    plr.IsGiveupGame,
		CityOutputMap:   plr.ToCityOutputPb(),
		IsSettled:       plr.IsSettled,
		FarmType:        plr.FarmType,
	}
	if alli != nil {
		ret.AllianceName = alli.Name
		ret.AllianceIcon = alli.Icon
	}
	return ret
}

func (this *Model) UpdateTempPlayerPb(plr *TempPlayer, plyPb *pb.TempPlayerInfo, alli *AllianceBaseData) {
	plyPb.Nickname = plr.Nickname
	plyPb.HeadIcon = plr.HeadIcon
	plyPb.PersonalDesc = plr.PersonalDesc
	plyPb.Title = plr.Title
	if plyPb.SpectateIndex == 0 {
		// 正常玩家 非观战
		plyPb.MainCityIndex = plr.MainCityIndex
		plyPb.AllianceUid = plr.AllianceUid
		plyPb.TowerLvMap = plr.ToTowerLvMapPb()
		plyPb.Policys = plr.PolicySlots.ToPolicys()
		plyPb.RodeleroCadetLv = plr.RodeleroCadetLv
		plyPb.MaxLandCount = plr.MaxLandCount
		plyPb.IsGiveupGame = plr.IsGiveupGame
		plyPb.CityOutputMap = plr.ToCityOutputPb()
		plyPb.IsSettled = plr.IsSettled
		if alli != nil {
			plyPb.AllianceName = alli.Name
			plyPb.AllianceIcon = alli.Icon
		} else {
			plyPb.AllianceName = ""
			plyPb.AllianceIcon = 0
		}
	}
}

// 更新观战者观战主城
func (this *Model) UpdateSepctatorMainIndex(uid string, mainIndex int32) {
	tempPlr := this.GetWorldPbPlayersByUid(uid)
	if tempPlr != nil && tempPlr.SpectateIndex != 0 {
		tempPlr.SpectateIndex = mainIndex
	}
}

func initEquips(equips []*g.EquipInfo) []*g.EquipInfo {
	for _, m := range equips {
		m.SetIDOrUID(m.UID, m.ID)
		m.UpdateAttr()
	}
	return equips
}

func initCityOutput(cityOutput map[int32][]*g.TypeObj) *ut.MapLock[int32, []*g.TypeObj] {
	obj := ut.NewMapLock[int32, []*g.TypeObj]()
	for k, v := range cityOutput {
		obj.Set(k, v)
	}
	return obj
}

func (this *Model) InitTempPlayers(players map[string]player.TableData) {
	alliNameMap := this.GetAlliNameMap()
	this.worldPb.Players = map[string]*pb.TempPlayerInfo{}
	for _, plr := range players {
		this.createTempPlayer(plr)
		this.worldPb.Players[plr.Uid] = this.ToTempPlayerPb(this.allTempPlayers.Map[plr.Uid], alliNameMap[plr.AllianceUid])
	}
}

func (this *Model) CreateTempPlayerLock(plr player.TableData) {
	alliNameMap := this.GetAlliNameMap()
	this.allTempPlayers.Lock()
	this.createTempPlayer(plr)
	this.allTempPlayers.Unlock()
	this.SetWorldPbPlayer(plr.Uid, this.ToTempPlayerPb(this.allTempPlayers.Map[plr.Uid], alliNameMap[plr.AllianceUid]))
}

// 获取玩家
func (this *Model) GetTempPlayer(uid string) *TempPlayer {
	if uid == "" {
		return nil
	}
	this.allTempPlayers.RLock()
	defer this.allTempPlayers.RUnlock()
	return this.allTempPlayers.Map[uid]
}

// 获取玩家昵称
func (this *Model) GetTempPlayerNickname(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Nickname
	}
	return ""
}

// 是否放弃本次对局
func (this *Model) GetPlayerIsGiveupGame(uid string) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.IsGiveupGame
	}
	return false
}

// 记录访客id
func (this *Model) RecordPlayerDistinctId(uid string, distinctId string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.DistinctId = distinctId
	}
}

// 获取访客id
func (this *Model) GetPlayerDistinctId(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.DistinctId
	}
	return ""
}

// 清理玩家信息
func (this *Model) CleanTempPlayerInfo(uid string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.OwnCells = []int32{}
		plr.TowerLvMap = deadlock.Map{}
		plr.RodeleroCadetLv = 0
		plr.MaxLandCount = 0
		plr.Equips = []*g.EquipInfo{}
		plr.HeroSlots = initHeroSlots([]map[string]interface{}{})
		plr.configPawnMutex.Lock()
		plr.ConfigPawnMap = map[int32]*g.PawnConfigInfo{}
		plr.configPawnMutex.Unlock()
		plr.PolicySlots.Clean()
		plr.FortAutoSupports = map[int32]bool{}
		plr.ApplyAlliUids = []string{}
		plr.AvoidWarReduce = 0
		plr.battleRecordMutex.Lock()
		plr.BattleRecordInfo[bdtype.DIE_COUNT] += 1 // 记录沦陷次数
		plr.battleRecordMutex.Unlock()
		plr.LandScore = 0
		plr.CityOutputMap.Clean()
		plr.InjuryPawns.Lock()
		plr.InjuryPawns.List = []*pb.InjuryPawnInfo{}
		plr.InjuryPawns.Unlock()
		plr.armyMutex.Lock()
		plr.Armys = map[string]int32{}
		plr.armyMutex.Unlock()
	}
}

// 攻陷玩家主城
func (this *Model) KillPlayerMain(attacker string, battlePlayerUids []string) {
	// 记录攻陷次数
	if plr := this.GetTempPlayer(attacker); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.battleRecordMutex.Lock()
		plr.BattleRecordInfo[bdtype.KILL_MAIN] += 1
		plr.battleRecordMutex.Unlock()
	}
	// 记录协助攻陷次数
	uids := map[string]bool{}
	for _, uid := range battlePlayerUids {
		if attacker != uid && !uids[uid] && this.CheckIsOneAlliance(attacker, uid) { // 是否盟友
			uids[uid] = true
			this.RecordPlayerAssistKillMainCount(uid, 1)
			if oPlr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model); oPlr != nil && !oPlr.IsOnline() {
				this.room.UpdatePlayerDB(oPlr) // 如果没在线 就保存到数据库
			}
		}
	}
}

// 记录协助攻陷次数
func (this *Model) RecordPlayerAssistKillMainCount(uid string, val int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.battleRecordMutex.Lock()
		plr.BattleRecordInfo[bdtype.ASSIST_KILL_MAIN] += val
		plr.battleRecordMutex.Unlock()
	}
}

func (this *Model) HasPlayer(uid string) bool {
	return this.GetTempPlayer(uid) != nil
}

func (this *Model) GetTempPlayerByIndex(index int32) *TempPlayer {
	cell := this.GetCell(index)
	if cell == nil || cell.Owner == "" {
		return nil
	}
	return this.GetTempPlayer(cell.Owner)
}

func (this *Model) GetTempPlayerByName(name string) *TempPlayer {
	if name == "" {
		return nil
	}
	this.allTempPlayers.RLock()
	defer this.allTempPlayers.RUnlock()
	for _, m := range this.allTempPlayers.Map {
		if m.Nickname == name {
			return m
		}
	}
	return nil
}

// 根据名字或UID获取玩家
func (this *Model) GetTempPlayerByNameOrUID(name string) *TempPlayer {
	if name == "" {
		return nil
	}
	var plr *TempPlayer
	if _, err := strconv.Atoi(name); err == nil {
		plr = this.GetTempPlayer(name)
	}
	if plr == nil {
		plr = this.GetTempPlayerByName(name)
	}
	if plr != nil && plr.IsSpectator() {
		return nil
	}
	return plr
}

// 获取玩家名字
func (this *Model) GetPlayerNickname(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Nickname
	}
	return ""
}

// 刷新玩家昵称
func (this *Model) UpdatePlayerNickname(uid string, name string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.Nickname = name
		// 刷新玩家在联盟的信息
		if alli := this.GetAlliance(plr.AllianceUid); alli != nil {
			alli.updateMemberBaseInfo(plr.Uid, plr.Nickname, plr.HeadIcon)
			msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBERS_NOTIFY{Members: alli.ToMembersPb(this)})
			this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMembers", msgBytes, alli.GetMemberUids())
		}
		// 通知
		this.PutNotifyQueue(constant.NQ_MODIFY_NICKNAME, &pb.OnUpdateWorldInfoNotify{
			Data_37: &pb.UpdateChangeName{
				Uid:      plr.Uid,
				Nickname: plr.Nickname,
			},
		})
	}
}

// 获取玩家头像
func (this *Model) GetPlayerHeadIcon(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.HeadIcon
	}
	return ""
}

// 改变玩家头像
func (this *Model) UpdatePlayerHeadIcon(uid string, icon string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.HeadIcon = icon
		// 刷新玩家在联盟的信息
		if alli := this.GetAlliance(plr.AllianceUid); alli != nil {
			alli.updateMemberBaseInfo(plr.Uid, plr.Nickname, plr.HeadIcon)
		}
		// 头像暂不用通知
	}
}

// 获取玩家个人简介
func (this *Model) GetPlayerPersonalDesc(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.PersonalDesc
	}
	return ""
}

// 改变玩家个人简介
func (this *Model) UpdatePlayerPersonalDesc(uid string, desc string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.PersonalDesc = desc
		// 简介暂不用通知
	}
}

// 获取玩家拥有的地块
func (this *Model) GetPlayerOwnCells(uid string) []int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.OwnCells
	}
	return []int32{}
}

// 获取玩家拥有的地块数量
func (this *Model) GetPlayerLandCount(uid string, notHasMainCityCount bool) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		count := int32(len(plr.OwnCells))
		if notHasMainCityCount {
			count = ut.MaxInt32(0, count-4)
		}
		return count
	}
	return 0
}

// 获取玩家x级土地数量 忽略掉主城占领的地块
func (this *Model) GetPlayerLandCountByLv(uid string, lv int32) int32 {
	var count int32
	cells := this.GetPlayerOwnCells(uid)
	for _, index := range cells {
		cell := this.GetCell(index)
		if cell == nil || !cell.IsHasRes() {
			continue
		} else if this.GetLandLv(index) == lv {
			count += 1
		}
	}
	return count
}

// 获取玩家x级土地数量 忽略掉主城占领的地块
func (this *Model) GetPlayerLandCountByType(uid string, tp int32) int32 {
	var count int32
	cells := this.GetPlayerOwnCells(uid)
	for _, index := range cells {
		cell := this.GetCell(index)
		if cell == nil || !cell.IsHasRes() {
			continue
		} else if this.GetLandType(index) == tp {
			count += 1
		}
	}
	return count
}

// 添加玩家的地块拥有
func (this *Model) AddPlayerOwnCell(uid string, index int32) bool {
	if plr := this.GetTempPlayer(uid); plr != nil && !array.Has(plr.OwnCells, index) {
		plr.IsNeedUpdateDB = true
		plr.OwnCells = append(plr.OwnCells, index)
		plr.MaxLandCount = ut.MaxInt32(int32(len(plr.OwnCells)), plr.MaxLandCount) // 记录最大拥有数量
		this.RefreshMainCityAvoidWar(plr)
		return true
	}
	return false
}

// 删除玩家的地块拥有 单个
func (this *Model) RemovePlayerOwnCellOne(uid string, index int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.OwnCells = array.Remove(plr.OwnCells, index)
		plr.CityOutputMap.Del(index) // 清理产出
	}
}

// 删除玩家的地块拥有 多个
func (this *Model) RemovePlayerOwnCells(uid string, indexs []int32) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	}
	for _, index := range indexs {
		plr.OwnCells = array.Remove(plr.OwnCells, index)
		plr.CityOutputMap.Del(index) // 清理产出
	}
	this.room.PutPlayerUpdateOpSec(uid) // 刷新产量
	// 发送一封邮件
	// content := ut.StringJoin("|", "ui.land_"+ut.Itoa(this.Lands[index]), helper.IndexToPoint(index).Join(","), plr.Nickname)
	// this.room.GetMail().SendMailOne(100002, "", content, "-1", oPlr.Uid)
}

// 获取玩家的主城位置
func (this *Model) GetPlayerMainIndex(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.MainCityIndex
	}
	return -1
}

// 改变玩家的主城位置
func (this *Model) ChangePlayerMainIndex(uid string, index int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.MainCityIndex = index
	}
}

// 查找玩家的主城位置
func (this *Model) FindPlayerMainIndex(uid string) int32 {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return -1
	}
	for _, index := range plr.OwnCells {
		if cell := this.GetCell(index); cell != nil && cell.CityId == constant.MAIN_CITY_ID {
			plr.MainCityIndex = index
			return index
		}
	}
	return -1
}

// 根据名字或UID获取玩家主城位置
func (this *Model) GetPlayerMainIndexByNameOrUID(name string) int32 {
	plr := this.GetTempPlayerByNameOrUID(name)
	if plr == nil {
		return -1
	}
	return plr.MainCityIndex
}

// 获取玩家的称号
func (this *Model) GetPlayerTitle(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Title
	}
	return 0
}

// 设置称号
func (this *Model) SetPlayerTitle(uid string, id int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.Title = id
		this.PutNotifyQueue(constant.NQ_CHANGE_TITLE, &pb.OnUpdateWorldInfoNotify{
			Data_49: &pb.UpdateTitleInfo{
				Uid:   uid,
				Title: id,
			},
		})
	}
}

// 刷新玩家在线状态 time=0表示 在线
func (this *Model) UpdatePlayerOnlineState(uid string, time int64) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	} else if time > 0 {
		plr.OfflineTime = time
	} else if plr.AllianceUid == "" {
	} else if alli := this.GetAlliance(plr.AllianceUid); alli == nil || !alli.IsInMember(plr.Uid) { // 玩家联盟数据兼容
		alliUid := plr.AllianceUid
		plr.AllianceUid = ""
		if player := this.room.GetPlayer(uid); player != nil {
			player.SetAlliUid("")
		}
		if alli != nil {
			this.PutNotifyQueue(constant.NQ_PLAYER_EXIT_ALLI, &pb.OnUpdateWorldInfoNotify{Data_43: &pb.JoinAndExitAlliInfo{
				Uid:      plr.Uid,
				BaseInfo: this.ToAlliBaseInfo(alli),
			}})
		}
		this.CheckAlliMembersArmys(alli, uid)
		log.Info("玩家联盟数据兼容 uid: %v, auid: %v", uid, alliUid)
	}
	// log.Info("UpdatePlayerOnlineState uid=" + uid)
	// 通知联盟成员
	if alli := this.GetAlliance(plr.AllianceUid); alli != nil && alli.UpdateMemberOnlineState(uid, time) {
		msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBERS_NOTIFY{Members: alli.ToMembersPb(this)})
		this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMembers", msgBytes, alli.GetMemberUids())
	}
	// 通知申请的联盟
	for _, auid := range plr.ApplyAlliUids {
		if alli := this.GetAlliance(auid); alli != nil && alli.UpdateApplyOnlineState(uid, time) {
			msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIAPPLYS_NOTIFY{Applys: alli.ToApplysPb()})
			this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliApplys", msgBytes, alli.GetMemberUidsByApplyMgr())
		}
	}
	// 被攻击离线通知次数清零
	plr.AttackNotifyCount = 0
}

// 获取玩家的在线状态 0.表示在线 否则表示离线时间
func (this *Model) GetPlayerOnlineState(plr *TempPlayer) int64 {
	if this.room.IsPlayerOnline(plr.Uid) {
		return 0
	}
	return plr.OfflineTime
}

// 获取玩家申请联盟数量
func (this *Model) GetPlayerApplyAlliCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return int32(len(plr.ApplyAlliUids))
	}
	return 0
}

// 获取玩家的某个城市数量
func (this *Model) GetPlayerCityCountByID(uid string, id int32) int32 {
	indexs := this.GetPlayerOwnCells(uid)
	var count int32
	for _, index := range indexs {
		if cell := this.GetCell(index); cell != nil && (cell.CityId == id || this.GetBTCityIdByIndex(index) == id) { // 这里修建中也要算上
			count += 1
		}
	}
	return count
}

// 获取玩家的某个城市数量 不算修建中
func (this *Model) GetPlayerOwnedCityCountByID(uid string, id int32) int32 {
	indexs := this.GetPlayerOwnCells(uid)
	var count int32
	for _, index := range indexs {
		if cell := this.GetCell(index); cell != nil && cell.CityId == id {
			count += 1
		}
	}
	return count
}

// 获取玩家是否拥有资源建筑
func (this *Model) GetPlayerHasResBuild(uid string) bool {
	indexs := this.GetPlayerOwnCells(uid)
	for _, index := range indexs {
		if cell := this.GetCell(index); cell != nil && cell.CityId != 0 && cell.IsHasRes() {
			return !cell.IsAncient()
		}
	}
	return false
}

// 查询玩家 通过名字或uid 或位置
func (this *Model) QueryTempPlayer(uid string, name string, idx int32) *TempPlayer {
	index, tuid := int32(-1), ""
	tplr := this.GetTempPlayerByNameOrUID(name)
	if tplr != nil {
		tuid = tplr.Uid
		index = tplr.MainCityIndex
	} else if idx != -1 {
		index = idx
	} else {
		return nil
	}
	cell := this.GetCell(index)
	if cell == nil || cell.Owner == "" || cell.Owner == uid || (tuid != "" && cell.Owner != tuid) {
		return nil
	} else if tplr != nil {
		return tplr
	}
	return this.GetTempPlayer(cell.Owner)
}

// 打造玩家装备
func (this *Model) ForgePlayerEquip(uid string, euid string, lockEffect int32) *g.EquipInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		equip := plr.GetPlayerEquipByUid(euid)
		// 随机属性
		if equip == nil { // 如果没有就表示是打造
			equip = g.NewEquip().SetUID(euid)
			equip.RandomAttr(0, this.ExclusiveEffectMap[equip.ID])
			plr.Equips = append(plr.Equips, equip)
			// 是否专属装备
			if equip.IsExclusive() {
				this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
					Data_54: &pb.SysMsgInfo{
						Id:      int32(slg.EXCLUSION_EQUIP_NOTICE_ID),
						Parames: []string{plr.Nickname},
					},
				})
			}
		} else {
			equip.RandomAttr(lockEffect, this.ExclusiveEffectMap[equip.ID])
			equip.RecastCount += 1
			// 更新玩家装备到临时记录列表
			this.UpdatePlayerPawnEquipInfo(uid, equip.UID, equip.Attrs)
		}
		return equip
	}
	return g.NewEquip().SetUID(euid)
}

// 融炼玩家装备
func (this *Model) SmeltPlayerEquip(uid, mainUid string, viceAttrs [][]int32) *g.EquipInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		equip := plr.GetPlayerEquipByUid(mainUid)
		if equip == nil { // 主装备不存在
			return nil
		}
		// 先移除融炼属性
		equip.RestoreEquipSmelt()
		// 添加新的融炼属性
		equip.Attrs = append(equip.Attrs, viceAttrs...)
		equip.UpdateAttr()
		// 更新玩家装备到临时记录列表
		this.UpdatePlayerPawnEquipInfo(uid, equip.UID, equip.Attrs)
		return equip
	}
	return nil
}

// 获取玩家装备列表
func (this *Model) GetPlayerEquips(uid string) []*g.EquipInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Equips
	}
	return []*g.EquipInfo{}
}

// 获取玩家装备
func (this *Model) GetPlayerEquip(uid string, id int32) *g.EquipInfo {
	if id == 0 || uid == "" {
		return nil
	} else if plr := this.GetTempPlayer(uid); plr != nil {
		if equip := array.Find(plr.Equips, func(m *g.EquipInfo) bool { return m.ID == id }); equip != nil {
			return equip
		}
	}
	return nil
}

// 获取玩家装备
func (this *Model) GetPlayerEquipByUid(uid, euid string) *g.EquipInfo {
	if euid == "" || uid == "" {
		return nil
	} else if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.GetPlayerEquipByUid(euid)
	}
	return nil
}

// 获取玩家专属装备
func (this *Model) GetPlayerExclusiveEquips(uid string) []*g.EquipInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return array.Filter(plr.Equips, func(m *g.EquipInfo, _ int) bool { return m.IsExclusive() })
	}
	return []*g.EquipInfo{}
}

// 获取装备
func (this *TempPlayer) GetPlayerEquipByUid(uid string) *g.EquipInfo {
	if uid == "" {
		return nil
	} else if equip := array.Find(this.Equips, func(m *g.EquipInfo) bool { return m.UID == uid }); equip != nil {
		return equip
	}
	return nil
}

// 获取玩家融炼装备数量
func (this *Model) GetPlayerSmeltEquipCount(uid string) int {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.GetPlayerSmeltEquipCount()
	}
	return 0
}

// 获取融炼装备数量
func (this *TempPlayer) GetPlayerSmeltEquipCount() int {
	rst := 0
	if this.Equips == nil {
		return rst
	}
	for _, equip := range this.Equips {
		if equip.IsSmelt() {
			rst++
		}
	}
	return rst
}

// 获取玩家一共重铸装备次数
func (this *Model) GetPlayerTotalEquipRecastCount(uid string) int32 {
	var count int32
	if plr := this.GetTempPlayer(uid); plr != nil {
		for _, m := range plr.Equips {
			count += m.RecastCount
		}
	}
	return count
}

// 检测玩家是否有满属性装备
func (this *Model) CheckPlayerHasMaxAttrEquip(uid string) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		for _, m := range plr.Equips {
			if m.IsMaxAttr() {
				return true
			}
		}
	}
	return false
}

// 改变玩家装备属性
func (this *Model) ChangePlayerEquipAttrs(uid string, id int32, attrs [][]int32) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		if equip := array.Find(plr.Equips, func(m *g.EquipInfo) bool { return m.ID == id }); equip != nil {
			equip.SetAttr(attrs)
			return true
		}
	}
	return false
}

// 玩家装备是否在熔炼中
func (this *Model) IsPlayerEquipSmelted(uid string, mainUid string, viceId int32) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		for _, equip := range plr.Equips {
			if equip.UID == mainUid {
				continue // 忽略当前正要融的
			}
			for _, attr := range equip.Attrs {
				if len(attr) >= 5 {
					// 该属性为熔炼属性
					if equip.ID == viceId {
						// 该装备已熔炼
						return true
					} else if attr[4] == viceId {
						// 熔炼的材料中有该装备
						return true
					}
				}
			}
		}
	}
	return false
}

// 获取玩家士兵装备配置
func (this *Model) ToPlayerConfigPawnMap(uid string) map[int32]*g.PawnConfigInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		conf := map[int32]*g.PawnConfigInfo{}
		plr.configPawnMutex.RLock()
		for k, v := range plr.ConfigPawnMap {
			conf[k] = v
		}
		plr.configPawnMutex.RUnlock()
		return conf
	}
	return map[int32]*g.PawnConfigInfo{}
}

func (this *Model) ToPlayerConfigPawnMapPb(uid string) map[int32]*pb.PawnConfigInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		conf := map[int32]*pb.PawnConfigInfo{}
		plr.configPawnMutex.RLock()
		for k, v := range plr.ConfigPawnMap {
			conf[k] = &pb.PawnConfigInfo{
				EquipUid:    v.EquipUid,
				SkinId:      v.SkinId,
				AttackSpeed: v.AttackSpeed,
			}
		}
		plr.configPawnMutex.RUnlock()
		return conf
	}
	return map[int32]*pb.PawnConfigInfo{}
}

// 刷新配置士兵的装备信息
func (this *Model) UpdateConfigPawnInfo(uid string, id int32, euid string, skinId, attackSpeed int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.configPawnMutex.Lock()
		plr.ConfigPawnMap[id] = &g.PawnConfigInfo{EquipUid: euid, SkinId: skinId, AttackSpeed: attackSpeed}
		plr.configPawnMutex.Unlock()
	}
}

// 刷新配置士兵的皮肤
func (this *Model) UpdateConfigPawnSkin(uid string, id, skinId int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.configPawnMutex.RLock()
		info := plr.ConfigPawnMap[id]
		plr.configPawnMutex.RUnlock()
		if info != nil {
			info.SkinId = skinId
		} else {
			plr.ConfigPawnMap[id] = &g.PawnConfigInfo{EquipUid: "", SkinId: skinId, AttackSpeed: 0}
		}
	}
}

// 清理配置士兵的指定皮肤
func (this *Model) ClearConfigPawnSkin(uid string, id, skinId int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.configPawnMutex.RLock()
		info := plr.ConfigPawnMap[id]
		plr.configPawnMutex.RUnlock()
		if info != nil && info.SkinId == skinId {
			info.SkinId = 0
		}
	}
}

// 获取配置士兵的装备
func (this *Model) GetConfigPawnInfo(uid string, pawnId int32) (euid string, attrs [][]int32, skinId, attackSpeed int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.configPawnMutex.RLock()
		info := plr.ConfigPawnMap[pawnId]
		plr.configPawnMutex.RUnlock()
		if info != nil {
			euid = info.EquipUid
			skinId = info.SkinId
			attackSpeed = info.AttackSpeed
		}
		// 兼容装备
		if euid != "" {
			equip := plr.GetPlayerEquipByUid(euid)
			if equip != nil && (!equip.IsExclusive() || equip.CheckExclusivePawn(pawnId)) {
				euid = equip.UID
				attrs = equip.Attrs
			} else {
				euid = ""
			}
			if info != nil {
				info.EquipUid = euid
			}
		}
	}
	return
}

// 获取要塞是否自动支援配置
func (this *Model) GetFortAutoSupports(uid string) map[int32]bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.FortAutoSupports
	}
	return map[int32]bool{}
}

// 获取要塞是否自动支援 (暂时取消该功能)
func (this *Model) IsFortAutoSupport(uid string, index int32) bool {
	// if plr := this.GetTempPlayer(uid); plr != nil {
	// 	return plr.FortAutoSupports[index]
	// }
	return false
}

// 获取要塞是否自动支援
func (this *Model) UpdateFortAutoSupport(uid string, index int32, val bool) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.FortAutoSupports[index] = val
	}
}

// 获取玩家减少行军的行军速度
func (this *Model) GetPlayerMarchCd(uid string) float64 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		// 获取政策效果
		cd := this.GetPlayerPolicyEffectFloat(plr, effect.MARCH_CD) * 0.01
		// 获取校场建筑效果
		if build := this.GetAreaBuildById(plr.MainCityIndex, constant.DRGR_BUILD_ID); build != nil && build.Lv > 0 {
			cd += build.GetEffectFloat(effect.MARCH_CD) * 0.01
		}
		// 获取季节效果
		cd += this.GetSeason().GetEffectFloat(effect.MARCH_CD)
		// 获取遗迹效果
		cd += this.GetAncientEffectFloatByPlayer(uid, effect.MARCH_CD) * 0.01
		return cd
	}
	return 0
}

// 获取玩家的箭塔等级根据士兵id
func (this *Model) GetPlayerTowerLvByPawn(uid string, pawnId int32) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		_lv, _ := plr.TowerLvMap.LoadOrStore(pawnId, int32(1))
		lv := ut.MaxInt32(1, ut.Int32(_lv))
		if pawnId == 7001 { // 如果是箭塔看是否有守护之心
			lv += this.GetPlayerPolicyEffectInt(plr, effect.TOWER_LV)
		}
		return lv
	}
	return 1
}

// 刷新玩家的箭塔等级
func (this *Model) UpdatePlayerTowerLv(area *Area) {
	if plr := this.GetTempPlayer(area.Owner); plr != nil {
		plr.TowerLvMap = deadlock.Map{}
		for _, m := range area.Builds.List {
			if m.Id == constant.WALL_BUILD_ID || m.Id == constant.PAVILION_BUILD_ID || m.Id == constant.FRONTIER_BUILD_ID {
				plr.TowerLvMap.Store(m.GetBuildPawnId(), m.Lv)
			}
		}
	}
}

// 获取玩家累计士兵数量
func (this *Model) GetPlayerAccTotalPawnCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.pawnDrillMutex.RLock()
		var count int32
		for _, m := range plr.PawnDrillMap {
			count += m
		}
		plr.pawnDrillMutex.RUnlock()
		return count
	}
	return 0
}

// 返回本局使用过士兵map
func (this *TempPlayer) ToPawnDrillMap() map[int32]bool {
	ret := map[int32]bool{}
	this.pawnDrillMutex.RLock()
	for k := range this.PawnDrillMap {
		ret[k] = true
	}
	this.pawnDrillMutex.RUnlock()
	return ret
}

func (this *TempPlayer) IsSpectator() bool {
	// 已放弃、血战到底已结算且没有联盟的玩家也是观战者
	return this.IsSpectate || this.IsGiveupGame || (this.IsSettled && this.AllianceUid == "")
}

// 返回玩家招募士兵信息
func (this *Model) ToPlayerPawnDrillMap(uid string) map[int32]int32 {
	ret := map[int32]int32{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.pawnDrillMutex.RLock()
		for k, v := range plr.PawnDrillMap {
			ret[k] = v
		}
		plr.pawnDrillMutex.RUnlock()
	}
	return ret
}

// 返回玩家满级士兵信息
func (this *Model) ToPlayerPawnMaxLvMap(uid string) map[int32]int32 {
	ret := map[int32]int32{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.pawnDrillMutex.RLock()
		for k, v := range plr.PawnMaxLvMap {
			ret[k] = v
		}
		plr.pawnDrillMutex.RUnlock()
	}
	return ret
}

// 返回本局使用过政策map
func (this *Model) ToPlayerPolicyUseMap(uid string) map[int32]bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.ToPolicyUseMap()
	}
	return map[int32]bool{}
}

// 返回本局使用过装备map
func (this *Model) ToPlayerEquipUseMap(uid string) map[int32]bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.ToEquipUseMap()
	}
	return map[int32]bool{}
}

// 返回本局使用过装备map
func (this *Model) ToPlayerKillRecordMap(uid string) map[int32]int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.KillRecordMap.Clone()
	}
	return map[int32]int32{}
}

// 添加累计招募士兵数量
func (this *Model) AddPlayerAccTotalPawnCount(uid string, id, add int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.pawnDrillMutex.Lock()
		plr.PawnDrillMap[id] += add
		plr.pawnDrillMutex.Unlock()
	}
}

// 添加累计满级士兵数量
func (this *Model) AddPlayerAccTotalMaxLvPawnCount(uid string, id, add int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.pawnDrillMutex.Lock()
		plr.PawnMaxLvMap[id] += add
		plr.pawnDrillMutex.Unlock()
	}
}

// 添加本局使用过的政策
func (this *TempPlayer) AddPolicyUseMap(uid string, id int32) {
	this.policyUseMutex.Lock()
	if !this.PolicyUseMap[id] {
		this.PolicyUseMap[id] = true
		this.IsNeedUpdateDB = true
	}
	this.policyUseMutex.Unlock()
}

// 添加本局使用过的装备
func (this *Model) AddEquipUseMap(uid string, id int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.equipUseMutex.Lock()
		if !plr.EquipUseMap[id] {
			plr.EquipUseMap[id] = true
			plr.IsNeedUpdateDB = true
		}
		plr.equipUseMutex.Unlock()
	}
}

// 返回本局使用过政策map
func (this *TempPlayer) ToPolicyUseMap() map[int32]bool {
	ret := map[int32]bool{}
	this.policyUseMutex.RLock()
	maps.Copy(ret, this.PolicyUseMap)
	this.policyUseMutex.RUnlock()
	return ret
}

// 返回本局使用过装备map
func (this *TempPlayer) ToEquipUseMap() map[int32]bool {
	ret := map[int32]bool{}
	this.equipUseMutex.RLock()
	maps.Copy(ret, this.EquipUseMap)
	this.equipUseMutex.RUnlock()
	return ret
}

// 记录击杀数量
func (this *Model) RecordPlayerKillCount(uid string, id, count int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		val := plr.KillRecordMap.Get(id)
		plr.KillRecordMap.Set(id, count+val)
	}
}

// 获取击杀数量
func (this *Model) GetPlayerKillCount(uid string, id int32) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.KillRecordMap.Get(id)
	}
	return 0
}

// 获取玩家最大拥有过的地块数量
func (this *Model) GetPlayerMaxLandCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.MaxLandCount
	}
	return 0
}

// 获取玩家宝箱损失补偿次数
func (this *Model) GetPlayerTreasureLostCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.TreasureLostCount
	}
	return 0
}

// 处理玩家宝箱损失补偿
func (this *Model) HandlePlayerLostTresureCompensate(uid string, resMap map[int32]int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		if plr.TreasureLostCount >= constant.LOST_TRESURE_COMPENSATE_LIMIT {
			return // 达到上限
		}
		// 发送补偿
		notifyMap := map[int32]int32{}
		items := []*g.TypeObj{}
		for tp, count := range resMap {
			if count == 0 {
				continue
			}
			items = append(items, g.NewTypeObj(tp, 0, count))
			notifyMap[tp] = count
		}
		this.room.SendMailItemOne(constant.COMPENSATE_LOST_TRESURE_MAIL_ID, "", plr.Nickname, "-1", plr.Uid, items)
		// 补偿数据记录
		plr.TreasureLostCount++
		plr.IsNeedUpdateDB = true
		this.room.InvokeLobbyRpcNR(rds.GetUserLid(uid), slg.RPC_SET_TRESURE_LOST_COUNT, uid, plr.TreasureLostCount)
		// 上报
		this.TaTrack(plr.Uid, 0, "ta_compensate", map[string]interface{}{
			"compensate_type":       1,
			"maincitylevel":         this.GetPlayerMainBuildLv(plr.Uid),
			"landcount":             len(this.GetPlayerOwnCells(plr.Uid)),
			"pawncount":             len(this.GetPlayerPawnTrackInfo(plr.Uid)),
			"compensate_cereal":     notifyMap[ctype.CEREAL],
			"compensate_timber":     notifyMap[ctype.TIMBER],
			"compensate_stone":      notifyMap[ctype.STONE],
			"compensate_expbook":    notifyMap[ctype.EXP_BOOK],
			"compensate_up_recruit": notifyMap[ctype.UP_RECRUIT],
		})
	}
}

// 获取玩家是否取消免战
func (this *Model) GetPlayerNoAvoidWar(uid string) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.NoAvoidWar
	}
	return false
}

// 设置玩家是否取消免战
func (this *Model) SetPlayerNoAvoidWar(uid string, noAvoidWar bool) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		if plr.NoAvoidWar == noAvoidWar {
			return
		}
		plr.NoAvoidWar = noAvoidWar
		if noAvoidWar {
			// 删除该玩家所有免战
			for _, index := range plr.OwnCells {
				this.RemoveAvoidWarArea(index)
			}
		}
	}
}

// 设置联盟是否取消免战
func (this *Model) SetAlliNoAvoidWar(uid string, noAvoidWar bool) {
	if alli := this.GetAlliance(uid); alli != nil {
		if alli.NoAvoidWar == noAvoidWar {
			return
		}
		alli.NoAvoidWar = noAvoidWar
		members := alli.GetMemberUids()
		for _, v := range members {
			this.SetPlayerNoAvoidWar(v, noAvoidWar)
		}
	}
}

// 获取玩家主城等级
func (this *Model) GetPlayerMainBuildLv(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return this.GetAreaBuildLvById(plr.MainCityIndex, constant.MAIN_BUILD_ID)
	}
	return 0
}

// 获取玩家大使馆等级
func (this *Model) GetPlayerEmbassyBuildLv(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return this.GetAreaBuildLvById(plr.MainCityIndex, constant.ALLI_BUILD_ID)
	}
	return 0
}

// 返回玩家战斗记录信息
func (this *Model) ToPlayerBattleRecordInfo(uid string) map[int32]int32 {
	ret := map[int32]int32{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.battleRecordMutex.RLock()
		maps.Copy(ret, plr.BattleRecordInfo)
		plr.battleRecordMutex.RUnlock()
	}
	return ret
}

// 获取玩家的建筑上报信息
func (this *Model) GetPlayerBuildTrackInfo(uid string) (builds []map[string]any) {
	builds = []map[string]any{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		if area := this.GetArea(plr.MainCityIndex); area != nil {
			area.Builds.RLock()
			for _, build := range area.Builds.List {
				builds = append(builds, map[string]any{
					"build_id": build.Id,
					"build_lv": build.Lv,
				})
			}
			area.Builds.RUnlock()
		}
	}
	if len(builds) == 0 {
		builds = append(builds, map[string]any{})
	}
	return
}

// 添加玩家 剑盾兵的见习勇士层数
func (this *Model) AddPlayerRodeleroCadet(uid string, val, maxLv int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		if plr.RodeleroCadetLv >= maxLv {
			return // 超出了 就不加了
		}
		plr.IsNeedUpdateDB = true
		plr.RodeleroCadetLv = ut.MinInt32(plr.RodeleroCadetLv+val, maxLv)
		this.PutNotifyQueue(constant.NQ_PLAYER_CADET_LV, &pb.OnUpdateWorldInfoNotify{Data_56: &pb.RodeleroCadetInfo{Uid: uid, Lv: plr.RodeleroCadetLv}})
		// 刷新所有剑盾兵
		dist := this.GetPlayerArmyDist(uid)
		for index := range dist {
			area := this.GetArea(index)
			if area == nil || area.IsBattle() {
				continue
			}
			list := area.GetArmysClone()
			for _, army := range list {
				if army.Owner != uid {
					continue
				}
				has := false
				army.Pawns.RLock()
				for _, pawn := range army.Pawns.List {
					if pawn.GetSkillByType(constant.PAWN_SKILL_TYPE_CADET) != nil {
						pawn.SetRodeleroCadetLv(plr.RodeleroCadetLv)
						pawn.UpdateAttr(area.IsRecoverPawnHP())
						has = true
					}
				}
				army.Pawns.RUnlock()
				if has {
					this.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
						Data_19: army.ToPb(area.ToArmyState(army)),
					})
				}
			}
		}
	}
}

// 获取主城免战减少信息
func (this *Model) GetPlayerAvoidWarReduce(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.AvoidWarReduce
	}
	return 0
}

func (this *Model) GetPlayerRodeleroCadetLv(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.RodeleroCadetLv
	}
	return 0
}

// 获取玩家领地积分和联盟积分
func (this *Model) GetPlayerLandScoreAndAlliScore(uid string) (int32, int32, int32, int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.LandScore, plr.AlliScore, plr.LandScoreTop, plr.AlliScoreTop
	}
	return 0, 0, 0, 0
}

// 添加玩家的领地积分
func (this *Model) AddPlayerLandScore(uid string, score int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsNeedUpdateDB = true
		plr.LandScore += score
		if plr.LandScore > plr.LandScoreTop {
			plr.LandScoreTop = plr.LandScore
		}
	}
}

// 获取玩家使用语言
func (this *Model) GetPlayerLanguage(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.language
	}
	return ""
}

// 设置玩家使用语言
func (this *Model) SetPlayerLanguage(uid, lang string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.language = lang
		plr.IsNeedUpdateDB = true
	}
}

// 获取玩家FCM令牌
func (this *Model) GetPlayerFCMToken(uid string) string {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.FCMToken
	}
	return ""
}

// 设置玩家FCM令牌
func (this *Model) SetPlayerFCMToken(uid, token string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.FCMToken = token
		plr.IsNeedUpdateDB = true
	}
}

// 获取玩家离线通知设置
func (this *Model) GetPlayerOfflineOpt(uid string) []int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.offlineOpt
	}
	return nil
}

// 设置玩家离线通知设置
func (this *Model) SetPlayerOfflineOpt(uid string, opt []int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.offlineOpt = opt
	}
}

// 离线消息通知
func (this *Model) OfflineNotify(uid string, msgType int32, params ...string) {
	if !slg.IsOpenOfflineMsg() {
		return
	}
	// 玩家在线不通知
	if plr := this.GetTempPlayer(uid); plr != nil && plr.FCMToken != "" && !plr.IsSpectator() &&
		(plr.OfflineTime > 0 || msgType == constant.OFFLINE_MSG_TYPE_SERVER_OPEN) { // 服务器开启通知由大厅服调用时判断是否离线
		open, typeOpen := false, false // 离线通知总开关和对应类型开关
		for _, v := range plr.offlineOpt {
			switch v {
			case 0:
				open = true
			case constant.OFFLINE_MSG_OPT_MAP[msgType]:
				typeOpen = true
			}
		}
		if !open || !typeOpen {
			return
		}
		data := map[string]string{}
		title, body := genOfflineMsg(plr, msgType, params...)
		if body != "" {
			sdk.SendOfflineMessge(plr.FCMToken, title, body, data)
		}
	}
}

// 玩家离线消息检测
func (this *Model) CheckUpdatePlayerOfflineMsg(now int64) {
	this.playerOfflineMsgMap.Range(func(key, value any) bool {
		msg := value.(*PlayerOfflineMsgInfo)
		if msg.endTime > now {
			return true
		}
		this.OfflineNotify(msg.uid, msg.msgType, msg.params...)
		this.playerOfflineMsgMap.Delete(key)
		return true
	})
}

// 添加玩家离线消息检测
func (this *Model) AddCheckPlayerOfflineMsg(uid string, msgType int32, endTime int64, params ...string) {
	key := getPlayerOfflineMsgKey(uid, msgType, params...)
	msg := &PlayerOfflineMsgInfo{
		uid:     uid,
		msgType: msgType,
		endTime: endTime,
		params:  params,
	}
	this.playerOfflineMsgMap.Store(key, msg)
}

// 移除玩家离线消息检测
func (this *Model) RemoveCheckPlayerOfflineMsg(uid string, msgType int32, params ...string) {
	key := getPlayerOfflineMsgKey(uid, msgType, params...)
	this.playerOfflineMsgMap.Delete(key)
}

// 修改玩家离线消息检测时间
func (this *Model) ChangeCheckPlayerOfflineMsgTime(uid string, msgType int32, add int32, params ...string) {
	key := getPlayerOfflineMsgKey(uid, msgType, params...)
	data, ok := this.playerOfflineMsgMap.Load(key)
	if ok {
		msg := data.(*PlayerOfflineMsgInfo)
		msg.endTime += int64(add)
	}
}

// 获取玩家离线消息检测map的key
func getPlayerOfflineMsgKey(uid string, msgType int32, params ...string) string {
	return fmt.Sprintf("%v:%v:%v", uid, msgType, params)
}

// 删除玩家所有免战
func (this *Model) DelPlayerAvoidWar(uid string) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		for _, index := range plr.OwnCells {
			this.RemoveAvoidWarArea(index)
		}
	}
}

// 获取沦陷标记
func (this *Model) GetPlayerCapture(uid string) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.IsCapture
	}
	return false
}

// 设置沦陷标记
func (this *Model) SetPlayerCapture(uid string, flag bool) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.IsCapture = flag
	}
}

// 获取开荒方式
func (this *Model) GetPlayerFarmType(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.FarmType
	}
	return 0
}

func (this *Model) ToPlayerCityOutputDB(uid string) map[int32][]*g.TypeObj {
	obj := map[int32][]*g.TypeObj{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.CityOutputMap.ForEach(func(v []*g.TypeObj, k int32) bool {
			obj[k] = array.Map(v, func(m *g.TypeObj, _ int) *g.TypeObj { return m.Clone() })
			return true
		})
	}
	return obj
}

// 清楚玩家某个城市的产出
func (this *Model) DelPlayerCityOutputByIndex(uid string, index int32, isNotify bool) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		this.DelPlayerCityOutput(plr, index, isNotify)
	}
}

func (this *Model) DelPlayerCityOutput(plr *TempPlayer, index int32, isNotify bool) {
	plr.CityOutputMap.Del(index)
	// 通知
	if isNotify {
		this.PutNotifyQueue(constant.NQ_PLAYER_CITY_OUTPUT, &pb.OnUpdateWorldInfoNotify{Data_31: &pb.PlayerCityOutputInfo{
			Uid:       plr.Uid,
			OutputMap: plr.ToCityOutputPb(),
		}})
	}
}

// 获取玩家的宝箱奖励倍数
func (this *Model) GetPlayerTreasureAwardMul(uid string) float64 {
	// 获取政策效果 包含联盟的
	val := this.GetPlayerPolicyEffectFloatByUid(uid, effect.TREASURE_AWARD) * 0.01
	// 获取季节效果
	val += this.GetSeason().GetEffectFloat(effect.TREASURE_AWARD)
	return 1.0 + val
}

// 获取活跃玩家数量 离线不超过12个小时的玩家
func (this *Model) GetActivePlayerCount() int {
	this.allTempPlayers.RLock()
	defer this.allTempPlayers.RUnlock()
	count, now, maxTime := 0, time.Now().UnixMilli(), ut.TIME_HOUR*12
	for _, m := range this.allTempPlayers.Map {
		if now-m.OfflineTime <= int64(maxTime) {
			count += 1
		}
	}
	return count
}

// 获取玩家总经济
func (this *Model) GetPlayerResAcc(uid string) int64 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.ResAcc
	}
	return 0
}

// 玩家总经济变动
func (this *Model) ChangePlayerResAcc(uid string, change int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.ResAcc += int64(change)
	}
}

// 获取玩家总积分
func (this *Model) GetPlayerTotalScore(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.LandScore + plr.AlliScore + plr.ExtraScore
	}
	return 0
}

// 获取玩家攻陷次数
func (this *Model) GetPlayerOccupyCount(uid string) int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.GetBattleRecordByType(bdtype.KILL_MAIN)
	}
	return 0
}

// 获取玩家战斗统计数据
func (this *TempPlayer) GetBattleRecordByType(tp int32) int32 {
	this.battleRecordMutex.RLock()
	defer this.battleRecordMutex.RUnlock()
	return this.BattleRecordInfo[tp]
}

// 获取玩家是否结算
func (this *Model) GetPlayerIsSettled(uid string) bool {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.IsSettled
	}
	return false
}

// 生成离线消息文本
func genOfflineMsg(plr *TempPlayer, msgType int32, params ...string) (title, msg string) {
	jsonCfg := config.GetJsonData("messagePushText", msgType)
	if jsonCfg == nil {
		log.Error("genOfflineMsg cfg nil msgType: %v", msgType)
		return
	}
	textCfg := ut.MapInterface(jsonCfg["text"])
	if textCfg == nil {
		log.Error("genOfflineMsg textCfg nil msgType: %v", msgType)
		return
	}
	language := plr.language
	if language == "" {
		language = "cn"
	}
	text := ut.String(textCfg[language])
	if text == "" {
		log.Error("genOfflineMsg text nil msgType: %v, language: %v", msgType, language)
		return
	}
	titleCfg := ut.MapInterface(jsonCfg["title"])
	if titleCfg == nil {
		log.Error("genOfflineMsg titleCfg nil msgType: %v", msgType)
		return
	}
	title = ut.String(titleCfg[language])

	switch msgType {
	case constant.OFFLINE_MSG_TYPE_BUILD: // 建造完成
		if len(params) < 2 {
			log.Error("genOfflineMsg build err params: %v", params)
			return
		}
		typeCfg := ut.MapInterface(jsonCfg["type"])
		if typeCfg == nil {
			log.Error("genOfflineMsg typeCfg nil msgType: %v", msgType)
			return
		}
		typeStr := ut.String(typeCfg[language])
		if text == "" {
			log.Error("genOfflineMsg typeStr nil msgType: %v, language: %v", msgType, language)
			return
		}
		buildTypes := strings.Split(typeStr, "/")
		if len(buildTypes) < 2 {
			log.Error("genOfflineMsg typeCfg err msgType: %v", msgType)
			return
		}
		// 区分建造和升级
		buildType := buildTypes[0]
		lv := ut.Int(params[1])
		if lv > 1 {
			buildType = buildTypes[1]
		}
		title = strings.ReplaceAll(title, "{0}", buildType)
		buildName := getNameByConfigIdStr("buildText", language, "name_"+params[0])
		msg = strings.ReplaceAll(text, "{0}", buildName)
		msg = strings.ReplaceAll(msg, "{1}", buildType)
	case constant.OFFLINE_MSG_TYPE_CITY: // 城市建造完成
		cityName := getNameByConfigIdStr("cityText", language, "name_"+params[0])
		msg = strings.ReplaceAll(text, "{0}", cityName)
	case constant.OFFLINE_MSG_TYPE_MARCH: // 行军完成
		armyName := params[0]
		msg = strings.ReplaceAll(text, "{0}", armyName)
	case constant.OFFLINE_MSG_TYPE_CERI: // 研究完成
		// 研究根据类型从具体配置获取名字
		ceriCfg := config.GetJsonData("ceri", ut.Int32(params[0]))
		if ceriCfg == nil {
			log.Error("genOfflineMsg ceriCfg nil id: %v", ut.Int(params[0]))
			return
		}
		ceriTextId := ut.String(ceriCfg["value"])
		ceriName := ""
		switch ut.Int(ceriCfg["type"]) {
		case 1: // 政策
			ceriName = getNameByConfigIdStr("policyText", language, "name_"+ceriTextId)
		case 2: // 士兵
			ceriName = getNameByConfigIdStr("pawnText", language, "name_"+ceriTextId)
		case 3, 4: // 装备, 专属
			ceriName = getNameByConfigIdStr("equipText", language, "name_"+ceriTextId)
		}
		msg = strings.ReplaceAll(text, "{0}", ceriName)
	case constant.OFFLINE_MSG_TYPE_FORGE, constant.OFFLINE_MSG_TYPE_RESTORE_FORGE: // 打造,重铸完成
		equipName := getNameByConfigIdStr("equipText", language, "name_"+params[0])
		if equipName == "" {
			log.Error("genOfflineMsg equipName nil msgType: %v, language: %v", msgType, language)
			return
		}
		msg = strings.ReplaceAll(text, "{0}", equipName)
	case constant.OFFLINE_MSG_TYPE_RES_FULL: // 资源满了
		resId := ut.Int(params[0])
		resNameCfg := constant.RES_NAME_MAP[resId]
		if resNameCfg == nil {
			log.Error("genOfflineMsg resNameCfg nil msgType: %v, resId: %v", msgType, resId)
			return
		}
		resName := resNameCfg[language]
		msg = strings.ReplaceAll(text, "{0}", resName)
	case constant.OFFLINE_MSG_TYPE_BE_ATTACK: // 受到攻击
		now := time.Now().UnixMilli()
		cdTime := ut.MaxInt32(plr.AttackNotifyCount*constant.OFFLINE_ATTACK_NOTIFY_CD_BASE*constant.OFFLINE_ATTACK_NOTIFY_CD_MUT, constant.OFFLINE_ATTACK_NOTIFY_CD_MAX)
		if now > plr.AttackLastNotifyTime+int64(cdTime) {
			plr.AttackNotifyCount++
			plr.AttackLastNotifyTime = now
			msg = text
		}
	case constant.OFFLINE_MSG_TYPE_SERVER_OPEN: // 服务器开启
		sid := ut.Int(params[0])
		serverName := slg.GetServerNoName(sid, language)
		msg = strings.ReplaceAll(text, "{0}", serverName)
	case constant.OFFLINE_MSG_TYPE_OCCUPY: // 占领成功
		if len(params) < 3 {
			log.Error("genOfflineMsg occupy err params: %v", params)
			return
		}
		cellNameArr := strings.Split(text, "/")
		if len(cellNameArr) < 5 {
			log.Error("genOfflineMsg occupy textCfg err text: %v", text)
			return
		}
		owner := params[0]
		cellName := ""
		if owner == "" {
			// 野地
			cellName = cellNameArr[1]
			landId := ut.Int(params[1])
			landType := landId / 100
			landLv := landId % 100
			resNameCfg := constant.LAND_RES_NAME_MAP[landType]
			if resNameCfg == nil {
				log.Error("genOfflineMsg resNameCfg nil msgType: %v, landType: %v", msgType, landType)
				return
			}
			resName := resNameCfg[language]
			cellName = strings.ReplaceAll(cellName, "{0}", ut.String(landLv))
			cellName = strings.ReplaceAll(cellName, "{1}", resName)
		} else {
			cityId := ut.Int(params[2])
			switch cityId {
			case constant.MAIN_CITY_ID:
				cellName = cellNameArr[3] // 主城
			case constant.FORT_CITY_ID:
				cellName = cellNameArr[4] // 要塞
			default:
				cellName = cellNameArr[2] // 领地
			}
		}
		msg = cellNameArr[0] + cellName
	case constant.OFFLINE_MSG_TYPE_ARMY_DEAD: // 军队团灭
		if len(params) < 1 {
			log.Error("genOfflineMsg army dead err params: %v", params)
			return
		}
		armyName := params[0]
		msg = strings.ReplaceAll(text, "{0}", armyName)
	case constant.OFFLINE_MSG_TYPE_DRILL: // 招募完成
		buildName := getNameByConfigIdStr("buildText", language, "name_"+params[0])
		msg = strings.ReplaceAll(text, "{0}", buildName)
	case constant.OFFLINE_MSG_TYPE_LV_UP: // 训练完成
		msg = text
	case constant.OFFLINE_MSG_TYPE_CURE_FINISH: // 治疗完成
		msg = text
	}
	return
}

// 根据配置名和id获取名字
func getNameByConfig(configName, language string, id int32) string {
	cfg := config.GetJsonData(configName, id)
	if cfg == nil {
		log.Error("getNameByConfig nil configName: %v, id: %v", configName, id)
		return ""
	}
	return ut.String(cfg[language])
}

// 根据配置名和id获取名字
func getNameByConfigIdStr(configName, language string, idStr string) string {
	cfg := config.GetJsonDataByIdStr(configName, idStr)
	if cfg == nil {
		log.Error("getNameByConfigIdStr nil configName: %v, id: %v", configName, idStr)
		return ""
	}
	return ut.String(cfg[language])
}
