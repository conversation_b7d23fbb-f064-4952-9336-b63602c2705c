package recharge

import (
	"context"
	"strconv"
	"time"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RechargeDbTable struct {
	table string
}

// 充值数据库
var RechargeDb = &RechargeDbTable{slg.DB_COLLECTION_NAME_RECHARGE}

func (this *RechargeDbTable) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

const (
	ORDER_STATE_NOT_PAY = iota // 未支付
	ORDER_STATE_PAY            // 已支付未发放
	ORDER_STATE_FINISH         // 已发放
	ORDER_STATE_REFUND         // 已退款
)

type RechargeOrderData struct {
	UID          string  `json:"uid" bson:"uid"`                     // 订单号
	OrderId      string  `json:"order_id" bson:"order_id"`           // 外部订单号
	Token        string  `json:"token" bson:"token"`                 // 购买时token
	UserId       string  `json:"user_id" bson:"user_id"`             // 玩家id
	State        int     `json:"state" bson:"state"`                 // 状态
	ProductId    string  `json:"product_id" bson:"product_id"`       // 商品id
	Price        string  `json:"price" bson:"price"`                 // 实际交易价格
	Platform     string  `json:"platform" bson:"platform"`           // 支付方式
	CreateTime   int64   `json:"create_time" bson:"create_time"`     // 创建时间
	PurchaseTime int     `json:"purchase_time" bson:"purchase_time"` // 支付时间
	FinishTime   int     `json:"finish_time" bson:"finish_time"`     // 完成时间
	CurrencyType string  `json:"currency_type" bson:"currency_type"` // 支付币种
	PayAmount    float32 `json:"pay_amount" bson:"pay_amount"`       // 支付金额
	Quantity     int     `json:"quantity" bson:"quantity"`           // 数量
}

// 创建订单
func (this *RechargeDbTable) CreateOrder(userId string, productId string, platform string) (orderData *RechargeOrderData, err string) {
	log.Info("CreateOrder userId: %v, productId: %v, platform: %v", userId, productId, platform)
	uid := strconv.FormatInt(orderIdGenerator.generateOrderId(), 10)
	orderData = &RechargeOrderData{
		UID:        uid,
		UserId:     userId,
		ProductId:  productId,
		Platform:   platform,
		State:      ORDER_STATE_NOT_PAY,
		CreateTime: ut.Now(),
	}
	if _, e := this.getCollection().InsertOne(context.TODO(), orderData); e != nil {
		err = e.Error()
		log.Error("CreateOrder userId: %v, productId: %v, err: %v", userId, productId, err)
	}
	return
}

// 查询订单
func (this *RechargeDbTable) FindByUid(uid string) (data RechargeOrderData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询订单 外部订单号
func (this *RechargeDbTable) FindByOrderId(orderId, platform string) (data RechargeOrderData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"order_id": orderId, "platform": platform}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询最近未验证订单
func (this *RechargeDbTable) FindByUserNotVerifyOrder(userId, productId, platform string) (data RechargeOrderData, err string) {
	opts := options.FindOne().SetSort(bson.M{"create_time": -1})
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"user_id": userId, "product_id": productId, "platform": platform, "state": ORDER_STATE_NOT_PAY}, opts).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 更新验证订单
func (this *RechargeDbTable) UpdateVerifiedOrder(uid, orderId, price, currencyType string, purchaseTime int, payAmount float32, quantity int, token string) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state":         ORDER_STATE_PAY,
		"order_id":      orderId,
		"price":         price,
		"purchase_time": purchaseTime,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
		"quantity":      quantity,
		"token":         token,
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateVerifiedOrder", err)
	}
	return
}

// 更新完成订单
func (this *RechargeDbTable) UpdateFinishedOrder(uid string) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state": ORDER_STATE_FINISH, "finish_time": ut.Now(),
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateVerifiedOrder", err)
	}
	return
}

// 更新退款订单
func (this *RechargeDbTable) UpdateRefundOrder(uid string) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state": ORDER_STATE_REFUND,
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateRefundOrder", err)
	}
	return
}

// 更新一条
func (this *RechargeDbTable) UpdateOne(uid string, key string, value interface{}) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("UpdateOrder", err)
	}
	return
}

// 更新订单
func (this *RechargeDbTable) UpdateData(uid string, data bson.M) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": data}); e != nil {
		err = e.Error()
		log.Error("UpdateOrder", err)
	}
	return
}

// 获取指定用户已完成订单数量
func (this *RechargeDbTable) GetUserFinishOrderCount(userId string) (count int, err string) {
	if c, e := this.getCollection().CountDocuments(context.TODO(), bson.M{"user_id": userId, "state": ORDER_STATE_FINISH}); e != nil {
		err = e.Error()
		if e != mongo.ErrNoDocuments {
			log.Error("GetUserFinishOrderCount userId: %v, err: %v", userId, err)
		}
	} else {
		count = int(c)
	}
	return
}

// 获取指定用户已支付的订单信息
func (this *RechargeDbTable) GetUserRechargeOrders(userId string) (data []RechargeOrderData, err error) {
	filter := bson.M{
		"user_id": userId,
		"$and": bson.A{
			bson.M{"state": bson.M{"$gte": ORDER_STATE_PAY}},
			bson.M{"state": bson.M{"$lt": ORDER_STATE_REFUND}},
		},
	}
	cursor, err := this.getCollection().Find(context.TODO(), filter)
	defer cursor.Close(context.TODO())
	if err != nil {
		log.Error("GetUserRechargeOrders userId: %v, err: %v", userId, err)
		return
	}
	err = cursor.All(context.TODO(), &data)
	return
}

// 查询指定用户已支付的订单信息
func (this *RechargeDbTable) FindUserRechargeOrders(userId string, sTime, eTime, size, skip int) (data []RechargeOrderData, err error) {
	filter := bson.M{
		"state": bson.M{"$gte": ORDER_STATE_PAY},
	}
	if userId != "" {
		filter["user_id"] = userId
	}
	if sTime > 0 && eTime > 0 {
		filter["purchase_time"] = bson.M{"$gte": sTime, "$lte": eTime}
	}
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"purchase_time": -1})
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(size))
	cursor, err := this.getCollection().Find(context.TODO(), filter, findOptions)
	defer cursor.Close(context.TODO())
	if err != nil {
		log.Error("GetUserRechargeOrders userId: %v, err: %v", userId, err)
		return
	}
	err = cursor.All(context.TODO(), &data)
	return
}

type SubscriptionDbTable struct {
	table string
}

// 订阅数据库
var SubscriptionDb = &SubscriptionDbTable{slg.DB_COLLECTION_NAME_SUBSCRIPTION}

func (this *SubscriptionDbTable) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 订阅数据
type SubscriptionData struct {
	UID           string  `json:"uid" bson:"uid"`                         // 订单号
	OrderId       string  `json:"order_id" bson:"order_id"`               // 外部订单号
	UserId        string  `json:"user_id" bson:"user_id"`                 // 玩家id
	ProductId     string  `json:"product_id" bson:"product_id"`           // 商品id
	Token         string  `json:"token" bson:"token"`                     // 订单token 仅Google使用
	CreateTime    int64   `json:"create_time" bson:"create_time"`         // 创建时间
	EndTime       int64   `json:"end_time" bson:"end_time"`               // 到期时间
	AutoRenewing  bool    `json:"auto_renewing" bson:"auto_renewing"`     // 是否到期自动订阅
	Platform      string  `json:"platform" bson:"platform"`               // 支付方式
	PurchaseTime  int64   `json:"purchase_time" bson:"purchase_time"`     // 支付时间
	Price         string  `json:"price" bson:"price"`                     // 实际交易价格
	CurrencyType  string  `json:"currency_type" bson:"currency_type"`     // 支付币种
	PayAmount     float32 `json:"pay_amount" bson:"pay_amount"`           // 支付金额
	Type          string  `json:"type" bson:"type"`                       // 订阅类型
	State         int32   `json:"state" bson:"state"`                     // 状态
	LastAwardTime int64   `json:"last_award_time" bson:"last_award_time"` // 上一次领取奖励时间
}

const (
	ORDER_SUB_STATE_NOT_PAY = iota // 未支付
	ORDER_SUB_STATE_ACTIVE         // 已激活
	ORDER_SUB_STATE_CANCEL         // 已取消续订
	ORDER_SUB_STATE_REFUND         // 已退款
)

// 创建订单
func (this *SubscriptionDbTable) CreateOrder(userId string, productId string, platform, subType string) (orderInfo *SubscriptionData, err string) {
	log.Info("CreateOrder Subscription userId: %v, productId: %v, platform: %v", userId, productId, platform)
	uid := strconv.FormatInt(orderIdGenerator.generateOrderId(), 10)
	if platform == slg.PAY_PLATFORM_GOOGLE {
		productId = GoogleSubProductIdTransfer(subType, productId)
	}
	orderInfo = &SubscriptionData{
		UID:        uid,
		UserId:     userId,
		ProductId:  productId,
		State:      ORDER_STATE_NOT_PAY,
		CreateTime: time.Now().UnixMilli(),
		Platform:   platform,
		Type:       subType,
	}
	if _, e := this.getCollection().InsertOne(context.TODO(), orderInfo); e != nil {
		err = e.Error()
		log.Error("CreateOrder Subscription userId: %v, productId: %v, err: %v", userId, productId, err)
	}
	return
}

// 查询订单
func (this *SubscriptionDbTable) FindByUid(uid string) (data SubscriptionData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询订单 外部订单号
func (this *SubscriptionDbTable) FindByOrderId(orderId, platform string) (data SubscriptionData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"order_id": orderId, "platform": platform}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询订单 token和平台 (目前仅Google使用)
func (this *SubscriptionDbTable) FindByToken(token, platform string) (data SubscriptionData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"token": token, "platform": platform}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询用户订阅成功过的订单
func (this *SubscriptionDbTable) FindUserSubOrder(userId string) (data SubscriptionData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"user_id": userId, "state": bson.M{"$gt": ORDER_SUB_STATE_NOT_PAY}}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 根据订单id查询订阅成功的订单
func (this *SubscriptionDbTable) FindSubOrderByIdList(list []string) (arr []string, err string) {
	filter := bson.M{"order_id": bson.M{"$in": list}, "state": bson.M{"$gt": ORDER_SUB_STATE_NOT_PAY}}
	// 查询集合中的所有文档
	cursor, e := this.getCollection().Find(context.TODO(), filter)
	if e != nil {
		log.Error("FindSubOrderByIdList err: %v", e)
		return
	}
	defer cursor.Close(context.TODO())

	// 遍历查询结果
	var results []bson.M
	if e = cursor.All(context.TODO(), &results); e != nil {
		log.Error("FindSubOrderByIdList err: %v", e)
		return
	}
	arr = []string{}
	for _, v := range results {
		arr = append(arr, ut.String(v["order_id"]))
	}
	return
}

// 更新验证订单
func (this *SubscriptionDbTable) UpdateVerifiedOrder(uid, orderId, token, userId string, startTime, endTime int64, price, currencyType string, purchaseTime int64, payAmount float32, auto bool) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"state":         ORDER_SUB_STATE_ACTIVE,
		"order_id":      orderId,
		"token":         token,
		"user_id":       userId,
		"create_time":   startTime,
		"end_time":      endTime,
		"auto_renewing": auto,
		"price":         price,
		"purchase_time": purchaseTime,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateVerifiedOrder", err)
	}
	return
}

// 更新订阅续订信息
func (this *SubscriptionDbTable) UpdateSubRenew(uid, userId string, endTime int64, state int32, auto bool) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"user_id":       userId,
		"state":         state,
		"end_time":      endTime,
		"auto_renewing": auto,
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateSubRenew", err)
	}
	return
}

// 更新月卡订阅领奖信息
func (this *SubscriptionDbTable) UpdateSubAwardInfo(uid, userId string, lastAwardTime int64) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"user_id":         userId,
		"last_award_time": lastAwardTime,
	}}); e != nil {
		err = e.Error()
		log.Error("UpdateSubAwardInfo", err)
	}
	return
}

const (
	SNOW_FLAKE_INIT_TIMEMILLIS = 1679388550000 // 初始时间戳
	SNOW_FLAKE_MACH_ID_BITS    = 5             // WorkerId占用的位数
	SNOW_FLAKE_SEIVICE_ID_BITS = 5             // SeviceId占用的位数
	SNOW_FLAKE_SEQUENCE_BITS   = 12            // 序列号占用的位数
	SNOW_FLAKE_SEQUENCE_MAX    = 1 << SNOW_FLAKE_SEQUENCE_BITS
)

type SnowFlakeIdGenerator struct {
	LastTimeMillis int64 // 最后使用的毫秒时间戳
	MachId         int64 // 机器id 占5位 最大31
	SeviceId       int64 // 服务id 占5位 最大31
	Sequence       int64 // 同一毫秒内生成的序列号 占12位 最大4095
	deadlock.Mutex
}

var orderIdGenerator = &SnowFlakeIdGenerator{
	LastTimeMillis: -1,
	MachId:         1, // 默认为1 该节点多开时可通过配置获取
	SeviceId:       1, // 默认为1 该节点多开时可通过配置获取
	Sequence:       0,
}

// 初始化id生成器
func InitSnowFlakeIdGenerator(machId, seviceId string) {
	orderIdGenerator.MachId = ut.Int64(machId)
	orderIdGenerator.SeviceId = ut.Int64(seviceId)
}

func GenerateOrderId() int64 {
	return orderIdGenerator.generateOrderId()
}

// 生成订单id
func (this *SnowFlakeIdGenerator) generateOrderId() int64 {
	this.Lock()
	defer this.Unlock()
	timestamp := time.Now().UnixMilli()
	if timestamp == this.LastTimeMillis {
		// 还在同一毫秒内
		this.Sequence += 1
		if this.Sequence >= SNOW_FLAKE_SEQUENCE_MAX {
			// Sequence超过上限 时间调到下一毫秒
			timestamp += 1
			this.LastTimeMillis = timestamp
			this.Sequence = 0
		}
	} else {
		this.LastTimeMillis = timestamp
		this.Sequence = 0
	}
	// 符号位1位 时间戳41位 机器id5位 服务id5位 序列12位
	return (timestamp-SNOW_FLAKE_INIT_TIMEMILLIS)<<(SNOW_FLAKE_SEQUENCE_BITS+SNOW_FLAKE_SEIVICE_ID_BITS+SNOW_FLAKE_MACH_ID_BITS) |
		(this.MachId << (SNOW_FLAKE_SEQUENCE_BITS + SNOW_FLAKE_SEIVICE_ID_BITS)) |
		(this.SeviceId << SNOW_FLAKE_SEQUENCE_BITS) | this.Sequence
}

// Google订阅productId转换和Apple一致
func GoogleSubProductIdTransfer(subType, productId string) string {
	switch subType {
	case slg.SUB_PRODUCTID_TYPE_WEEK: // 周订阅
		switch productId {
		case slg.SUB_PRODUCTID_GOOGLE: // 超级月卡
			return slg.SUB_PRODUCTID_WEEK
			// 月卡无周订阅
		}
	case slg.SUB_PRODUCTID_TYPE_MONTH: // 月订阅
		switch productId {
		case slg.SUB_PRODUCTID_GOOGLE: // 超级月卡
			return slg.SUB_PRODUCTID_MONTH
		case slg.SUB_PRODUCTID_AWARD_GOOGLE: // 月卡
			return slg.SUB_PRODUCTID_AWARD_MOUNTH
		}
	case slg.SUB_PRODUCTID_TYPE_QUARTER: // 季度订阅
		switch productId {
		case slg.SUB_PRODUCTID_GOOGLE: // 超级月卡
			return slg.SUB_PRODUCTID_QUARTER
		case slg.SUB_PRODUCTID_AWARD_GOOGLE: // 月卡
			return slg.SUB_PRODUCTID_AWARD_QUARTER
		}
	}
	return productId
}
