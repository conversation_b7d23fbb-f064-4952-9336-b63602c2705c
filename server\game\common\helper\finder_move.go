package helper

import ut "slgsrv/utils"

type FinderMove struct {
	opened       []*ut.Vec2            //开放列表
	closed       []*ut.Vec2            //关闭列表
	sp           map[string]int        //记录每个格子的移动力
	checkHasPass func(x, y int32) bool //检测方法
}

//检查节点
func (this *FinderMove) findNode(point *ut.Vec2, tx, ty int32) {
	step := this.sp[point.ID()] - 1
	if step < 0 || !this.checkHasPass(tx, ty) {
		return //如果已经没有移动力了 或者 有障碍
	}
	id := ut.Itoa(tx) + "_" + ut.Itoa(ty)
	i := arrayHasPoint(this.closed, tx, ty)
	if i >= 0 && this.sp[id] >= step {
		return //如果已经找过 则检查这个点的行动力是否小于当前这个行动力
	}
	// 如果都不是，则代表是个可以拓展的节点 把新的节点的移动力继承
	this.sp[id] = step
	var p *ut.Vec2
	if i >= 0 {
		p = this.closed[i]
	} else { //新的点
		p = ut.NewVec2(tx, ty)
		this.closed = append(this.closed, p)
	}
	// 如果这个点还有移动点 那么就继续扩展
	if step > 0 {
		this.opened = append(this.opened, p)
	}
}

func (this *FinderMove) search(start *ut.Vec2, step int, check func(x, y int32) bool) []*ut.Vec2 {
	this.checkHasPass = check
	this.sp = map[string]int{}
	this.opened = []*ut.Vec2{start}
	this.closed = []*ut.Vec2{start}
	this.sp[start.ID()] = step
	// 开始搜寻
	openedLen := len(this.opened)
	for openedLen > 0 {
		p := this.opened[openedLen-1]
		this.opened = this.opened[:openedLen-1]
		this.findNode(p, p.X, p.Y-1) //上
		this.findNode(p, p.X+1, p.Y) //右
		this.findNode(p, p.X, p.Y+1) //下
		this.findNode(p, p.X-1, p.Y) //左
		openedLen = len(this.opened)
	}
	this.closed = this.closed[1:] //删除第一个原始点
	return this.closed
}

func FindCanMovePoints(start *ut.Vec2, step int, check func(x, y int32) bool) []*ut.Vec2 {
	return new(FinderMove).search(start, step, check)
}
