package http

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/sdk"
	"slgsrv/server/http/db"
	"slgsrv/server/lobby"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/geoip"
	mgo "slgsrv/utils/mgodb"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"

	"go.mongodb.org/mongo-driver/mongo"

	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	secret = "twomiles@2020|Slg"
	limit  = 86400
)

const LOBBY_LEFT_NUM_INTERVAL = ut.TIME_SECOND // 所有大厅服剩余空位更新最小间隔

var (
	version = &db.MapCol[string]{
		Val: "",
	}
	bigVersion = &db.MapCol[string]{
		Val: "",
	}
	testVersion = &db.MapCol[string]{
		Val: "",
	}
	fullServerPers = &db.MapCol[string]{
		Val: "1500",
	}
	notEnterServerTime = &db.MapCol[string]{
		Val: ut.String(ut.TIME_DAY * 3),
	}
	openCdk = &db.MapCol[string]{
		Val: ut.String(0),
	}
	openGuide = &db.MapCol[string]{
		Val: ut.String(ut.If(slg.GetDefaultOpenGuide(), 1, 0)),
	}
	gameDownloadUrl       = map[string]string{}
	version_lock          sync.Once
	allServerMaintainTime int64 = 0 // 维护后开启时间

	cdkModelList = []*db.CdkModel{}
	cdkModelLock sync.Once

	lobbyLeftNum                  = 0 // 所有大厅服剩余空位数量
	lobbyLeftLastUpdateTime int64 = 0 // 上次更新大厅服剩余空位时间

	noticeVersion = &db.MapCol[string]{
		Val: "",
	}
	noticeContentMap = ut.NewMapLock[string, string]()
)

type BaseHandler struct {
	serve  string
	topic  string
	params []string
	// path   string
	// hand   func(writer http.ResponseWriter, request *http.Request)
	method string // 接口访问方式 GET/POST/ALL
	token  bool   // 是否需要token验证,有些公共接口需要放开,token验证时必然触发权限验证
	isSet  bool   // 是否修改数据
}

func NewBaseHandler(serve string, topic string, params []string, method string, token, isSet bool) *BaseHandler {
	if method != "POST" && method != "GET" {
		method = "ALL"
	}
	if serve == "game" {
		sid := array.Find(params, func(p string) bool {
			return p == "sid"
		})
		if sid == "" {
			log.Error("注册了一个game模块的http消息,但是没有指定sid")
		}
	}
	return &BaseHandler{
		serve, topic, params, method, token, isSet,
	}
}

func NewPostBaseHandler(serve string, topic string, params []string, token, isSet bool) *BaseHandler {
	return NewBaseHandler(serve, topic, params, "POST", token, isSet)
}

func NewGetBaseHandler(serve string, topic string, params []string, token, isSet bool) *BaseHandler {
	return NewBaseHandler(serve, topic, params, "GET", token, isSet)
}

type HandlerContainer struct {
	routers map[string]*BaseHandler
	http    *Http
	isEnd   bool
}

// 增加一个路由匹配 不能存入没有handler的路由
func (this *HandlerContainer) push(path string, base *BaseHandler) *HandlerContainer {
	if base == nil || this.isEnd {
		return nil
	}
	this.routers[path] = base
	return this
}

// 获取一个路由对应的handler
func (this *HandlerContainer) get(path string) (*BaseHandler, error) {
	base := this.routers[path]
	if base == nil {
		return nil, errors.New("no value")
	}
	return base, nil
}

// 需要在如果没有end router不会开始处理请求，算是个http请求同步吧
func (this *HandlerContainer) end() {
	this.isEnd = true
}

func (this *HandlerContainer) ServeHTTP(writer http.ResponseWriter, request *http.Request) {
	// 设置请求体最大大小为 20MB
	request.Body = http.MaxBytesReader(writer, request.Body, 20*1024*1024)
	writer.Header().Set("Access-Control-Allow-Origin", "*") // 允许访问所有域
	writer.Header().Set("Access-Control-Allow-Methods", "*")
	writer.Header().Add("Access-Control-Allow-Headers", "accessToken,appKey,User-Agent,DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type") // header的类型
	writer.Header().Set("content-type", "application/json")
	method := request.Method
	if method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}
	defer func() {
		if err := recover(); err != nil {
			marshal, _ := json.Marshal(err)
			log.Info("O   %s", err)
			writer.Write(marshal)
		}
	}()
	if !this.isEnd {
		// 未加载完毕
		panic(map[string]interface{}{
			"status": -1,
			"desc":   "服务器未初始化完毕",
		})
	}
	reqPath := request.URL.Path
	handlerObj, err := this.get(reqPath)
	if err != nil {
		// http请求错误
		panic(map[string]interface{}{
			"status": -1,
			"desc":   fmt.Sprintf("未知的路由:%s", reqPath),
		})
	}
	if handlerObj.method != "ALL" && strings.ToUpper(method) != handlerObj.method {
		panic(map[string]interface{}{
			"status": -1,
			"desc":   fmt.Sprintf("不允许的访问方式:%s", method),
		})
	}
	username := ""
	userType := -1
	token := request.Header.Get("Authorization")
	if handlerObj.token {
		if ut.IsEmpty(token) {
			panic(map[string]interface{}{
				"status": -1,
				"desc":   fmt.Sprintf("鉴权错误:%s", reqPath),
			})
		}
		mapClaims, err := CheckToken(token)
		if err != nil {
			panic(map[string]interface{}{
				"status": -1,
				"desc":   fmt.Sprintf("鉴权错误:%s", err),
			})
		}
		username = ut.String(mapClaims["username"])
		userType = ut.Int(mapClaims["type"])
	}
	var input map[string]interface{}
	if method == "POST" {
		decoder := json.NewDecoder(request.Body)
		decoder.Decode(&input)
	}

	// 转换form表单数据
	err = request.ParseForm()
	form := request.Form
	params := handlerObj.params
	hasSid, hasLid, uid := 0, "", ""
	var t []interface{}
	for _, v := range params {
		// token字段暂定为系统占用
		if v == "token" {
			t = append(t, token)
			continue
		}
		var tryGetValue interface{} = nil
		if method == "POST" {
			tryGetValue = input[v]
		} else {
			tryGetValue = form.Get(v)
		}
		// sid为部分服务器特有
		if v == "sid" && hasSid == 0 {
			if tryGetValue == "" {
				log.Info("http没有设置sid的值,已经终止本次请求.")
				return
			}
			hasSid = ut.Int(tryGetValue)
			if hasSid == 0 {
				log.Info("http设置的sid值有错误,已经终止本次请求.")
				return
			}
			t = append(t, ut.String(tryGetValue))
			continue
		} else if v == "lid" {
			hasLid = ut.String(tryGetValue)
		} else if v == "uid" {
			uid = ut.String(tryGetValue)
		}
		t = append(t, ut.String(tryGetValue))
	}
	// 对入参列表检查
	t = ut.If(t == nil, []interface{}{}, t)
	// 入参输出
	out := "{}"
	if method == "GET" {
		bytes, err := json.Marshal(form)
		if err == nil {
			out = string(bytes)
		}
	}
	if method == "POST" {
		bytes, err := json.Marshal(input)
		if err == nil {
			out = string(bytes)
		}
	}
	if reqPath != "/" {
		log.Info("I   [%s],%v", reqPath, out)
	}
	// 权限验证
	if !CheckAuth(userType, handlerObj.topic, t) {
		panic(map[string]interface{}{
			"status": -1,
			"desc":   fmt.Sprintf("权限不足:%s", handlerObj.topic),
		})
	}
	// 如果是game服务器的req  需要设置sid
	serve := handlerObj.serve
	switch serve {
	case "game":
		serve = fmt.Sprintf("game@game%d", hasSid)
	case "lobby":
		lid := ""
		if hasLid != "" {
			lid = hasLid
		} else if uid != "" {
			// 通过用户uid获取lid
			lid = rds.GetUserLid(uid)
			if lid == "" {
				log.Error("分配大厅服失败 uid: %v, err: %v", uid, err)
				return
			}
		} else {
			// 获取随机的lid
			lid = rds.GetRandomLid()
		}
		serve = fmt.Sprintf("lobby@lobby%v", lid)
	}

	// 特殊处理
	if handlerObj.topic == "getUpdateMaintainTime" || handlerObj.topic == "getHotUpdateInfo" {
		ip := ""
		log.Info("getUpdateMaintainTime: %v", request.RemoteAddr)
		addr := strings.Split(request.RemoteAddr, ":")
		if len(addr) > 0 {
			ip = addr[0]
		}
		// 接口中ip自动需设置在第一个
		t[0] = ip
	}

	result := this.http.httpHandler(writer, serve, handlerObj.topic, func() []interface{} {
		return t
	})
	// 返回输出
	out = "{}"
	if err == nil {
		out = string(result)
		// 操作成功记录日志
		if handlerObj.isSet {
			AddOperateLog(username, handlerObj.topic, handlerObj.params, t)
		}
		writer.WriteHeader(http.StatusOK)
	}
	if reqPath != "/" {
		log.Info("O   [%s],%s", reqPath, out)
	}
	writer.Write(result)
}

// GenToken 生成登录token
func GenToken(username string, password string, rType int32) (t string, err error) {
	data := make(jwt.MapClaims)
	data["username"] = username
	data["password"] = password
	data["type"] = rType
	data["ts"] = time.Now().Unix() + limit
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, data)
	t, err = token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}
	return t, nil
}

// CheckToken 检查token
func CheckToken(token string) (jwt.MapClaims, error) {
	parse, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})
	if err != nil {
		return nil, err
	}
	mapClaims := parse.Claims.(jwt.MapClaims)
	ts := mapClaims["ts"]
	if ts == nil {
		return nil, errors.New("解析token失败")
	}
	if ts.(float64) < float64(time.Now().Unix()) {
		return nil, errors.New("token已经过期")
	}
	return mapClaims, nil
}

// 接口权限检查
func CheckAuth(userType int, funcName string, t []interface{}) bool {
	if funcName == "sendMailToLogin" && ut.String(t[4]) == "-1" && userType != 0 {
		return false
	}
	return true
}

func (this *Http) httpLogin(username string, password string) (response map[string]interface{}, e error) {
	filter := bson.M{
		"username": username,
		"password": password,
	}
	userCol := &db.UserCol{}
	err := db.GlobalGetTableManager().GetCollection(userCol).FindOne(context.TODO(), filter).Decode(&userCol)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("用户名或密码错误"), nil
	}
	token, err := GenToken(username, password, userCol.Type)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("gen token err:%s", err)), nil
	}
	response = slg.HttpResponseSuccessWithDataWithDesc(token, "登陆成功!")
	return response, nil
}

// 获取后台用户信息
func (this *Http) httpInfo(token string) (response map[string]interface{}, e error) {
	mapClaims, e := CheckToken(token)
	if e != nil {
		return slg.HttpResponseErrorNoDataWithDesc("鉴权错误."), nil
	}
	data := make(map[string]interface{})
	rType := ut.Int(mapClaims["type"])
	switch rType {
	case 0:
		data["roles"] = []string{"admin"}
	case 1:
		data["roles"] = []string{"CS"}
	}
	data["name"] = mapClaims["username"]
	data["avatar"] = "https://husong.vip/img/head.c91dc7d1.jpg"
	data["introduction"] = ""
	data["email"] = ""
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Http) httpCreateRole(username string, password string, uType string) (response map[string]interface{}, e error) {
	bytes := md5.Sum([]byte(password))
	userCol := &db.UserCol{}
	col := db.GlobalGetTableManager().GetCollection(userCol)
	_, err := col.UpdateOne(context.TODO(),
		bson.D{{Key: "username", Value: username}},
		bson.D{{Key: "$set", Value: bson.D{{Key: "password", Value: fmt.Sprintf("%x", bytes)}, {Key: "type", Value: ut.Int(uType)}}}},
		options.Update().SetUpsert(true))
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("创建失败:%s", err)), nil
	}
	response = slg.HttpResponseSuccessNoDataWithDesc("创建成功!")
	return response, nil
}

func (this *Http) httpGetRoles() (response map[string]interface{}, e error) {
	userCol := &db.UserCol{}
	filter := &bson.M{"type": bson.M{"$gt": 0}}
	cur, err := db.GlobalGetTableManager().GetCollection(userCol).Find(context.TODO(), filter)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("获取失败:%s", err)), nil
	}
	defer cur.Close(context.TODO())
	arr := []db.UserCol{}
	err = cur.All(context.TODO(), &arr)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("获取失败:%s", err)), nil
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": arr,
	}), nil
}

func (this *Http) httpSetVersion(_version, _bigVersion, _testVersion string) (response map[string]interface{}, e error) {
	if r, e := setGmConfig("version", _version); e != nil {
		return r, e
	}
	version.Val = _version
	if r, e := setGmConfig("bigVersion", _bigVersion); e != nil {
		return r, e
	}
	bigVersion.Val = _bigVersion
	if r, e := setGmConfig("testVersion", _testVersion); e != nil {
		return r, e
	}
	testVersion.Val = _testVersion
	return slg.HttpResponseSuccessNoDataWithDesc("保存成功"), nil
}

func setGmConfig(key, val string) (response map[string]interface{}, e error) {
	col := &db.MapCol[string]{
		Key: key,
		Val: val,
	}
	filter := db.FieldToBson(col, "Key")
	update := &bson.M{
		"$set": db.FieldToBson(col, "Val"),
	}
	opt := &options.UpdateOptions{}
	opt.SetUpsert(true)
	_, err := db.GlobalGetTableManager().GetCollection(col).UpdateOne(context.TODO(), &filter, &update, opt)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("保存 [%s] 失败", key)), err
	}
	return nil, nil
}

func getGmConfig(key string) (col *db.MapCol[string], err error) {
	col = &db.MapCol[string]{
		Key: key,
	}
	toBson := db.FieldToBson(col, "Key")
	err = db.GlobalGetTableManager().GetCollection(col).FindOne(context.TODO(), &toBson).Decode(&col)
	if err != nil && mongo.ErrNoDocuments != err {
		log.Error(err.Error())
	}
	return col, err
}

// 获取热更新信息
func (this *Http) httpGetHotUpdateInfo(ip, shopPlatform string) (response map[string]interface{}, err error) {
	// 只在第一次的时候去数据库取
	version_lock.Do(func() {
		if data, err := getGmConfig("version"); err == nil {
			version = data
		}
		if data, err := getGmConfig("bigVersion"); err == nil {
			bigVersion = data
		}
		if data, err := getGmConfig("testVersion"); err == nil {
			testVersion = data
		}
		if data, err := getGmConfig("fullServerPers"); err == nil {
			fullServerPers = data
		}
		if data, err := getGmConfig("notEnterServerTime"); err == nil {
			notEnterServerTime = data
		}
		if data, err := getGmConfig("openCdk"); err == nil {
			openCdk = data
		}
		if data, err := getGmConfig("openGuide"); err == nil {
			openGuide = data
		}
	})
	// 根据平台获取游戏下载路径
	key := shopPlatform + "_url"
	_, ok := gameDownloadUrl[key]
	if !ok {
		if gameDownloadUrlCol, err := getGmConfig(key); err == nil {
			gameDownloadUrl[key] = gameDownloadUrlCol.Val
		}
	}
	now := ut.Now()
	maintainTime := getMaintainTime(now, ip)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"clientVersion":      version.Val,
		"clientBigVersion":   bigVersion.Val,
		"testVersion":        testVersion.Val,
		"serverInitTime":     now,
		"serverZoneOffset":   slg.GetServerZoneOffset(),
		"gameDownloadUrl":    gameDownloadUrl[key],
		"maintainTime":       maintainTime,
		"fullServerPers":     ut.Int(fullServerPers.Val),
		"notEnterServerTime": ut.Int(notEnterServerTime.Val),
		"openCdk":            ut.Int(openCdk.Val),
		"openGuide":          ut.Int(openGuide.Val),
		"noticeVersion":      ut.Int(noticeVersion.Val),
	}), nil
}

// 获取服务器信息
func (this *Http) httpGetServerInfo() (response map[string]interface{}, err error) {
	// 只在第一次的时候去数据库取
	version_lock.Do(func() {
		if data, err := getGmConfig("version"); err == nil {
			version = data
		}
		if data, err := getGmConfig("bigVersion"); err == nil {
			bigVersion = data
		}
		if data, err := getGmConfig("testVersion"); err == nil {
			testVersion = data
		}
		if data, err := getGmConfig("fullServerPers"); err == nil {
			fullServerPers = data
		}
		if data, err := getGmConfig("notEnterServerTime"); err == nil {
			notEnterServerTime = data
		}
		if data, err := getGmConfig("openCdk"); err == nil {
			openCdk = data
		}
		if data, err := getGmConfig("openGuide"); err == nil {
			openGuide = data
		}
	})
	now := ut.Now()
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"serverInitTime":     now,
		"serverZoneOffset":   slg.GetServerZoneOffset(),
		"fullServerPers":     ut.Int(fullServerPers.Val),
		"notEnterServerTime": ut.Int(notEnterServerTime.Val),
		"openCdk":            ut.Int(openCdk.Val),
		"openGuide":          ut.Int(openGuide.Val),
		"maintainTime":       ut.Max(int(allServerMaintainTime-now), 0),
		"noticeVersion":      ut.Int(noticeVersion.Val),
	}), nil
}

// 获取服务器是否已满
func (this *Http) httpGetServerLoad() (response map[string]interface{}, err error) {
	now := ut.Now()
	lobbyFull := false
	if now-lobbyLeftLastUpdateTime > LOBBY_LEFT_NUM_INTERVAL {
		updateLobbyLeftNum()
		lobbyFull = lobbyLeftNum == 0
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"lobbyFull": lobbyFull,
	}), nil
}

// 获取维护剩余时间
func (this *Http) httpGetUpdateMaintainTime(ip string) (response map[string]interface{}, err error) {
	maintainTime := getMaintainTime(ut.Now(), ip)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"maintainTime": maintainTime,
	}), nil
}

// 获取公告
func (this *Http) httpGetNotice(lang string) (response map[string]interface{}, err error) {
	if lang == "all" {
		// 获取所有语言
		list := []map[string]interface{}{}
		noticeContentMap.ForEach(func(v, k string) bool {
			list = append(list, map[string]interface{}{
				"lang":    k,
				"content": v,
			})
			return true
		})
		return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
			"noticeVersion": ut.Int(noticeVersion.Val),
			"list":          list,
		}), nil
	}
	content := noticeContentMap.Get(lang)
	if content == "" {
		content = noticeContentMap.Get("en")
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"noticeVersion": ut.Int(noticeVersion.Val),
		"noticeContent": content,
	}), nil
}

// 设置公告
func (this *Http) httpSetNotice(version, contentJson string) (response map[string]interface{}, err error) {
	if _, e := setGmConfig("noticeVersion", version); e != nil {
		return slg.HttpResponseErrorNoDataWithDesc("设置失败"), err
	}
	noticeVersion.Val = version
	dataMap := []map[string]string{}
	err = json.Unmarshal([]byte(contentJson), &dataMap)
	for _, data := range dataMap {
		lang := data["lang"]
		content := data["content"]
		if lang != "" {
			if _, e := setGmConfig("noticeContent_"+lang, content); e != nil {
				return slg.HttpResponseErrorNoDataWithDesc("设置失败"), err
			}
		}
		noticeContentMap.Set(lang, content)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功"), nil
}

// 获取玩家订单信息
func (this *Http) httpGetUserRecharge(userId, sTime, eTime, size, skip string) (response map[string]interface{}, err error) {
	_sTime, _eTime, _size, _skip := ut.Int(sTime), ut.Int(eTime), ut.Int(size), ut.Int(skip)
	orderList, err := recharge.RechargeDb.FindUserRechargeOrders(userId, _sTime, _eTime, _size, _skip)
	rsp := map[string]interface{}{}
	list := []map[string]interface{}{}
	if orderList != nil {
		userMap := map[string]interface{}{}
		for _, v := range orderList {
			info := map[string]interface{}{}
			info["uid"] = v.UID
			info["orderId"] = v.OrderId
			info["userId"] = v.UserId
			info["purchaseTime"] = v.PurchaseTime
			info["currencyType"] = v.CurrencyType
			info["payAmount"] = v.PayAmount
			info["state"] = v.State
			info["platform"] = v.Platform
			if v.ProductId == slg.BATTLE_PASS_PRODUCT_ID {
				info["name"] = "宝典"
			} else if cfg := config.GetRechargeConfByProductId(v.ProductId); cfg != nil {
				info["id"] = cfg["id"]
				info["name"] = "元宝x" + ut.String(cfg["ingot"])
			}
			curUserId := v.UserId
			if userMap[curUserId] == nil {
				lid := rds.GetUserLid(userId)
				rst, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(lid, slg.RPC_GET_USER_INFO, userId))
				if err == "" {
					userMap[curUserId] = rst
				}
			}
			userInfo := ut.MapInterface(userMap[curUserId])
			if userInfo != nil {
				info["nickname"] = userInfo["nickname"]
				if area := ut.String(userInfo["area"]); area == "" {
					userInfo["area"] = geoip.GetGeoInfo(ut.String(userInfo["ip"]))
				}
				info["area"] = userInfo["area"]
			}
			list = append(list, info)
		}
	}
	rsp["list"] = list
	rsp["isDebug"] = slg.IsDebug()
	return slg.HttpResponseSuccessWithDataNoDesc(rsp), nil
}

// 测试服订单退款
func (this *Http) httpTestOrderRefund(orderId, platform string) (response map[string]interface{}, err error) {
	if !slg.IsDebug() {
		// 仅测试环境有效
		return slg.HttpResponseErrorNoDataWithDesc("仅测试环境有效"), err
	}
	this.handleOrderRefund(orderId, platform, "test", "gm", slg.SERVER_AREA)
	return slg.HttpResponseSuccessNoDataWithDesc("退款成功"), nil
}

// 获取谷歌术语表默认配置
func (this *Http) httpGetGoogleGlossryParam() (response map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"id":       sdk.GOOGLE_TRANSLATE_GLOSSARY_ID,
		"uri":      sdk.GOOGLE_TRANSLATE_GLOSSARY_URI,
		"creating": sdk.IsGlossaryCreating(),
		"deleting": sdk.IsGlossaryDeleting(),
	}), nil
}

// 创建谷歌术语表
func (this *Http) httpCreateGoogleGlossry(id, uri string) (response map[string]interface{}, err error) {
	err = sdk.CreateGlossary(id, uri)
	if err != nil {
		return slg.HttpResponseErrorNoDataNoDesc(), err
	}
	return slg.HttpResponseSuccessNoDataWithDesc("创建中"), nil
}

// 删除谷歌术语表
func (this *Http) httpDelGoogleGlossry(id string) (response map[string]interface{}, err error) {
	err = sdk.DelGlossary(id)
	if err != nil {
		return slg.HttpResponseErrorNoDataNoDesc(), err
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除中"), nil
}

// 初始化公告
func InitNotify() {
	if data, err := getGmConfig("noticeVersion"); err == nil {
		noticeVersion = data
	} else {
		return
	}
	filter := bson.M{
		"key": bson.M{
			"$regex": "noticeContent_",
		},
	}
	cur, err := db.GlobalGetTableManager().GetCollection(&db.MapCol[string]{}).Find(context.TODO(), filter)
	if err != nil {
		log.Error("InitNotify db err: %v", err)
		return
	}
	defer cur.Close(context.TODO())
	retList := []db.MapCol[string]{}
	cur.All(context.TODO(), &retList)
	for _, v := range retList {
		strArr := strings.Split(v.Key, "_")
		if len(strArr) < 2 {
			continue
		}
		lang := strArr[1]
		noticeContentMap.Set(lang, v.Val)
	}
}

// 设置额外参数（服务器不做处理 客户端调用http获取）
func (this *Http) httpSetExtraParams(_fullServerPers, _notEnterServerTime, _openCdk, _openGuide string) (response map[string]interface{}, e error) {
	logic := func(key, val string) (response map[string]interface{}, e error) {
		col := &db.MapCol[string]{
			Key: key,
			Val: val,
		}
		filter := db.FieldToBson(col, "Key")
		update := &bson.M{
			"$set": db.FieldToBson(col, "Val"),
		}
		opt := &options.UpdateOptions{}
		opt.SetUpsert(true)
		_, err := db.GlobalGetTableManager().GetCollection(col).UpdateOne(context.TODO(), &filter, &update, opt)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("保存 [%s] 失败", key)), err
		}
		return nil, nil
	}
	if r, e := logic("fullServerPers", _fullServerPers); e != nil {
		return r, e
	}
	fullServerPers.Val = _fullServerPers
	if r, e := logic("notEnterServerTime", _notEnterServerTime); e != nil {
		return r, e
	}
	notEnterServerTime.Val = _notEnterServerTime
	if r, e := logic("openCdk", _openCdk); e != nil {
		return r, e
	}
	openCdk.Val = _openCdk
	if r, e := logic("openGuide", _openGuide); e != nil {
		return r, e
	}
	openGuide.Val = _openGuide
	return slg.HttpResponseSuccessNoDataWithDesc("保存成功"), nil
}

// 添加平台对应的游戏下载链接
func (this *Http) httpAddUpdateUrl(shopPlatform, url string) (response map[string]interface{}, err error) {
	logic := func(key, val string) (response map[string]interface{}, e error) {
		col := &db.MapCol[string]{
			Key: key,
			Val: val,
		}
		filter := db.FieldToBson(col, "Key")
		update := &bson.M{
			"$set": db.FieldToBson(col, "Val"),
		}
		opt := &options.UpdateOptions{}
		opt.SetUpsert(true)
		_, err := db.GlobalGetTableManager().GetCollection(col).UpdateOne(context.TODO(), &filter, &update, opt)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("添加 [%s] 失败", key)), err
		}
		return nil, nil
	}
	key := shopPlatform + "_url"
	if r, e := logic(key, url); e != nil {
		return r, e
	}
	gameDownloadUrl[key] = url
	return slg.HttpResponseSuccessNoDataWithDesc("添加成功"), nil
}

// 删除平台对应的游戏下载链接
func (this *Http) httpDelUpdateUrl(shopPlatform string) (response map[string]interface{}, err error) {
	logic := func(key string) (response map[string]interface{}, e error) {
		col := &db.MapCol[string]{
			Key: key,
		}
		filter := db.FieldToBson(col, "Key")
		_, err := db.GlobalGetTableManager().GetCollection(col).DeleteOne(context.TODO(), &filter)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(fmt.Sprintf("删除 [%s] 失败", key)), err
		}
		return nil, nil
	}
	key := shopPlatform + "_url"
	if r, e := logic(key); e != nil {
		return r, e
	}
	delete(gameDownloadUrl, key)
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功"), nil
}

// 获取多个平台对应的游戏下载链接
func (this *Http) httpGetUpdateUrls(shopPlatforms string) (response map[string]interface{}, err error) {
	shopPlatformList := strings.Split(shopPlatforms, "&")
	if len(shopPlatformList) == 0 {
		return
	}
	logic := func(key string) (col *db.MapCol[string], err error) {
		col = &db.MapCol[string]{
			Key: key,
		}
		toBson := db.FieldToBson(col, "Key")
		err = db.GlobalGetTableManager().GetCollection(col).FindOne(context.TODO(), &toBson).Decode(&col)
		if mongo.ErrNoDocuments == err {
			return col, nil
		}
		return col, err
	}
	list := []map[string]string{}
	for _, playform := range shopPlatformList {
		key := playform + "_url"
		url, ok := gameDownloadUrl[key]
		if !ok {
			if gameDownloadUrlCol, err := logic(key); err == nil {
				gameDownloadUrl[key] = gameDownloadUrlCol.Val
			}
		}
		list = append(list, map[string]string{"key": playform, "url": url})
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 错误上报
func (this *Http) httpErrorReport(uid, platform, errors, version string) (response map[string]interface{}, err error) {
	log.Info("httpErrorReport uid: %v, platform: %v, version: %v, errors: %v", uid, platform, version, errors)
	col := &db.ErrorReport{}
	errList := strings.Split(errors, "|")
	datas := []interface{}{}
	for _, errInfo := range errList {
		data := &db.ErrorReport{}
		err = json.Unmarshal([]byte(errInfo), &data)
		if err != nil {
			log.Error("httpErrorReport parse errors err: %v", err)
			continue
		}
		data.Uid = uid
		data.Platform = platform
		data.Version = version
		bsonData := db.FieldToBsonAuto(data)
		datas = append(datas, bsonData)
	}
	if len(datas) == 0 {
		return
	}
	_, err = db.GlobalGetTableManager().GetCollection(col).InsertMany(context.TODO(), datas)
	if err != nil {
		log.Error("httpErrorReport db err: %v", err)
	}
	// 更新已上报的版本
	mapCol := &db.MapCol[string]{
		Key: "errReportVersions",
	}
	toBson := db.FieldToBson(mapCol, "Key")
	err = db.GlobalGetTableManager().GetCollection(mapCol).FindOne(context.TODO(), &toBson).Decode(&mapCol)
	if err == nil || err == mongo.ErrNoDocuments {
		newVersions := version
		isNew := true
		if mapCol.Val != "" {
			vl := strings.Split(mapCol.Val, ",")
			for _, v := range vl {
				if v == version {
					isNew = false
					break
				}
			}
			if isNew {
				newVersions = mapCol.Val + "," + version
				mapCol.Val = newVersions
				update := &bson.M{
					"$set": db.FieldToBson(mapCol, "Val"),
				}
				_, err := db.GlobalGetTableManager().GetCollection(mapCol).UpdateOne(context.TODO(), &toBson, &update)
				if err != nil {
					log.Error("httpErrorReport update version err: %v", err)
				}
			}
		} else if isNew {
			mapCol.Val = newVersions
			_, err := db.GlobalGetTableManager().GetCollection(mapCol).InsertOne(context.TODO(), db.FieldToBsonAuto(mapCol))
			if err != nil {
				log.Error("httpErrorReport insert version err: %v", err)
			}
		}
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取客户端上报的错误
func (this *Http) httpGetErrorReport(eType, uid, version, sortField, sortType, size, skip string) (response map[string]interface{}, err error) {
	col := &db.ErrorReport{
		Type:    eType,
		Uid:     uid,
		Version: version,
	}
	filter := db.FieldToBson(col, "Type", "Version")
	if uid != "" {
		filter = db.FieldToBson(col, "Type", "Version", "Uid")
	}
	opt := options.Find()
	sType := ut.Atoi(sortType)
	// 默认排序为时间倒序
	if sortField == "" {
		opt.Sort = bson.M{"time": -1}
	}
	if sortField == "time" || sortField == "level" {
		sortBson := bson.M{}
		sort := ut.If(sType == 1, -1, 1)
		sortBson[sortField] = sort
		opt.Sort = sortBson
	}

	cur, err := db.GlobalGetTableManager().GetCollection(col).Find(context.TODO(), filter, opt)
	if err != nil {
		log.Error("httpGetErrorReport db err: %v", err)
		return slg.HttpResponseSuccessNoDataNoDesc(), err
	}
	defer cur.Close(context.TODO())
	retList := []*db.ErrorReportRetInfo{}
	retMap := map[string]*db.ErrorReportRetInfo{}
	for cur.Next(context.TODO()) {
		var data db.ErrorReport
		if e := cur.Decode(&data); e == nil {
			key := ut.String(data.Level) + "|" + data.Version + "|" + data.Platform + "|" + data.Exception
			ret, ok := retMap[key]
			if ok {
				ret.Count++
				if data.Time > ret.Info.Time {
					ret.Info.Time = data.Time
				}
				newUid := true
				for _, u := range ret.Uids {
					if data.Uid == u {
						newUid = false
						break
					}
				}
				if newUid {
					ret.Uids = append(ret.Uids, data.Uid)
				}
			} else {
				ret = &db.ErrorReportRetInfo{
					Info:  &data,
					Uids:  []string{data.Uid},
					Count: 1,
				}
				retList = append(retList, ret)
				retMap[key] = ret
			}
		}
	}
	switch sortField {
	case "count":
		sort.Slice(retList, func(i, j int) bool {
			if sType == 1 {
				return retList[i].Count > retList[j].Count
			} else {
				return retList[i].Count < retList[j].Count
			}
		})
	case "userNum":
		sort.Slice(retList, func(i, j int) bool {
			if sType == 1 {
				return len(retList[i].Uids) > len(retList[j].Uids)
			} else {
				return len(retList[i].Uids) < len(retList[j].Uids)
			}
		})
	}
	// 排序之后分页
	_size := ut.Int(size)
	_skip := ut.Int(skip)
	tail := ut.Min(len(retList), _size+_skip)
	retList = retList[_skip:tail]
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"infoList": retList,
	}), nil
}

// 获取上报错误的所有版本号
func (this *Http) httpGetErrorReportVersions() (response map[string]interface{}, err error) {
	versionList := []string{}
	mapCol := &db.MapCol[string]{
		Key: "errReportVersions",
	}
	toBson := db.FieldToBson(mapCol, "Key")
	err = db.GlobalGetTableManager().GetCollection(mapCol).FindOne(context.TODO(), &toBson).Decode(&mapCol)
	if err == nil || err == mongo.ErrNoDocuments {
		if mapCol.Val != "" {
			vl := strings.Split(mapCol.Val, ",")
			for _, v := range vl {
				versionList = append(versionList, v)
			}
		}
	} else {
		log.Error("getErrorReportVersions db err: %v", err)
	}
	sort.Slice(versionList, func(i, j int) bool {
		return versionList[i] > versionList[j]
	})
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"versionList": versionList,
	}), nil
}

// 问题反馈
func (this *Http) httpFeedback(uid, distinctId, serverId, deviceOS, platform, version, content string) (response map[string]interface{}, err error) {
	col := &db.Feedback{
		Uid:        ut.ID(),
		UserId:     uid,
		DistinctId: distinctId,
		Sid:        ut.Int(serverId),
		DeviceOS:   deviceOS,
		Platform:   platform,
		Version:    version,
		Content:    content,
		Time:       ut.Now(),
	}
	db.GlobalGetTableManager().GetCollection(col).InsertOne(context.TODO(), col)
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取问题反馈的所有版本
func (this *Http) httpGetFeedbackVersions() (response map[string]interface{}, err error) {
	col := &db.Feedback{}
	distinctValues, err := db.GlobalGetTableManager().GetCollection(col).Distinct(context.TODO(), "version", bson.M{}, options.Distinct())
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": distinctValues,
	}), nil
}

// 获取问题反馈
func (this *Http) httpGetFeedback(uid, version, responseType string) (response map[string]interface{}, err error) {
	list, err := GetFeedback(uid, version, responseType)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 回复问题反馈
func (this *Http) httpResponseFeedback(id, content, title string) (response map[string]interface{}, err error) {
	col := &db.Feedback{}
	toBson := bson.M{"uid": id}
	err = db.GlobalGetTableManager().GetCollection(col).FindOne(context.TODO(), &toBson).Decode(&col)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据不存在"), nil
	}
	if col.Response != nil {
		return slg.HttpResponseErrorNoDataWithDesc("该反馈已回复"), nil
	}
	if content == "" || title == "" {
		return slg.HttpResponseErrorNoDataWithDesc("回复内容不能为空"), nil
	}
	userData := &lobby.TableData{}
	if col.UserId == "" {
		if col.DistinctId != "" {
			// 没有玩家uid 先通过设备id去查找
			e := mgo.GetCollection(slg.DB_COLLECTION_NAME_USER).FindOne(context.TODO(), bson.M{"distinct_id": col.DistinctId}).Decode(userData)
			if e != nil || col.UserId == "" {
				return slg.HttpResponseErrorNoDataWithDesc("该访客id无法定位到玩家"), nil
			}
			col.UserId = userData.Uid
		} else {
			return slg.HttpResponseErrorNoDataWithDesc("没有uid或访客id无法定位到玩家"), nil
		}
	}
	// 记录回复并发送邮件
	col.Response = map[string]string{"title": title, "content": content}
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, 0, 0, title, content, "-1", col.UserId, nil)
	update := &bson.M{
		"$set": db.FieldToBson(col, "Response", "Uid"),
	}
	_, err = db.GlobalGetTableManager().GetCollection(col).UpdateOne(context.TODO(), &toBson, update)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("回复失败"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("回复成功"), nil
}

func (this *Http) httpSaveGoCode(id, desc, code, params string) (response map[string]interface{}, err error) {
	doc := &bson.M{
		"code":   code,
		"desc":   desc,
		"params": params,
	}
	if id == "" {
		_, err = db.GlobalGetTableManager().GetCollection(&db.GoCodeCol{}).InsertOne(context.TODO(), &doc, &options.InsertOneOptions{})
	} else {
		objectID, _ := primitive.ObjectIDFromHex(id)
		_, err = db.GlobalGetTableManager().GetCollection(&db.GoCodeCol{}).UpdateOne(context.TODO(), &bson.M{
			"_id": objectID,
		}, &bson.M{"$set": &doc}, options.Update().SetUpsert(false))
	}
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("保存失败"), err
	}
	response = slg.HttpResponseSuccessNoDataWithDesc("保存成功")
	return response, nil
}

func (this *Http) httpGetGoCode() (response map[string]interface{}, err error) {
	filter := bson.M{}
	cur, err := db.GlobalGetTableManager().GetCollection(&db.GoCodeCol{}).Find(context.TODO(), filter)
	if err != nil {
		return nil, err
	}
	var data []struct {
		ID     primitive.ObjectID `bson:"_id"`
		Desc   string             `bson:"desc"`
		Code   string             `bson:"code"`
		Params string             `bson:"params"`
	}
	cur.All(context.TODO(), &data)
	cur.Close(context.TODO())
	return slg.HttpResponseSuccessWithDataNoDesc(data), nil
}

func (this *Http) httpDelGoCode(id string) (response map[string]interface{}, err error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	_, err = db.GlobalGetTableManager().GetCollection(&db.GoCodeCol{}).DeleteOne(context.TODO(), &bson.M{
		"_id": objectID,
	})
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("刪除失败"), err
	}
	return slg.HttpResponseSuccessNoDataWithDesc("刪除成功"), nil
}

func (this *Http) httpGetTime() (response map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(ut.Now()), nil
}

// 热更新开始上报
func (this *Http) httpHotUpdateStart(uid, distinctId, curVer, version string) (response map[string]interface{}, err error) {
	hotUpdateInfo := &db.HotUpdateReport{
		Uid:        uid,
		DistinctId: distinctId,
		CurVer:     curVer,
		Version:    version,
		State:      0,
	}
	_, err = db.GlobalGetTableManager().GetCollection(&db.HotUpdateReport{}).InsertOne(context.TODO(), db.FieldToBsonAuto(hotUpdateInfo))
	if err != nil {
		log.Warning("httpHotUpdateStart err: %v", err)
	}
	return slg.HttpResponseSuccessNoDataWithDesc(""), nil
}

// 热更新结束上报
func (this *Http) httpHotUpdateEnd(uid, distinctId, curVer, version, costTime, size, files, os string) (response map[string]interface{}, err error) {
	filter := &db.HotUpdateReport{
		Uid:        uid,
		DistinctId: distinctId,
		CurVer:     curVer,
		Version:    version,
		State:      0,
	}
	hotUpdateInfo := &db.HotUpdateReport{
		Uid:        uid,
		DistinctId: distinctId,
		CurVer:     curVer,
		Version:    version,
		CostTime:   ut.Int(costTime),
		Size:       ut.Int(size),
		Files:      files,
		Os:         os,
		State:      1,
	}
	update := &bson.M{
		"$set": db.FieldToBsonAuto(hotUpdateInfo),
	}
	opt := &options.UpdateOptions{}
	opt.SetUpsert(false)
	_, err = db.GlobalGetTableManager().GetCollection(&db.HotUpdateReport{}).UpdateOne(context.TODO(), db.FieldToBsonAuto(filter), update, opt)
	if err != nil {
		log.Warning("httpHotUpdateEnd err: %v", err)
	}
	return slg.HttpResponseSuccessNoDataWithDesc(""), nil
}

// 获取指定版本热更情况
func (this *Http) httpGetHotUpdateReportInfo(version string) (response map[string]interface{}, err error) {
	plyNum, finishNum := 0, 0
	filter := bson.M{"version": version}
	cursor, err := db.GlobalGetTableManager().GetCollection(&db.HotUpdateReport{}).Find(context.TODO(), filter)
	if err != nil {
		log.Warning("httpGetHotUpdateReportInfo err: %v", err)
	} else {
		var infos []db.HotUpdateReport
		cursor.All(context.TODO(), &infos)
		for _, v := range infos {
			plyNum++
			if v.State == 1 {
				finishNum++
			}
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"plyNum":    plyNum,
		"finishNum": finishNum,
	}), nil
}

// 设置服务器维护时间
func (this *Http) httpSetServerStopTime(time string) (response map[string]interface{}, err error) {
	allServerMaintainTime = ut.Now() + ut.Int64(time)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"time": allServerMaintainTime,
	}), nil
}

// 获取服务器维护时间
func (this *Http) httpGetServerStopTime() (response map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"time": allServerMaintainTime,
	}), nil
}

// 添加操作记录
func AddOperateLog(username, route string, paramNames []string, params []interface{}) {
	if len(params) < len(paramNames) {
		log.Warning("AddOperateLog params err, username: %v, route: %v, paramNames: %v, params: %v", username, route, paramNames, params)
		return
	}
	paramMap := map[string]interface{}{}
	for i, v := range paramNames {
		paramMap[v] = params[i]
	}
	opLog := &db.OperationLog{
		Username: username,
		Route:    route,
		Params:   paramMap,
		Time:     ut.Now(),
	}
	_, err := db.GlobalGetTableManager().GetCollection(opLog).InsertOne(context.TODO(), db.FieldToBsonAuto(opLog))
	if err != nil {
		log.Warning("AddOperateLog err: %v", err)
	}
}

// 修改后台账号密码
func (this *Http) httpChangePassword(username, oldPassword, newPassword string) (response map[string]interface{}, err error) {
	if oldPassword == newPassword {
		return slg.HttpResponseErrorNoDataWithDesc("密码相同!"), nil
	}
	filter := bson.M{
		"username": username,
		"password": oldPassword,
	}
	userCol := &db.UserCol{}
	err = db.GlobalGetTableManager().GetCollection(userCol).FindOne(context.TODO(), filter).Decode(&userCol)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("密码错误"), nil
	}
	userCol.Password = newPassword
	db.GlobalGetTableManager().GetCollection(userCol).UpdateOne(context.TODO(), filter, bson.M{"$set": userCol})
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

// 查询操作记录
func (this *Http) httpQueryOperateLog(username, route, size, skip string) (response map[string]interface{}, err error) {
	col := &db.OperationLog{
		Username: username,
		Route:    route,
	}
	filter := bson.M{}
	if username != "" {
		filter["username"] = username
	}
	if route != "" {
		filter["route"] = route
	}

	opt := options.Find()
	// 默认排序为时间倒序
	opt.Sort = bson.M{"time": -1}
	cur, err := db.GlobalGetTableManager().GetCollection(col).Find(context.TODO(), filter, opt)
	if err != nil {
		log.Error("httpQueryOperateLog db err: %v", err)
		return slg.HttpResponseSuccessNoDataNoDesc(), err
	}
	defer cur.Close(context.TODO())
	retList := []db.OperationLog{}
	cur.All(context.TODO(), &retList)
	// 排序之后分页
	_size := ut.Int(size)
	_skip := ut.Int(skip)
	tail := ut.Min(len(retList), _size+_skip)
	retList = retList[_skip:tail]
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"infoList": retList,
	}), nil
}

// 获取兑换码模版
func (this *Http) httpGetCdkModel() (response map[string]interface{}, err error) {
	cdkModelLock.Do(func() {
		// 第一次从数据库获取
		cdkModelCol := &db.CdkModel{}
		filter := bson.M{}
		cur, err := db.GlobalGetTableManager().GetCollection(cdkModelCol).Find(context.TODO(), filter)
		if err != nil {
			return
		}
		defer cur.Close(context.TODO())
		arr := []db.CdkModel{}
		err = cur.All(context.TODO(), &arr)
		// 倒序
		for _, v := range arr {
			cdkModelList = append([]*db.CdkModel{&v}, cdkModelList...)
		}
	})
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": cdkModelList,
	}), nil
}

// 添加兑换码模版
func (this *Http) httpAddCdkModel(name, receiver, items, isClaim string) (response map[string]interface{}, err error) {
	var item []*g.TypeObj
	err = json.Unmarshal([]byte(items), &item)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("处理奖励数据失败!!"), nil
	}
	_isClaim, _ := strconv.ParseBool(isClaim)
	cdkModel := &db.CdkModel{
		Name:           name,
		Receiver:       receiver,
		Items:          item,
		IsForeverClaim: _isClaim,
	}
	filter := db.FieldToBson(cdkModel, "name")
	update := &bson.M{
		"$set": db.FieldToBsonAuto(cdkModel),
	}
	opt := &options.UpdateOptions{}
	opt.SetUpsert(true)
	_, err = db.GlobalGetTableManager().GetCollection(cdkModel).UpdateOne(context.TODO(), filter, update, opt)
	if err != nil {
		log.Error("httpAddCdkModel name: %v, items: %v err: %v", name, items, err)
		return slg.HttpResponseErrorNoDataWithDesc("添加兑换码模版失败"), err
	}
	exist := false
	for _, v := range cdkModelList {
		if v.Name == name {
			exist = true
			break
		}
	}
	if !exist {
		cdkModelList = append([]*db.CdkModel{cdkModel}, cdkModelList...)
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), err
}

var noticeMap = map[string]bool{}

// 公告
func (this *Http) httpNotice(sid string, id string, params, lastTime, internal, count, sTime, uid string) (ret map[string]interface{}, err error) {
	serverId, nid, paramsArr := ut.Int32(sid), ut.Int32(id), strings.Split(params, "|")
	now := ut.Now()
	endTime := ut.Int64(lastTime)*ut.TIME_MINUTE + now
	startTime := ut.Int64(sTime)
	if ut.Int(lastTime) > 0 {
		// 周期性定时发送
		inter := ut.Int(internal) * ut.TIME_SECOND
		cnt := ut.Int(count)
		nextNoticeId += 1
		if uid == "" {
			uid = ut.ID()
		}
		noticeMap[uid] = true
		go func(id int, uid string) {
			round, curCount, waitTime := 0, 0, 0
			tiker := time.NewTicker(time.Second * 1)
			defer tiker.Stop()
			for noticeMap[uid] && nextNoticeId == id && endTime >= now {
				<-tiker.C
				if startTime > 0 && now < startTime {
					continue
				}
				if curCount < cnt {
					// 本轮未发送完 直接发送
					this.GameNotice(serverId, nid, paramsArr)
					curCount++
				} else {
					// 本轮已发送完 累计时间
					waitTime += ut.TIME_SECOND
					if waitTime >= inter {
						// 进入下轮发送
						this.GameNotice(serverId, nid, paramsArr)
						round++
						curCount = 1
						waitTime = 0
					}
				}
			}
		}(nextNoticeId, uid)
	} else {
		err = this.GameNotice(serverId, nid, paramsArr)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc(err.Error()), nil
		}
	}
	return slg.HttpResponseSuccessNoDataWithDesc("发送成功!"), nil
}

// 查询问题反馈
func GetFeedback(uid, version, responseType string) (arr []db.Feedback, err error) {
	col := &db.Feedback{}
	filter := bson.M{}
	if uid != "" {
		filter["user_id"] = uid
	}
	if version != "" {
		filter["version"] = version
	}
	if responseType != "0" {
		switch responseType {
		case "1":
			// 未回复
			filter["response"] = bson.M{"$exists": false}
		case "2":
			// 已回复
			filter["response"] = bson.M{"$exists": true}
		}
	}
	cur, err := db.GlobalGetTableManager().GetCollection(col).Find(context.TODO(), filter)
	err = cur.All(context.TODO(), &arr)
	return
}

// 客户端请求统计
type ReqLog struct {
	Topic string `bson:"topic"`
	Uid   string `bson:"uid"`
	Time  int    `bson:"time"`
}
type ReqLogList struct {
	List []*ReqLog
	deadlock.RWMutex
}

// 客户端请求统计相关
var (
	reqLogMap          = ut.NewMapLock[string, *ReqLogList]()
	reqLogLastSaveTime = time.Now().UnixMilli()
)

// 添加客户端请求统计
func AddReqLog(uid, topic string, time int) {
	list := reqLogMap.Get(topic)
	if list == nil {
		list = &ReqLogList{List: []*ReqLog{}}
		reqLogMap.Set(topic, list)
	}
	list.Lock()
	list.List = append(list.List, &ReqLog{
		Topic: topic,
		Uid:   uid,
		Time:  time,
	})
	list.Unlock()
}

// 每天凌晨4点保存请求统计到数据库
func (this *Http) RunSaveReqLog() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		for isRunning {
			<-tiker.C
			timeFour := ut.TodayHourTime(4)
			now := time.Now().UnixMilli()
			if reqLogLastSaveTime < timeFour && now >= timeFour {
				reqLogLastSaveTime = now
				saveReqLog()
			}
		}
	}()
}

// 保存客户端请求统计到数据库
func saveReqLog() {
	toSave := [][]*ReqLog{}
	reqLogMap.ForEach(func(v *ReqLogList, k string) bool {
		v.Lock()
		if len(v.List) > 0 {
			toSave = append(toSave, v.List)
			v.List = []*ReqLog{} // 清空原日志
		}
		v.Unlock()
		return true
	})

	for _, logs := range toSave {
		colName := slg.DB_COLLECTION_NAME_REQUEST_LOG
		dataList := []interface{}{}
		for _, log := range logs {
			dataList = append(dataList, log)
			if len(dataList) >= 100 {
				mgo.GetCollection(colName).InsertMany(context.TODO(), dataList)
				dataList = []interface{}{}
			}
		}
		if len(dataList) > 0 {
			mgo.GetCollection(colName).InsertMany(context.TODO(), dataList)
		}
	}
}

// 获取客户端请求记录
func (this *Http) httpGetReqLogs(startTime, endTime, topic string) (response map[string]interface{}, err error) {
	start, end := ut.Int64(startTime), ut.Int64(endTime)
	sumMap := map[string]int{}
	userSumMap := map[string]map[string]int{}
	handleLog := func(reqLog *ReqLog) {
		// 数量累计
		sumMap[reqLog.Topic]++
		// 玩家对应数量累计
		userMap := userSumMap[reqLog.Topic]
		if userMap == nil {
			userMap = map[string]int{}
			userSumMap[reqLog.Topic] = userMap
		}
		userMap[reqLog.Uid]++
	}
	if start < reqLogLastSaveTime {
		// 从数据库获取
		col := slg.DB_COLLECTION_NAME_REQUEST_LOG
		filter := bson.M{"time": bson.M{"$gte": start, "$lte": end}}
		if topic != "" {
			filter["topic"] = topic
		}
		cursor, err := mgo.GetCollection(col).Find(context.Background(), filter)
		if err == nil {
			for cursor.Next(context.Background()) {
				var result *ReqLog
				if err := cursor.Decode(&result); err != nil {
					continue
				}
				handleLog(result)
			}
		}
		cursor.Close(context.Background())
	}
	if end > reqLogLastSaveTime {
		// 从内存中获取
		reqLogMap.ForEach(func(v *ReqLogList, k string) bool {
			if topic != "" && k != topic {
				return true
			}
			v.RLock()
			for _, m := range v.List {
				handleLog(m)
			}
			v.RUnlock()
			return true
		})
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"sumMap":     sumMap,
		"userSumMap": userSumMap,
	}), nil
}

// 更新大厅服剩余空位数量
func updateLobbyLeftNum() {
	rst, err := rds.RdsHGetAll(rds.RDS_LOBBY_LOAD_MAP_KEY)
	if err != nil {
		log.Error("updateLobbyLeftNum err: %v", err)
	}
	leftNum := 0
	for _, v := range rst {
		leftNum += rds.LOBBY_ASSIGN_MAX_NUM - ut.Int(v)
	}
	lobbyLeftNum = leftNum
	lobbyLeftLastUpdateTime = ut.Now()
}

// 获取维护剩余时间
func getMaintainTime(now int64, ip string) int {
	maintainTime := ut.Max(int(allServerMaintainTime-now), 0)
	if maintainTime > 0 {
		curLimitType, err := rds.RdsGet(rds.RDS_LOGIN_LIMIT_TYPE_KEY)
		if err != nil || curLimitType == "" || ut.Int(curLimitType) != slg.LOGIN_LIMIT_TYPE_WHITE_LIST {
			// 登录限制不是只允许白名单登录 则返回正常维护时间
			return maintainTime
		}
		// 维护中 如果在白名单则返回维护时间为0
		rst, err := rds.RdsHGet(rds.RDS_LOGIN_WHITE_MAP_KEY, ip)
		if err == nil && rst != "" {
			maintainTime = 0
		}
	}
	return maintainTime
}

// dc关注有礼
func (this *Http) httpGetDcFollowGift(code string) (response map[string]interface{}, err error) {
	log.Info("httpGetDcFollowGift code: %v", code)
	uidNum := ut.DecodeBase62(code) / 1000
	log.Info("httpGetDcFollowGift uid: %v", uidNum)
	uid := ut.String(uidNum)
	lid := rds.GetUserLid(uid)
	if lid != "" {
		// 通知大厅服
		_, err := this.InvokeLobbyRpc(lid, slg.RPC_GET_DC_FOLLOW_GIFT, uid)
		if err == "" {
			return map[string]interface{}{"code": 200}, nil
		}
	}
	return map[string]interface{}{"code": -1}, nil
}

func (this *Http) InitHandleFunc() {
	container := &HandlerContainer{
		http:    this,
		routers: make(map[string]*BaseHandler),
	}
	http.Handle("/", container)
	// 参数的token字段已经被占用,如果参数列表有token则会把用户访问的Authorization传递过去
	// NewPostBaseHandler => post访问
	// NewGetBaseHandler => get访问
	// NewBaseHandler 可以指定ALL的访问方式
	container.
		// http client
		push("/", NewBaseHandler("http", "getTime", []string{}, "ALL", false, false)).
		push("/getHotUpdateInfo", NewPostBaseHandler("http", "getHotUpdateInfo", []string{"ip", "shopPlatform"}, false, false)).
		push("/getServerInfo", NewPostBaseHandler("http", "getServerInfo", []string{}, false, false)).
		push("/getUpdateMaintainTime", NewPostBaseHandler("http", "getUpdateMaintainTime", []string{"ip"}, false, false)).
		push("/errorReport", NewPostBaseHandler("http", "errorReport", []string{"uid", "platform", "errors", "version"}, false, false)).
		push("/hotUpdateEnd", NewPostBaseHandler("http", "hotUpdateEnd", []string{"uid", "distinctId", "curVer", "version", "costTime", "size", "files", "os"}, false, false)).
		push("/hotUpdateStart", NewPostBaseHandler("http", "hotUpdateStart", []string{"uid", "distinctId", "curVer", "version"}, false, false)).
		push("/feedback", NewPostBaseHandler("http", "feedback", []string{"uid", "distinctId", "serverId", "deviceOS", "platform", "version", "content"}, false, false)).
		push("/getServerLoad", NewPostBaseHandler("http", "getServerLoad", []string{}, false, false)).
		push("/getNotice", NewPostBaseHandler("http", "getNotice", []string{"lang"}, false, false)).
		push("/getDcFollowGift", NewPostBaseHandler("http", "getDcFollowGift", []string{"code"}, false, false)).

		// http 支付
		push("/appleRefundNotify", NewPostBaseHandler("http", "appleRefundNotify", []string{"signedPayload"}, false, false)).
		push("/googleNotify", NewPostBaseHandler("http", "googleNotify", []string{"message"}, false, false)).

		// http 后台
		push("/users/login", NewPostBaseHandler("http", "login", []string{"username", "password"}, false, false)).
		push("/users/info", NewPostBaseHandler("http", "info", []string{"token"}, false, false)).
		push("/saveGoCode", NewPostBaseHandler("http", "saveGoCode", []string{"id", "desc", "code", "params"}, true, true)).
		push("/getGoCode", NewPostBaseHandler("http", "getGoCode", []string{}, true, false)).
		push("/delGoCode", NewPostBaseHandler("http", "delGoCode", []string{"id"}, true, false)).
		push("/setVersion", NewPostBaseHandler("http", "setVersion", []string{"version", "bigVersion", "testVersion"}, true, true)).
		push("/setExtraParams", NewPostBaseHandler("http", "setExtraParams", []string{"fullServerPers", "notEnterServerTime", "openCdk", "openGuide"}, true, true)).
		push("/getErrorReport", NewPostBaseHandler("http", "getErrorReport", []string{"eType", "uid", "version", "sortField", "sortType", "size", "skip"}, true, false)).
		push("/getErrorReportVersions", NewPostBaseHandler("http", "getErrorReportVersions", []string{}, true, false)).
		push("/addUpdateUrl", NewPostBaseHandler("http", "addUpdateUrl", []string{"shopPlatform", "url"}, true, true)).
		push("/delUpdateUrl", NewPostBaseHandler("http", "delUpdateUrl", []string{"shopPlatform"}, true, true)).
		push("/getUpdateUrls", NewPostBaseHandler("http", "getUpdateUrls", []string{"shopPlatforms"}, true, false)).
		push("/getServerStopTime", NewPostBaseHandler("http", "getServerStopTime", []string{}, true, false)).
		push("/setServerStopTime", NewPostBaseHandler("http", "setServerStopTime", []string{"time"}, true, true)).
		push("/getHotUpdateReportInfo", NewPostBaseHandler("http", "getHotUpdateReportInfo", []string{"version"}, true, false)).
		push("/createRole", NewPostBaseHandler("http", "createRole", []string{"username", "password", "uType"}, true, true)).
		push("/getRoles", NewPostBaseHandler("http", "getRoles", []string{}, true, false)).
		push("/queryOperateLog", NewPostBaseHandler("http", "queryOperateLog", []string{"username", "route", "size", "skip"}, true, false)).
		push("/getCdkModel", NewPostBaseHandler("http", "getCdkModel", []string{}, true, false)).
		push("/addCdkModel", NewPostBaseHandler("http", "addCdkModel", []string{"name", "receiver", "items", "isClaim"}, true, true)).
		push("/getFeedback", NewPostBaseHandler("http", "getFeedback", []string{"uid", "version", "responseType"}, true, false)).
		push("/getFeedbackVersions", NewPostBaseHandler("http", "getFeedbackVersions", []string{}, true, false)).
		push("/responseFeedback", NewPostBaseHandler("http", "responseFeedback", []string{"id", "content", "title"}, true, true)).
		push("/notice", NewGetBaseHandler("http", "notice", []string{"sid", "id", "params", "lastTime", "internal", "count", "sTime", "uid"}, true, true)).
		push("/getReqLogs", NewPostBaseHandler("http", "getReqLogs", []string{"startTime", "endTime", "topic"}, true, false)).
		push("/changePassword", NewPostBaseHandler("http", "changePassword", []string{"username", "oldPassword", "newPassword"}, true, true)).
		push("/setNotice", NewPostBaseHandler("http", "setNotice", []string{"version", "contentJson"}, true, true)).
		push("/getUserRacharge", NewPostBaseHandler("http", "getUserRacharge", []string{"userId", "sTime", "eTime", "size", "skip"}, true, false)).
		push("/testOrderRefund", NewPostBaseHandler("http", "testOrderRefund", []string{"orderId", "platform"}, true, false)).
		push("/getGoogleGlossryParam", NewPostBaseHandler("http", "getGoogleGlossryParam", []string{}, true, false)).
		push("/createGoogleGlossry", NewPostBaseHandler("http", "createGoogleGlossry", []string{"id", "uri"}, true, true)).
		push("/delGoogleGlossry", NewPostBaseHandler("http", "delGoogleGlossry", []string{"id"}, true, true)).

		// game
		push("/hello", NewGetBaseHandler("game", "hello", []string{"sid", "test"}, false, false)).
		push("/addArmys", NewPostBaseHandler("game", "addArmys", []string{"sid", "owner", "index", "parames"}, true, true)).
		push("/changeEquipAttr", NewGetBaseHandler("game", "changeEquipAttr", []string{"sid", "owner", "id", "attrs"}, false, false)).
		push("/getPlayers", NewPostBaseHandler("game", "getPlayers", []string{"sid", "page", "size", "sortType", "uid", "nickName", "alliUid"}, true, false)).
		push("/getChats", NewPostBaseHandler("game", "getChats", []string{"sid", "_type", "alli", "uids", "index", "size", "startTime", "endTime", "filterUid"}, true, false)).
		push("/getPlayerChatList", NewPostBaseHandler("game", "getPlayerChatList", []string{"sid", "uid"}, true, false)).
		push("/getAlliances", NewPostBaseHandler("game", "getAlliances", []string{"sid"}, true, false)).
		push("/reqDelPlayer", NewPostBaseHandler("game", "delPlayer", []string{"sid", "uid"}, true, false)).
		push("/getAreaInfo", NewPostBaseHandler("game", "getAreaInfo", []string{"sid"}, true, false)).
		push("/gameExecute", NewPostBaseHandler("game", "gameExecute", []string{"sid", "code", "fun"}, true, true)).
		push("/changeAreaVersionLimit", NewPostBaseHandler("game", "changeAreaVersionLimit", []string{"sid", "clientVersion", "clientBigVersion"}, true, true)).
		push("/delGameChat", NewPostBaseHandler("game", "delGameChat", []string{"uid", "sid", "channel"}, true, true)).
		push("/getGmParams", NewPostBaseHandler("game", "getGmParams", []string{"sid"}, true, false)).
		push("/setGmParams", NewPostBaseHandler("game", "setGmParams", []string{
			"sid", "march", "ceri", "drill", "canOccupyTime", "restTime", "alliSwitch", "build", "guestCreateAlli", "ancientCtbLimit", "ancientSpeedUpTime", "transit", "ancientCtbCount", "heroRevivesTime",
			"cellTondenCount",
		}, true, true)).
		push("/setWinLandCount", NewPostBaseHandler("game", "setWinLandCount", []string{"sid", "landCount"}, true, true)).
		push("/setCreateTime", NewPostBaseHandler("game", "setCreateTime", []string{"sid", "time"}, true, true)).
		push("/delAvoidWar", NewPostBaseHandler("game", "delAvoidWar", []string{"sid", "index"}, true, true)).
		push("/delPlayerAvoidWar", NewPostBaseHandler("game", "delPlayerAvoidWar", []string{"sid", "uid"}, true, true)).
		push("/addPlayerCell", NewPostBaseHandler("game", "addPlayerCell", []string{"sid", "uid", "index"}, true, true)).
		push("/getBazaarPrices", NewPostBaseHandler("game", "getBazaarPrices", []string{"sid"}, true, false)).
		push("/modifyTradePrice", NewPostBaseHandler("game", "modifyTradePrice", []string{"sid", "sellType", "buyType", "price"}, true, true)).
		push("/changeAlliNotice", NewPostBaseHandler("game", "changeAlliNotice", []string{"sid", "uid", "notice"}, true, true)).
		push("/getAlliCeriParams", NewPostBaseHandler("game", "getAlliCeriParams", []string{"sid"}, true, false)).
		push("/setAlliCeriParams", NewPostBaseHandler("game", "setAlliCeriParams", []string{"sid", "params"}, true, true)).
		push("/resetHeroSlots", NewPostBaseHandler("game", "resetHeroSlots", []string{"sid", "uid", "index"}, true, true)).
		push("/getAreaArmys", NewPostBaseHandler("game", "getAreaArmys", []string{"sid", "index"}, true, false)).
		push("/addPlayerCellByNum", NewPostBaseHandler("game", "addPlayerCellByNum", []string{"sid", "uid", "mainCity", "num"}, true, false)).
		push("/randomExclusiveEffects", NewPostBaseHandler("game", "randomExclusiveEffects", []string{"sid"}, true, true)).

		// lobby
		push("/genCdk", NewPostBaseHandler("lobby", "genCdk", []string{"sid", "code", "receiver", "items", "isClaim", "endTime"}, true, true)).
		push("/cdkList", NewPostBaseHandler("lobby", "cdkList", []string{"sid", "page", "size", "sortType"}, true, false)).
		push("/delGift", NewPostBaseHandler("lobby", "delGift", []string{"sid", "uid"}, true, true)).
		push("/getUserList", NewPostBaseHandler("lobby", "getUserList", []string{"lastId", "size", "sortType", "sTime", "eTime", "uid", "nickName", "onlineType"}, true, false)).
		push("/deleteUser", NewPostBaseHandler("lobby", "deleteUser", []string{"id"}, true, true)).
		push("/resetLoginRooms", NewPostBaseHandler("lobby", "resetLoginRooms", []string{}, true, false)).
		push("/banChat", NewPostBaseHandler("lobby", "bannedChat", []string{"uid", "time"}, true, true)).
		push("/bannedAccount", NewPostBaseHandler("lobby", "bannedAccount", []string{"uid", "time", "banDevice", "banType"}, true, true)).
		push("/bannedCheat", NewPostBaseHandler("lobby", "bannedCheat", []string{"uid", "time"}, true, true)).
		push("/getBanRecordList", NewPostBaseHandler("lobby", "getBanRecordList", []string{"uid"}, true, true)).
		push("/getPlayerArmys", NewPostBaseHandler("game", "getPlayerArmys", []string{"sid", "uid"}, true, false)).
		push("/lobbyExecute", NewPostBaseHandler("lobby", "lobbyExecute", []string{"lid", "code", "fun"}, true, true)).
		push("/changeGuestId", NewPostBaseHandler("lobby", "changeGuestId", []string{"uid", "guestId"}, true, true)).
		push("/getUserGoldRecordList", NewPostBaseHandler("lobby", "getUserGoldRecordList", []string{"size", "page", "sortType", "sTime", "eTime", "uid"}, true, false)).
		push("/getUserIngotRecordList", NewPostBaseHandler("lobby", "getUserIngotRecordList", []string{"tp", "id", "size", "sTime", "eTime", "uid", "skip"}, true, false)).
		push("/getUserItemRecordList", NewPostBaseHandler("lobby", "getUserItemRecordList", []string{"tp", "id", "size", "sortType", "sTime", "eTime", "uid", "skip"}, true, false)).
		push("/getBattleRecord", NewPostBaseHandler("lobby", "getBattleRecord", []string{"sid", "uid"}, false, false)).
		push("/setUserMaxLandCount", NewPostBaseHandler("lobby", "setUserMaxLandCount", []string{"uid", "landCount"}, true, true)).
		push("/changeLoginType", NewPostBaseHandler("lobby", "changeLoginType", []string{"uid", "loginType"}, true, true)).
		push("/modifyUserRankScore", NewPostBaseHandler("lobby", "modifyUserRankScore", []string{"uid", "score"}, true, true)).
		push("/userSaveDb", NewPostBaseHandler("lobby", "userSaveDb", []string{"uid"}, true, true)).
		push("/userOffline", NewPostBaseHandler("lobby", "userOffline", []string{"uid"}, true, true)).
		push("/getUserTeamInfo", NewPostBaseHandler("lobby", "getUserTeamInfo", []string{"uid"}, true, false)).
		push("/resetTeamState", NewPostBaseHandler("lobby", "resetTeamState", []string{"teamUid", "roomType", "playSid"}, true, true)).
		push("/resetUserTeamUid", NewPostBaseHandler("lobby", "resetUserTeamUid", []string{"uid"}, true, true)).
		push("/getUserRechargeSum", NewPostBaseHandler("lobby", "getUserRechargeSum", []string{"uid"}, true, false)).
		push("/setUserCloginDays", NewPostBaseHandler("lobby", "setUserCloginDays", []string{"uid", "cloginDays"}, true, true)).
		push("/setUserloginDays", NewPostBaseHandler("lobby", "setUserloginDays", []string{"uid", "loginDays"}, true, true)).
		push("/setUserSid", NewPostBaseHandler("lobby", "setUserSid", []string{"uid", "curSid"}, true, true)).
		push("/setUserPlaySid", NewPostBaseHandler("lobby", "setUserPlaySid", []string{"uid", "playSid"}, true, true)).
		push("/setUserNewbieCount", NewPostBaseHandler("lobby", "setUserNewbieCount", []string{"uid", "count"}, true, true)).
		push("/getUserPortrayalList", NewPostBaseHandler("lobby", "getUserPortrayalList", []string{"uid"}, true, false)).
		push("/setUserPortrayalAttr", NewPostBaseHandler("lobby", "setUserPortrayalAttr", []string{"uid", "id", "strategys", "isChosenOne"}, true, true)).
		push("/getWorkshopItems", NewPostBaseHandler("lobby", "getWorkshopItems", []string{"uid", "userId", "page", "size", "itemType", "state", "sortField", "sortType"}, true, false)).
		push("/workshopAudit", NewPostBaseHandler("lobby", "workshopAudit", []string{"uid", "state"}, true, true)).
		push("/delTeam", NewPostBaseHandler("lobby", "delTeam", []string{"teamUid"}, true, true)).
		push("/delTeamUser", NewPostBaseHandler("lobby", "delTeamUser", []string{"teamUid", "uid"}, true, true)).
		push("/getCdkClaimNum", NewPostBaseHandler("lobby", "getCdkClaimNum", []string{"uid"}, true, false)).
		push("/getUserCdkClaim", NewPostBaseHandler("lobby", "getUserCdkClaim", []string{"code", "userId"}, true, false)).
		push("/createTeamApply", NewPostBaseHandler("lobby", "createTeamApply", []string{"playerNum", "teamNum", "roomType", "teamData"}, true, true)).
		push("/getCreateTeamInfo", NewPostBaseHandler("lobby", "getCreateTeamInfo", []string{}, true, false)).

		// match
		push("/getRooms", NewPostBaseHandler("match", "getRooms", []string{"closed"}, true, false)).
		push("/getMapList", NewPostBaseHandler("match", "getMapList", []string{}, true, false)).
		push("/uploadMapData", NewPostBaseHandler("match", "uploadMapData", []string{"mapData", "cityData", "index", "len", "mapId"}, true, false)).
		push("/createRoom", NewPostBaseHandler("match", "createRoom", []string{"sid", "roomType", "addr"}, true, true)).
		push("/openRoom", NewPostBaseHandler("match", "openRoom", []string{"sid", "addr"}, true, true)).
		push("/getRoomGenSid", NewPostBaseHandler("match", "getRoomGenSid", []string{"roomType", "subType"}, true, true)).
		push("/resetMapUse", NewPostBaseHandler("match", "resetMapUse", []string{"mapId"}, true, true)).
		push("/getApplyParams", NewPostBaseHandler("match", "getApplyParams", []string{}, true, false)).
		push("/setApplyParams", NewPostBaseHandler("match", "setApplyParams", []string{"finishNumber", "openTimeStart", "openTimeEnd", "openTime", "cancelCd", "rookieCapMin", "matchIntervalDay", "rookiePreNum", "matchMinNum", "matchMaxNum", "matchBallenceNumMin", "matchBallenceNumMax", "matchBallenceInitParam", "matchBallenceStepParam"}, true, true)).
		push("/getGameMachs", NewPostBaseHandler("match", "getGameMachs", []string{}, true, false)).
		push("/addGameMach", NewPostBaseHandler("match", "addGameMach", []string{"ip", "serverTypes", "maxCap", "name"}, true, true)).
		push("/updateGameMachMaxCap", NewPostBaseHandler("match", "updateGameMachMaxCap", []string{"ip", "maxCap"}, true, true)).
		push("/updateGameMachServerTypes", NewPostBaseHandler("match", "updateGameMachServerTypes", []string{"ip", "serverTypes"}, true, true)).
		push("/updateGameMachServerName", NewPostBaseHandler("match", "updateGameMachServerName", []string{"ip", "name"}, true, true)).
		push("/delMachServer", NewPostBaseHandler("match", "delMachServer", []string{"ip"}, true, true)).
		push("/closeGameServer", NewPostBaseHandler("match", "closeGameServer", []string{"sid"}, true, true)).
		push("/delRoom", NewPostBaseHandler("match", "delRoom", []string{"id"}, true, true)).
		push("/gameMachGitUpdate", NewPostBaseHandler("match", "gameMachGitUpdate", []string{"ip"}, true, true)).
		push("/closeGameMach", NewPostBaseHandler("match", "closeGameMach", []string{"ip"}, true, true)).
		push("/getGameMachStatus", NewPostBaseHandler("match", "getGameMachStatus", []string{"ip"}, true, true)).
		push("/startGamePids", NewPostBaseHandler("match", "startGamePids", []string{"ip"}, true, true)).
		push("/stopGameServer", NewPostBaseHandler("match", "stopGameServer", []string{"ip"}, true, true)).
		push("/getLobbyMachInfo", NewPostBaseHandler("match", "getLobbyMachInfo", []string{}, true, false)).
		push("/lobbyMachGitUpdate", NewPostBaseHandler("match", "lobbyMachGitUpdate", []string{"ip"}, true, true)).
		push("/closeLobbyMach", NewPostBaseHandler("match", "closeLobbyMach", []string{"ip"}, true, true)).
		push("/startLobbyPids", NewPostBaseHandler("match", "startLobbyPids", []string{"ip"}, true, true)).
		push("/stopLobbyServer", NewPostBaseHandler("match", "stopLobbyServer", []string{"ip"}, true, true)).
		push("/resetLobbyMachStatus", NewPostBaseHandler("match", "resetLobbyMachStatus", []string{"ip"}, true, true)).
		push("/resetGameMachStatus", NewPostBaseHandler("match", "resetGameMachStatus", []string{"ip"}, true, true)).
		push("/getSupMachInfo", NewPostBaseHandler("match", "getSupMachInfo", []string{}, true, false)).
		push("/closeSupMach", NewPostBaseHandler("match", "closeSupMach", []string{"ip"}, true, true)).
		push("/startSupPids", NewPostBaseHandler("match", "startSupPids", []string{"ip"}, true, true)).
		push("/resetSupMachStatus", NewPostBaseHandler("match", "resetSupMachStatus", []string{"ip"}, true, true)).
		push("/supMachGitUpdate", NewPostBaseHandler("match", "supMachGitUpdate", []string{"ip"}, true, true)).
		push("/getStopUpdateTaskList", NewPostBaseHandler("match", "getStopUpdateTaskList", []string{}, true, false)).
		push("/addStopUpdateTask", NewPostBaseHandler("match", "addStopUpdateTask", []string{"stopTime", "openTime", "updateCode", "extraTime", "version", "bigVersion", "updateGate"}, true, true)).
		push("/delStopUpdateTask", NewPostBaseHandler("match", "delStopUpdateTask", []string{"uid"}, true, true)).
		push("/matchExecute", NewPostBaseHandler("match", "matchExecute", []string{"code", "fun"}, true, true)).
		push("/getFriendParams", NewPostBaseHandler("match", "getFriendParams", []string{}, true, false)).
		push("/setFriendParams", NewPostBaseHandler("match", "setFriendParams", []string{"maxNum", "applyNum", "landCount", "blackListMax"}, true, true)).
		push("/getTaParams", NewPostBaseHandler("match", "getTaParams", []string{}, true, false)).
		push("/setTaParams", NewPostBaseHandler("match", "setTaParams", []string{"taOpen", "taDebug"}, true, true)).
		push("/getCleanLogPrams", NewPostBaseHandler("match", "getCleanLogPrams", []string{}, true, false)).
		push("/setCleanLogPrams", NewPostBaseHandler("match", "setCleanLogPrams", []string{"diskUse", "daysOld"}, true, true)).
		push("/setSaveRoom", NewPostBaseHandler("match", "setSaveRoom", []string{"id", "save"}, true, true)).
		push("/addMach", NewPostBaseHandler("match", "addMach", []string{"ip", "name", "machType", "id"}, true, true)).
		push("/setLobbyMachAbandon", NewPostBaseHandler("match", "setLobbyMachAbandon", []string{"ip", "abandon"}, true, true)).
		push("/getApplyInfo", NewPostBaseHandler("match", "getApplyInfo", []string{}, true, false)).
		push("/getMachTime", NewPostBaseHandler("match", "getMachTime", []string{"ip"}, true, true)).
		push("/setMachTime", NewPostBaseHandler("match", "setMachTime", []string{"ip", "time"}, true, true)).
		push("/resetMachTime", NewPostBaseHandler("match", "resetMachTime", []string{"ip"}, true, true)).
		push("/setApplyMatchTime", NewPostBaseHandler("match", "setApplyMatchTime", []string{"roomType", "time"}, true, true)).
		push("/setApplyClose", NewPostBaseHandler("match", "setApplyClose", []string{"roomType", "close"}, true, true)).
		push("/getLoginLimitInfo", NewPostBaseHandler("match", "getLoginLimitInfo", []string{}, true, false)).
		push("/setLoginLimitType", NewPostBaseHandler("match", "setLoginLimitType", []string{"limitType"}, true, true)).
		push("/setLoginLimitIp", NewPostBaseHandler("match", "setLoginLimitIp", []string{"limitType", "add", "ip"}, true, true)).
		push("/getTeamParams", NewPostBaseHandler("match", "getTeamParams", []string{}, true, false)).
		push("/setTeamParams", NewPostBaseHandler("match", "setTeamParams", []string{"userNum", "chatNum"}, true, true)).
		push("/checkApplyTeam", NewPostBaseHandler("match", "checkApplyTeam", []string{"roomType"}, true, true)).
		push("/getApplyTeamInfo", NewPostBaseHandler("match", "getApplyTeamInfo", []string{"roomType"}, true, false)).
		push("/delApplyTeam", NewPostBaseHandler("match", "delApplyTeam", []string{"roomType", "teamUid"}, true, true)).
		push("/getCheckCheat", NewPostBaseHandler("match", "getCheckCheat", []string{}, true, false)).
		push("/setCheckCheat", NewPostBaseHandler("match", "setCheckCheat", []string{"open"}, true, true)).
		push("/getPortrayalOdds", NewPostBaseHandler("match", "getPortrayalOdds", []string{}, true, false)).
		push("/setPortrayalOdds", NewPostBaseHandler("match", "setPortrayalOdds", []string{"odds"}, true, true)).
		push("/getOfflineMsgParams", NewPostBaseHandler("match", "getOfflineMsgParams", []string{}, true, false)).
		push("/setOfflineMsgParams", NewPostBaseHandler("match", "setOfflineMsgParams", []string{"offlineMsgOpen"}, true, true)).

		// chat
		push("/getLobbyChats", NewPostBaseHandler("chat", "getLobbyChats", []string{"channel", "index", "size", "startTime", "endTime", "filterUid"}, true, false)).
		push("/getChatTeamList", NewPostBaseHandler("chat", "getChatTeamList", []string{"uid"}, true, false)).
		push("/delLobbyChatByUid", NewPostBaseHandler("chat", "delLobbyChatByUid", []string{"channel", "uid"}, true, true)).

		// mail
		push("/sendMailToLogin", NewPostBaseHandler("mail", "sendMailToLogin", []string{"roomId", "contentId", "title", "content", "receiver", "items"}, true, true)).
		push("/mails", NewPostBaseHandler("mail", "mails", []string{"sid", "sortType", "sender", "receiver", "page", "size"}, true, false)).
		push("/tagMail", NewPostBaseHandler("mail", "tagMail", []string{"sid", "uid"}, true, false)).
		end()
}
