package g

import ut "slgsrv/utils"

// 装备效果对象
type EquipEffectObj struct {
	Value float64
	Type  int32
	Odds  int32
}

func NewEquipEffectObj(t int32, value float64, odds int32) *EquipEffectObj {
	return &EquipEffectObj{
		Type:  t,
		Value: value,
		Odds:  odds,
	}
}

func (this *EquipEffectObj) String() string {
	return "(" + ut.Itoa(this.Type) + "," + ut.Itoa(this.Value) + "," + ut.Itoa(this.Odds) + ")"
}

func (this *EquipEffectObj) Clone() *EquipEffectObj {
	return NewEquipEffectObj(this.Type, this.Value, this.Odds)
}
