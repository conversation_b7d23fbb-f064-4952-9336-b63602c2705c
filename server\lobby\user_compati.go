package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

const VERSION = 15

var (
	ANIMAL_WORLD_SKIN_MAP = map[int32]bool{
		3105102: true,
		3201103: true,
		3302102: true,
		3401104: true,
	} // 动物世界兼容皮肤
	ANIMAL_WORLD_SKIN_TIME = 1735660800000 // 动物世界皮肤兼容皮肤物品获得时间 2025-01-01 00:00:00
)

// 兼容 英雄技能效果
func (this *User) compatiPortrayalAttr(id int32, oMin, oMax, nMin, nMax float64) {
	if portrayal := this.GetPortrayalInfo(id); portrayal != nil && portrayal.IsUnlock() {
		attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
			portrayal.UpdateAttr()
		}
		attr = array.Find(portrayal.LastAttrs, func(arr []int32) bool { return arr[0] == 1 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
		}
	}
}

func (this *User) Compati(ver int32, lobby *Lobby) {
	this.Version = VERSION
	if ver == 0 {
		// 兼容连续登陆
		this.LoginDayCount = 0
		this.CLoginDayCount = 0
		// 删除完成的连续登陆任务
		for i := len(this.GeneralTasks.Finishs) - 1; i >= 0; i-- {
			if this.GeneralTasks.Finishs[i]/100000 == 302 {
				this.GeneralTasks.Finishs = append(this.GeneralTasks.Finishs[:i], this.GeneralTasks.Finishs[i+1:]...)
			}
		}
		// 补偿不能玩新手村的玩家
		if !this.IsNovicePlayer() {
			lobby.sendMailItemOne(0, 100018, "", "", "-1", this.UID, []*g.TypeObj{g.NewTypeObj(ctype.WAR_TOKEN, 0, 100)})
		}
	} else if ver == 1 {
		// 兼容 黄忠
		if portrayal := this.GetPortrayalInfo(330101); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = ut.RoundInt32(float64(attr[2]) / 20.0 * 30.0)
				portrayal.UpdateAttr()
			}
		}
		// 兼容 黄盖
		if portrayal := this.GetPortrayalInfo(320401); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = ut.RoundInt32(float64(attr[2]) / 60.0 * 50.0)
				portrayal.UpdateAttr()
			}
		}
	} else if ver == 2 {
	} else if ver == 3 {
		// 兼容 曹操
		if portrayal := this.GetPortrayalInfo(320501); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				if attr[2] <= 15 {
					attr[2] = 10 + (attr[2] - 5)
					portrayal.UpdateAttr()
				}
			}
		}
		// 兼容 刘备
		if portrayal := this.GetPortrayalInfo(330201); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				if attr[2] <= 15 {
					attr[2] = 10 + (attr[2] - 5)
					portrayal.UpdateAttr()
				}
			}
		}
	} else if ver == 4 {
		// 兼容 赵云
		if portrayal := this.GetPortrayalInfo(310102); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(5, 10, 10, 20, attr[2])
				portrayal.UpdateAttr()
			}
		}
		// 兼容 文鸯
		if portrayal := this.GetPortrayalInfo(310601); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(2, 6, 40, 70, attr[2])
				portrayal.UpdateAttr()
			}
		}
	} else if ver == 5 {
		// 兼容 吕蒙
		if portrayal := this.GetPortrayalInfo(310502); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(10, 30, 20, 50, attr[2])
				portrayal.UpdateAttr()
			}
		}
	} else if ver == 6 {
		// 兼容 周瑜
		if portrayal := this.GetPortrayalInfo(310302); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(10, 30, 20, 40, attr[2])
				portrayal.UpdateAttr()
			}
		}
		// 兼容 秦琼
		if portrayal := this.GetPortrayalInfo(310603); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(50, 100, 20, 30, attr[2])
				portrayal.UpdateAttr()
			}
		}
		// 兼容 裴行俨
		if portrayal := this.GetPortrayalInfo(340402); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(10, 20, 20, 30, attr[2])
				portrayal.UpdateAttr()
			}
		}
		// 兼容 养由基
		if portrayal := this.GetPortrayalInfo(330401); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(10, 20, 200, 300, attr[2])
				portrayal.UpdateAttr()
			}
		}
		// 兼容 孙权
		if portrayal := this.GetPortrayalInfo(330402); portrayal != nil && portrayal.IsUnlock() {
			attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
			if attr != nil {
				attr[2] = slg.CompatiValueInt32(50, 100, 5, 15, attr[2])
				portrayal.UpdateAttr()
			}
		}
	} else if ver == 7 {
		// 兼容 周瑜
		this.compatiPortrayalAttr(310302, 10, 30, 20, 40)
		// 兼容 秦琼
		this.compatiPortrayalAttr(310603, 50, 100, 20, 30)
		// 兼容 裴行俨
		this.compatiPortrayalAttr(340402, 10, 20, 20, 30)
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 10, 20, 200, 300)
		// 兼容 孙权
		this.compatiPortrayalAttr(330402, 50, 100, 5, 15)
	} else if ver == 8 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 200, 300, 100, 150)
	} else if ver == 9 {
		now := time.Now().UnixMilli()
		if ut.MaxInt64(0, this.BanAccountEndTime-now) > 0 {
			this.BanAccountType = slg.BAN_TYPE_ACCOUNT
		} else {
			this.BanAccountType = -1
		}
		if this.Ingot < 0 {
			banTime, reason := int64(ut.TIME_DAY*3650), int32(slg.BAN_TYPE_REFUND)
			this.AddBanRecord(reason, banTime, now) // 添加封禁记录
			this.BanAccountType = reason
			this.BanAccountEndTime = time.Now().UnixMilli() + banTime
			// 更新同一设备id账号的封禁时间
			db.UpdateBanByDistinct(this.DistinctId, this.BanAccountEndTime, reason)
		}
	} else if ver == 10 {
		this.LastBuyOptionalHeroTime = 0
		// 兼容皮肤物品溯源信息
		skinTrackList := []interface{}{}
		skinList := []*SkinItem{}
		this.SkinItemListLock.Lock()
		for _, skinItem := range this.SkinItemList {
			for i := int32(0); i < skinItem.Count; i++ {
				skin := &SkinItem{
					UID: GenUid(),
					Id:  skinItem.Id,
				}
				skinList = append(skinList, skin)
				skinTrackList = append(skinTrackList, &SkinItemTrack{
					UID:      skin.UID,
					Owner:    this.UID,
					CurOwner: this.UID,
					Id:       skin.Id,
				})
			}
		}
		this.SkinItemList = skinList
		this.SkinItemListLock.Unlock()
		go func() {
			// 添加到数据库
			err := skinItemTrackDb.InsertSkinItemTrackList(skinTrackList)
			if err != nil {
				log.Error("user Compati skinItemTrack err: %v, uid: %v", err, this.UID)
			}
		}()
	} else if ver == 11 {
		// 离线消息设置兼容
		this.OfflineNotifyOpt = []int32{0, 1, 2, 3, 4, 5}
	} else if ver == 12 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 100, 150, 10, 30)
		// 兼容 邓艾
		this.compatiPortrayalAttr(320502, 5, 10, 10, 30)
		// 动物世界皮肤兼容 从解锁的皮肤列表转移到皮肤物品列表
		addSkinItem := []int32{}
		this.PawnSkinLock.Lock()
		for i := len(this.UnlockPawnSkinIds) - 1; i >= 0; i-- {
			skinId := this.UnlockPawnSkinIds[i]
			if ANIMAL_WORLD_SKIN_MAP[skinId] {
				addSkinItem = append(addSkinItem, skinId)
				this.UnlockPawnSkinIds = append(this.UnlockPawnSkinIds[:i], this.UnlockPawnSkinIds[i+1:]...)
			}
		}
		this.PawnSkinLock.Unlock()
		for _, skinId := range addSkinItem {
			skinItem := this.AddSkinItem("", skinId, false, false)
			skinItem.State = int64(ANIMAL_WORLD_SKIN_TIME)
			// 添加溯源记录
			skinItemTrackDb.InsertSkinItemTrack(&SkinItemTrack{
				UID:      skinItem.UID,
				Owner:    this.UID,
				CurOwner: this.UID,
				Id:       skinId,
			})
		}
	} else if ver == 13 {
		// 兼容 姜维
		this.compatiPortrayalAttr(310501, 5, 15, 5, 10)
		// 兼容 张辽
		this.compatiPortrayalAttr(340101, 10, 30, 20, 50)
		// 兼容 霍去病
		this.compatiPortrayalAttr(340102, 50, 100, 5, 10)
	} else if ver == 14 {
		// 兼容 画像历史属性和保存槽位
		this.PortrayalsLock.Lock()
		for _, v := range this.Portrayals {
			v.SaveSlotsFix()
			v.HistoryFix()
		}
		this.PortrayalsLock.Unlock()

		// 兼容 大转盘未领取的记录发送邮件
		items := []*g.TypeObj{}
		for _, v := range this.WheelRecords {
			if !v.IsClaim && len(v.Items) > 0 {
				for _, m := range v.Items {
					items = g.MergeTypeObjsCount(items, m)
				}
			}
		}
		lobbyModule.sendMailItemOne(0, slg.MAIL_WHEEL_REWARD_FIX_ID, "", "", "-1", this.UID, items)
		this.WheelRecords = []*WheelRecord{} // 清空未领取记录

		// TODO 订阅领奖时间兼容
	} else {
		this.Version = VERSION
		return
	}
	this.Compati(ver+1, lobby)
}
