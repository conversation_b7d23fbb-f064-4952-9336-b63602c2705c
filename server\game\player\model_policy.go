package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

func (this *Model) ToPolicyMap() map[int32]*g.PolicyInfo {
	return this.room.GetWorld().ToPolicyMap(this.Uid)
}

func (this *Model) ToPolicys() map[int32]int32 {
	return this.room.GetWorld().ToPolicys(this.Uid)
}

func (this *Model) ToPolicysPb() map[int32]int32 {
	return this.room.GetWorld().ToPolicysPb(this.Uid)
}

func (this *Model) GetPolicyEffectFloat(tp int32) float64 {
	return this.room.GetWorld().GetPlayerPolicyEffectFloatByUid(this.Uid, tp)
}

func (this *Model) GetPolicyEffectInt(tp int32) int32 {
	return this.room.GetWorld().GetPlayerPolicyEffectIntByUid(this.Uid, tp)
}

// 刷新重置政策需要的金币
func (this *Model) UpdateResetPolicyNeedGold() {
	if this.ResetPolicyNeedGold > 0 {
		this.ResetPolicyNeedGold += constant.RESET_POLICY_ADD_GOLD //累加
	} else {
		i := 0
		for l := len(constant.POLICY_SLOT_CONF); i < l; i++ {
			if !this.CheckCTypeOne(constant.POLICY_SLOT_CONF[i]) {
				break
			}
		}
		this.ResetPolicyNeedGold = constant.RESET_POLICY_GOLD[ut.Clamp(i-1, 0, len(constant.RESET_POLICY_GOLD)-1)]
	}
}

// 刷新政策
func (this *Model) UpdatePolicyEffect(effectMap map[int32]bool) {
	RES_OUTPUT, GW_CAP := false, false
	for tp := range effectMap {
		if tp == effect.RES_OUTPUT ||
			tp == effect.CEREAL_OUTPUT ||
			tp == effect.TIMBER_OUTPUT ||
			tp == effect.STONE_OUTPUT { //增加资源产量
			RES_OUTPUT = true
		} else if tp == effect.GW_CAP { //粮食和仓库容量
			GW_CAP = true
		}
	}
	if RES_OUTPUT {
		this.UpdateOpSec(true)
	}
	if GW_CAP {
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{
			Data_1: &pb.UpdateOutPut{
				GranaryCap:   int32(this.GetGranaryCap()),
				WarehouseCap: int32(this.GetWarehouseCap()),
				Flag:         pb.AddFlags(int32(pb.OutPutFlagEnum_GranaryCap), int32(pb.OutPutFlagEnum_WarehouseCap)),
			}})
	}
}
