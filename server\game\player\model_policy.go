package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
)

func (this *Model) ToPolicySlotMap() map[int32]*g.CeriSlotInfo {
	return this.room.GetWorld().ToPolicySlotMap(this.Uid)
}

func (this *Model) ToPolicys() map[int32]int32 {
	return this.room.GetWorld().ToPolicys(this.Uid)
}

func (this *Model) ToPolicysPb() map[int32]*pb.CeriSlotInfo {
	return this.room.GetWorld().ToPolicysPb(this.Uid)
}

func (this *Model) GetPolicyEffectFloat(tp int32) float64 {
	return this.room.GetWorld().GetPlayerPolicyEffectFloatByUid(this.Uid, tp)
}

func (this *Model) GetPolicyEffectInt(tp int32) int32 {
	return this.room.GetWorld().GetPlayerPolicyEffectIntByUid(this.Uid, tp)
}

// 刷新政策
func (this *Model) UpdatePolicyEffect(effectMap map[int32]bool) {
	RES_OUTPUT, GW_CAP := false, false
	for tp := range effectMap {
		if tp == effect.RES_OUTPUT {
			RES_OUTPUT = true
		} else if tp == effect.GW_CAP { //粮食和仓库容量
			GW_CAP = true
		}
	}
	if RES_OUTPUT {
		this.UpdateOpSec(true)
	}
	if GW_CAP {
		this.PutNotifyQueue(constant.NQ_OUTPUT, &pb.OnUpdatePlayerInfoNotify{
			Data_1: &pb.UpdateOutPut{
				GranaryCap:   int32(this.GetGranaryCap()),
				WarehouseCap: int32(this.GetWarehouseCap()),
				Flag:         pb.AddFlags(int64(pb.OutPutFlagEnum_GranaryCap), int64(pb.OutPutFlagEnum_WarehouseCap)),
			}})
	}
}
