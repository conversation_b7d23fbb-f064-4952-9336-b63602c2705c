package behavior

type IBaseDecorator interface {
	IBaseNode
	AddChild(child IBaseNode)
	GetChild() IBaseNode
}

// 装饰节点
type BaseDecorator struct {
	BaseNode
	BaseWorker

	child IBaseNode
}

func (this *BaseDecorator) Ctor() {
	this.category = DECORATOR
}

// 添加子节点
func (this *BaseDecorator) AddChild(node IBaseNode) {
	this.child = node
}

func (this *BaseDecorator) GetChild() IBaseNode {
	return this.child
}
