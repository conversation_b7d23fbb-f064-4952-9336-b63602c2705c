package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"

	"github.com/sasha-s/go-deadlock"
)

type ScoreInfo struct {
	UID        string
	Statistics map[int32]int32
	PawnUse    map[int32]bool //士兵使用
	EquipUse   map[int32]bool //装备使用
	PolicyUse  map[int32]bool //政策使用
	Score      int32
	LandScore  int32
	Win        bool
}

type RankManager struct {
	deadlock.RWMutex
	Players        []*pb.PlayerRankInfo  //玩家排行列表
	Allis          []*pb.AlliRankInfo    //联盟排行列表
	Scores         []*pb.PlayerScoreInfo //玩家积分排行
	Scores2        []*pb.PlayerScoreInfo //玩家积分排行
	lastUpdateTime int                   //最后一次刷新排行榜时间
}

func (this *Model) checkUpdateRankInfo(force bool) {
	rank := this.rank
	now := ut.Now()
	if !force && now-rank.lastUpdateTime < ut.TIME_MINUTE && rank.Players != nil {
		return
	}
	rank.Lock()
	defer rank.Unlock()
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	rank.lastUpdateTime = now
	// 获取联盟的信息
	rank.Allis = []*pb.AlliRankInfo{}
	for _, m := range this.Alliances.Map {
		rankCount, sumCount := this.GetAlliRankLandCount(m)
		rank.Allis = append(rank.Allis, &pb.AlliRankInfo{
			Uid:            m.Uid,
			Icon:           m.Icon,
			Name:           m.Name,
			Pers:           []int32{int32(len(m.Members.List)), m.MemberPersLimit},
			LandCount:      rankCount,
			BeAddLandCount: ut.MaxInt32(sumCount-rankCount, 0),
			Time:           m.CreateTime,
		})
	}
	// 排序 从大到小
	sort.Slice(rank.Allis, func(i, j int) bool {
		a, b := rank.Allis[i], rank.Allis[j]
		aLandCount, bLandCount := a.LandCount+a.BeAddLandCount, b.LandCount+b.BeAddLandCount
		if aLandCount == bLandCount {
			return a.Time < b.Time
		}
		return aLandCount > bLandCount
	})
	// 获取玩家的信息
	rank.Players = []*pb.PlayerRankInfo{}
	rank.Scores = []*pb.PlayerScoreInfo{}
	rank.Scores2 = []*pb.PlayerScoreInfo{}
	winType, winUID := this.room.GetGameWiner()
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		alliName := ""
		if alli := this.Alliances.Map[m.AllianceUid]; alli != nil {
			alliName = alli.Name
		}
		if landCount := len(m.OwnCells); landCount > 0 {
			rank.Players = append(rank.Players, &pb.PlayerRankInfo{
				Uid:       m.Uid,
				HeadIcon:  m.HeadIcon,
				Nickname:  m.Nickname,
				AlliName:  alliName,
				LandCount: int32(landCount),
				Time:      m.CreateTime,
			})
		}
		if landScore, alliScore, extraScore := this.GetPlayerGameSumScore(m, winType, winUID); landScore > 0 || alliScore > 0 || extraScore > 0 {
			info := &pb.PlayerScoreInfo{
				Uid:        m.Uid,
				HeadIcon:   m.HeadIcon,
				Nickname:   m.Nickname,
				AlliName:   alliName,
				LandScore:  landScore,
				AlliScore:  alliScore,
				ExtraScore: extraScore,
				Time:       int64(m.CreateTime),
			}
			rank.Scores = append(rank.Scores, info)
			rank.Scores2 = append(rank.Scores2, info)
		}
	}
	this.allTempPlayers.RUnlock()
	// 排序 从大到小
	sort.Slice(rank.Players, func(i, j int) bool {
		a, b := rank.Players[i], rank.Players[j]
		if a.LandCount == b.LandCount {
			return a.Time < b.Time
		}
		return a.LandCount > b.LandCount
	})
	sort.Slice(rank.Scores, func(i, j int) bool {
		a, b := rank.Scores[i], rank.Scores[j]
		aScore := a.LandScore + a.AlliScore
		bScore := b.LandScore + b.AlliScore
		if aScore == bScore {
			return a.Time < b.Time
		}
		return aScore > bScore
	})
	sort.Slice(rank.Scores2, func(i, j int) bool {
		a, b := rank.Scores2[i], rank.Scores2[j]
		aScore := a.LandScore + a.AlliScore + a.ExtraScore
		bScore := b.LandScore + b.AlliScore + b.ExtraScore
		if aScore == bScore {
			return a.Time < b.Time
		}
		return aScore > bScore
	})
}

// 获取玩家排行信息
func (this *Model) GetPlayerRankList(uid string) ([]*pb.PlayerRankInfo, int) {
	this.checkUpdateRankInfo(false)
	return this.rank.Players[:ut.Min(len(this.rank.Players), 20)], array.FindIndex(this.rank.Players, func(m *pb.PlayerRankInfo) bool { return m.Uid == uid })
}

// 获取联盟排行信息
func (this *Model) GetAlliRankList() []*pb.AlliRankInfo {
	this.checkUpdateRankInfo(false)
	return this.rank.Allis[:ut.Min(len(this.rank.Allis), 30)]
}

// 获取玩家积分排行信息
func (this *Model) GetPlayerScoreList(uid string, tp int) (list []*pb.PlayerScoreInfo, no int, me *pb.MeScoreInfo) {
	this.checkUpdateRankInfo(false)
	scores := ut.If(tp == 0, this.rank.Scores, this.rank.Scores2)
	list = scores[:ut.Min(len(scores), 50)]
	if no = array.FindIndex(scores, func(m *pb.PlayerScoreInfo) bool { return m.Uid == uid }); no != -1 {
		landScore, alliScore, landScoreTop, alliScoreTop := this.GetPlayerLandScoreAndAlliScore(uid)
		me = &pb.MeScoreInfo{
			LandScore:    landScore,
			AlliScore:    alliScore,
			LandScoreTop: landScoreTop,
			AlliScoreTop: alliScoreTop,
			ExtraScore:   scores[no].ExtraScore,
		}
	}
	return
}

// 获取玩家当前最高领地和联盟最高领地
func (this *Model) GetPlayerAndAlliTop() (playerTop, alliTop, alliTopBeAddCount int) {
	this.checkUpdateRankInfo(false)
	if len(this.rank.Players) > 0 {
		playerTop = int(this.rank.Players[0].LandCount)
	}
	if len(this.rank.Allis) > 0 {
		alliTop = int(this.rank.Allis[0].LandCount)
		alliTopBeAddCount = int(this.rank.Allis[0].BeAddLandCount)
	}
	return
}

// 获取玩家总积分
func (this *Model) GetPlayerGameSumScore(plr *TempPlayer, winType int32, winUID string) (int32, int32, int32) {
	win, extraScore := false, int32(0)
	if winType == 1 { //1.玩家 2.联盟
		win = winUID == plr.Uid
	} else if winType == 2 {
		win = winUID == plr.AllianceUid
	}
	if win {
		extraScore = constant.GAME_EXTRA_SCORE //获胜者额外获得积分
	}
	return plr.LandScoreTop, plr.AlliScoreTop, extraScore
}

// 获取所有玩家的积分排名
func (this *Model) GetAllPlayerScoreRankInfo(winType int32, winUid string) []map[string]interface{} {
	defer func() {
		go this.checkUpdateRankInfo(true) //结束的时候刷一下
	}()
	arr := []ScoreInfo{}
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		if m.IsGiveupGame || m.IsSpectate {
			continue //已经放弃或观战的 跳过
		}
		score := m.LandScoreTop + m.AlliScoreTop
		win := false
		if winUid == "" {
		} else if winType == 1 { //个人
			win = m.Uid == winUid
		} else if winType == 2 { //联盟
			win = m.AllianceUid == winUid
		}
		if win /* && score > 0 */ {
			score += constant.GAME_EXTRA_SCORE
		}
		arr = append(arr, ScoreInfo{
			UID:        m.Uid,
			Score:      score,
			LandScore:  m.LandScoreTop,
			Win:        win,
			Statistics: m.ToStatistics(),
			PawnUse:    m.ToPawnDrillMap(),
			EquipUse:   m.ToEquipUseMap(),
			PolicyUse:  m.ToPolicyUseMap(),
		})
	}
	this.allTempPlayers.RUnlock()
	sort.Slice(arr, func(i, j int) bool { return arr[i].Score > arr[j].Score })
	return array.Map(arr, func(m ScoreInfo, i int) map[string]interface{} {
		return map[string]interface{}{
			"uid":        m.UID,
			"win":        m.Win,
			"score":      m.Score,
			"land_score": m.LandScore,
			"rank":       i,
			"statistics": m.Statistics, //统计
			"pawn_use":   m.PawnUse,    //士兵使用
			"equip_use":  m.EquipUse,   //装备使用
			"policy_use": m.PolicyUse,  //政策使用
		}
	})
}
