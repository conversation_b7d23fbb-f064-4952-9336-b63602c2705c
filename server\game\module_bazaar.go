package game

import (
	"math"
	"time"

	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/bazaar"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDBazaar() {
	this.GetServer().RegisterGO("HD_GetTradingRess", this.getTradingRess)     // 获取交易中心的资源列表
	this.GetServer().RegisterGO("HD_BazaarSellRes", this.bazaarSellRes)       // 出售资源
	this.GetServer().RegisterGO("HD_BazaarCancelSell", this.bazaarCancelSell) // 取消出售资源
	this.GetServer().RegisterGO("HD_BazaarBuyRes", this.bazaarBuyRes)         // 购买资源
	this.GetServer().RegisterGO("HD_BazaarGiveRes", this.bazaarGiveRes)       // 赠送资源
	this.GetServer().RegisterGO("HD_BazaarSellToSys", this.bazaarSellToSys)   // 与系统置换资源
	this.GetServer().RegisterGO("HD_GetTradePrice", this.getTradePrice)       // 获取市场交易价格信息
}

// 获取交易中心的资源列表
func (this *Game) getTradingRess(session gate.Session, msg *pb.GAME_HD_GETTRADINGRESS_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETTRADINGRESS_S2C{
		TradingRess: room.GetBazaar().ToTradingRessPb(plr.Uid),
	})
}

// 出售资源
func (this *Game) bazaarSellRes(session gate.Session, msg *pb.GAME_HD_BAZAARSELLRES_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	} else if room.GetBazaar().GetTradingResCount() > constant.TRADINGRES_MAX_COUNT {
		return nil, ecode.TRADINGRES_FULL.String()
	}
	sell := g.NewTypeObjNotId(msg.GetSellType(), msg.GetSellCount())
	buy := g.NewTypeObjNotId(msg.GetBuyType(), msg.GetBuyCount())
	if sell.Count <= 0 || buy.Count <= 0 {
		return nil, ecode.PLEASE_INPUT_RES_COUNT.String()
	}
	// 计算需要多少个商人
	sellCount, buyCount := helper.ChangeNumByMerchantCap(sell), helper.ChangeNumByMerchantCap(buy)
	maxCount := ut.MaxInt32(sellCount, buyCount)
	needMerchantCount := ut.Ceil(float64(maxCount) / float64(plr.GetMerchantTransitCap()))
	if plr.GetMerchantIdleCount() < needMerchantCount {
		return nil, ecode.MERCHANT_NOT_ENOUGH.String()
	} else if !plr.CheckCostByTypeObjOne(sell) { // 检测需要扣除的资源是否够
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 非自由区 只能联盟中交易
	onlyAlli := !room.IsFreeServer() || msg.GetOnlyAlli()
	// 扣除出售的资源
	plr.ChangeCostByTypeObjOne(sell, -1)
	plr.UpdatePlrResAccChange(sell.Type, -sell.Count)
	// 添加资源到交易中心
	room.GetBazaar().AddTradingRes(sell, buy, plr.Uid, int32(needMerchantCount), onlyAlli)
	// 改变商人状态
	plr.ChangeMerchantState(constant.MS_NONE, constant.MS_TRADING, int32(needMerchantCount))
	// 记录
	room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_URES, plr.Uid, "", map[string]interface{}{
		"resType":  sell.Type,
		"resCount": sell.Count,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_BAZAARSELLRES_S2C{
		Items:     plr.ToItemByTypeObjsPb([]*g.TypeObj{sell}),
		Merchants: plr.Merchants.ToPb(),
	})
}

// 取消出售资源
func (this *Game) bazaarCancelSell(session gate.Session, msg *pb.GAME_HD_BAZAARCANCELSELL_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	model := room.GetBazaar().(*bazaar.Model)
	tres := model.GetTradingResByUID(msg.GetUid())
	now := time.Now().UnixMilli()
	if tres == nil || tres.IsEnd() || tres.Owner != plr.Uid {
		return nil, ecode.TRES_NOT_EXIST.String()
	} else if (tres.Time == 0 && tres.NoticeTime > now) || (tres.Time > 0 && now > tres.Time+constant.CANCEL_SELL_CD_TIME) {
		return nil, ecode.NOT_CANCEL_RES.String() // 已经过了可取消的时间
	}
	// 删除
	room.GetBazaar().RemoveTradingRes(tres.UID)
	// 退还资源
	item := g.NewTypeObjNotId(tres.SellType, tres.SellCount)
	_, add := plr.ChangeCostByTypeObjOne(item, 1)
	// 改变商人状态
	plr.ChangeMerchantState(constant.MS_TRADING, constant.MS_NONE, tres.MerchantCount)
	// 记录
	room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_DRES, plr.Uid, "", map[string]interface{}{
		"resType":  tres.SellType,
		"resCount": tres.SellCount,
		"actCount": add,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_BAZAARCANCELSELL_S2C{
		Items:     plr.ToItemByTypeObjsPb([]*g.TypeObj{item}),
		Merchants: plr.Merchants.ToPb(),
	})
}

// 购买资源
func (this *Game) bazaarBuyRes(session gate.Session, msg *pb.GAME_HD_BAZAARBUYRES_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	model := room.GetBazaar().(*bazaar.Model)
	tres := model.GetTradingResByUID(msg.GetUid())
	now := time.Now().UnixMilli()
	if tres == nil || tres.IsEnd() || plr.Uid == tres.Owner {
		return nil, ecode.TRES_NOT_EXIST.String()
	} else if tres.OnlyAlli && !wld.CheckIsOneAlliance(plr.Uid, tres.Owner) {
		return nil, ecode.TRES_NOT_EXIST.String() // 仅联盟可见的不是同一联盟不可购买
	}
	if tres.NoticeTime > now {
		// 未过公示期无法购买
		// return nil, ecode.TRADE_NOTICE_NOT_FINISH.String()
	}
	p, ok := room.GetOnlinePlayerOrDB(tres.Owner).(*player.Model)
	if !ok || p == nil {
		return nil, ecode.TRES_NOT_EXIST.String()
	}
	buy := g.NewTypeObjNotId(tres.BuyType, tres.BuyCount)
	if !plr.CheckCostByTypeObjOne(buy) {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 扣除资源
	plr.ChangeCostByTypeObjOne(buy, -1)
	plr.UpdatePlrResAccChange(buy.Type, -buy.Count)
	// 开始运送
	wld.AddTransitRes(tres, plr.MainCityIndex, constant.BAZAAR_RECORD_TYPE_BUY_TO)
	// 删除
	room.GetBazaar().RemoveTradingRes(tres.UID)
	// 改变商人状态
	p.ChangeMerchantState(constant.MS_TRADING, constant.MS_TRANSIT, tres.MerchantCount)
	if p.IsOnline() {
		p.PutNotifyQueue(constant.NQ_UPDATE_MERCHANT, &pb.OnUpdatePlayerInfoNotify{Data_20: p.Merchants.ToPb()})
	} else {
		room.UpdatePlayerDB(p)
	}
	// 添加交易成功记录
	// model.AddTradeRecord(tres.SellType, tres.BuyType, tres.SellCount, tres.BuyCount)
	// 记录
	room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_BUY, p.Uid, plr.Uid, map[string]interface{}{
		"resType":   tres.BuyType,
		"resCount":  tres.BuyCount,
		"costType":  tres.SellType,
		"costCount": tres.SellCount,
	}, p.Nickname, plr.Nickname)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_BAZAARBUYRES_S2C{
		Items: plr.ToItemByTypeObjsPb([]*g.TypeObj{buy}),
	})
}

// 赠送资源
func (this *Game) bazaarGiveRes(session gate.Session, msg *pb.GAME_HD_BAZAARGIVERES_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	tplr := wld.QueryTempPlayer(plr.Uid, msg.GetName(), msg.GetIndex())
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if !room.IsFreeServer() && !wld.CheckIsOneAlliance(plr.Uid, tplr.Uid) {
		return nil, ecode.ONLY_ALLI_GIVE.String() // 非自由区 只能给盟友赠送
	}
	res := g.NewTypeObjNotId(msg.GetType(), msg.GetCount())
	if res.Count <= 0 {
		return nil, ecode.PLEASE_INPUT_RES_COUNT.String()
	}
	resCount := helper.ChangeNumByMerchantCap(res)
	needMerchantCount := ut.Ceil(float64(resCount) / float64(plr.GetMerchantTransitCap()))
	if plr.GetMerchantIdleCount() < needMerchantCount {
		return nil, ecode.MERCHANT_NOT_ENOUGH.String()
	} else if !plr.CheckCostByTypeObjOne(res) {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 扣除资源
	plr.ChangeCostByTypeObjOne(res, -1)
	plr.UpdatePlrResAccChange(res.Type, -res.Count)
	// 记录累计赠送数量
	plr.AccTotalGiveResCount += int64(res.Count)
	// 改变商人状态
	plr.ChangeMerchantState(constant.MS_NONE, constant.MS_TRANSIT, int32(needMerchantCount))
	// 开始运送
	wld.AddTransitRes(&bazaar.TRes{
		Owner:         plr.Uid,
		MerchantCount: int32(needMerchantCount),
		SellType:      res.Type,
		SellCount:     res.Count,
	}, tplr.MainCityIndex, constant.BAZAAR_RECORD_TYPE_GIVE_TO)
	// 记录
	room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_GIVE, plr.Uid, tplr.Uid, map[string]interface{}{
		"resType":  res.Type,
		"resCount": res.Count,
	}, tplr.Nickname)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_BAZAARGIVERES_S2C{
		Items:     plr.ToItemByTypeObjsPb([]*g.TypeObj{res}),
		Merchants: plr.Merchants.ToPb(),
	})
}

// 置换资源
func (this *Game) bazaarSellToSys(session gate.Session, msg *pb.GAME_HD_BAZAARSELLTOSYS_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	todayCount := constant.REPLACEMENT_TODAY_COUNT_MAP[room.GetType()]
	if plr.GetPolicyEffectInt(effect.MARKET_SERVICE_CHARGE) > 0 { // 政策加一次置换次数
		todayCount += 1
	}
	if plr.TodayReplacementCount >= todayCount {
		return nil, ecode.TRANSIT_SYS_RES_TYPE.String()
	}
	sellType, buyType, sellCount := msg.GetSellType(), msg.GetBuyType(), msg.GetSellCount()
	// 三种基础资源可与系统交换 粮、木、石
	if (sellType != ctype.CEREAL && sellType != ctype.TIMBER && sellType != ctype.STONE) ||
		(buyType != ctype.CEREAL && buyType != ctype.TIMBER && buyType != ctype.STONE) {
		return nil, ecode.TRANSIT_SYS_RES_TYPE.String()
	}
	// 最少100
	sellCount = ut.MaxInt32(sellCount, constant.REPLACEMENT_MIN_RES_COUNT)
	sell := g.NewTypeObjNotId(sellType, sellCount)
	if !plr.CheckCostByTypeObjOne(sell) { // 检测需要扣除的资源是否够
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 出售给系统
	serviceCharge := math.Max(plr.GetEffectFloat(effect.MARKET_SERVICE_CHARGE)-plr.GetPolicyEffectFloat(effect.MARKET_SERVICE_CHARGE), 1) * 0.01
	buy := g.NewTypeObjNotId(buyType, ut.Int32(float64(sellCount)*(1.0-serviceCharge)))
	// 扣除出售的资源
	plr.ChangeCostByTypeObjOne(sell, -1)
	// 添加购买的资源
	plr.ChangeCostByTypeObjOne(buy, 1)
	// 添加置换次数
	plr.TodayReplacementCount += 1
	// 记录
	room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_SYS, plr.Uid, "", map[string]interface{}{
		"resType":   sell.Type,
		"resCount":  sell.Count,
		"costType":  buy.Type,
		"costCount": buy.Count,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_BAZAARSELLTOSYS_S2C{
		Items:                 plr.ToItemByTypeObjsPb([]*g.TypeObj{sell, buy}),
		TodayReplacementCount: plr.TodayReplacementCount,
	})
}

// 获取市场交易价格信息
func (this *Game) getTradePrice(session gate.Session, msg *pb.GAME_HD_GETTRADEPRICE_C2S) (ret []byte, err string) {
	e, room, _, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETTRADEPRICE_S2C{
		TradePrice: room.GetBazaar().TradePriceToPb(),
	})
}
