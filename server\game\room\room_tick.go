package r

import (
	slg "slgsrv/server/common"
	ut "slgsrv/utils"
	"time"
)

// 20帧
func (this *Model) runTick() {
	fps := 1.0 / 20.0 * 1000.0
	fpsDuration := time.Millisecond * time.Duration(fps)
	go func() {
		tiker := time.NewTicker(fpsDuration)
		defer tiker.Stop()
		last := time.Now().UnixNano()
		for this.isRunning {
			now := <-tiker.C
			dt := (now.UnixNano() - last) / 1e6
			last = now.UnixNano()
			if this.isRunning {
				this.update(dt, time.Now().UnixMilli())
			}
		}
	}()

	// 刷新修建城市tick
	this.goRunTick(this.world.CheckUpdateBTCityQueue, fpsDuration)
	// 刷新免战信息tick
	this.goRunTick(this.world.CheckUpdateAreaAvoidWar, fpsDuration)
	// 刷新行军信息tick
	this.goRunTick(this.world.CheckUpdateMarch, fpsDuration)
	// 刷新训练士兵tick
	this.goRunTick(this.world.CheckUpdateDrillPawnQueue, fpsDuration)
	// 刷新士兵练级tick
	this.goRunTick(this.world.CheckUpdatePawnLvingQueue, fpsDuration)
	// 资源运送tick
	this.goRunTick(this.world.CheckUpdateTransit, fpsDuration)
	// 市场tick
	this.goRunTick(this.bazaar.Update, fpsDuration)
	// 屯田tick
	this.goRunTick(this.world.CheckUpdateAreaCellTonden, fpsDuration)
	// 治疗tick
	this.goRunTick(this.world.CheckUpdatePawnCuringQueue, fpsDuration)
}

// 定时保存地块信息
func (this *Model) runUpdateCellInfoToDB() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateWorldDB(false)
			}
		}
	}()
}

// 定时游戏信息
func (this *Model) runUpdateGameToDB() {
	go func() {
		tiker := time.NewTicker(time.Second * time.Duration(ut.Random(120, 150)))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateGameDB()
			}
		}
	}()
}

// 定时联盟信息
func (this *Model) runUpdateAllianceToDB() {
	go func() {
		tiker := time.NewTicker(time.Second * time.Duration(ut.Random(200, 300)))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateAllianceDB()
			}
		}
	}()
}

// 定时通知
func (this *Model) runCheckWorldNotify() {
	go func() {
		tiker := time.NewTicker(time.Millisecond * 25)
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateNotify()
			}
		}
	}()
}

// 定时检测玩家是否很久没上线了
func (this *Model) runCheckPlayerOfflineForLong() {
	if this.Type == slg.NORMAL_SERVER_TYPE || !slg.CHECK_PLAYER_OFFLINE {
		return //普通区 不检测
	}
	go func() {
		tiker := time.NewTicker(time.Minute * 10)
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if !this.isRunning {
			} else if this.Type == slg.RANK_SERVER_TYPE {
				this.world.UpdatePlayerOfflineForLongByRank()
			} else {
				this.world.UpdatePlayerOfflineForLong()
			}
		}
	}()
}

// 定时聊天信息
func (this *Model) runUpdateChatToDB() {
	go func() {
		tiker := time.NewTicker(time.Second * time.Duration(ut.Random(200, 300)))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateChatDB()
			}
		}
	}()
}

// 每天联盟分结算
func (this *Model) runUpdatePlayerAlliScore() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		var updateTime int64
		timeFour := ut.NowFourTime() - ut.TIME_MINUTE*5 //提前5分钟
		if time.Now().UnixMilli() > timeFour {
			// 当前时间大于凌晨4点 则第一次结算在明天凌晨4点
			updateTime = timeFour + ut.TIME_DAY
		} else {
			// 当前时间小于等于凌晨4点 则第一次结算在今天凌晨4点
			updateTime = timeFour
		}
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				if this.IsGameOver() {
					break
				}
				now := time.Now().UnixMilli()
				if now < updateTime {
					continue
				}
				t := time.UnixMilli(int64(now - ut.TIME_DAY))
				// 结算前一天的联盟分
				this.world.UpdatePlayerAlliScore(t)
				updateTime = ut.NowFourTime() + ut.TIME_DAY - ut.TIME_MINUTE*5 //提前5分钟
			}
		}
	}()
}

// 玩家离线消息通知
func (this *Model) runUpdateCheckOfflineNotify() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.IsGameOver() {
				break
			}
			if this.isRunning {
				this.world.CheckUpdatePlayerOfflineMsg(time.Now().UnixMilli())
			}
		}
	}()
}

// 定时触发任务
func (this *Model) runUpdateTriggerTask() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.world.UpdateTriggerTask()
			}
		}
	}()
}

// 创建定时tick
func (this *Model) goRunTick(cb func(), duration time.Duration) {
	go func() {
		timer := time.NewTicker(duration)
		defer timer.Stop()
		for this.isRunning {
			<-timer.C
			if this.isRunning {
				cb()
			}
		}
	}()
}
