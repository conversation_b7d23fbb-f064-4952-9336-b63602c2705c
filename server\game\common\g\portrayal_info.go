package g

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var PORTRAYAL_CHOSEN_ONE_ODD_MAX = 20000 // 天选画像概率分母

// 画像信息
type PortrayalInfo struct {
	Attrs      [][]int32           `json:"attrs" bson:"attrs"`
	LastAttrs  [][]int32           `json:"lastAttrs" bson:"last_attrs"` // 上一次重铸属性 已弃用
	StoreSlots []*PortrayalSlot    `json:"slots" bson:"slots"`          // 保存槽位
	History    []*PortrayalHistory `json:"history" bson:"history"`      // 历史属性
	strategys  []*StrategyObj      // 韬略列表

	json           map[string]interface{}
	skill          *PortrayalSkill // 技能
	StoreSlotsLock *deadlock.RWMutex

	ID          int32 `json:"id" bson:"id"`
	RecompCount int32 `json:"recompCount" bson:"recomp_count"` // 重新合成次数
	Debris      int32 `json:"debris" bson:"debris"`            // 残卷
	attack      int32
	hp          int32
}

// 画像保存槽位
type PortrayalSlot struct {
	Attrs [][]int32 `json:"attrs" bson:"attrs"`
}

// 画像历史属性
type PortrayalHistory struct {
	Attrs [][]int32 `json:"attrs" bson:"attrs"`
}

func NewPortrayal(id, debris int32) *PortrayalInfo {
	return &PortrayalInfo{
		ID:             id,
		Attrs:          [][]int32{},
		RecompCount:    0,
		Debris:         debris,
		json:           config.GetJsonData("portrayalBase", id),
		StoreSlots:     []*PortrayalSlot{{}}, // 默认添加一个空保存槽位
		StoreSlotsLock: new(deadlock.RWMutex),
		History:        []*PortrayalHistory{},
	}
}

func NewPortrayalByJson(data map[string]interface{}) *PortrayalInfo {
	if data == nil {
		return nil
	}
	info := NewPortrayal(ut.Int32(data["id"]), ut.Int32(data["debris"]))
	if attrs, ok := data["attrs"].(primitive.A); ok {
		for _, m := range attrs {
			if arr, ok := m.(primitive.A); ok {
				attr := []int32{}
				for _, val := range arr {
					attr = append(attr, ut.Int32(val))
				}
				info.Attrs = append(info.Attrs, attr)
			}
		}
	}
	info.UpdateAttr()
	return info
}

func NewPortrayalPbByJson(data map[string]interface{}) *pb.PortrayalInfo {
	if data == nil {
		return nil
	}
	attrs := []*pb.AttrArrayInfo{}
	if datas, ok := data["attrs"].([]interface{}); ok {
		for _, m := range datas {
			arr := ut.IntArray(m)
			attrInfo := &pb.AttrArrayInfo{}
			for _, val := range arr {
				attrInfo.Attr = append(attrInfo.Attr, int32(val))
			}
			attrs = append(attrs, attrInfo)
		}
	} else if int32Arr, ok := data["attrs"].([][]int32); ok {
		for _, subArr := range int32Arr {
			attrInfo := &pb.AttrArrayInfo{}
			for _, val := range subArr {
				attrInfo.Attr = append(attrInfo.Attr, val)
			}
			attrs = append(attrs, attrInfo)
		}
	} else {
		return nil
	}
	return &pb.PortrayalInfo{
		Id:    pb.Int32(data["id"]),
		Attrs: attrs,
	}
}

func NewPortrayalByPb(data *pb.PortrayalInfo) *PortrayalInfo {
	info := NewPortrayal(data.Id, data.Debris)
	if data.Attrs != nil {
		for _, m := range data.Attrs {
			attr := []int32{}
			for _, val := range m.Attr {
				attr = append(attr, ut.Int32(val))
			}
			info.Attrs = append(info.Attrs, attr)
		}
	}
	info.UpdateAttr()
	return info
}

func (this *PortrayalInfo) UpdateInfoByDB() {
	this.StoreSlots = []*PortrayalSlot{{}} // 默认添加一个空保存槽位
	this.StoreSlotsLock = new(deadlock.RWMutex)
	this.json = config.GetJsonData("portrayalBase", this.ID)
	this.UpdateAttr()
}

type PortrayalStrip struct {
	Id    int32     `bson:"id"`
	Attrs [][]int32 `bson:"attrs"`
}

func (this *PortrayalInfo) ToStrip() *PortrayalStrip {
	return &PortrayalStrip{
		Id:    this.ID,
		Attrs: CloneAttrs(this.Attrs),
	}
}

func (this *PortrayalInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"id":    this.ID,
		"attrs": CloneAttrs(this.Attrs),
	}
}

func (this *PortrayalInfo) ToPb() *pb.PortrayalInfo {
	return &pb.PortrayalInfo{
		Id:          this.ID,
		RecompCount: this.RecompCount,
		Debris:      this.Debris,
		Attrs:       CloneEffectAttrsToPb(this.Attrs),
		Slots:       array.Map(this.StoreSlots, func(m *PortrayalSlot, i int) *pb.PortrayalStoreSlot { return m.ToPb() }),
		History:     array.Map(this.History, func(m *PortrayalHistory, i int) *pb.PortrayalHistory { return m.ToPb() }),
	}
}

func (this *PortrayalInfo) ToPbForPawn() *pb.PortrayalInfo {
	return &pb.PortrayalInfo{
		Id:    this.ID,
		Attrs: CloneEffectAttrsToPb(this.Attrs),
	}
}

func (this *PortrayalInfo) ToBasePbForPawn() *pb.PortrayalInfo {
	return &pb.PortrayalInfo{
		Id: this.ID,
	}
}

func (this *PortrayalInfo) GetJson() map[string]interface{} { return this.json }
func (this *PortrayalInfo) GetAttack() int32                { return this.attack }
func (this *PortrayalInfo) GetHP() int32                    { return this.hp }
func (this *PortrayalInfo) CloneAttrs() [][]int32           { return CloneAttrs(this.Attrs) }
func (this *PortrayalInfo) GetSkill() *PortrayalSkill       { return this.skill }
func (this *PortrayalInfo) GetStrategys() []*StrategyObj    { return this.strategys }
func (this *PortrayalInfo) GetAvatarPawn() int32            { return ut.Int32(this.json["avatar_pawn"]) }
func (this *PortrayalInfo) GetMoveVelocity() int32          { return ut.Int32(this.json["velocity"]) }

// 获取技能id 也是英雄类型
func (this *PortrayalInfo) GetSkillID() int32 {
	if this.skill != nil {
		return this.skill.Id
	}
	return 0
}

// 是否解锁了
func (this *PortrayalInfo) IsUnlock() bool {
	return len(this.Attrs) > 0
}

// 设置属性
func (this *PortrayalInfo) SetAttr(attrs [][]int32) {
	this.Attrs = CloneAttrs(attrs)
	this.UpdateAttr()
}

// 设置韬略
func (this *PortrayalInfo) ChangeStrategys(ids []int32) {
	// 先删除之前的韬略
	this.Attrs = array.Delete(this.Attrs, func(attr []int32) bool { return attr[0] == 2 })
	// 添加新的韬略
	for _, id := range ids {
		this.Attrs = append(this.Attrs, []int32{2, id, 0})
	}
	// 刷新属性
	this.UpdateAttr()
}

// 刷新属性
func (this *PortrayalInfo) UpdateAttr() {
	this.attack = 0
	this.hp = 0
	this.skill = nil
	this.strategys = []*StrategyObj{}
	for _, arr := range this.Attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if fieldType == 0 { // 属性
			if tp == 1 {
				this.hp += value
			} else if tp == 2 {
				this.attack += value
			}
		} else if fieldType == 1 { // 技能
			this.skill = NewPortrayalSkill(tp, value)
			arr[2] = this.skill.Value
		} else if fieldType == 2 { // 韬略
			this.strategys = append(this.strategys, NewStrategyObj(tp))
		}
	}
}

// 画像属性随机
func (this *PortrayalInfo) RandomInfo() (bool, bool) {
	isChosenOneOld := this.IsChosenOne()
	isChosenOne := isChosenOneOld
	if !isChosenOne {
		// 之前不是天选 先根据概率随机出本次是否出天选
		odds := int32(ut.Random(1, PORTRAYAL_CHOSEN_ONE_ODD_MAX))
		if odds <= this.RecompCount {
			// 随机出天选
			isChosenOne = true
			this.SetChosenOne()
		} else {
			// 不是天选 随机属性
			for i := 0; i < PORTRAYAL_CHOSEN_ONE_ODD_MAX; i++ {
				this.RandomAttr()
				isChosenOne = this.IsChosenOne()
				// 随机后的属性为天选则重新随机
				if !isChosenOne {
					break
				}
				log.Info("RandomInfo i: %v, id: %v", i, this.ID)
			}
		}
	} else {
		this.Attrs = array.Delete(this.Attrs, func(attr []int32) bool { return attr[0] == 2 })
		// 随机2条韬略
		strategyIds := ut.StringToInt32s(ut.String(this.json["strategy"]), "|")
		for i := 0; i < 2 && i < len(strategyIds); i++ {
			index := ut.Random(0, len(strategyIds)-1)
			this.Attrs = append(this.Attrs, []int32{2, strategyIds[index], 0})
			strategyIds = append(strategyIds[:index], strategyIds[index+1:]...)
		}
		// 刷新一下属性 提取可以直接使用的数据
		this.UpdateAttr()
	}
	// 记录历史属性
	this.StoreSlotsLock.Lock()
	this.History = array.AppendToHead(this.History, &PortrayalHistory{Attrs: CloneAttrs(this.Attrs)})
	// 限制历史记录数量
	if len(this.History) > slg.PORTRAYAL_HISTORY_MAX_COUNT {
		this.History = this.History[:slg.PORTRAYAL_HISTORY_MAX_COUNT]
	}
	this.StoreSlotsLock.Unlock()
	return isChosenOneOld, isChosenOne
}

// 随机属性
func (this *PortrayalInfo) RandomAttr() {
	this.Attrs = [][]int32{}
	// 主属性 0
	if hp := ut.String(this.json["hp"]); hp != "" { // 1
		arr := ut.StringToInt32s(hp, ",")
		this.Attrs = append(this.Attrs, []int32{0, 1, ut.RandomInt32(arr[0], arr[1])})
	}
	if attack := ut.String(this.json["attack"]); attack != "" { // 2
		arr := ut.StringToInt32s(attack, ",")
		this.Attrs = append(this.Attrs, []int32{0, 2, ut.RandomInt32(arr[0], arr[1])})
	}
	// 技能 1
	skiilId := ut.Int32(this.json["skill"])
	if skillJson := config.GetJsonData("portrayalSkill", skiilId); skillJson != nil {
		if value := ut.String(skillJson["value"]); value != "" {
			arr := ut.StringToInt32s(value, ",")
			this.Attrs = append(this.Attrs, []int32{1, skiilId, ut.RandomInt32(arr[0], arr[1])})
		}
	}
	// 韬略 2  第一个韬略
	strategyIds := ut.StringToInt32s(ut.String(this.json["strategy"]), "|")
	index := ut.Random(0, len(strategyIds)-1)
	this.Attrs = append(this.Attrs, []int32{2, strategyIds[index], 0})
	// 刷新一下属性 提取可以直接使用的数据
	this.UpdateAttr()
}

// 还原属性
func (this *PortrayalInfo) RestoreAttr(slotInfo *PortrayalSlot) {
	if slotInfo.Attrs == nil || len(slotInfo.Attrs) == 0 {
		return
	}
	this.Attrs = CloneAttrs(slotInfo.Attrs)
	this.UpdateAttr()
}

// 直接设置为天选
func (this *PortrayalInfo) SetChosenOne() {
	// 先清空属性
	this.Attrs = [][]int32{}
	// 主属性 0
	if hp := ut.String(this.json["hp"]); hp != "" { // 1
		arr := ut.StringToInt32s(hp, ",")
		this.Attrs = append(this.Attrs, []int32{0, 1, arr[1]})
	}
	if attack := ut.String(this.json["attack"]); attack != "" { // 2
		arr := ut.StringToInt32s(attack, ",")
		this.Attrs = append(this.Attrs, []int32{0, 2, arr[1]})
	}
	// 技能 1
	skiilId := ut.Int32(this.json["skill"])
	if skillJson := config.GetJsonData("portrayalSkill", skiilId); skillJson != nil {
		if value := ut.String(skillJson["value"]); value != "" {
			arr := ut.StringToInt32s(value, ",")
			this.Attrs = append(this.Attrs, []int32{1, skiilId, arr[ut.Int(skillJson["value_max"])]})
		}
	}
	// 韬略 2
	strategyIds := ut.StringToInt32s(ut.String(this.json["strategy"]), "|")
	for i := 0; i < 2 && i < len(strategyIds); i++ {
		index := ut.Random(0, len(strategyIds)-1)
		this.Attrs = append(this.Attrs, []int32{2, strategyIds[index], 0})
		strategyIds = append(strategyIds[:index], strategyIds[index+1:]...)
	}
	// 刷新一下属性 提取可以直接使用的数据
	this.UpdateAttr()
}

// 是否天选
func (this *PortrayalInfo) IsChosenOne() bool {
	if !this.IsUnlock() {
		return false
	} else if this.hp != ut.StringToInt32s(ut.String(this.json["hp"]), ",")[1] {
		return false
	} else if this.attack != ut.StringToInt32s(ut.String(this.json["attack"]), ",")[1] {
		return false
	}
	// 技能
	return this.skill != nil && this.skill.IsChosenOne()
}

// 获取保存槽位
func (this *PortrayalInfo) GetSlotByIndex(index int) *PortrayalSlot {
	this.StoreSlotsLock.RLock()
	defer this.StoreSlotsLock.RUnlock()
	if index >= len(this.StoreSlots) {
		return nil
	}
	return this.StoreSlots[index]
}

// 添加槽位
func (this *PortrayalInfo) AddSaveSlot() {
	this.StoreSlotsLock.Lock()
	defer this.StoreSlotsLock.Unlock()
	this.StoreSlots = append(this.StoreSlots, &PortrayalSlot{})
}

// 兼容槽位列表
func (this *PortrayalInfo) SaveSlotsFix() {
	this.StoreSlotsLock.Lock()
	defer this.StoreSlotsLock.Unlock()
	if this.StoreSlots == nil {
		this.StoreSlots = []*PortrayalSlot{} // 兼容
	}
	if len(this.StoreSlots) == 0 {
		this.StoreSlots = append(this.StoreSlots, &PortrayalSlot{})
	}
}

// 兼容历史记录
func (this *PortrayalInfo) HistoryFix() {
	if this.History == nil || len(this.History) == 0 {
		this.History = []*PortrayalHistory{{Attrs: CloneAttrs(this.Attrs)}}
		if this.LastAttrs != nil && len(this.LastAttrs) > 0 {
			this.History = append(this.History, &PortrayalHistory{Attrs: CloneAttrs(this.LastAttrs)})
		}
	}
}

// 判断属性是否为天选
func (this *PortrayalInfo) IsAttrsChosenOne(attrs [][]int32) bool {
	if len(attrs) == 0 {
		return false
	}
	for _, arr := range attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if fieldType == 0 { // 属性
			if tp == 1 && value != ut.StringToInt32s(ut.String(this.json["hp"]), ",")[1] {
				return false
			} else if tp == 2 && value != ut.StringToInt32s(ut.String(this.json["attack"]), ",")[1] {
				return false
			}
		} else if fieldType == 1 { // 技能
			skillJson := config.GetJsonData("portrayalSkill", tp)
			if skillJson == nil {
				return false
			}
			maxIndex := ut.Int(skillJson["value_max"])
			skillValueArr := ut.StringToInt32s(ut.String(skillJson["value"]), ",")
			if len(skillValueArr) <= maxIndex {
				return false
			}
			if value != skillValueArr[maxIndex] {
				return false
			}
		}
	}
	return true
}

// 保存属性
func (this *PortrayalSlot) StoreAttr(attr [][]int32) {
	this.Attrs = CloneAttrs(attr)
}

func (this *PortrayalSlot) ToPb() *pb.PortrayalStoreSlot {
	return &pb.PortrayalStoreSlot{Attrs: CloneEffectAttrsToPb(this.Attrs)}
}

func (this *PortrayalHistory) ToPb() *pb.PortrayalHistory {
	return &pb.PortrayalHistory{Attrs: CloneEffectAttrsToPb(this.Attrs)}
}
