package game

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDAlliance() {
	this.GetServer().RegisterGO("HD_GetAllAlliBaseInfo", this.getAllAlliBaseInfo)               // 获取所有联盟基础信息
	this.GetServer().RegisterGO("HD_CreateAlliance", this.createAlliance)                       // 创建联盟
	this.GetServer().RegisterGO("HD_GetAlliances", this.getAlliances)                           // 获取联盟列表
	this.GetServer().RegisterGO("HD_GetAlliance", this.getAlliance)                             // 获取联盟
	this.GetServer().RegisterGO("HD_ApplyJoinAlliance", this.applyJoinAlliance)                 // 申请加入联盟
	this.GetServer().RegisterGO("HD_CancelJoinAlliance", this.cancelJoinAlliance)               // 取消申请加入联盟
	this.GetServer().RegisterGO("HD_AgreeJoinAlliance", this.agreeJoinAlliance)                 // 同意拒绝加入联盟
	this.GetServer().RegisterGO("HD_KickoutAlliance", this.kickoutAlliance)                     // 踢出联盟
	this.GetServer().RegisterGO("HD_ExitAlliance", this.exitAlliance)                           // 退出联盟
	this.GetServer().RegisterGO("HD_ChangeAllianceNotice", this.changeAllianceNotice)           // 编辑联盟公告
	this.GetServer().RegisterGO("HD_ChangeAlliMemberJob", this.changeAlliMemberJob)             // 改变成员职位
	this.GetServer().RegisterGO("HD_AlliMapFlag", this.alliMapFlag)                             // 地图标记
	this.GetServer().RegisterGO("HD_DelAlliMapFlag", this.delAlliMapFlag)                       // 删除地图标记
	this.GetServer().RegisterGO("HD_GetAlliBattleRecord", this.getAlliBattleRecord)             // 获取联盟成员的战斗记录 根据每天
	this.GetServer().RegisterGO("HD_GetAlliMemberBattleRecord", this.getAlliMemberBattleRecord) // 获取指定联盟成员的全部战斗记录
	this.GetServer().RegisterGO("HD_AlliSelectPolicy", this.alliSelectPolicy)                   // 联盟选择政策
	this.GetServer().RegisterGO("HD_GetAlliLogs", this.getAlliLogs)                             // 获取联盟日志
	this.GetServer().RegisterGO("HD_ChangeAlliApplyDesc", this.changeAlliApplyDesc)             // 改变联盟的申请说明
	this.GetServer().RegisterGO("HD_CreateAlliChatChannel", this.createAlliChatChannel)         // 创建联盟聊天副频道
	this.GetServer().RegisterGO("HD_DelAlliChatChannel", this.delAlliChatChannel)               // 删除联盟聊天副频道
	this.GetServer().RegisterGO("HD_UpdateAlliChatChannel", this.updateAlliChatChannel)         // 更新联盟聊天副频道
	this.GetServer().RegisterGO("HD_VoteAlliLeader", this.voteAlliLeader)                       // 盟主投票
	this.GetServer().RegisterGO("HD_AlliLeaderConfirm", this.alliLeaderConfirm)                 // 盟主确认
}

// 获取所有联盟内政
func (this *Game) getAllAlliBaseInfo(session gate.Session, msg *pb.GAME_HD_GETALLALLIBASEINFO_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLALLIBASEINFO_S2C{Allis: wld.ToAllAlliBaseInfoPb()})
}

// 创建联盟
func (this *Game) createAlliance(session gate.Session, msg *pb.GAME_HD_CREATEALLIANCE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid != "" {
		if alli := wld.GetAlliance(plr.AllianceUid); alli != nil {
			return nil, ecode.YET_HAS_ALLIANCE.String() // 已经有联盟了
		}
		plr.AllianceUid = ""
		if tplr := wld.GetTempPlayer(plr.Uid); tplr != nil {
			tplr.AllianceUid = ""
		}
	}
	if helper.IsNotJoinAlliTime() && !slg.GetAlliSwitch() {
		return nil, ecode.OT_NOT_CREATE_ALLI.String() // 自由交战期间不可创建联盟
	} else if wld.GetSeason().GetType() >= 3 {
		return nil, ecode.LATE_GAME_CANT_CREATE_ALLI.String() // 冬季不能创建联盟
	}
	name := msg.GetName()
	if ut.GetStringLen(name) > 12 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if wld.CheckHasAllianceName(name) {
		return nil, ecode.NICKNAME_EXIST.String() // 名字已存在
	} else if sta := sensitive.CheckName(name); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	} else if len(wld.GetPlayerOwnCells(plr.Uid)) < constant.CREATE_ALLI_COND {
		return nil, ecode.NEED_CREATE_ALLI_COND.String()
	}
	// 获取大使馆
	build := wld.GetArea(plr.MainCityIndex).GetAllianceBuild()
	if build == nil || build.Lv < constant.CREATE_ALLI_MAX_LV {
		return nil, ecode.UNKNOWN.String()
	} else if this.IsBannedChat(plr) {
		return nil, ecode.BAN_OPT.String() // 禁言无法操作
	}
	cost := g.StringToTypeObjs(constant.CREATE_ALLI_COST)
	if !plr.CheckAndDeductCostByTypeObjs(cost) { // 扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 创建了
	alli := wld.CreateAlliance(name, msg.GetIcon(), build.Lv, plr)
	// 从其他联盟的申请列表中删除这个玩家
	wld.CleanPlayerAlliApply(plr.Uid, true)
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_alliance", map[string]interface{}{
		"alliance_uid":            alli.Uid,
		"alliance_type":           "create",
		"alliance_member":         0,
		"alliance_maxmember":      alli.MemberPersLimit,
		"alliance_sum_embassy_lv": alli.SumEmbassyLv,
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CREATEALLIANCE_S2C{
		Alliance: alli.ToPb(wld, plr.Uid),
		Cost:     plr.ToItemByTypeObjsPb(cost),
	})
}

// 获取联盟列表
func (this *Game) getAlliances(session gate.Session, msg *pb.GAME_HD_GETALLIANCES_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLIANCES_S2C{
		List: wld.ToAlliances(plr.Uid),
	})
}

// 获取联盟
func (this *Game) getAlliance(session gate.Session, msg *pb.GAME_HD_GETALLIANCE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	uid := ut.String(msg.GetUid())
	alli := wld.GetAlliance(uid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLIANCE_S2C{
		Alliance: alli.ToPb(wld, plr.Uid),
	})
}

// 申请加入联盟
func (this *Game) applyJoinAlliance(session gate.Session, msg *pb.GAME_HD_APPLYJOINALLIANCE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid != "" {
		if alli := wld.GetAlliance(plr.AllianceUid); alli != nil {
			return nil, ecode.YET_HAS_ALLIANCE.String() // 已经有联盟了
		}
		plr.AllianceUid = ""
		if tplr := wld.GetTempPlayer(plr.Uid); tplr != nil {
			tplr.AllianceUid = ""
		}
	}
	if helper.IsNotJoinAlliTime() && !slg.GetAlliSwitch() {
		return nil, ecode.OT_NOT_APPLY_ALLI.String() // 自由交战期间不可申请加入联盟
	}
	count := wld.GetPlayerApplyAlliCount(plr.Uid)
	if count >= constant.ALLI_APPLY_MAX_COUNT {
		return nil, ecode.APPLY_ALLIANCE_TOOMANY.String() // 当前申请的太多了
	}
	uid := msg.GetUid()
	alli := wld.GetAlliance(uid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if alli.IsInMember(plr.Uid) {
		return nil, ecode.YET_IN_MEMBER.String()
	} else if alli.IsInApplyList(plr.Uid) {
		return nil, ecode.YET_IN_APPLY.String()
	}
	// 直接添加
	wld.AddAlliApply(alli, plr, msg.GetDesc())
	// 通知申请列表刷新
	msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIAPPLYS_NOTIFY{Applys: alli.ToApplysPb()})
	room.PutNotifyAllPlayersQueue("game/OnUpdateAlliApplys", msgBytes, alli.GetMemberUidsByApplyMgr())
	return
}

// 取消申请加入联盟
func (this *Game) cancelJoinAlliance(session gate.Session, msg *pb.GAME_HD_CANCELJOINALLIANCE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid != "" {
		return // 已经有联盟了
	}
	uid := msg.GetUid()
	alli := wld.GetAlliance(uid)
	if alli == nil || alli.IsInMember(uid) {
		return // 联盟不存在
	}
	wld.RemoveAlliApply(alli, plr.Uid)
	// 通知申请列表刷新
	msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIAPPLYS_NOTIFY{Applys: alli.ToApplysPb()})
	room.PutNotifyAllPlayersQueue("game/OnUpdateAlliApplys", msgBytes, alli.GetMemberUidsByApplyMgr())
	return
}

// 同意拒绝加入联盟
func (this *Game) agreeJoinAlliance(session gate.Session, msg *pb.GAME_HD_AGREEJOINALLIANCE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	me := alli.GetMember(plr.Uid)
	if me == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if me.Job > 1 {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	uid, isAgree := msg.GetUid(), msg.GetIsAgree()
	tplr := wld.GetTempPlayer(uid)
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if !isAgree { // 如果是拒绝就不管是否加入其他或者取消申请
		alli.AddAlliLog(constant.ALLI_LOG_TYPE_REFUSE, plr.Nickname, tplr.Nickname)
	} else if this.IsBannedChat(plr) {
		return nil, ecode.BAN_OPT.String() // 禁言无法操作
	} else if helper.IsNotJoinAlliTime() && !slg.GetAlliSwitch() {
		return nil, ecode.OT_NOT_AGREE_JOIN_ALLI.String() // 不能在交战期间同意
	} else if tplr.AllianceUid != "" || alli.IsInMember(uid) {
		err = ecode.YET_JOIN_ALLIANCE.String() // 对方已经加入其他盟
		isAgree = false
	} else if !alli.IsInApplyList(uid) {
		return nil, ecode.YET_CANCEL_APPLY.String() // 对方已取消申请
	} else if len(alli.Members.List) >= int(alli.MemberPersLimit) {
		return nil, ecode.MEMBER_COUNT_FULL.String()
	} else if area := wld.GetArea(tplr.MainCityIndex); area != nil && area.IsBattle() { // 如果被加入的主城在战斗中也不能加入
		return nil, ecode.BATTLEING.String()
	}
	// 先删除
	wld.RemoveAlliApply(alli, uid)
	// 加入
	if isAgree {
		uids := alli.GetMemberUids() // 在还没有加入新人的时候获取
		// 直接加入
		wld.AddAlliMember(alli, uid, 10)
		alli.AddAlliLog(constant.ALLI_LOG_TYPE_JOIN, tplr.Nickname, plr.Nickname)
		// 从其他联盟的申请列表中删除这个玩家
		wld.CleanPlayerAlliApply(uid, false)
		// 通知被同意的玩家
		msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONAGREEJOINALLIANCE_NOTIFY{Data: alli.ToPb(wld, uid)})
		room.PutNotifyAllPlayersQueue("game/OnAgreeJoinAlliance", msgBytes, []string{uid})
		// 通知其他成员有人加入联盟
		room.PutNotifyAllPlayersQueue("game/OnPlayerJoinAlliance", pb.Bytes(&pb.GAME_ONPLAYERJOINALLIANCE_NOTIFY{
			Uid:     uid,
			Members: alli.ToMembersPb(wld),
		}), uids)
		// 上报
		wld.TaTrack(uid, 0, "ta_alliance", map[string]interface{}{
			"alliance_uid":            alli.Uid,
			"alliance_type":           "add",
			"alliance_member":         alli.GetMemberCount() - 1,
			"alliance_maxmember":      alli.MemberPersLimit,
			"alliance_sum_embassy_lv": alli.SumEmbassyLv,
		})
	}
	return pb.ProtoMarshal(&pb.GAME_HD_AGREEJOINALLIANCE_S2C{
		Applys: alli.ToApplysPb(),
	})
}

// 踢出联盟
func (this *Game) kickoutAlliance(session gate.Session, msg *pb.GAME_HD_KICKOUTALLIANCE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	uid := msg.GetUid()
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if alli.Creater == uid || plr.Uid == uid {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 不能踢出盟主 和 自己
	} else if alli.Creater != plr.Uid && alli.GetMemberJob(plr.Uid) != constant.CREATER_VICE {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	member := alli.GetMember(uid)
	if member == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if room.Type == slg.RANK_SERVER_TYPE && member.GetOfflineTime() <= ut.TIME_DAY*3 {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 评分区 如果没有离线3天不能踢出
	} else if wld.CheckPlayerHasAncient(uid) {
		return nil, ecode.CANT_LEAVE_ALLI_WITH_ANCIENT.String() // 拥有古城不能踢出
	}
	// 删除联盟
	wld.RemoveAlliMember(alli, uid, "kick")
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_KICK, plr.Nickname, wld.GetPlayerNickname(uid))
	// 通知玩家被提出联盟
	body, _ := pb.ProtoMarshal(&pb.GAME_ONKICKOUTALLIANCE_NOTIFY{})
	room.PutNotifyAllPlayersQueue("game/OnKickoutAlliance", body, []string{uid})
	// 发邮件通知被踢的人
	room.SendMailOne(100006, "", plr.Nickname, "-1", uid)
	return
}

// 退出联盟
func (this *Game) exitAlliance(session gate.Session, msg *pb.GAME_HD_EXITALLIANCE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if room.Type == slg.RANK_SERVER_TYPE {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 评分区不能退出联盟
	}
	member := alli.GetMember(plr.Uid)
	if member == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if wld.CheckPlayerHasAncient(plr.Uid) {
		return nil, ecode.CANT_LEAVE_ALLI_WITH_ANCIENT.String() // 拥有古城不能退出
	}
	s2c := &pb.GAME_HD_EXITALLIANCE_S2C{}
	exitCount := ut.MinInt32(plr.ExitAllianceCount+1, 10)
	maxTime := exitCount * constant.CAN_EXIT_ALLI_TIME
	cdTime := int32(time.Now().UnixMilli() - member.JoinTime)
	if cdTime < maxTime {
		s2c.Time = maxTime - cdTime
		ret, _ = pb.ProtoMarshal(s2c)
		return ret, ecode.JOIN_ALLI_TIME_TOO_SHORT.String() // 加入时间太短
	}
	// 删除联盟
	wld.RemoveAlliMember(alli, plr.Uid, "exit")
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_QUIT, plr.Nickname)
	// 发邮件通知创建人
	room.SendMailOne(100008, "", plr.Nickname, "-1", alli.Creater)
	// 返回
	plr.ExitAllianceCount = exitCount
	s2c.ExitAllianceCount = plr.ExitAllianceCount
	return pb.ProtoMarshal(s2c)
}

// 编辑联盟公告
func (this *Game) changeAllianceNotice(session gate.Session, msg *pb.GAME_HD_CHANGEALLIANCENOTICE_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if alli.Creater != plr.Uid {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	cd := alli.GetEditNoticeCD()
	s2c := &pb.GAME_HD_CHANGEALLIANCENOTICE_S2C{EditNoticeCD: cd, Notice: alli.Notice}
	if cd > 0 {
		return pb.ProtoMarshal(s2c)
	} else if this.IsBannedChat(plr) {
		return nil, ecode.BAN_OPT.String() // 禁言无法操作
	}
	notice := msg.GetNotice()
	if ut.GetStringLen(notice) > 200 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if sensitive.Validate(notice) {
		return nil, ecode.TEXT_HAS_SENSITIVE.String()
	}
	alli.Notice = notice
	alli.LastEditNoticeTime = time.Now().UnixMilli()
	s2c.EditNoticeCD = int32(constant.ALLI_EDIT_NOTICE_INTERVAL)
	s2c.Notice = alli.Notice
	// 通知玩家公告变更
	room.PutNotifyAllPlayersQueue("game/OnAllianceChangeNotice", pb.Bytes(&pb.GAME_ONALLIANCECHANGENOTICE_NOTIFY{Notice: alli.Notice}), alli.GetMemberUids())
	return pb.ProtoMarshal(s2c)
}

// 改变成员职位
func (this *Game) changeAlliMemberJob(session gate.Session, msg *pb.GAME_HD_CHANGEALLIMEMBERJOB_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	} else if alli.Creater != plr.Uid {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	member, job := alli.GetMember(msg.GetUid()), msg.GetJob()
	if member == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if member.Job == job {
		return
	} else if job == 1 && alli.GetJobCount(job) >= 1 { // 副盟主有1个
		return nil, ecode.ALLI_JOB_FULL.String()
	} else if job == 2 && alli.GetJobCount(job) >= 2 { // 军师有2个
		return nil, ecode.ALLI_JOB_FULL.String()
	} else if this.IsBannedChat(plr) {
		return nil, ecode.BAN_OPT.String() // 禁言无法操作
	}
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_CHANGE_JOB, wld.GetPlayerNickname(member.Uid), ut.String(member.Job), ut.String(job))
	member.Job = job
	// 通知玩家成员职位变更
	room.PutNotifyAllPlayersQueue("game/OnAllianceChangeMemberJob", pb.Bytes(&pb.GAME_ONALLIANCECHANGEMEMBERJOB_NOTIFY{Uid: member.Uid, Job: msg.GetJob()}), alli.GetMemberUids())
	return pb.ProtoMarshal(&pb.GAME_HD_CHANGEALLIMEMBERJOB_S2C{Job: msg.GetJob()})
}

// 地图标记
func (this *Game) alliMapFlag(session gate.Session, msg *pb.GAME_HD_ALLIMAPFLAG_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	} else if alli.GetMemberJob(plr.Uid) != constant.MILITARY {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	index, flag, desc := msg.GetIndex(), msg.GetFlag(), msg.GetDesc()
	alli.AddMapFlag(index, flag, desc)
	// 通知玩家成员地图标记
	room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMapFlag", pb.Bytes(&pb.GAME_ONUPDATEALLIMAPFLAG_NOTIFY{MapFlag: alli.ToMapFlagPb()}), alli.GetMemberUids())
	return
}

// 删除地图标记
func (this *Game) delAlliMapFlag(session gate.Session, msg *pb.GAME_HD_DELALLIMAPFLAG_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	} else if alli.GetMemberJob(plr.Uid) != constant.MILITARY {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	alli.RemoveMapFlag(msg.GetIndex())
	// 通知玩家成员地图标记
	room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMapFlag", pb.Bytes(&pb.GAME_ONUPDATEALLIMAPFLAG_NOTIFY{MapFlag: alli.ToMapFlagPb()}), alli.GetMemberUids())
	return
}

// 获取玩家指定日期的战斗积分数据
func (this *Game) getAlliBattleRecord(session gate.Session, msg *pb.GAME_HD_GETALLIBATTLERECORD_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	records := room.GetRecord().GetAlliPlayersBattleScoreRecordsByDate(plr.AllianceUid, msg.GetDate())
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLIBATTLERECORD_S2C{Records: records})
}

// 获取指定联盟成员的全部战斗记录
func (this *Game) getAlliMemberBattleRecord(session gate.Session, msg *pb.GAME_HD_GETALLIMEMBERBATTLERECORD_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		// 自己没有联盟
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alliPly := wld.GetTempPlayer(msg.GetUid())
	if alliPly == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if alliPly.AllianceUid == "" || alliPly.AllianceUid != plr.AllianceUid {
		// 和查询的玩家不在同一个联盟
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(alliPly.AllianceUid)
	if alli == nil {
		// 联盟不存在
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	plyMember := alli.GetMember(alliPly.Uid)
	if plyMember == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	today := ut.GetToday4Date()
	if plyMember.BattleScoreRecord == nil || plyMember.BattleScoreRecord.Date != today {
		// 从联盟成员数据获取战斗积分数据 日期更新则从数据库获取
		record, rst := room.GetRecord().GetPlayerBattleScoreRecords(alliPly.Uid, today, alliPly.AllianceUid)
		if rst {
			plyMember.BattleScoreRecord = room.GetRecord().BattleScoreRecordToPb(*record)
		}
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLIMEMBERBATTLERECORD_S2C{Record: plyMember.BattleScoreRecord})
}

// 选择联盟政策
func (this *Game) alliSelectPolicy(session gate.Session, msg *pb.GAME_HD_ALLISELECTPOLICY_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	} else if !alli.IsCreaterOrVice(plr.Uid) {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 没有操作权限
	}
	index, id := msg.GetIndex(), msg.GetId()
	if index < 0 || index >= int32(len(constant.ALLI_POLICY_SLOT_CONF)) {
		return nil, ecode.UNKNOWN.String()
	} else if alli.SumEmbassyLv < constant.ALLI_POLICY_SLOT_CONF[index] {
		return nil, ecode.COND_NOT_ENOUGH.String() // 等级不够
	}
	// 设置政策
	policy, err := alli.SetPolicy(index, id)
	if policy == nil {
		return nil, err
	} else if effectType := policy.GetEffect(); effectType > 0 {
		// 刷新玩家的政策效果
		effectMap := map[int32]bool{effectType: true}
		uids := alli.GetMemberUids()
		for _, m := range uids {
			if m == plr.Uid {
			} else if p := room.GetPlayer(m); p != nil {
				p.UpdatePolicyEffect(effectMap)
				// 激活政策获得物品
				wld.ActivePolicyAddItems(id, m)
			}
		}
		plr.UpdatePolicyEffect(effectMap)
	}
	// 刷新选择
	alli.CheckUpdateSelectPolicys()
	// 通知联盟成员
	msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBEREMBASSYLV_NOTIFY{
		SumEmbassyLv:  alli.SumEmbassyLv,
		PersLimit:     alli.MemberPersLimit,
		SelectPolicys: alli.ToSelectPolicysPb(),
	})
	room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMemberEmbassyLv", msgBytes, alli.GetMemberUids())
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_CHOOSE_POLICY, plr.Nickname, ut.String(id))
	// 通知
	wld.PutNotifyQueue(constant.NQ_ALLI_BASE_INFO, &pb.OnUpdateWorldInfoNotify{Data_62: []*pb.AlliBaseInfo{wld.ToAlliBaseInfo(alli)}})
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_selectAlliPolicy", map[string]interface{}{
		"policy_id": id,
		"policys":   alli.ToPolicyTrack(),
	})
	return pb.ProtoMarshal(&pb.GAME_HD_ALLISELECTPOLICY_S2C{})
}

// 获取联盟日志
func (this *Game) getAlliLogs(session gate.Session, msg *pb.GAME_HD_GETALLILOGS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil || !alli.IsInMember(plr.Uid) {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETALLILOGS_S2C{List: alli.ToLogsPb(0, constant.ALLI_LOG_MAX_COUNT)})
}

// 改变联盟的申请说明
func (this *Game) changeAlliApplyDesc(session gate.Session, msg *pb.GAME_HD_CHANGEALLIAPPLYDESC_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil || !alli.IsCreaterOrVice(plr.Uid) {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	alli.ApplyDesc = msg.GetApplyDesc()
	// 通知玩家申请说明变更
	room.PutNotifyAllPlayersQueue("game/OnAllianceChangeApplyDesc", pb.Bytes(&pb.GAME_ONALLIANCECHANGEAPPLYDESC_NOTIFY{ApplyDesc: alli.ApplyDesc}), alli.GetMemberUids())
	return
}

// 创建联盟聊天副频道
func (this *Game) createAlliChatChannel(session gate.Session, msg *pb.GAME_HD_CREATEALLICHATCHANNEL_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil || !alli.IsCreaterOrMilitary(plr.Uid) {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	uid := ut.ID()
	name, color, memberUids, memberFilter := msg.GetName(), msg.GetColor(), msg.GetMemberUids(), msg.GetMemberFilter()
	if alliChannel, e := alli.CreateChatChannel(name, uid, color, memberUids, memberFilter); e == "" {
		channel := wld.GetAlliChatChannel(alli.Uid, uid)
		wld.AddChatChannel(channel)
		// 通知创建联盟聊天副频道
		notifyUids := memberUids
		if !memberFilter {
			notifyUids = alli.GetMemberUids()
		}
		alliChannelPb := alliChannel.ToPb()
		room.PutNotifyAllPlayersQueue("game/OnAllianceCreateChatChannel", pb.Bytes(&pb.GAME_ONALLIANCECREATECHATCHANNEL_NOTIFY{Info: alliChannelPb}), notifyUids)
		return pb.ProtoMarshal(&pb.GAME_HD_CREATEALLICHATCHANNEL_S2C{Info: alliChannelPb})
	} else {
		return nil, e
	}
}

// 删除联盟聊天副频道
func (this *Game) delAlliChatChannel(session gate.Session, msg *pb.GAME_HD_DELALLICHATCHANNEL_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil || !alli.IsCreaterOrMilitary(plr.Uid) {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	uid := msg.GetUid()
	name := alli.DelChatChannel(uid)
	channel := wld.GetAlliChatChannel(alli.Uid, uid)
	wld.DelChatChannel(channel)
	// 通知删除联盟聊天副频道
	room.PutNotifyAllPlayersQueue("game/OnAllianceDelChatChannel", pb.Bytes(&pb.GAME_ONALLIANCEDELCHATCHANNEL_NOTIFY{Uid: uid, Name: name}), alli.GetMemberUids())
	return
}

// 更新联盟聊天副频道
func (this *Game) updateAlliChatChannel(session gate.Session, msg *pb.GAME_HD_UPDATEALLICHATCHANNEL_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.AllianceUid == "" {
		return nil, ecode.ALLIANCE_NOT_EXIST.String()
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil || !alli.IsCreaterOrMilitary(plr.Uid) {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	uid, memberUids, memberFilter := msg.GetUid(), msg.GetMemberUids(), msg.GetMemberFilter()
	if alliChannel, e := alli.UpdateChatChannel(uid, memberUids, memberFilter); e == "" {
		channel := wld.GetAlliChatChannel(alli.Uid, uid)
		wld.AddChatChannel(channel)
		// 通知更新联盟聊天副频道
		room.PutNotifyAllPlayersQueue("game/OnAllianceCreateChatChannel", pb.Bytes(&pb.GAME_ONALLIANCECREATECHATCHANNEL_NOTIFY{Info: alliChannel.ToPb()}), alli.GetMemberUids())
		return pb.ProtoMarshal(&pb.GAME_HD_UPDATEALLICHATCHANNEL_S2C{})
	} else {
		return nil, e
	}
}

// 盟主投票
func (this *Game) voteAlliLeader(session gate.Session, msg *pb.GAME_HD_VOTEALLILEADER_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	if alli.Creater != "" {
		return nil, ecode.ALLI_CREATOR_EXIST.String() // 盟主已存在
	}
	me := alli.GetMember(plr.Uid)
	if me == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	leaderUid := msg.GetUid()
	candidate := alli.GetMember(leaderUid)
	if candidate == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 候选人不存在
	}
	if !alli.VoteLeader(leaderUid, plr.Uid) {
		return nil, ecode.ALLI_VOTE_LIMIT.String() // 投票次数上限
	}
	// 通知
	room.PutNotifyAllPlayersQueue("game/OnAllianceVoteLeader", pb.Bytes(&pb.GAME_ONALLIANCEVOTELEADER_NOTIFY{
		Info:               alli.LeaderVotes.ToPb(),
		Creater:            alli.Creater,
		ConfirmSurplusTime: int32(ut.MaxInt64(0, alli.LeaderConfirmEndTime-ut.Now())),
	}), alli.GetMemberUids())
	return pb.ProtoMarshal(&pb.GAME_HD_VOTEALLILEADER_S2C{VoteTimes: alli.GetVoteTimes(plr.Uid)})
}

// 盟主确认
func (this *Game) alliLeaderConfirm(session gate.Session, msg *pb.GAME_HD_ALLILEADERCONFIRM_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	alli := wld.GetAlliance(plr.AllianceUid)
	if alli == nil {
		return nil, ecode.ALLIANCE_NOT_EXIST.String() // 联盟不存在
	}
	if alli.Creater != plr.Uid {
		return nil, ecode.NOT_ALLI_CREATER.String() // 不是盟主
	}
	if alli.LeaderConfirmEndTime == 0 {
		return nil, ecode.ALLI_CONFIRM_REPEAT.String() // 已经确认过
	}
	name := msg.GetName()
	if ut.GetStringLen(name) > 12 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if wld.CheckHasAllianceName(name) {
		return nil, ecode.NICKNAME_EXIST.String() // 名字已存在
	} else if sta := sensitive.CheckName(name); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	}
	alli.LeaderConfirm(name, msg.GetIcon())
	// 通知
	room.PutNotifyAllPlayersQueue("game/OnAllianceConfirm", pb.Bytes(&pb.GAME_ONALLIANCECONFIRM_NOTIFY{
		Name: alli.Name,
		Icon: alli.Icon,
	}), alli.GetMemberUids())
	return
}
