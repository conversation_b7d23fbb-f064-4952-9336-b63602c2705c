package sdk

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"math/big"
	"net/http"
	ut "slgsrv/utils"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
)

const (
	FACEBOOK_APPID            = "1341203366719764"
	FACEBOOK_SECRET           = "********************************"
	FACEBOOK_GET_TOKEN_URL    = "https://graph.facebook.com/v16.0/oauth/access_token?"
	FACEBOOK_VERIFY_TOKEN_URL = "https://graph.facebook.com/debug_token?"
	FACEBOOK_REDIRECT_URI     = ""
	FACEBOOK_BASE_URL         = "https://graph.facebook.com/"
	FACEBOOK_PUBLICK_KEY_URL  = "https://limited.facebook.com/.well-known/oauth/openid/jwks/"
)

type FaceBookGetUserInfoRet struct {
	Id      string           `json:"id"`
	Name    string           `json:"name"`
	Picture *FaceBookPicData `json:"picture"`
}
type FaceBookPicData struct {
	Data *FaceBookPic `json:"data"`
}
type FaceBookPic struct {
	Url string `json:"url"`
}

type FacebookPublickKeyRet struct {
	Keys []*FacebookPublickKey `json:"keys"`
}
type FacebookPublickKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

type FacebookTokenHeader struct {
	Kid string   `json:"kid"`
	Alg string   `json:"alg"`
	X5c []string `json:"x5c"`
}

func FacebookLoginVerify(token, jwtToken, fbId string) (ret *FaceBookGetUserInfoRet, err error) {
	log.Info("FacebookLoginVerify token: %v, jwtToken: %v, fbId: %v", token, jwtToken, fbId)
	if token != "" {
		// 先验证第一个token 不受限登陆
		ret, err = FaceBookLoginCheck(token, fbId)
	}
	if jwtToken != "" && (token == "" || err != nil) {
		// 验证第二个受限登陆
		ret, err = FaceBookLimitLoginCheck(jwtToken, fbId)
	}
	return
}

// facebook登录验证
func FaceBookLoginCheck(token, fbId string) (ret *FaceBookGetUserInfoRet, err error) {
	//根据token获取用户信息
	url := FACEBOOK_BASE_URL + fbId + "?fields=id,name,picture&access_token=" + token
	respGetUserInfo, err := http.Get(url)
	defer func() {
		if respGetUserInfo != nil && respGetUserInfo.Body != nil {
			_ = respGetUserInfo.Body.Close()
		}
	}()
	if err != nil {
		log.Error("FaceBookGetUserInfo http err: %v", err)
		return
	}
	if respGetUserInfo.StatusCode != http.StatusOK {
		err = errors.New("FaceBookGetUserInfo http resp status err")
		log.Error("FaceBookGetUserInfo http resp status err, status: %v", respGetUserInfo.Status)
		return
	}
	body, _ := io.ReadAll(respGetUserInfo.Body)
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("FaceBookGetUserInfo ret err: %v", err)
		return
	}
	log.Info("FaceBookLoginCheck done. id: %v, name: %v", ret.Id, ret.Name)
	return
}

// facebook受限登录验证
func FaceBookLimitLoginCheck(jwtToken, fbId string) (ret *FaceBookGetUserInfoRet, err error) {
	tokenArr := strings.Split(jwtToken, ".")
	if len(tokenArr) < 2 {
		err = errors.New("FaceBookLimitLoginCheck token err")
		log.Error(err.Error())
		return
	}
	headerBytes, err := base64.RawStdEncoding.DecodeString(tokenArr[0])
	if err != nil {
		log.Error("FaceBookLimitLoginCheck resp headerStr decode err: %v", err)
		return
	}
	headerData := &FacebookTokenHeader{}
	err = json.Unmarshal(headerBytes, headerData)
	if err != nil {
		log.Error("FaceBookLimitLoginCheck resp headerStr Unmarshal err: %v", err)
		return
	}
	kid := ut.String(headerData.Kid)
	// 获取publickKey
	publicKey, err := getFacebookPublicKey(kid)
	if publicKey == nil {
		log.Error("FaceBookLimitLoginCheck get publicKey nil kid: %v", kid)
		return
	}
	// 解析并验签idToken
	retToken, err := jwt.Parse(jwtToken, func(token *jwt.Token) (interface{}, error) {
		return publicKey, nil
	})
	if err != nil {
		log.Error("FaceBookLimitLoginCheck Parse reponse jwt err: %v", err)
		return
	}
	mapClaims := retToken.Claims.(jwt.MapClaims)
	if mapClaims == nil {
		errStr := "FaceBookLimitLoginCheck response mapClaims nil"
		log.Error(errStr)
		err = errors.New(errStr)
		return
	}
	// APPid是否一致
	if aud := ut.String(mapClaims["aud"]); aud != FACEBOOK_APPID {
		log.Error("FaceBookLimitLoginCheck response appid err: %v", aud)
		err = errors.New("FaceBookLimitLoginCheck appid err")
		return
	}
	// openid是否一致
	if sub := mapClaims["sub"]; sub != fbId {
		log.Error("FaceBookLimitLoginCheck response openid err: %v", sub)
		err = errors.New("FaceBookLimitLoginCheck openid err")
		return
	}
	ret = &FaceBookGetUserInfoRet{
		Id:   ut.String(mapClaims["sub"]),
		Name: ut.String(mapClaims["name"]),
	}
	log.Info("FaceBookLimitLoginCheck done. id: %v, name: %v", ret.Id, ret.Name)
	return
}

// 获取公钥
func getFacebookPublicKey(kid string) (pubKey *rsa.PublicKey, err error) {
	log.Info("getFacebookPublicKey kid: %v", kid)
	publicKeyResp, err := http.Get(FACEBOOK_PUBLICK_KEY_URL)
	defer func() {
		if publicKeyResp != nil && publicKeyResp.Body != nil {
			publicKeyResp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("getFacebookPublicKey publicKeyResp http err: %v", err)
		return
	}
	if publicKeyResp.StatusCode != http.StatusOK {
		err = errors.New("getFacebookPublicKey publicKeyResp resp status err")
		log.Error("getFacebookPublicKey publicKeyResp http resp status err, status: %v", publicKeyResp.Status)
		return
	}
	publicKeyRespBody, _ := io.ReadAll(publicKeyResp.Body)
	publickKeyRet := &FacebookPublickKeyRet{}
	err = json.Unmarshal(publicKeyRespBody, &publickKeyRet)
	if err != nil {
		log.Error("getFacebookPublicKey publicKeyResp ret err: %v", err)
		return
	}
	// 根据配置的kid返回对应的公钥
	for _, v := range publickKeyRet.Keys {
		if v.Kid == kid {
			n, err := base64.RawURLEncoding.DecodeString(v.N)
			if err != nil {
				return nil, err
			}
			e, err := base64.RawURLEncoding.DecodeString(v.E)
			if err != nil {
				return nil, err
			}
			pubKey = &rsa.PublicKey{N: new(big.Int).SetBytes(n), E: int(new(big.Int).SetBytes(e).Int64())}
		}
	}
	return
}
