package match

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/http/db"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"strconv"
	"strings"

	"github.com/huyangv/vmqant/log"
	"github.com/traefik/yaegi/interp"
	"github.com/traefik/yaegi/stdlib"
	"go.mongodb.org/mongo-driver/bson"
)

func (this *Match) InitHttp() {
	// 创建房间相关
	this.GetServer().RegisterGO("/http/getMapList", this.httpGetMapList)
	this.GetServer().RegisterGO("/http/uploadMapData", this.httpUploadMapData)
	this.GetServer().RegisterGO("/http/createRoom", this.httpCreateRoom)
	this.GetServer().RegisterGO("/http/openRoom", this.httpOpenRoom)
	this.GetServer().RegisterGO("/http/getRoomGenSid", this.httpGetRoomGenSid)
	this.GetServer().RegisterGO("/http/resetMapUse", this.httpResetMapUse)

	// 物理机相关
	this.GetServer().RegisterGO("/http/getGameMachs", this.httpGetGameMachs)
	this.GetServer().RegisterGO("/http/addGameMach", this.httpAddGameMach)
	this.GetServer().RegisterGO("/http/updateGameMachMaxCap", this.httpUpdateGameMachMaxCap)
	this.GetServer().RegisterGO("/http/updateGameMachServerTypes", this.httpUpdateGameMachServerTypes)
	this.GetServer().RegisterGO("/http/updateGameMachServerName", this.httpUpdateGameMachServerName)
	this.GetServer().RegisterGO("/http/delMachServer", this.httpDelMachServer)
	this.GetServer().RegisterGO("/http/getMachTime", this.httpGetMachTime)
	this.GetServer().RegisterGO("/http/setMachTime", this.httpSetMachTime)
	this.GetServer().RegisterGO("/http/resetMachTime", this.httpResetMachTime)
	this.GetServer().RegisterGO("/http/getCleanLogPrams", this.httpGetCleanLogPrams)
	this.GetServer().RegisterGO("/http/setCleanLogPrams", this.httpSetCleanLogPrams)
	this.GetServer().RegisterGO("/http/addMach", this.httpAddMach)

	// 游戏服相关
	this.GetServer().RegisterGO("/http/getRooms", this.httpGetRooms)
	this.GetServer().RegisterGO("/http/closeGameServer", this.httpCloseGameServer)
	this.GetServer().RegisterGO("/http/delRoom", this.httpDelRoom)
	this.GetServer().RegisterGO("/http/gameMachGitUpdate", this.httpGameMachGitUpdate)
	this.GetServer().RegisterGO("/http/closeGameMach", this.httpCloseGameMach)
	this.GetServer().RegisterGO("/http/getGameMachStatus", this.httpGetGameMachStatus)
	this.GetServer().RegisterGO("/http/startGamePids", this.httpStartGamePids)
	this.GetServer().RegisterGO("/http/stopGameServer", this.httpStopGameServer)
	this.GetServer().RegisterGO("/http/resetGameMachStatus", this.httpResetGameMachStatus)
	this.GetServer().RegisterGO("/http/setSaveRoom", this.httpSetSaveRoom)

	// 大厅服相关
	this.GetServer().RegisterGO("/http/getLobbyMachInfo", this.httpGetLobbyMachInfo)
	this.GetServer().RegisterGO("/http/lobbyMachGitUpdate", this.httpLobbyMachGitUpdate)
	this.GetServer().RegisterGO("/http/closeLobbyMach", this.httpCloseLobbyMach)
	this.GetServer().RegisterGO("/http/startLobbyPids", this.httpStartLobbyPids)
	this.GetServer().RegisterGO("/http/stopLobbyServer", this.httpStopLobbyServer)
	this.GetServer().RegisterGO("/http/resetLobbyMachStatus", this.httpResetLobbyMachStatus)
	this.GetServer().RegisterGO("/http/setLobbyMachAbandon", this.httpSetLobbyMachAbandon)

	// 辅助服相关
	this.GetServer().RegisterGO("/http/getSupMachInfo", this.httpGetSupMachInfo)
	this.GetServer().RegisterGO("/http/closeSupMach", this.httpCloseSupMach)
	this.GetServer().RegisterGO("/http/startSupPids", this.httpStartSupPids)
	this.GetServer().RegisterGO("/http/resetSupMachStatus", this.httpResetSupMachStatus)
	this.GetServer().RegisterGO("/http/supMachGitUpdate", this.httpSupMachGitUpdate)

	// TA相关
	this.GetServer().RegisterGO("/http/getTaParams", this.httpGetTaParams)
	this.GetServer().RegisterGO("/http/setTaParams", this.httpSetTaParams)

	// 报名相关
	this.GetServer().RegisterGO("/http/getApplyParams", this.getApplyParams)
	this.GetServer().RegisterGO("/http/setApplyParams", this.setApplyParams)
	this.GetServer().RegisterGO("/http/getApplyInfo", this.httpGetApplyInfo)
	this.GetServer().RegisterGO("/http/setApplyMatchTime", this.httpSetApplyMatchTime)
	this.GetServer().RegisterGO("/http/setApplyClose", this.httpSetApplyClose)
	this.GetServer().RegisterGO("/http/checkApplyTeam", this.httpCheckApplyTeam)
	this.GetServer().RegisterGO("/http/getApplyTeamInfo", this.httpGetApplyTeamInfo)
	this.GetServer().RegisterGO("/http/delApplyTeam", this.httpDelApplyTeam)

	// 组队相关
	this.GetServer().RegisterGO("/http/getTeamParams", this.getTeamParams)
	this.GetServer().RegisterGO("/http/setTeamParams", this.setTeamParams)

	// 好友相关
	this.GetServer().RegisterGO("/http/getFriendParams", this.getFriendParams)
	this.GetServer().RegisterGO("/http/setFriendParams", this.setFriendParams)

	// 自动更新相关
	this.GetServer().RegisterGO("/http/getStopUpdateTaskList", this.httpGetStopUpdateTaskList)
	this.GetServer().RegisterGO("/http/addStopUpdateTask", this.httpAddStopUpdateTask)
	this.GetServer().RegisterGO("/http/delStopUpdateTask", this.httpDelStopUpdateTask)

	// 后台相关
	this.GetServer().RegisterGO("/http/matchExecute", this.httpMatchExecute)
	this.GetServer().RegisterGO("/http/getLoginLimitInfo", this.httpGetLoginLimitInfo)
	this.GetServer().RegisterGO("/http/setLoginLimitType", this.httpSetLoginLimitType)
	this.GetServer().RegisterGO("/http/setLoginLimitIp", this.httpSetLoginLimitIp)
	this.GetServer().RegisterGO("/http/getCheckCheat", this.httpGetCheckCheat)
	this.GetServer().RegisterGO("/http/setCheckCheat", this.httpSetCheckCheat)
	this.GetServer().RegisterGO("/http/getPortrayalOdds", this.httpGetPortrayalOdds)
	this.GetServer().RegisterGO("/http/setPortrayalOdds", this.httpSetPortrayalOdds)

}

// 获取地图文件列表
func (this *Match) httpGetMapList() (response map[string]interface{}, err error) {
	fileMap := map[int32]map[string]interface{}{}
	mapUseInfo := getMapUseInfo()
	files, err := os.ReadDir(fmt.Sprintf("%v/bin/maps/", ut.WorkDir()))
	if err != nil {
		log.Error("httpGetMapList err: %v", err)
		return slg.HttpResponseErrorNoDataWithDesc("地图文件读取失败"), err
	}
	for _, f := range files {
		fullName := f.Name()
		fullNameArr := strings.Split(fullName, ".")
		if len(fullNameArr) < 2 {
			continue
		}
		if fullNameArr[1] != "json" {
			continue
		}
		fname := fullNameArr[0]
		fnameArr := strings.Split(fname, "_")
		if len(fnameArr) < 2 {
			continue
		}
		mapId := ut.Int32(fnameArr[1])

		info, ok := fileMap[mapId]
		if !ok {
			info = map[string]interface{}{}
			info["mapId"] = mapId
			useInfo, ok := mapUseInfo[mapId]
			if ok {
				info["sidList"] = useInfo.SidList
			}
			fileMap[mapId] = info
		}
		if fnameArr[0] == "maps" {
			info["maps"] = fullName
		} else if fnameArr[0] == "mainCitys" {
			info["mainCitys"] = fullName
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"fileMap": fileMap,
	}), nil
}

var (
	uploadMapIndex = 0
	uploadMapId    int32
	uploadMapLen          = 0
	curMapData     string = ""
	curcityData    string = ""
)

// 上传地图
func (this *Match) httpUploadMapData(mapData, cityData, index, len, mapId string) (response map[string]interface{}, err error) {
	dataIndex := ut.Int(index)
	if dataIndex == 0 {
		// 从头开始上传
		uploadMapIndex = 1
		uploadMapId = getMapGenId()
		uploadMapLen = ut.Int(len)
		curMapData = mapData
		curcityData = cityData
	} else {
		// 续传
		if ut.Int32(mapId) != uploadMapId || uploadMapIndex != dataIndex {
			// 续传的id或序号不对
			return slg.HttpResponseErrorNoDataWithDesc("地图文件创建失败"), err
		}
		if mapData != "" {
			curMapData += mapData
		} else if cityData != "" {
			curcityData += cityData
		}
		if dataIndex == uploadMapLen-1 {
			// 当前数据为分片的末尾 上传结束
			mapFile, err := os.Create(fmt.Sprintf("%v/bin/maps/maps_%v.json", ut.WorkDir(), mapId))
			if err != nil {
				log.Error("httpUploadMapData mapFile err: %v", err)
				return slg.HttpResponseErrorNoDataWithDesc("地图文件创建失败"), err
			}
			cityFile, err := os.Create(fmt.Sprintf("%v/bin/maps/mainCitys_%v.json", ut.WorkDir(), mapId))
			if err != nil {
				log.Error("httpUploadMapData cityFile err: %v", err)
				return slg.HttpResponseErrorNoDataWithDesc("地图文件创建失败"), err
			}
			mapStrData := []map[string]interface{}{}
			json.Unmarshal([]byte(curMapData), &mapStrData)
			mapEncoder := json.NewEncoder(mapFile)
			err = mapEncoder.Encode(mapStrData)
			if err != nil {
				log.Error("httpUploadMapData mapFile encode err: %v", err)
				return slg.HttpResponseErrorNoDataWithDesc("地图文件创建失败"), err
			}
			cityStrData := [][]int{}
			json.Unmarshal([]byte(curcityData), &cityStrData)
			cityEncoder := json.NewEncoder(cityFile)
			err = cityEncoder.Encode(cityStrData)
			if err != nil {
				log.Error("httpUploadMapData cityFile encode err: %v", err)
				return slg.HttpResponseErrorNoDataWithDesc("地图文件创建失败"), err
			}
			// 上传成功 设置地图id
			setMapGenId()
			mapUseInfoMap[uploadMapId] = &MapUseInfo{
				Id:      uploadMapId,
				SidList: []int32{},
			}
			uploadMapIndex = 0
		} else {
			uploadMapIndex++
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"mapId": uploadMapId,
	}), nil
}

// 创建区服
func (this *Match) httpCreateRoom(sid, roomType, addr string) (response map[string]interface{}, err error) {
	_roomType, _sid := uint8(uint8(ut.Int(roomType))), ut.Int32(sid)
	if _roomType < 0 {
		return slg.HttpResponseErrorNoDataWithDesc("区服类型错误"), err
	}
	subType := uint8(_sid / slg.ROOM_SUB_TYPE_FLAT % (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT))
	// 设置区服自增sid
	typeKey := GetGenIdMapKey(_roomType, subType)
	setGenSid(typeKey, _sid)
	var state int8
	if _roomType == slg.NORMAL_SERVER_TYPE || _roomType == slg.RANK_SERVER_TYPE {
		// 普通服和排位服创建为报名中状态
		state = slg.SERVER_STATUS_APPLYING
	} else if _roomType == slg.ROOKIE_SERVER_TYPE {
		// 新手服创建到开启之前为创建中状态
		state = slg.SERVER_STATUS_CREATING
	}
	CreateRoom(_sid, _roomType, subType, state, addr, false)
	return slg.HttpResponseSuccessNoDataNoDesc(), err
}

// 开启区服
func (this *Match) httpOpenRoom(sid, addr string) (response map[string]interface{}, err error) {
	_sid := ut.Int(sid)
	if _sid <= 0 {
		return slg.HttpResponseErrorNoDataWithDesc("sid错误"), err
	}
	// 远程执行脚本修改配置并启动服务
	_, err = ut.SshExcuteShell(addr, slg.OPEN_GAME_SERVER_BASH, sid)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("执行脚本失败"), err
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), err
}

// 获取区服类型自增id
func (this *Match) httpGetRoomGenSid(roomType, subType string) (response map[string]interface{}, err error) {
	_roomType, _subType := uint8(ut.Int(roomType)), uint8(ut.Int(subType))
	if _roomType < 0 {
		return slg.HttpResponseErrorNoDataWithDesc("区服类型错误"), err
	}
	sid := getGenSid(_roomType, _subType)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"sid": sid,
	}), nil
}

// 重置地图使用情况
func (this *Match) httpResetMapUse(mapId string) (response map[string]interface{}, err error) {
	_mapId := ut.Int32(mapId)
	useInfo, ok := mapUseInfoMap[_mapId]
	if !ok {
		return slg.HttpResponseErrorNoDataWithDesc("地图id错误"), err
	}
	useInfo.SidList = []int32{}
	db_map_use.UpdateMapUse(useInfo)
	return slg.HttpResponseSuccessNoDataNoDesc(), err
}

// 获取大厅服物理机信息
func (this *Match) httpGetLobbyMachInfo() (response map[string]interface{}, err error) {
	arr := []*LobbyMach{}
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		// 已关闭则直接返回
		if v != nil && v.Status == slg.MACH_STATUS_CLOSE {
			arr = append(arr, v)
			return true
		}
		if v.Status == slg.MACH_STATUS_CLOSING || v.Status == slg.MACH_STATUS_OPENNING {
			// 物理机在关闭中或开启中则查询进程
			e := this.GetLobbyProcessInfo(v)
			if e == "" {
				arr = append(arr, v)
			}
		} else if v.Status == slg.MACH_STATUS_OPEN || v.Status == slg.MACH_STATUS_STOPPING {
			// Rpc获取大厅服信息
			e := this.GetLobbyStopInfo(v)
			if e == "" {
				arr = append(arr, v)
			}
		} else {
			arr = append(arr, v)
		}
		return true
	})

	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": arr,
	}), nil
}

// 更新大厅服代码
func (this *Match) httpLobbyMachGitUpdate(ip string) (response map[string]interface{}, err error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("大厅服数据错误"), nil
	}
	if lobbyMach.UpdateStatus == slg.MACH_UPDATE_STATUS_UPDATING {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 代码正在更新中"), nil
	}
	lobbyMach.HandleUpdateCode()
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": lobbyMach.UpdateStatus,
	}, "开始更新代码!"), nil
}

// 关闭物理机大厅服进程
func (this *Match) httpCloseLobbyMach(ip string) (map[string]interface{}, error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("大厅服数据错误"), nil
	}
	if lobbyMach.Status != slg.MACH_STATUS_STOPPED {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(lobbyMach.Status)), nil
	}
	_, err := ut.SshExcuteShell(lobbyMach.Addr, slg.STOP_SERVER_BASH)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("关闭大厅服进程失败!"), nil
	}
	lobbyMach.UpdateMachStatus(slg.MACH_STATUS_CLOSING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": lobbyMach.Status,
	}, "开始关闭大厅服进程!"), nil
}

// 开启物理机大厅服进程
func (this *Match) httpStartLobbyPids(ip string) (map[string]interface{}, error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("大厅服数据错误"), nil
	}
	if lobbyMach.Status != slg.MACH_STATUS_CLOSE {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(lobbyMach.Status)), nil
	}
	_, err := ut.SshExcuteShell(lobbyMach.Addr, slg.START_SERVER_BASH)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("开启大厅服进程失败!"), nil
	}
	lobbyMach.UpdateMachStatus(slg.MACH_STATUS_OPENNING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": lobbyMach.Status,
	}, "开始开启大厅服进程!"), nil
}

// 大厅服停机保存
func (this *Match) httpStopLobbyServer(ip string) (map[string]interface{}, error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach.Status != slg.MACH_STATUS_OPEN {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(lobbyMach.Status)), nil
	}
	this.InvokeNR("lobby", slg.RPC_STOP_SERVER)
	lobbyMach.UpdateMachStatus(slg.MACH_STATUS_STOPPING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": lobbyMach.Status,
	}, "大厅服开始停机!"), nil
}

// 大厅服物理机重置状态
func (this *Match) httpResetLobbyMachStatus(ip string) (map[string]interface{}, error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("大厅服数据错误"), nil
	}
	lobbyMach.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
	lobbyMach.UpdateMachStatus(slg.MACH_STATUS_OPEN)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status":       lobbyMach.Status,
		"updateStatus": lobbyMach.UpdateStatus,
	}, "状态重置成功!"), nil
}

// 设置大厅服停用状态
func (this *Match) httpSetLobbyMachAbandon(ip, abandon string) (map[string]interface{}, error) {
	lobbyMach := lobbyMachs.MachsMap.Get(ip)
	if lobbyMach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("大厅服数据错误"), nil
	}
	abandonStatus := false
	if abandon == "true" {
		abandonStatus = true
	}
	if lobbyMach.Abandon != abandonStatus {
		if abandonStatus {
			// 停用
			rds.Client.HDel(context.TODO(), rds.RDS_LOBBY_LOAD_MAP_KEY, ut.String(lobbyMach.Id))
		} else {
			// 启用
			rst, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(ut.String(lobbyMach.Id), slg.RPC_GET_LOBBY_INFO))
			if err != "" {
				return slg.HttpResponseErrorNoDataWithDesc("该大厅服异常 暂时无法启用"), nil
			}
			userNum := ut.Int(rst["userNum"])
			rds.Client.HSet(context.TODO(), rds.RDS_LOBBY_LOAD_MAP_KEY, ut.String(lobbyMach.Id), userNum)
		}
		lobbyMach.Abandon = abandonStatus
		db_lobby_machs.UpdateLobbyMachDb(lobbyMach)
	}
	return slg.HttpResponseSuccessNoDataWithDesc("大厅服停用状态设置成功!"), nil
}

// 获取辅助服物理机信息
func (this *Match) httpGetSupMachInfo() (response map[string]interface{}, err error) {
	arr := []*SupMach{}
	supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
		// 已关闭则直接返回
		if v != nil && v.Status == slg.MACH_STATUS_CLOSE {
			arr = append(arr, v)
			return true
		}
		if v.Status == slg.MACH_STATUS_CLOSING || v.Status == slg.MACH_STATUS_OPENNING {
			// 物理机在关闭中或开启中则查询进程
			e := this.GetSupProcessInfo(v)
			if e == "" {
				arr = append(arr, v)
			}
		} else {
			arr = append(arr, v)
		}
		return true
	})

	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": arr,
	}), nil
}

// 关闭物理机辅助服进程
func (this *Match) httpCloseSupMach(ip string) (map[string]interface{}, error) {
	mach := supMachs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在"), nil
	}
	if mach.Status != slg.MACH_STATUS_OPEN {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(mach.Status)), nil
	}
	_, err := ut.SshExcuteShell(mach.Addr, slg.STOP_SERVER_BASH)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("关闭辅助服进程失败!"), nil
	}
	mach.UpdateMachStatus(slg.MACH_STATUS_CLOSING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.Status,
	}, "开始关闭辅助服进程!"), nil
}

// 开启物理机辅助服进程
func (this *Match) httpStartSupPids(ip string) (map[string]interface{}, error) {
	mach := supMachs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在"), nil
	}
	if mach.Status != slg.MACH_STATUS_CLOSE {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(mach.Status)), nil
	}
	_, err := ut.SshExcuteShell(mach.Addr, slg.START_SERVER_BASH)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("开启辅助服进程失败!"), nil
	}
	mach.UpdateMachStatus(slg.MACH_STATUS_OPENNING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.Status,
	}, "开始开启辅助服进程!"), nil
}

// 辅助服物理机重置状态
func (this *Match) httpResetSupMachStatus(ip string) (map[string]interface{}, error) {
	mach := supMachs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在"), nil
	}
	mach.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
	mach.UpdateMachStatus(slg.MACH_STATUS_OPEN)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status":       mach.Status,
		"updateStatus": mach.UpdateStatus,
	}, "状态重置成功!"), nil
}

// 更新辅助服物理机代码
func (this *Match) httpSupMachGitUpdate(ip string) (map[string]interface{}, error) {
	mach := supMachs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	if mach.UpdateStatus == slg.MACH_UPDATE_STATUS_UPDATING {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 代码正在更新中"), nil
	}
	mach.HandleUpdateCode()
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.UpdateStatus,
	}, "开始更新代码!"), nil
}

// 获取胜利条件参数
func (this *Match) httpGetWinCond() (response map[string]interface{}, err error) {
	winCond := GetWinCond()
	var rstStr string
	for i, v := range winCond {
		rstStr += ut.String(v)
		if i < len(winCond)-1 {
			rstStr += ","
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"winCond": rstStr,
	}), err
}

// 设置胜利条件参数
func (this *Match) httpSetWinCond(winCond string) (response map[string]interface{}, err error) {
	strArr := strings.Split(winCond, ",")
	if len(strArr) == 0 {
		return slg.HttpResponseErrorNoDataWithDesc("参数格式错误!"), nil
	}
	arr := []int32{}
	for _, v := range strArr {
		arr = append(arr, ut.Int32(v))
	}
	winCondParams = arr
	mapCol := &db.MapCol[string]{
		Key: "win_cond",
		Val: winCond,
	}
	filter := db.FieldToBson(mapCol, "Key")
	update := &bson.M{
		"$set": db.FieldToBson(mapCol, "Val"),
	}
	_, err = db.GlobalGetTableManager().GetCollection(mapCol).UpdateOne(context.TODO(), &filter, update)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("数据库错误!"), nil
	}
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取游戏服物理机
func (this *Match) httpGetGameMachs() (map[string]interface{}, error) {
	machList := []*Mach{}
	machs.MachsMap.ForEach(func(m *Mach, k string) bool {
		machList = append(machList, m)
		return true
	})
	for _, m := range machList {
		m.StoppedList = []int32{}
		m.lock.RLock()
		sidList := array.Clone(m.SidList)
		m.lock.RUnlock()
		for _, v := range sidList {
			room := GetRoomById(v)
			if room == nil || room.state == slg.SERVER_STATUS_STOP || room.IsClose() {
				m.StoppedList = append(m.StoppedList, v)
			}
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": machList,
	}), nil
}

// 添加游戏服物理机
func (this *Match) httpAddGameMach(ip, serverTypes, maxCap, name string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach != nil {
		return slg.HttpResponseErrorNoDataWithDesc("重复添加!"), nil
	}
	mach, err := this.AddGameMach(ip, serverTypes, maxCap, name)
	if err == nil {
		return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
			"mach": mach,
		}, "添加成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("添加失败!"), err
	}
}

// 设置物理机容量
func (this *Match) httpUpdateGameMachMaxCap(ip, maxCap string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	err := mach.UpdateGameMachMaxCap(maxCap)
	if err == nil {
		return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
			"mach": mach,
		}, "设置成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("设置失败!"), err
	}
}

// 设置物理机可开启游戏服类型
func (this *Match) httpUpdateGameMachServerTypes(ip, serverTypes string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	err := mach.UpdateGameMachMachServerTypes(serverTypes)
	if err == nil {
		return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
			"mach": mach,
		}, "设置成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("设置失败!"), err
	}
}

// 设置物理机名字
func (this *Match) httpUpdateGameMachServerName(ip, serverTypes string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	err := mach.UpdateGameMachName(serverTypes)
	if err == nil {
		return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
			"mach": mach,
		}, "设置成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("设置失败!"), err
	}
}

// 删除物理机
func (this *Match) httpDelMachServer(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	err := mach.delMach()
	if err == nil {
		return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
			"mach": mach,
		}, "删除成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("删除失败!"), err
	}
}

// 获取物理机时间
func (this *Match) httpGetMachTime(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	dateBytes, err := ut.SshExcuteShell(ip, slg.GET_MACH_TIME)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("获取系统时间失败!"), err
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"time": string(dateBytes),
	}), nil
}

// 设置物理机时间
func (this *Match) httpSetMachTime(ip, time string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	_, err := ut.SshExcuteShell(ip, slg.SET_MACH_TIME, time)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("设置系统时间失败!"), err
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置系统时间成功!"), nil
}

// 还原物理机时间
func (this *Match) httpResetMachTime(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	_, err := ut.SshExcuteShell(ip, slg.RESET_MACH_TIME)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("还原系统时间失败!"), err
	}
	dateBytes, err := ut.SshExcuteShell(ip, slg.GET_MACH_TIME)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("获取系统时间失败!"), err
	}
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"time": string(dateBytes),
	}, "还原系统时间成功!"), nil
}

// 获取日志清理设置参数
func (this *Match) httpGetCleanLogPrams() (map[string]interface{}, error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"diskUse": slg.CLEAN_LOG_DISK_USAGE_THRESHOLD,
		"daysOld": slg.CLEAN_LOG_DAYS_OLD,
	}), nil
}

// 设置日志清理参数
func (this *Match) httpSetCleanLogPrams(diskUse, daysOld string) (map[string]interface{}, error) {
	_, e := strconv.Atoi(diskUse)
	if e != nil {
		return slg.HttpResponseErrorNoDataWithDesc("磁盘阈值 参数错误!"), e
	}
	_, e = strconv.Atoi(daysOld)
	if e != nil {
		return slg.HttpResponseErrorNoDataWithDesc("保留天数 参数错误!"), e
	}
	slg.CLEAN_LOG_DISK_USAGE_THRESHOLD = diskUse
	slg.CLEAN_LOG_DAYS_OLD = daysOld
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

// 添加物理机
func (this *Match) httpAddMach(ip, name, machType, id string) (rst map[string]interface{}, err error) {
	rst = map[string]interface{}{}
	switch machType {
	case slg.MACH_SERVER_TYPE_GAME:
		// 游戏服
		mach := machs.MachsMap.Get(ip)
		if mach != nil {
			return slg.HttpResponseErrorNoDataWithDesc("重复添加!"), nil
		}
		mach, err = this.AddGameMach(ip, "0", ut.String(slg.MACH_MAX_SERVER_COUNT), name)
		if err == nil {
			rst["mach"] = mach
		}
	case slg.MACH_SERVER_TYPE_LOBBY:
		// 大厅服
		mach := lobbyMachs.MachsMap.Get(ip)
		if mach != nil {
			return slg.HttpResponseErrorNoDataWithDesc("重复添加!"), nil
		}
		mach = &LobbyMach{
			Addr: ip,
			Name: name,
			Id:   ut.Int32(id),
		}
		err = db_lobby_machs.AddLobbyMach(mach)
		if err == nil {
			lobbyMachs.MachsMap.Set(ip, mach)
			rst["mach"] = mach
		}
	case slg.MACH_SERVER_TYPE_GATE, slg.MACH_SERVER_TYPE_LOGIN, slg.MACH_SERVER_TYPE_HTTP, slg.MACH_SERVER_TYPE_CHAT, slg.MACH_SERVER_TYPE_MAIL:
		// 连接、登录、工具、聊天等辅助服
		mach := supMachs.MachsMap.Get(ip)
		if mach != nil {
			return slg.HttpResponseErrorNoDataWithDesc("重复添加!"), nil
		}
		mach = &SupMach{
			Addr: ip,
			Name: name,
			Id:   ut.Int32(id),
			Type: machType,
		}
		err = db_sup_machs.AddSupMach(mach)
		if err == nil {
			supMachs.MachsMap.Set(ip, mach)
			rst["mach"] = mach
		}
	}
	return
}

// 获取开服的房间列表 closed: 是否获取已经关闭的房间
func (this *Match) httpGetRooms(closed string) (ret map[string]interface{}, err error) {
	toRooms := ToHttpRooms()
	return slg.HttpResponseSuccessWithDataNoDesc(toRooms), nil
}

// 关闭游戏服
func (this *Match) httpCloseGameServer(sid string) (map[string]interface{}, error) {
	room := GetRoomById(ut.Int32(sid))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("服务器不存在!"), nil
	}
	if !room.IsGameOver() {
		return slg.HttpResponseErrorNoDataWithDesc("服务器未结束!"), nil
	}
	if room.state != slg.SERVER_STATUS_OPEN {
		return slg.HttpResponseErrorNoDataWithDesc("服务器已关闭!"), nil
	}
	room.state = slg.SERVER_STATUS_STOP
	_, err := ut.SshExcuteShell(room.Addr, slg.CLOSE_GAME_SERVER_BASH, sid)
	if err == nil {
		return slg.HttpResponseSuccessNoDataWithDesc("关闭成功!"), nil
	} else {
		return slg.HttpResponseErrorNoDataWithDesc("关闭失败!"), err
	}
}

// 获取数数设置参数
func (this *Match) httpGetTaParams() (map[string]interface{}, error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"taOpen":  ut.If(slg.TA_OPEN, 1, 0),
		"taDebug": ut.If(slg.TA_DEBUG, 1, 0),
	}), nil
}

// 设置数数参数
func (this *Match) httpSetTaParams(taOpen, taDebug string) (map[string]interface{}, error) {
	slg.TA_OPEN = ut.If(taOpen == "1", true, false)
	slg.TA_DEBUG = ut.If(taDebug == "1", true, false)
	// 设置所有游戏服的参数
	rooms.ForEach(func(r *Room, k int32) bool {
		if r != nil && !r.IsClose() {
			this.Invoke("game@game"+ut.String(r.Id), slg.RPC_SET_TA_PARAMS, ut.Bytes(taOpen), ut.Bytes(taDebug))
		}
		return true
	})
	// 设置所有大厅服的参数
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		if v != nil {
			this.Invoke("lobby@lobby"+ut.String(v.Id), slg.RPC_SET_TA_PARAMS, ut.Bytes(taOpen), ut.Bytes(taDebug))
		}
		return true
	})
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

// 删除游戏服数据
func (this *Match) httpDelRoom(id string) (map[string]interface{}, error) {
	room := GetRoomById(ut.Int32(id))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("游戏服不存在!"), nil
	}
	if !room.IsClose() {
		return slg.HttpResponseErrorNoDataWithDesc("游戏服尚未关闭!"), nil
	}
	if room.state == slg.SERVER_STATUS_DELETE {
		return slg.HttpResponseErrorNoDataWithDesc("游戏服已删除!"), nil
	}
	if room.Save {
		return slg.HttpResponseErrorNoDataWithDesc("已保留的游戏服无法删除 请先取消保留!"), nil
	}
	// 清理room数据
	delFailStr := room.DelDbData()
	return slg.HttpResponseSuccessNoDataWithDesc("删除完成, " + "未删除成功的表有: " + delFailStr), nil
}

// 设置是否保留游戏服数据
func (this *Match) httpSetSaveRoom(id, save string) (map[string]interface{}, error) {
	room := GetRoomById(ut.Int32(id))
	if room == nil {
		return slg.HttpResponseErrorNoDataWithDesc("游戏服不存在!"), nil
	}
	saveRoom := false
	if save == "true" {
		saveRoom = true
	}
	room.Save = saveRoom
	db_room.UpdateRoomSave(room)
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 更新游戏服代码
func (this *Match) httpGameMachGitUpdate(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	if mach.UpdateStatus == slg.MACH_UPDATE_STATUS_UPDATING {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 代码正在更新中"), nil
	}
	mach.HandleCodeUpdate()
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.UpdateStatus,
	}, "开始更新代码!"), nil
}

// 关闭物理机游戏服进程
func (this *Match) httpCloseGameMach(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	if mach.Status != slg.MACH_STATUS_STOPPED {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(mach.Status)), nil
	}
	if len(mach.SidList) > 0 {
		_, err := ut.SshExcuteShell(mach.Addr, slg.STOP_SERVER_BASH)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc("关闭游戏服进程失败!"), nil
		}
	}
	mach.UpdateGameMachStatus(slg.MACH_STATUS_CLOSING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.Status,
	}, "开始关闭游戏服进程!"), nil
}

// 查询物理机游戏服进程
func (this *Match) httpGetGameMachStatus(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	stoppedServerArr := []int32{}
	if mach.Status == slg.MACH_STATUS_CLOSING || mach.Status == slg.MACH_STATUS_OPENNING {
		// 物理机在关闭中或开启中则查询进程
		rst, err := ut.SshExcuteShell(mach.Addr, slg.SHOW_PID_BASH)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc("查询游戏服进程失败!"), nil
		}
		// 根据进程查询输出更新状态
		if pInfos := strings.Split(rst, slg.PROCESS_DIR); len(pInfos) > 1 {
			// 进程已开启
			if mach.Status == slg.MACH_STATUS_OPENNING {
				mach.UpdateGameMachStatus(slg.MACH_STATUS_OPEN)
			}
		} else if mach.Status == slg.MACH_STATUS_CLOSING {
			mach.UpdateGameMachStatus(slg.MACH_STATUS_CLOSE)
		}
	} else if mach.Status == slg.MACH_STATUS_STOPPING {
		// 物理机在停机中
		mach.lock.RLock()
		sidList := array.Clone(mach.SidList)
		mach.lock.RUnlock()
		for _, v := range sidList {
			room := GetRoomById(v)
			if (room != nil && room.state == slg.SERVER_STATUS_STOP) || room.IsClose() {
				stoppedServerArr = append(stoppedServerArr, v)
			}
		}
		if len(sidList) == len(stoppedServerArr) {
			// 该物理机的游戏服已全部停机
			mach.UpdateGameMachStatus(slg.MACH_STATUS_STOPPED)
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"status":       mach.Status,
		"updateStatus": mach.UpdateStatus,
		"stoppedList":  stoppedServerArr,
	}), nil
}

// 开启物理机游戏服进程
func (this *Match) httpStartGamePids(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	if mach.Status != slg.MACH_STATUS_CLOSE {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(mach.Status)), nil
	}
	_, err := ut.SshExcuteShell(mach.Addr, slg.START_SERVER_BASH)
	if err != nil {
		return slg.HttpResponseErrorNoDataWithDesc("开启游戏服进程失败!"), nil
	}
	mach.UpdateGameMachStatus(slg.MACH_STATUS_OPENNING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.Status,
	}, "开始开启游戏服进程!"), nil
}

// 游戏服停机保存
func (this *Match) httpStopGameServer(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	if mach.Status != slg.MACH_STATUS_OPEN {
		return slg.HttpResponseErrorNoDataWithDesc("物理机状态错误! 当前状态: " + ut.String(mach.Status)), nil
	}
	rooms.ForEach(func(v *Room, k int32) bool {
		if v.Addr == ip && v.state == slg.SERVER_STATUS_OPEN {
			this.Invoke("game@game"+ut.Itoa(v.Id), slg.RPC_STOP_SERVER, ut.Bytes(v.Id))
		}
		return true
	})
	mach.UpdateGameMachStatus(slg.MACH_STATUS_STOPPING)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status": mach.Status,
	}, "游戏服开始停机!"), nil
}

// 游戏服物理机重置状态
func (this *Match) httpResetGameMachStatus(ip string) (map[string]interface{}, error) {
	mach := machs.MachsMap.Get(ip)
	if mach == nil {
		return slg.HttpResponseErrorNoDataWithDesc("物理机不存在!"), nil
	}
	mach.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
	mach.UpdateGameMachStatus(slg.MACH_STATUS_OPEN)
	return slg.HttpResponseSuccessWithDataWithDesc(map[string]interface{}{
		"status":       mach.Status,
		"updateStatus": mach.UpdateStatus,
	}, "状态重置成功!"), nil
}

func (this *Match) httpMatchExecute(code, fun string) (map[string]interface{}, error) {
	defer func() {
		err := recover()
		if err != nil {
			log.Error("httpMatchExecute err:%s", err)
		}
	}()
	context := interp.New(interp.Options{})
	context.Use(stdlib.Symbols)
	context.Use(Symbols)
	code, err := url.QueryUnescape(code)
	if err != nil {
		return nil, err
	}
	// 实际测试，部分字符需要转URI编码 ，所以code通过http发上来之前最好通过encodeURIComponent编码一次
	if strings.ReplaceAll(code, " ", "") == "" {
		return nil, errors.New("code is empty")
	}
	if strings.Index(code, "go") > -1 {
		return nil, errors.New("can not use go in customer code")
	}
	_, err = context.Eval(code)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	v, err := context.Eval(fun)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	return slg.HttpResponseSuccessWithDataNoDesc(v.Interface()), nil
}

// 获取服务器报名参数
func (this *Match) getApplyParams() (ret map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"finishNumber":     slg.SERVER_APPLY_FINISH_NUMBER,
		"openTimeStart":    slg.SERVER_APPLY_OPEN_TIME_START,
		"openTimeEnd":      slg.SERVER_APPLY_OPEN_TIME_END,
		"openTime":         slg.SERVER_APPLY_TO_OPEN_TIME / ut.TIME_MINUTE,
		"cancelCd":         slg.SERVER_APPLY_CANCEL_CD,
		"rookieCapMin":     slg.ROOKIE_CAP_MIN,
		"matchIntervalDay": matchIntervalTime / ut.TIME_DAY,
		"rookiePreNum":     slg.ROOKIE_SERVER_PRE_NUM,
		"matchMinNum":      slg.MATCH_NEW_ROOM_MIN_PLAYER_NUM,
		"matchMaxNum":      slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM,

		"matchBallenceNumMin":    slg.MATCH_BALLENCE_PLAYER_NUM_MIN,
		"matchBallenceNumMax":    slg.MATCH_BALLENCE_PLAYER_NUM_MAX,
		"matchBallenceInitParam": slg.MATCH_BALLENCE_PLAYER_NUM_INIT_PARAM,
		"matchBallenceStepParam": slg.MATCH_BALLENCE_PLAYER_NUM_STEP_PARAM,
	}), nil
}

// 设置报名相关参数
func (this *Match) setApplyParams(finishNumber, openTimeStart, openTimeEnd, openTime, cancelCd,
	rookieCapMin, matchIntervalDay, rookiePreNum, matchMinNum, matchMaxNum,
	matchBallenceNumMin, matchBallenceNumMax, matchBallenceInitParam, matchBallenceStepParam string) (ret map[string]interface{}, err error) {
	_finishNumber := ut.Int(finishNumber)
	_openTimeStart := ut.Int(openTimeStart)
	_openTimeEnd := ut.Int(openTimeEnd)
	_openTime := ut.Int64(openTime)
	_cancelCd := ut.Int64(cancelCd)
	_rookieCapMin := ut.Int32(rookieCapMin)
	_matchIntervalDay := ut.Int64(matchIntervalDay)

	if _finishNumber > 0 {
		slg.SERVER_APPLY_FINISH_NUMBER = _finishNumber
	}
	if _openTimeEnd >= _openTimeStart {
		slg.SERVER_APPLY_OPEN_TIME_START = _openTimeStart
		slg.SERVER_APPLY_OPEN_TIME_END = _openTimeEnd
	}
	slg.SERVER_APPLY_TO_OPEN_TIME = _openTime * ut.TIME_MINUTE
	slg.SERVER_APPLY_CANCEL_CD = _cancelCd
	if _rookieCapMin > 0 {
		slg.ROOKIE_CAP_MIN = _rookieCapMin
	}
	_rookiePreNum := ut.Int32(rookiePreNum)
	if _rookiePreNum >= 0 {
		slg.ROOKIE_SERVER_PRE_NUM = _rookiePreNum
	}
	if _matchMinNum := ut.Int(matchMinNum); _matchMinNum > 0 {
		slg.MATCH_NEW_ROOM_MIN_PLAYER_NUM = _matchMinNum
	}
	if _matchMaxNum := ut.Int32(matchMaxNum); _matchMaxNum > 0 {
		slg.MATCH_NEW_ROOM_MAX_PLAYER_NUM = _matchMaxNum
	}
	if _matchIntervalDay > 0 {
		SetMatchIntervalTime(_matchIntervalDay * ut.TIME_DAY)
	}

	_matchBallenceNumMin := ut.Int32(matchBallenceNumMin)
	_matchBallenceNumMax := ut.Int32(matchBallenceNumMax)
	_matchBallenceInitParam := ut.Float64(matchBallenceInitParam)
	_matchBallenceStepParam := ut.Float64(matchBallenceStepParam)
	if _matchBallenceNumMin > 0 {
		slg.MATCH_BALLENCE_PLAYER_NUM_MIN = _matchBallenceNumMin
	}
	if _matchBallenceNumMax > 0 {
		slg.MATCH_BALLENCE_PLAYER_NUM_MAX = _matchBallenceNumMax
	}
	if _matchBallenceInitParam > 0 {
		slg.MATCH_BALLENCE_PLAYER_NUM_INIT_PARAM = _matchBallenceInitParam
	}
	if _matchBallenceStepParam > 0 {
		slg.MATCH_BALLENCE_PLAYER_NUM_STEP_PARAM = _matchBallenceStepParam
	}

	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取报名数据
func (this *Match) httpGetApplyInfo() (ret map[string]interface{}, err error) {
	list := []map[string]interface{}{}
	for roomType, info := range applyInfoMap {
		data := map[string]interface{}{}
		data["roomType"] = roomType
		data["teamNum"] = info.teamMap.Count()
		userNum := 0
		info.teamMap.ForEach(func(v *MatchTeam, k string) bool {
			userNum += len(v.UserList)
			return true
		})
		data["userNum"] = userNum
		data["matchTime"] = info.MatchTime
		data["close"] = info.Close
		list = append(list, data)
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 设置报名匹配时间
func (this *Match) httpSetApplyMatchTime(roomType, time string) (ret map[string]interface{}, err error) {
	_roomType, matchTime := uint8(ut.Int(roomType)), ut.Int64(time)
	for k, info := range applyInfoMap {
		if k == _roomType {
			info.MatchTime = matchTime
			applyDb.UpdateApplyInfo(info)
			break
		}
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 设置是否关闭报名
func (this *Match) httpSetApplyClose(roomType, close string) (ret map[string]interface{}, err error) {
	_roomType := uint8(ut.Int(roomType))
	var isClose bool
	if close == "true" {
		isClose = true
	} else {
		isClose = false
	}
	for k, info := range applyInfoMap {
		if k == _roomType {
			info.Close = isClose
			applyDb.UpdateApplyInfo(info)
			// 通知所有大厅服
			lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
				if v != nil {
					this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_SET_APPLY_CLOSE, _roomType, isClose)
				}
				return true
			})
			break
		}
	}
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功!"), nil
}

// 检测报名队伍
func (this *Match) httpCheckApplyTeam(roomType string) (ret map[string]interface{}, err error) {
	_roomType := uint8(ut.Int(roomType))
	list := []map[string]interface{}{}
	for k, info := range applyInfoMap {
		if k == _roomType {
			teamList := []string{}
			info.teamMap.ForEach(func(v *MatchTeam, k string) bool {
				teamList = append(teamList, k)
				return true
			})
			for _, teamUid := range teamList {
				lid := rds.MallocTeamLid(teamUid)
				_, err := this.InvokeLobbyRpc(lid, slg.RPC_MATCH_CHECK_APPLY_TEAM, teamUid)
				if err != "" {
					data := map[string]interface{}{}
					data["uid"] = teamUid
					data["err"] = err
					list = append(list, data)
				}
			}
			break
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 查看报名中的队伍
func (this *Match) httpGetApplyTeamInfo(roomType string) (ret map[string]interface{}, err error) {
	list := []map[string]interface{}{}
	_roomType := uint8(ut.Int(roomType))
	for k, info := range applyInfoMap {
		if k == _roomType {
			info.teamMap.ForEach(func(v *MatchTeam, k string) bool {
				data := map[string]interface{}{}
				data["uid"] = v.Uid
				data["userNum"] = len(v.UserList)
				list = append(list, data)
				return true
			})
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"list": list,
	}), nil
}

// 删除报名中的队伍
func (this *Match) httpDelApplyTeam(roomType, teamUid string) (ret map[string]interface{}, err error) {
	_roomType := uint8(ut.Int(roomType))
	for k, info := range applyInfoMap {
		if k == _roomType {
			info.teamMap.Del(teamUid)
		}
	}
	return slg.HttpResponseSuccessNoDataWithDesc("删除成功!"), nil
}

// 获取好友相关参数
func (this *Match) getFriendParams() (ret map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"maxNum":       slg.FRIENDS_MAX_NUM,
		"applyNum":     slg.FRIENDS_APPLY_MAX_NUM,
		"landCount":    slg.FRIENDS_MIN_LAND_COUNT,
		"blackListMax": slg.BLACK_LIST_MAX,
	}), nil
}

// 设置好友相关参数
func (this *Match) setFriendParams(maxNum, applyNum, landCount, blackListMax string) (ret map[string]interface{}, err error) {
	_maxNum := ut.Int(maxNum)
	_applyNum := ut.Int(applyNum)
	_landCount := ut.Int(landCount)
	_blackListMax := ut.Int(blackListMax)

	if _maxNum > 0 {
		slg.FRIENDS_MAX_NUM = _maxNum
	}
	if _applyNum > 0 {
		slg.FRIENDS_APPLY_MAX_NUM = _applyNum
	}
	if _landCount > 0 {
		slg.FRIENDS_MIN_LAND_COUNT = _landCount
	}
	if _blackListMax > 0 {
		slg.BLACK_LIST_MAX = _blackListMax
	}
	// 设置所有大厅服的参数
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		if v != nil {
			this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_SET_FRIEND_PARAMS, maxNum, applyNum, landCount, blackListMax)
		}
		return true
	})
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取组队相关参数
func (this *Match) getTeamParams() (ret map[string]interface{}, err error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"userNum": slg.TEAM_USER_NUM_MAX,
		"chatNum": slg.TEAM_CHAT_NUM_MAX,
	}), nil
}

// 设置组队相关参数
func (this *Match) setTeamParams(userNum, chatNum string) (ret map[string]interface{}, err error) {
	userNumMax := ut.Int(userNum)
	chatNumMax := ut.Int(chatNum)

	if userNumMax > 0 {
		slg.TEAM_USER_NUM_MAX = userNumMax
	}
	if chatNumMax > 0 {
		slg.TEAM_CHAT_NUM_MAX = chatNumMax
	}
	// 设置所有大厅服的参数
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		if v != nil {
			this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_SET_TEAM_PARAMS, userNumMax, chatNumMax)
		}
		return true
	})
	return slg.HttpResponseSuccessNoDataNoDesc(), nil
}

// 获取大厅服停机保存信息
func (this *Match) GetLobbyStopInfo(lobbyMach *LobbyMach) string {
	if lobbyMach != nil {
		// Rpc获取大厅服信息
		info, err := ut.RpcInterfaceMap(this.Invoke("lobby@lobby"+ut.String(lobbyMach.Id), slg.RPC_GET_LOBBY_INFO))
		if err != "" {
			log.Error("GetLobbyStopInfo err: %v", err)
			return "Rpc获取大厅服信息失败!"
		}
		lobbyMach.UserNum = ut.Int32(info["userNum"])
		if lobbyMach.Status == slg.MACH_STATUS_STOPPING {
			// 停机中
			lobbyMach.UserCount = ut.Int32(info["userCount"])
			lobbyMach.SaveCount = ut.Int32(info["saveCount"])
			if lobbyMach.UserCount == lobbyMach.SaveCount {
				// 保存完成
				lobbyMach.UpdateMachStatus(slg.MACH_STATUS_STOPPED)
			}
		}
	}
	return ""
}

// 获取大厅服进程信息
func (this *Match) GetLobbyProcessInfo(lobbyMach *LobbyMach) string {
	rst, err := ut.SshExcuteShell(lobbyMach.Addr, slg.SHOW_PID_BASH)
	if err != nil {
		log.Error("GetLobbyProcessInfo err: %v", err)
		return "查询大厅服进程失败!"
	}
	// 根据进程查询输出更新状态
	if pInfos := strings.Split(rst, slg.PROCESS_DIR); len(pInfos) > 1 {
		// 进程已开启
		if lobbyMach.Status == slg.MACH_STATUS_OPENNING {
			lobbyMach.UpdateMachStatus(slg.MACH_STATUS_OPEN)
		}
	} else if lobbyMach.Status == slg.MACH_STATUS_CLOSING {
		// 进程已关闭
		lobbyMach.UpdateMachStatus(slg.MACH_STATUS_CLOSE)
	}
	return ""
}

// 获取辅助功能服的进程信息
func (this *Match) GetSupProcessInfo(mach *SupMach) string {
	rst, err := ut.SshExcuteShell(mach.Addr, slg.SHOW_PID_BASH)
	if err != nil {
		log.Error("GetSupProcessInfo err: %v", err)
		return "查询大厅服进程失败!"
	}
	// 根据进程查询输出更新状态
	if pInfos := strings.Split(rst, slg.PROCESS_DIR); len(pInfos) > 1 {
		// 进程已开启
		if mach.Status == slg.MACH_STATUS_OPENNING {
			mach.UpdateMachStatus(slg.MACH_STATUS_OPEN)
		}
	} else if mach.Status == slg.MACH_STATUS_CLOSING {
		// 进程已关闭
		mach.UpdateMachStatus(slg.MACH_STATUS_CLOSE)
	}
	return ""
}

// 获取登录限制信息
func (this *Match) httpGetLoginLimitInfo() (response map[string]interface{}, err error) {
	curLimitType, _ := rds.RdsGet(rds.RDS_LOGIN_LIMIT_TYPE_KEY)
	whiteIpMap, _ := rds.RdsHGetAll(rds.RDS_LOGIN_WHITE_MAP_KEY)
	blackIpMap, _ := rds.RdsHGetAll(rds.RDS_LOGIN_BLACK_MAP_KEY)
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"curLimitType": ut.Int(curLimitType),
		"whiteIpMap":   whiteIpMap,
		"blackIpMap":   blackIpMap,
	}), nil
}

// 设置登录限制类型
func (this *Match) httpSetLoginLimitType(limitType string) (response map[string]interface{}, err error) {
	rds.RdsSet(rds.RDS_LOGIN_LIMIT_TYPE_KEY, ut.Int(limitType))
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_SET_LOGIN_LIMIT, ut.Int(limitType))
		return true
	})
	return slg.HttpResponseSuccessNoDataWithDesc("设置成功"), nil
}

// 设置登录限制ip
func (this *Match) httpSetLoginLimitIp(limitType, add, ip string) (response map[string]interface{}, err error) {
	loginLimitType := ut.Int(limitType)
	now := ut.Now()
	if loginLimitType == slg.LOGIN_LIMIT_TYPE_WHITE_LIST {
		// 白名单
		if add == "1" {
			// 添加
			rds.RdsHSet(rds.RDS_LOGIN_WHITE_MAP_KEY, ip, now)
		} else {
			// 移除
			rds.RdsHDel(rds.RDS_LOGIN_WHITE_MAP_KEY, ip)
		}
	} else if loginLimitType == slg.LOGIN_LIMIT_TYPE_BLACK_LIST {
		// 黑名单
		if add == "1" {
			// 添加
			rds.RdsHSet(rds.RDS_LOGIN_BLACK_MAP_KEY, ip, now)
		} else {
			// 移除
			rds.RdsHDel(rds.RDS_LOGIN_BLACK_MAP_KEY, ip)
		}
	}
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"now": now,
	}), nil
}

// 获取作弊检测开关
func (this *Match) httpGetCheckCheat() (map[string]interface{}, error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"open": ut.If(constant.ANTI_CHEAT_OPEN, 1, 0),
	}), nil
}

// 设置作弊检测开关
func (this *Match) httpSetCheckCheat(open string) (map[string]interface{}, error) {
	constant.ANTI_CHEAT_OPEN = ut.If(open == "1", true, false)
	// 设置所有游戏服的参数
	rooms.ForEach(func(r *Room, k int32) bool {
		if r != nil && !r.IsClose() {
			this.Invoke("game@game"+ut.String(r.Id), slg.RPC_SET_TA_PARAMS, ut.Bytes(constant.ANTI_CHEAT_OPEN))
		}
		return true
	})
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}

// 获取天选画像概率
func (this *Match) httpGetPortrayalOdds() (map[string]interface{}, error) {
	return slg.HttpResponseSuccessWithDataNoDesc(map[string]interface{}{
		"odds": g.PORTRAYAL_CHOSEN_ONE_ODD_MAX,
	}), nil
}

// 设置天选画像概率
func (this *Match) httpSetPortrayalOdds(odds string) (map[string]interface{}, error) {
	_odds := ut.Int(odds)
	if _odds > 0 {
		g.PORTRAYAL_CHOSEN_ONE_ODD_MAX = _odds
		// 设置所有大厅服的参数
		lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
			if v != nil {
				this.InvokeLobbyRpcNR(ut.String(v.Id), slg.RPC_SET_PORTRAYAL_ODDS, _odds)
			}
			return true
		})
	}
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}
