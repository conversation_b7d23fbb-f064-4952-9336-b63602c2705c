package lobby

import (
	"slgsrv/server/common/pb"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// // 初始化玩家订阅
// func (this *User) InitRedisSub() {
// 	initChannels := []string{}
// 	if this.TeamUid != "" {
// 		initChannels = append(initChannels, rds.RDS_USER_SUB_CHANNEL_TEAM+this.TeamUid)
// 	}
// 	if this.redisSub != nil {
// 		this.redisSub.Close()
// 		this.redisSub = nil
// 	}
// 	this.redisSub = rds.RdsSubChannels(initChannels...)
// 	this.SubChannelReceive()
// }

// // 处理用户接受到的订阅消息
// func (this *User) SubChannelReceive() {
// 	go func() {
// 		// 通过订阅器接收消息
// 		ch := this.redisSub.Channel()
// 		for {
// 			select {
// 			case msg, ok := <-ch:
// 				if !ok {
// 					// 如果接收到中断信号，退出循环
// 					return
// 				}
// 				if !this.IsOnline() || this.Session == nil {
// 					this.redisSub.Close()
// 					// 离线则不再接收
// 					return
// 				}
// 				// 处理接收到的消息
// 				data := []byte(msg.Payload)
// 				notifyInfo := &pb.PublishInfo{}
// 				err := pb.ProtoUnMarshal(data, notifyInfo)
// 				if err == nil {
// 					this.Session.SendNR(notifyInfo.Topic, notifyInfo.Data)
// 				} else {
// 					log.Error("SubChannelReceive msg: %v, err: %v", msg, err)
// 				}
// 			}
// 		}
// 	}()
// }

// // 用户添加订阅频道
// func (this *User) AddSubChannels(channels ...string) {
// 	if this.Session != nil && this.redisSub != nil {
// 		rds.RdsSubAddChannels(this.redisSub, channels...)
// 	}
// }

// // 用户取消订阅频道
// func (this *User) UnSubChannels(channels ...string) {
// 	if this.Session != nil && this.redisSub != nil {
// 		rds.RdsUnSubChannels(this.redisSub, channels...)
// 	}
// }

// 发布用户相关通知的订阅消息
func PublishUserNotify(channel, topic string, m protoreflect.ProtoMessage) {
	data, err := pb.ProtoMarshal(m)
	if err != "" {
		log.Error("PublishUserNotify ProtoMarshal1 topic: %v err: %v", topic, err)
		return
	}
	notifyInfo := &pb.PublishInfo{
		Topic: topic,
		Data:  data,
	}
	notifyData, err := pb.ProtoMarshal(notifyInfo)
	if err == "" {
		rds.RdsPublish(channel, notifyData)
	} else {
		log.Error("PublishUserNotify ProtoMarshal2 topic: %v err: %v", topic, err)
	}
}
