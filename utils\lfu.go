package ut

import (
	"container/heap"

	"github.com/sasha-s/go-deadlock"
)

// 缓存数据结果
type CacheItem struct {
	Key       string
	Value     interface{}
	Frequency int
	Index     int
}

// LFU缓存结构
type LFUCache struct {
	Capacity int
	Items    map[string]*CacheItem
	Priority PriorityQueue
	deadlock.RWMutex
}

// 根据容量新建缓存
func NewLFUCache(capacity int) *LFUCache {
	return &LFUCache{
		Capacity: capacity,
		Items:    make(map[string]*CacheItem),
		Priority: make(PriorityQueue, 0),
	}
}

// 获取数据
func (c *LFUCache) Get(key string) (interface{}, bool) {
	c.RLock()
	defer c.RUnlock()
	if item, found := c.Items[key]; found {
		item.Frequency++
		heap.Fix(&c.Priority, item.Index)
		return item.Value, true
	}
	return nil, false
}

// 更新数据
func (c *LFUCache) Set(key string, value interface{}) {
	if c.Capacity == 0 {
		return
	}
	c.Lock()
	defer c.Unlock()
	if item, found := c.Items[key]; found {
		item.Value = value
		item.Frequency++
		heap.Fix(&c.Priority, item.Index)
	} else {
		if len(c.Items) >= c.Capacity {
			// 容量已满则移除最小值
			removed := heap.Pop(&c.Priority).(*CacheItem)
			delete(c.Items, removed.Key)
		}
		item := &CacheItem{Key: key, Value: value, Frequency: 1}
		heap.Push(&c.Priority, item)
		c.Items[key] = item
	}
}

// 更新容量
func (c *LFUCache) UpdateCapacity(capacity int) {
	c.Capacity = capacity
}

// 小顶堆
type PriorityQueue []*CacheItem

// 获取长度
func (p PriorityQueue) Len() int {
	return len(p)
}

// 比较
func (p PriorityQueue) Less(i, j int) bool {
	return p[i].Frequency < p[j].Frequency
}

// 交换
func (p PriorityQueue) Swap(i, j int) {
	p[i], p[j] = p[j], p[i]
	p[i].Index = i
	p[j].Index = j
}

// 插入
func (p *PriorityQueue) Push(x interface{}) {
	item := x.(*CacheItem)
	item.Index = len(*p)
	*p = append(*p, item)
}

// 移除并返回最小值
func (p *PriorityQueue) Pop() interface{} {
	old := *p
	n := len(old)
	item := old[n-1]
	item.Index = -1
	*p = old[0 : n-1]
	return item
}
