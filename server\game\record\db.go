package record

import (
	"context"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	ARMY         = "record_army"
	BATTLE       = "record_battle"
	BAZAAR       = "record_bazaar"
	FORGE        = "record_forge"
	SCORE        = "record_battle_score"
	SCORE_PLAYER = "record_battle_score_player"
)

type Mongodb struct {
	sid    int32
	sidStr string
}

func (this *Mongodb) getCollection(table string) *mongo.Collection {
	return mgo.GetCollection(table + "_" + this.sidStr)
}

// 删除多个
func (this *Mongodb) DeleteMany(table string, uid string) (err string) {
	if _, e := this.getCollection(table).DeleteMany(context.TODO(), bson.M{"owner": uid}); e != nil {
		err = e.Error()
	}
	return
}

// --------------------------------------------------------军队记录--------------------------------------------------------------------
type ArmyRecordData struct {
	UID       string                 `bson:"uid"` // 军队uid
	Owner     string                 `bson:"owner"`
	ArmyName  string                 `bson:"army_name"`
	BattleUid string                 `bson:"battle_uid"` // 战斗记录uid
	Battle    map[string]interface{} `bson:"battle"`     // 战斗信息

	Time        int64 `bson:"time"` // 发生时间
	SID         int32 `bson:"sid"`
	Type        int32 `bson:"type"` // 类型 0.战斗 1.移动 2.撤回 3.遣返 4.解散 5.被遣返
	ArmyIndex   int32 `bson:"army_index"`
	TargetIndex int32 `bson:"target_index"`
}

func (this ArmyRecordData) Strip() map[string]interface{} {
	return map[string]interface{}{
		"type":        this.Type,
		"armyName":    this.ArmyName,
		"armyIndex":   this.ArmyIndex,
		"targetIndex": this.TargetIndex,
		"time":        this.Time,
		"battle":      this.Battle,
	}
}

func (this ArmyRecordData) ToPb() *pb.ArmyRecordInfo {
	record := &pb.ArmyRecordInfo{
		Type:        int32(this.Type),
		ArmyName:    this.ArmyName,
		ArmyIndex:   int32(this.ArmyIndex),
		TargetIndex: int32(this.TargetIndex),
		Time:        int64(this.Time),
		Battle: &pb.ArmyBattleRecordInfo{
			Pawns:      []*pb.AreaPawnInfo{},
			BattleInfo: map[int32]int32{},
		},
	}
	if battleInfoMap, ok := this.Battle["battleInfo"].(map[string]interface{}); ok {
		for k, v := range battleInfoMap {
			record.Battle.BattleInfo[pb.Int32(k)] = pb.Int32(v)
		}
	}
	if pawns, ok := this.Battle["pawns"].(bson.A); ok {
		for _, p := range pawns {
			m := p.(map[string]interface{})

			record.Battle.Pawns = append(record.Battle.Pawns, &pb.AreaPawnInfo{
				Uid:       ut.String(m["uid"]),
				Id:        ut.Int32(m["id"]),
				Lv:        ut.Int32(m["lv"]),
				Hp:        map[int32]int32{0: ut.Int32(m["curHp"]), 1: ut.Int32(m["maxHp"])},
				Portrayal: &pb.PortrayalInfo{Id: ut.Int32(m["portrayalId"])},
			})
		}
	}
	return record
}

// 插入单个
func (this *Mongodb) InsertArmyRecord(data ArmyRecordData) (err string) {
	if _, e := this.getCollection(ARMY).InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertArmyRecord " + err)
	}
	return
}

// 插入多个
func (this *Mongodb) InsertManyArmyRecord(datas []interface{}) (err string) {
	if _, e := this.getCollection(ARMY).InsertMany(context.TODO(), datas); e != nil {
		err = e.Error()
		log.Error("InsertMany " + err)
	}
	return
}

// 获取数据
func (this *Mongodb) FindArmyRecordsByOwner(uid string, page, count int, isBattle bool) (arr []ArmyRecordData, err string) {
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"time": -1}) // 从大到小排序
	findOptions.SetLimit(int64(count))
	findOptions.SetSkip(int64(page * count))
	filter := bson.M{"owner": uid, "sid": this.sid, "type": bson.M{"$ne": 0}}
	if isBattle {
		filter = bson.M{"owner": uid, "sid": this.sid, "type": 0}
	}
	cur, e := this.getCollection(ARMY).Find(context.TODO(), filter, findOptions)
	defer cur.Close(context.TODO())
	if e != nil {
		return []ArmyRecordData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []ArmyRecordData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		err = e.Error()
		log.Error("FindArmyRecordsByOwner " + err)
	}
	return
}

// 根据军队uids获取军队战斗记录
func (this *Mongodb) FindArmyBattleRecordsByUids(uids []string, battleUid string) (arr []ArmyRecordData, err string) {
	filter := bson.M{"uid": bson.M{"$in": uids}, "battle_uid": battleUid}
	cur, e := this.getCollection(ARMY).Find(context.TODO(), filter)
	defer cur.Close(context.TODO())
	if e != nil {
		log.Error("FindArmyRecordsByUids " + err)
		return []ArmyRecordData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []ArmyRecordData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		err = e.Error()
		log.Error("FindArmyRecordsByUids " + err)
	}
	return
}

// --------------------------------------------------------战斗记录--------------------------------------------------------------------
type BattleRecordData struct {
	UID       string                `bson:"uid"`
	Frames    []*g.BattleRecordData `bson:"frames"`    // 战斗信息
	Treasures []*g.TreasureInfo     `bson:"treasures"` // 宝箱信息

	BeginTime int64 `bson:"begin_time"`
	EndTime   int64 `bson:"end_time"`

	SID     int32 `bson:"sid"`
	Version int32 `bson:"version"` // 版本号
	Index   int32 `bson:"index"`
}

func (this BattleRecordData) Strip() map[string]interface{} {
	return map[string]interface{}{
		"uid":       this.UID,
		"index":     this.Index,
		"beginTime": this.BeginTime,
		"endTime":   this.EndTime,
		"frames":    this.Frames,
	}
}

func (this BattleRecordData) ToPb() *pb.BattleRecordInfo {
	ret := &pb.BattleRecordInfo{
		Uid:       this.UID,
		Index:     int32(this.Index),
		BeginTime: int64(this.BeginTime),
		EndTime:   int64(this.EndTime),
		Treasures: []*pb.TreasureBaseInfo{},
	}
	for _, v := range this.Frames {
		tp := v.Type
		frameInfo := &pb.FrameInfo{
			Type:              tp,
			CurrentFrameIndex: v.CurrentFrameIndex,
			ArmyUid:           v.ArmyUid,
			Uid:               v.Uid,
		}
		if tp == 0 {
			frameInfo.Fps = v.Fps
			frameInfo.CityId = v.CityId
			frameInfo.Owner = v.Owner
			frameInfo.Camp = v.Camp
			frameInfo.RandSeed = int32(v.RandSeed)
		}
		if v.Hp != nil {
			for _, val := range v.Hp {
				frameInfo.Hp = append(frameInfo.Hp, pb.Int32(val))
			}
		}
		if v.Builds != nil {
			for _, m := range v.Builds {
				frameInfo.Builds = append(frameInfo.Builds, BuildInfoBsonToPb(m))
			}
		}
		if v.BuildInfo != nil {
			for _, val := range v.BuildInfo {
				frameInfo.BuildInfo = append(frameInfo.BuildInfo, pb.Int32(val))
			}
		}
		if v.Fighters != nil {
			for _, m := range v.Fighters {
				fighter := &pb.FighterInfo{
					Uid:          m.Uid,
					Camp:         m.Camp,
					AttackIndex:  m.AttackIndex,
					WaitRound:    m.WaitRound,
					AttackTarget: m.AttackTarget,
					RoundCount:   m.RoundCount,
					AttackCount:  m.AttackCount,
					TowerId:      m.TowerId,
					TowerLv:      m.TowerLv,
				}
				if m.Point != nil {
					fighter.Point = pb.NewVec2(m.Point)
				}
				if m.Buffs != nil && len(m.Buffs) > 0 {
					fighter.Buffs = m.ToBuffsPb()
				}
				frameInfo.Fighters = append(frameInfo.Fighters, fighter)
			}
		}
		if v.Armys != nil {
			for _, m := range v.Armys {
				frameInfo.Armys = append(frameInfo.Armys, ArmyInfoBsonToPb(m))
			}
		}
		if v.Pawn != nil {
			frameInfo.Pawn = PawnInfoBsonToPb(v.Pawn)
		}
		if v.Army != nil {
			frameInfo.Army = ArmyInfoBsonToPb(v.Army)
		}
		ret.Frames = append(ret.Frames, frameInfo)
	}
	for _, v := range this.Treasures {
		ret.Treasures = append(ret.Treasures, &pb.TreasureBaseInfo{Uid: v.UID, Id: v.ID})
	}
	return ret
}

func BuildInfoBsonToPb(buidInfo *g.BuildStrip) *pb.AreaBuildInfo {
	build := &pb.AreaBuildInfo{
		Index: buidInfo.Index,
		Uid:   buidInfo.Uid,
		Id:    buidInfo.Id,
		Lv:    buidInfo.Lv,
	}
	if buidInfo.Point != nil {
		build.Point = &pb.Vec2{
			X: buidInfo.Point.X,
			Y: buidInfo.Point.Y,
		}
	}
	return build
}

func ArmyInfoBsonToPb(armyData *g.ArmyStrip2) *pb.AreaArmyInfo {
	army := &pb.AreaArmyInfo{
		Index:    armyData.Index,
		Uid:      armyData.Uid,
		Name:     armyData.Name,
		Owner:    armyData.Owner,
		State:    armyData.State,
		EnterDir: armyData.EnterDir,
	}
	if armyData.Pawns != nil {
		for _, v := range armyData.Pawns {
			army.Pawns = append(army.Pawns, PawnInfoBsonToPb(v))
		}
	}
	return army
}

func PawnInfoBsonToPb(pawnData *g.PawnStrip2) *pb.AreaPawnInfo {
	pawn := &pb.AreaPawnInfo{
		Index:           pawnData.Index,
		Uid:             pawnData.Uid,
		Id:              pawnData.Id,
		Lv:              pawnData.Lv,
		SkinId:          pawnData.SkinId,
		CurAnger:        pawnData.CurAnger,
		AttackSpeed:     pawnData.AttackSpeed,
		RodeleroCadetLv: pawnData.RodeleroCadetLv,
		PetId:           pawnData.PetId,
	}
	pawn.Hp = map[int32]int32{}
	pawn.Hp[0] = pawnData.CurHp
	if pawnData.Point != nil {
		pawn.Point = pb.NewVec2(pawnData.Point)
	}
	if pawnData.Equip != nil {
		equip := pawnData.Equip

		if equip.Attrs != nil && equip.Id != 0 {
			pawn.Equip = &pb.EquipInfo{Id: equip.Id}
			for _, attr := range equip.Attrs {
				attrInfo := &pb.AttrArrayInfo{}
				if attr != nil {
					for _, val := range attr {
						attrInfo.Attr = append(attrInfo.Attr, pb.Int32(val))
					}
					pawn.Equip.Attrs = append(pawn.Equip.Attrs, attrInfo)
				}
			}
		}
	}
	if pawnData.Portrayal != nil {
		portrayal := pawnData.Portrayal
		if portrayal.Attrs != nil && portrayal.Id != 0 {
			pawn.Portrayal = &pb.PortrayalInfo{Id: portrayal.Id}
			for _, attr := range portrayal.Attrs {
				attrInfo := &pb.AttrArrayInfo{}
				if attr != nil {
					for _, val := range attr {
						attrInfo.Attr = append(attrInfo.Attr, pb.Int32(val))
					}
					pawn.Portrayal.Attrs = append(pawn.Portrayal.Attrs, attrInfo)
				}
			}
		}
	}
	return pawn
}

// 插入单个
func (this *Mongodb) InsertBattleRecord(data BattleRecordData) (err string) {
	if _, e := this.getCollection(BATTLE).InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertBattleRecord " + err)
	}
	return
}

// 获取单个
func (this *Mongodb) FindBattleRecord(uid string) (data BattleRecordData, err string) {
	if e := this.getCollection(BATTLE).FindOne(context.TODO(), bson.M{"sid": this.sid, "uid": uid}).Decode(&data); e != nil {
		err = e.Error()
		log.Error("FindBattleRecord " + err)
	}
	return
}

// --------------------------------------------------------市场记录--------------------------------------------------------------------
type BazaarRecordData struct {
	UID    string `bson:"uid"`
	Owner  string `bson:"owner"`
	Target string `bson:"target"` // 目标

	Res   map[string]interface{} `bson:"res"` // 资源
	Names []string               `bson:"names"`

	Time int64 `bson:"time"`
	SID  int32 `bson:"sid"`
	Type int8  `bson:"type"` // 类型 0.战斗 1.移动 2.撤回 3.遣返 4.解散
}

func (this BazaarRecordData) Strip() map[string]interface{} {
	return map[string]interface{}{
		"uid":   this.UID,
		"type":  this.Type,
		"time":  this.Time,
		"owner": this.Owner,
		"res":   this.Res,
		"names": this.Names,
	}
}

func (this BazaarRecordData) ToPb() *pb.BazaarRecordInfo {
	return &pb.BazaarRecordInfo{
		Uid:    this.UID,
		Sid:    int32(this.SID),
		Type:   int32(this.Type),
		Time:   int64(this.Time),
		Owner:  this.Owner,
		Target: this.Target,
		Res: &pb.BazaarRecordResourceInfo{
			ResType:   pb.Int32(this.Res["resType"]),
			ResCount:  pb.Int32(this.Res["resCount"]),
			CostType:  pb.Int32(this.Res["costType"]),
			CostCount: pb.Int32(this.Res["costCount"]),
			ActCount:  pb.Int32(this.Res["actCount"]),
		},
		Names: this.Names,
	}
}

// 插入单个
func (this *Mongodb) InsertBazaarRecord(data BazaarRecordData) (err string) {
	if _, e := this.getCollection(BAZAAR).InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertBazaarRecord " + err)
	}
	return
}

// 获取多条
func (this *Mongodb) FindBazaarRecords(uid string, page, count int) (arr []BazaarRecordData, err string) {
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"time": -1}) // 从大到小排序
	findOptions.SetLimit(int64(count))
	findOptions.SetSkip(int64(page * count))
	cur, e := this.getCollection(BAZAAR).Find(context.TODO(), bson.M{
		"sid": this.sid,
		"$or": []bson.M{{"owner": uid}, {"type": constant.BAZAAR_RECORD_TYPE_BUY, "target": uid}}, // 如果是购买 两个uid都可以获取
	}, findOptions)
	defer cur.Close(context.TODO())
	if e != nil {
		return []BazaarRecordData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []BazaarRecordData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		err = e.Error()
		log.Error("FindBazaarRecords " + err)
	}
	return
}

// --------------------------------------------------------战斗积分记录--------------------------------------------------------------------
// 插入多个
func (this *Mongodb) InsertManyBattleSocreRecord(datas []interface{}) (err error) {
	if _, err = this.getCollection(SCORE).InsertMany(context.TODO(), datas); err != nil {
		log.Error("InsertManyBattleSocreecord err: %v", err)
	}
	return
}

// 根据玩家id和日期获取战斗积分记录
func (this *Mongodb) FindBattleScoreRecords(userId, date, alli string) (arr []g.BattleScoreRecordData, err error) {
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"end_time": -1}) // 从大到小排序
	// findOptions.SetLimit(int64(count))
	// findOptions.SetSkip(int64(page * count))
	filter := bson.M{"owner": userId, "date": date, "alli_uid": alli}
	cur, e := this.getCollection(SCORE).Find(context.TODO(), filter, findOptions)
	defer cur.Close(context.TODO())
	if e != nil {
		return
	} else if e = cur.Err(); e != nil {
		return
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		log.Error("FindBattleScoreRecords userId: %v err: %v", userId, e)
	}
	return
}

// 根据玩家id获取战斗积分记录
func (this *Mongodb) FindBattleScoreRecordsByOwner(userId string) (arr []g.BattleScoreRecordData, err error) {
	filter := bson.M{"owner": userId}
	cur, e := this.getCollection(SCORE).Find(context.TODO(), filter)
	defer cur.Close(context.TODO())
	if e != nil {
		return
	} else if e = cur.Err(); e != nil {
		return
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		log.Error("FindBattleScoreRecordsByOwner userId: %v err: %v", userId, e)
	}
	return
}

// 获取指定玩家指定数量的战斗积分记录
func (this *Mongodb) FindPlayerBattleScoreRecordsByPage(uid string, page, count int) (arr []g.BattleScoreRecordData, err string) {
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"end_time": -1}) // 从大到小排序
	findOptions.SetLimit(int64(count))
	findOptions.SetSkip(int64(page * count))
	filter := bson.M{"owner": uid}
	cur, e := this.getCollection(SCORE).Find(context.TODO(), filter, findOptions)
	defer cur.Close(context.TODO())
	if e != nil {
		return []g.BattleScoreRecordData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []g.BattleScoreRecordData{}, e.Error()
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		err = e.Error()
		log.Error("FindPlayerBattleScoreRecordsByPage " + err)
	}
	return
}

// 获取指定玩家所有战斗积分记录(已结算)
func (this *Mongodb) FindPlayerBattleScoreRecords(userId, alli string) (arr []g.BattleScoreRecordData, err error) {
	filter := bson.M{"owner": userId, "alli_uid": alli}
	cur, e := this.getCollection(SCORE_PLAYER).Find(context.TODO(), filter)
	defer cur.Close(context.TODO())
	if e != nil {
		return
	} else if e = cur.Err(); e != nil {
		return
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		log.Error("FindPlayerBattleScoreRecords userId: %v err: %v", userId, e)
	}
	return
}

// 插入单个玩家当天数据
func (this *Mongodb) InsertPlayerBattleScoreRecord(data *g.BattleScoreRecordData) (err error) {
	if _, err = this.getCollection(SCORE_PLAYER).InsertOne(context.TODO(), data); err != nil {
		log.Error("InsertPlayerBattleScoreRecord data: %v, err: %v", data, err)
	}
	return
}

// 根据玩家id和日期查询当天战斗积分记录
func (this *Mongodb) FindPlayerBattleScoreRecord(uid, date string) (data g.BattleScoreRecordData, err error) {
	if err = this.getCollection(SCORE_PLAYER).FindOne(context.TODO(), bson.M{"owner": uid, "date": date}).Decode(&data); err != nil {
		// log.Warning("FindPlayerBattleScoreRecord uid: %v, err: %v", uid, err)
	}
	return
}

// 根据联盟id和日期查询当天该联盟所有玩家战斗积分记录
func (this *Mongodb) FindAlliPlayersBattleScoreRecords(alli, date string) (arr []g.BattleScoreRecordData, err error) {
	findOptions := options.Find()
	filter := bson.M{"date": date, "alli_uid": alli}
	cur, e := this.getCollection(SCORE_PLAYER).Find(context.TODO(), filter, findOptions)
	defer cur.Close(context.TODO())
	if e != nil {
		return
	} else if e = cur.Err(); e != nil {
		return
	}
	if e = cur.All(context.TODO(), &arr); e != nil {
		log.Error("FindAlliPlayersBattleScoreRecords alli: %v, date: %v, err: %v", alli, date, err)
	}
	return
}
