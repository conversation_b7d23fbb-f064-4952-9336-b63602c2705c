package db

import (
	"context"
	"crypto/md5"
	"fmt"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const DefaultUser = "root"
const DefaultPassword = "twomiles@2022"

type UserCol struct {
	Username string `bson:"username"`
	Password string `bson:"password"`
	Type     int32  `bson:"type"`
}

func (this *UserCol) GetTableDef() *TableDef {
	// 返回索引信息
	return USER.Index(map[string]bool{
		"username": true,
		"password": false,
	})
}

// InsertDefaultUser http默认用户  root
func InsertDefaultUser() {
	bytes := md5.Sum([]byte(DefaultPassword))
	this := &UserCol{}
	col := GlobalGetTableManager().GetCollection(this)
	col.UpdateOne(context.TODO(),
		bson.D{{Key: "username", Value: DefaultUser}},
		bson.D{{Key: "$set", Value: bson.D{{Key: "password", Value: fmt.Sprintf("%x", bytes)}}}},
		options.Update().SetUpsert(true))
}

type GoCodeCol struct {
	Desc   string `bson:"desc"`
	Code   string `bson:"code"`
	Params string `bson:"params"`
}

func (this *GoCodeCol) GetTableDef() *TableDef {
	return CODE.Index(map[string]bool{})
}

type MapCol[V string | int] struct {
	Key string `bson:"key"`
	Val V      `bson:"val"`
}

func (this *MapCol[V]) GetTableDef() *TableDef {
	return MAP.Index(map[string]bool{
		"key": true,
	})
}

// 客户端错误上报
type ErrorReport struct {
	Uid       string `bson:"uid"`
	Platform  string `bson:"platform"`
	Version   string `bson:"version"`
	Exception string `bson:"exception"`
	Type      string `bson:"type"`
	Level     int    `bson:"level"`
	Time      int    `bson:"time"`
}

func (this *ErrorReport) GetTableDef() *TableDef {
	// 返回索引信息
	return ERROR_REPORT.Index(map[string]bool{
		"uid":      false,
		"platform": false,
		"version":  false,
		"type":     false,
	})
}

// 获取客户端上报错误返回结果
type ErrorReportRetInfo struct {
	Info  *ErrorReport `json:"info"`
	Uids  []string     `json:"uids"`
	Count int          `json:"count"`
}

// 客户端热更新上报
type HotUpdateReport struct {
	Uid        string `bson:"uid"`
	DistinctId string `bson:"distinct_id"`
	CurVer     string `bson:"cur_ver"`
	Version    string `bson:"version"`
	CostTime   int    `bson:"cost_time"`
	Size       int    `bson:"size"`
	Os         string `bson:"os"`
	Files      string `bson:"files"`
	State      int    `bson:"state"`
}

func (this *HotUpdateReport) GetTableDef() *TableDef {
	// 返回索引信息
	return HOT_UPDATE.Index(map[string]bool{
		"uid":         false,
		"distinct_id": false,
		"cur_ver":     false,
		"version":     false,
		"state":       false,
	})
}

// 用户反馈
type Feedback struct {
	Uid        string            `bson:"uid"`         //uid
	UserId     string            `bson:"user_id"`     //玩家uid
	DistinctId string            `bson:"distinct_id"` //访客id
	Sid        int               `bson:"sid"`         //0表示在登录界面
	DeviceOS   string            `bson:"device_os"`   //设备
	Platform   string            `bson:"platform"`    //平台
	Version    string            `bson:"version"`     //版本
	Content    string            `bson:"content"`     //内容
	Time       int64             `bson:"time"`        //时间
	Response   map[string]string `bson:"response"`    //回复内容
}

func (this *Feedback) GetTableDef() *TableDef {
	// 返回索引信息
	return FEEDBACK.Index(map[string]bool{
		"uid":         false,
		"distinct_id": false,
	})
}

// 操作记录日志
type OperationLog struct {
	Username string                 `bson:"username"`
	Route    string                 `bson:"route"`
	Params   map[string]interface{} `bson:"params"`
	Time     int64                  `bson:"time"`
}

func (this *OperationLog) GetTableDef() *TableDef {
	// 返回索引信息
	return OPERATION_LOG.Index(map[string]bool{
		"username": false,
		"route":    false,
	})
}

// cdk模版
type CdkModel struct {
	Name           string       `bson:"name"`             // 名字
	Items          []*g.TypeObj `bson:"items"`            // 奖励列表
	Receiver       string       `bson:"receiver"`         // 接受人 0.所有人 多个用|隔开
	IsForeverClaim bool         `bson:"is_forever_claim"` // 是否可无限领取
}

func (this *CdkModel) GetTableDef() *TableDef {
	// 返回索引信息
	return CDK_MODEL.Index(map[string]bool{
		"name": true,
	})
}

// 登录服物理机信息
type LoginMach struct {
	Addr         string `bson:"ip"`            // 物理机地址
	Name         string `bson:"name"`          // 名字
	Status       int    `bson:"status"`        // 状态
	UpdateStatus int    `bson:"update_status"` // 更新状态
	Id           int    `bson:"id"`            // 服务器id
	UserNum      int    // 内存中玩家数量
	UserCount    int    // 停机前玩家数量
	SaveCount    int    // 已保存玩家数量

}

func (this *LoginMach) GetTableDef() *TableDef {
	// 返回索引信息
	return LOGIN_MACH.Index(map[string]bool{
		"ip": true,
	})
}

// 更新物理机状态
func (this *LoginMach) UpdateMachStatus(status int) {
	this.Status = status
	this.UpdateLoginMachDb()
}

// 更新物理机信息
func (this *LoginMach) UpdateLoginMachDb() {
	filter := FieldToBson(this, "Addr")
	update := &bson.M{
		"$set": FieldToBsonAuto(this),
	}
	_, err := GlobalGetTableManager().GetCollection(this).UpdateOne(context.TODO(), filter, update, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("UpdateLoginMachDb err: %v", err)
	}
}

// 更新物理机代码
func (this *LoginMach) HandleUpdateCode() {
	go func() {
		_, err := ut.SshExcuteShell(this.Addr, slg.GIT_UPDATE_BASH)
		if err != nil {
			log.Error("httpLoginMachGitUpdate err: %v", err)
		}
		// 更新完成
		this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
		this.UpdateLoginMachDb()
		log.Info("httpLoginMachGitUpdate finifsh")
	}()
	this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATING
	this.UpdateLoginMachDb()
}
