package ut

import (
	"math"
	"sync"
)

type Vec2 struct {
	X int32 `json:"x" bson:"x"`
	Y int32 `json:"y" bson:"y"`
}

// Vec2对象池，用于减少内存分配
var vec2Pool = sync.Pool{
	New: func() interface{} { return &Vec2{} },
}

// 调试模式下跟踪对象池中的对象
var DebugMode = false
var pooledObjects = make(map[*Vec2]bool)
var poolMutex sync.Mutex

// 从对象池获取Vec2对象
func GetVec2FromPool(x, y int32) *Vec2 {
	v := vec2Pool.Get().(*Vec2)
	v.X, v.Y = x, y

	// 调试模式下标记对象已从池中取出
	if DebugMode {
		poolMutex.Lock()
		delete(pooledObjects, v)
		poolMutex.Unlock()
	}

	return v
}

// 将Vec2对象放回对象池
func PutVec2ToPool(v *Vec2) {
	if v == nil {
		return
	}

	// 调试模式下检测重复放入
	if DebugMode {
		poolMutex.Lock()
		if pooledObjects[v] {
			// 检测到重复放入，记录日志但不崩溃
			// log.Warning("Vec2对象重复放入对象池: %p (%d,%d)", v, v.X, v.Y)
			poolMutex.Unlock()
			return
		}
		pooledObjects[v] = true
		poolMutex.Unlock()
	}

	// 重置值，防止数据污染
	v.X, v.Y = 0, 0
	vec2Pool.Put(v)
}

// 安全的批量放回对象池（自动去重）
func PutVec2SliceToPool(vecs []*Vec2) {
	if len(vecs) == 0 {
		return
	}

	// 使用map去重
	pooled := make(map[*Vec2]bool, len(vecs))
	for _, v := range vecs {
		if v != nil && !pooled[v] {
			PutVec2ToPool(v)
			pooled[v] = true
		}
	}
}

// 原始的NewVec2函数保持不变，以保证兼容性
func NewVec2(x int32, y int32) *Vec2 {
	return &Vec2{X: x, Y: y}
}

// 获取对象池调试信息
func GetVec2PoolDebugInfo() (bool, int) {
	poolMutex.Lock()
	defer poolMutex.Unlock()
	return DebugMode, len(pooledObjects)
}

func NewVec2ByObj(val interface{}) *Vec2 {
	switch reply := val.(type) {
	case map[string]interface{}:
		return NewVec2(Int32(reply["x"]), Int32(reply["y"]))
	case *Vec2:
		return reply.Clone()
	case nil:
		return NewVec2(0, 0)
	}
	return NewVec2(0, 0)
}

func NewVec2ByString(str, separator string) *Vec2 {
	arr := StringToInts(str, separator)
	return NewVec2(int32(arr[0]), int32(arr[1]))
}

func (this *Vec2) ID() string {
	return this.Join("_")
}

func (this *Vec2) Equals(vec *Vec2) bool {
	return vec.X == this.X && vec.Y == this.Y
}

func (this *Vec2) Equal2(x, y int32) bool {
	return x == this.X && y == this.Y
}

func (this *Vec2) Init(x int32, y int32) {
	this.X = x
	this.Y = y
}

func (this *Vec2) Join(separator string) string {
	return Itoa(this.X) + separator + Itoa(this.Y)
}

func (this *Vec2) Set(vec *Vec2) {
	this.X = vec.X
	this.Y = vec.Y
}

func (this *Vec2) Clone() *Vec2 {
	return NewVec2(this.X, this.Y)
}

// 使用对象池的Clone方法
func (this *Vec2) ClonePooled() *Vec2 {
	return GetVec2FromPool(this.X, this.Y)
}

func (this *Vec2) AddSelf(vec *Vec2) *Vec2 {
	this.X += vec.X
	this.Y += vec.Y
	return this
}

func (this *Vec2) Add(vec *Vec2) *Vec2 {
	return GetVec2FromPool(this.X+vec.X, this.Y+vec.Y)
}

func (this *Vec2) Sub(vec *Vec2) *Vec2 {
	return GetVec2FromPool(this.X-vec.X, this.Y-vec.Y)
}

// 向量除法
func (this *Vec2) DivSelf(v int32) *Vec2 {
	this.X /= v
	this.Y /= v
	return this
}

func (this *Vec2) Div(v int32) *Vec2 {
	return GetVec2FromPool(this.X/v, this.Y/v)
}

// 返回长度
func (this *Vec2) Len() int32 {
	return AbsInt32(this.X) + AbsInt32(this.Y)
}

func (this *Vec2) Mul(mul int32) *Vec2 {
	return GetVec2FromPool(this.X*mul, this.Y*mul)
}

func (this *Vec2) Mag() float64 {
	a := this.X*this.X + this.Y*this.Y
	return math.Sqrt(float64(a))
}

func (this *Vec2) MagSqr() int32 {
	return this.X*this.X + this.Y*this.Y
}

func (this *Vec2) ToIndex(size *Vec2) int32 {
	if this.X < 0 || this.X >= size.X || this.Y < 0 || this.Y >= size.Y {
		return -1
	}
	return this.Y*size.X + this.X
}
