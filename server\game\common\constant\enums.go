package constant

// 玩家状态
const (
	PS_NONE    = iota
	PS_ONLINE  // 在线
	PS_OFFLINE // 离线
	PS_CAPTURE // 沦陷
)

// 通知类型
const (
	NQ_NONE                  = iota
	NQ_OUTPUT                // 产出
	NQ_ARMY_DIST             // 军队分布
	NQ_ADD_CELL              // 添加地块
	NQ_REMOVE_CELLS          // 删除地块 4
	NQ_BUILD_UP              // 建筑升级
	NQ_BT_QUEUE              // 建造队列
	NQ_MOVE_BUILD            // 移动建筑
	NQ_ADD_BUILD             // 添加建筑
	NQ_REMOVE_BUILD          // 删除建筑
	NQ_ADD_ARMY              // 添加军队
	NQ_MOVE_PAWN             // 移动士兵
	NQ_REMOVE_ARMY           // 删除军队
	NQ_ADD_MARCH             // 添加行军
	NQ_REMOVE_MARCH          // 删除行军 14
	NQ_AREA_BATTLE_BEGIN     // 发生战斗
	NQ_AREA_BATTLE_END       // 战斗结束
	NQ_BATTLE_DIST           // 战斗分布
	NQ_PAWN_DRILL_QUEUE      // 士兵训练队列 18
	NQ_UPDATE_ARMY           // 更新军队 19
	NQ_UPDATE_MERCHANT       // 更新商人 20
	NQ_ADD_TRANSIT           // 添加运送 21
	NQ_REMOVE_TRANSIT        // 删除运送 22
	NQ_UPDATE_CELL_HP        // 刷新区域血量 23
	NQ_UPDATE_ALL_PAWN_HP    // 刷新所有士兵血量 24
	NQ_CHANGE_PAWN_ATTR      // 改变士兵属性 25
	NQ_AREA_AVOID_WAR        // 通知免战信息 26
	NQ_ADD_BTCITY            // 添加修建城市 27
	NQ_REMOVE_BTCITY         // 删除修建城市 28
	NQ_CAPTURE               // 有玩家被攻陷 29
	NQ_DISSOLVE_ALLIANCE     // 解散联盟 30
	NQ_PLAYER_CITY_OUTPUT    // 玩家城市产出 31
	NQ_PLAYER_JOIN_ALLI      // 有玩家加入联盟 32
	NQ_NEW_MAIL              // 有新邮件 33
	NQ_FORGE_EQUIP_RET       // 打造装备结果 34
	NQ_UPDATE_INVITES        // 刷新邀请列表 35
	NQ_GAME_OVER             // 游戏结束 36
	NQ_MODIFY_NICKNAME       // 修改昵称 37
	NQ_ADD_OUTPUT_TIME       // 刷新添加的产量时间 38
	NQ_UPDATE_PAWN_TREASURE  // 刷新士兵宝箱信息 39
	NQ_PAWN_LEVELING_QUEUE   // 刷新士兵练级队列 40
	NQ_UPDATE_ITEMS          // 通知更新物品 41
	NQ_UPDATE_RP_GOLD        // 刷新重置政策需要的金币 42
	NQ_PLAYER_EXIT_ALLI      // 有玩家退出联盟 43
	NQ_DELETE_PLAYER         // 彻底删除玩家 44
	NQ_PLAYER_TOWER_LV       // 刷新玩家的箭塔等级 45
	NQ_UPDATE_CERI_SLOT      // 刷新研究所槽位信息 46
	NQ_CERI_STUDY_DONE       // 研究完成 47
	NQ_UPDATE_GENERAL_TASKS  // 刷新常规任务列表 48
	NQ_CHANGE_TITLE          // 改变称号 49
	NQ_NEW_TREASURE          // 是否有新宝箱 50
	NQ_UPDATE_WHEEL_COUNT    // 转盘次数 51
	NQ_UPDATE_TODAY_INFO     // 每日信息 52
	NQ_PLAYER_POLICY         // 玩家政策 53
	NQ_SYS_MSG               // 系统消息 54
	NQ_UPDATE_TASKS          // 任务进度更新通知 55
	NQ_PLAYER_CADET_LV       // 刷新玩家见习勇者层数 56
	NQ_UPDATE_LAND_SCORE     // 刷新玩家领地积分 57
	NQ_PLAYER_GIVEUP_GAME    // 玩家放弃对局 58
	NQ_USER_SUBSCRIPTION     // 用户订阅更新 59
	NQ_TRADE_PRICE           // 市场价更新 60 弃用
	NQ_CELL_EMOJI            // 地图表情 61
	NQ_ALLI_BASE_INFO        // 盟主基础信息 62
	NQ_UPDATE_ARMY_TREASURES // 刷新军队宝箱信息 63
	NQ_SMELT_EQUIP_RET       // 融炼装备完成 64
	NQ_UPDATE_SEASON         // 刷新季节信息 65
	NQ_ANCIENT_INFO          // 古城信息更新 66
	NQ_AREA_PLAYER_CHANGE    // 通知战场人员变动 67
	NQ_AREA_CHAT             // 通知战场聊天 68
	NQ_CHANGE_PAWN_PORTRAYAL // 改变士兵画像 69
	NQ_CHANGE_HERO_SLOT_INFO // 英雄殿信息改变 70
	NQ_ADD_PLAYER            // 添加玩家 71
	NQ_TODAY_TRUMPET_COUNT   // 当天喇叭次数 72
	NQ_TODAY_FREE_GOLD_TIME  // 免费金币剩余时间 73
	NQ_COMPENSATE            // 战损补偿 74
	NQ_CHANGE_CITY_SKIN      // 改变城市皮肤 75
	NQ_UPDATE_TITLES         // 刷新称号列表 76
	NQ_ACTIVITY_RECORD       // 活动记录 77
	NQ_BATTLE_PASS_HAS_AWARD // 战令有奖励可领取 78
	NQ_CELL_TONDEN           // 通知屯田信息 79
	NQ_CELL_TONDEN_END       // 通知屯田结束 80
	NQ_CELL_TONDEN_COUNT     // 通知屯田次数 81
	NQ_PAWN_CURING_QUEUE     // 通知士兵治疗队列 82
	NQ_PAWN_INJURY_ADD       // 受伤士兵添加通知 83
	NQ_PAWN_INJURY_REMOVE    // 受伤士兵移除通知 84
	NQ_UPDATE_POLICY_SLOT    // 政策槽位更新 85
	NQ_UPDATE_EQUIP_SLOT     // 装备槽位更新 86
	NQ_UPDATE_PAWN_SLOT      // 士兵槽位更新 87
	NO_ALLI_SETTLE           // 联盟结算通知 88
	NO_WORLD_EVENT           // 世界事件通知 89
	NQ_REMOVE_ARMY_BY_MARCH  // 删除军队来至行军 这里其实只是告诉客户端修改军队的状态 90
)

// 由大厅服发送通知的通知类型map
var NOTIFY_TYPE_BY_LOBBY = map[int]bool{
	NQ_NEW_MAIL:             true,
	NQ_UPDATE_INVITES:       true,
	NQ_UPDATE_GENERAL_TASKS: true,
	NQ_UPDATE_WHEEL_COUNT:   true,
	NQ_USER_SUBSCRIPTION:    true,
}

// 军队状态
const (
	AS_NONE   = iota // 待机中
	AS_MARCH         // 行军中
	AS_FIGHT         // 战斗中
	AS_DRILL         // 训练中
	AS_LVING         // 练级中
	AS_TONDEN        // 屯田中
)

// 行军类型
const (
	MT_NONE    = iota
	MT_ARMY    // 军队
	MT_CARAVAN // 商队
)

// 获取军队列表类型
const (
	GAT_NONE = iota // 全部
	GAT_IDLE        // 空闲的
)

// 商人状态
const (
	MS_NONE    = iota
	MS_TRADING // 在交易中心
	MS_TRANSIT // 运输中
)

// 士兵状态
const (
	PAWN_STATE_NONE = iota
	_
	PAWN_STATE_STAND     // 待机 以下都表示战斗中
	PAWN_STATE_MOVE      // 移动
	PAWN_STATE_ATTACK    // 攻击
	PAWN_STATE_HIT       // 受击
	PAWN_STATE_DIAUP     // 击飞
	PAWN_STATE_HEAL      // 回血
	PAWN_STATE_DEDUCT_HP // 掉血
	PAWN_STATE_ADD_ANGER // 添加怒气
	PAWN_STATE_FEAR      // 恐惧 快速移动
	PAWN_STATE_DIE       // 直接死亡

	PAWN_STATE_SKILL     = 100 // 技能
	PAWN_STATE_SKILL_MAX = 110 // 技能 这个是为了播放同一个技能 然后切换动作的
	MOVE_ATTACK          = 200 // 移动攻击
	MOVE_ATTACK_MAX      = 210
)

// 帧同步通知类型
const (
	FSP_NOTIFY_TYPE_NONE        = iota
	FSP_NOTIFY_TYPE_ADD_ARMY          // 添加军队
	FSP_NOTIFY_TYPE_UPDATE_HP         // 更新血量
	FSP_NOTIFY_TYPE_REMOVE_ARMY       // 删除军队
	FSP_NOTIFY_TYPE_ADD_PAWN          // 添加士兵
	FSP_NOTIFY_TYPE_ARMY_ACC          // 增援军队数量更新
	FSP_NOTIFY_TYPE_END         = 200 // 结束
)

// 士兵类型
const (
	PAWN_TYPE_NONE      = iota
	PAWN_TYPE_PIKEMAN         // 枪兵 1
	PAWN_TYPE_PELTAST         // 盾兵 2
	PAWN_TYPE_ARCHER          // 弓兵 3
	PAWN_TYPE_SOWAR           // 骑兵 4
	PAWN_TYPE_MACHINE         // 器械 5
	PAWN_TYPE_BEAST           // 野兽 6
	PAWN_TYPE_CATERAN         // 山贼 7
	PAWN_TYPE_BUILD     = 100 // 建筑
	PAWN_TYPE_TOWER     = 101 // 箭塔
	PAWN_TYPE_NONCOMBAT = 102 // 非战斗单位
)

// 士兵技能类型
const (
	PAWN_SKILL_TYPE_NONE               = iota
	PAWN_SKILL_TYPE_ATTACK_RESTRAIN          // 攻击克制
	PAWN_SKILL_TYPE_DEFENSE_RESTRAIN         // 防御克制
	PAWN_SKILL_TYPE_REDUCTION_RANGED   = 103 // 减少远程伤害
	PAWN_SKILL_TYPE_RESTRAIN_BEAST     = 104 // 被动 狩猎
	PAWN_SKILL_TYPE_THE_INJURY         = 105 // 被动 反伤
	PAWN_SKILL_TYPE_REDUCTION          = 106 // 被动 鳞甲
	PAWN_SKILL_TYPE_EVADE              = 107 // 被动 躲闪
	PAWN_SKILL_TYPE_INSTABILITY_ATTACK = 108 // 被动 不稳定攻击
	PAWN_SKILL_TYPE_CRIT               = 109 // 被动 暴击
	PAWN_SKILL_TYPE_PEOPLE_BROKEN      = 110 // 被动 人马俱碎
	PAWN_SKILL_TYPE_PULL_STRING        = 111 // 被动 拉弦
	PAWN_SKILL_TYPE_FULL_STRING        = 112 // 被动 满弦
	PAWN_SKILL_TYPE_THE_DODGE          = 113 // 被动 闪避
	PAWN_SKILL_TYPE_CADET              = 114 // 被动 见习勇士
	PAWN_SKILL_TYPE_TELSON             = 115 // 被动 毒刺
	PAWN_SKILL_TYPE_SHIELD_CRUSHING    = 116 // 被动 盾甲粉碎
	PAWN_SKILL_TYPE_LONGITUDINAL_CLEFT = 117 // 被动 顺劈
	PAWN_SKILL_TYPE_STILL_MOUNTAIN     = 120 // 被动 不动如山
	PAWN_SKILL_TYPE_INFECTION_PLAGUE   = 121 // 被动 瘟疫感染
)

// 市场记录类型
const (
	BAZAAR_RECORD_TYPE_NONE      = iota
	BAZAAR_RECORD_TYPE_GIVE      // 赠送
	BAZAAR_RECORD_TYPE_GIVE_TO   // 赠送 运到
	BAZAAR_RECORD_TYPE_URES      // 上架到市场
	BAZAAR_RECORD_TYPE_DRES_AUTO // 时间到了下架
	BAZAAR_RECORD_TYPE_DRES      // 主动下架
	BAZAAR_RECORD_TYPE_BUY       // 购买
	BAZAAR_RECORD_TYPE_BUY_TO    // 购买 运到
	BAZAAR_RECORD_TYPE_BUY_BACK  // 购买 运回
	BAZAAR_RECORD_TYPE_SYS       // 与系统交易
)

// 邀请好友使用类型
const (
	INVITE_FRIEND_USE_TYPE_NONE = iota
	INVITE_FRIEND_USE_TYPE_TASK // 任务
)

// 士兵战斗记录
const (
	PAWN_RECORD_BATTLE_COUNT   = "battleCount"
	PAWN_RECORD_ATTACK_COUNT   = "attackCount"
	PAWN_RECORD_ATTACK_DAMAGE  = "attackDamage"
	PAWN_RECORD_BEARING_DAMAGE = "bearingDamage"
	PAWN_RECORD_KILL_COUNT     = "killCount"
)

// 任务类型
const (
	TASK_TYPE_NONE         = iota
	TASK_TYPE_GUIDE             // 新手任务 1
	TASK_TYPE_TODAY             // 每日任务 2
	TASK_TYPE_LIMIT             // 限时任务 3
	TASK_TYPE_TODAY_PLAYER = 20 // 每日任务 20
)

// 金币变动原因
const (
	GOLD_CHANGE_WHEEL_GET            = iota // 转盘获得 0
	GOLD_CHANGE_TASK_GET                    // 任务获得 1
	GOLD_CHANGE_MAIL_GET                    // 邮件获得 2
	GOLD_CHANGE_RECHARGE_GET                // 充值获得 3
	GOLD_CHANGE_PRAISE_GET                  // 评论获得 4
	GOLD_CHANGE_EXCHANGE_GET                // 兑换获得 5
	GOLD_CHANGE_FREE_GET                    // 免费获得 6
	GOLD_CHANGE_RANK_REWARD_GET             // 排位奖励获得 7
	GOLD_CHANGE_BATTLE_PASS_GET             // 战令获得 8
	GOLD_CHANGE_MAIL_TIMEOUT_GET            // 邮件过期自动领取 9
	GOLD_CHANGE_MONTH_CARD_BUY_GET          // 月卡购买奖励 10
	GOLD_CHANGE_MONTH_CARD_DAILY_GET        // 月卡每日奖励 11

	GOLD_CHANGE_BUY_HEADICON_COST     = 1000 // 购买头像
	GOLD_CHANGE_BUY_PAWN_SKIN_COST    = 1001 // 购买士兵皮肤
	GOLD_CHANGE_BUY_EMOJI_COST        = 1002 // 购买表情
	GOLD_CHANGE_CHANGE_NAME_COST      = 1003 // 改名
	GOLD_CHANGE_IN_DONE_BT            = 1004 // 立即完成修建
	GOLD_CHANGE_RESET_CERI_SLOT       = 1005 // 刷新研究槽位待选项
	GOLD_CHANGE_IN_DONE_FORGE         = 1006 // 立即完成装备铸造
	GOLD_CHANGE_RESET_POLICY          = 1007 // 重置政策
	GOLD_CHANGE_BUY_ADD_OUTPUT_GOLD   = 1008 // 购买添加产量
	GOLD_CHANGE_BATTLE_FORECAST_COST  = 1009 // 预测战斗费用
	GOLD_CHANGE_EVALUATE_COST         = 1010 // 添加评价费用
	GOLD_CHANGE_SEND_TRUMPET_COST     = 1011 // 发送喇叭费用
	GOLD_CHANGE_EXCHANGE_COST         = 1011 // 兑换扣除
	GOLD_CHANGE_BUY_OPT_HERO          = 1012 // 购买自选英雄
	GOLD_CHANGE_BUY_SKIN_BLIND_BOX    = 1013 // 购买皮肤盲盒
	GOLD_CHANGE_FRIEND_GIFT           = 1014 // 好友礼物
	GOLD_CHANGE_PLANTING_COST         = 1015 // 种植种子费用
	GOLD_CHANGE_WATERING_COST         = 1016 // 种植浇水费用
	GOLD_CHANGE_POINTSETS_COST        = 1017 // 点将五次费用
	GOLD_CHANGE_SAVE_PORTRAYAL_COST   = 1018 // 保存画像费用
	GOLD_CHANGE_BUY_BATTLE_PASS_SCORE = 1019 // 购买战令积分
	GOLD_CHANGE_BUY_PORTRAYAL_SLOT    = 1020 // 购买画像保存槽位
	GOLD_CHANGE_RESET_POLICY_SLOT     = 1021 // 重置政策槽位
	GOLD_CHANGE_CREATE_CUSTOM_ROOM    = 1022 // 创建自定义房间

	GOLD_CHANGE_REFUND = 2001 // 退款扣除
)

// 地块服务器状态
const (
	AREA_SERVER_STATE_DEFAULT = iota // 未初始化
	AREA_SERVER_STATE_INIT           // 已初始化
	AREA_SERVER_STATE_CLEAR          // 已清理
)

// 联盟职位
const (
	CREATER      = iota // 盟主
	CREATER_VICE        // 副盟主
	MILITARY            // 军师
	MEMBER       = 10   // 普通成员
)

// 联盟记录类型
const (
	ALLI_LOG_TYPE_NONE          = iota
	ALLI_LOG_TYPE_CREATE        // p1创建联盟
	ALLI_LOG_TYPE_JOIN          // p1加入联盟，通过者p2
	ALLI_LOG_TYPE_REFUSE        // p1拒绝p2加入联盟
	ALLI_LOG_TYPE_KICK          // p1将p2踢出联盟
	ALLI_LOG_TYPE_QUIT          // p1退出联盟
	ALLI_LOG_TYPE_EMBASSY_LVUP  // p1将大使馆升到了p2级
	ALLI_LOG_TYPE_CHOOSE_POLICY // p1选择了联盟政策p2
	ALLI_LOG_TYPE_CHANGE_JOB    // p1的职位从p2调整到p3
	ALLI_LOG_TYPE_CAPTURE       // p1已沦陷 大使馆降为0级
	ALLI_LOG_TYPE_SETTLED       // p1已出局
)

// 地块类型
const (
	LAND_TYPE_NONE   = iota
	LAND_TYPE_BLOCK  // 不可通行地块 河、海、山
	LAND_TYPE_BRIDGE // 桥
	LAND_TYPE_CREAL  // 粮食地
	LAND_TYPE_TIMBER // 木头地
	LAND_TYPE_STONE  // 石头地
)
