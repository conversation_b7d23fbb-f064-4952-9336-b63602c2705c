package behavior

import (
	"fmt"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

type BehaviorTree struct {
	root IBaseNode
}

func NewBehaviorTree() *BehaviorTree {
	return &BehaviorTree{}
}

func (this *BehaviorTree) Load(id int32, target g.IFighter) *BehaviorTree {
	this.root = this.loadNode(id, target, 0)
	// 打印
	//printNode(this.root, 0)
	return this
}

func (this *BehaviorTree) loadNode(id int32, target g.IFighter, index int32) IBaseNode {
	if id == 0 {
		return nil
	}
	json := config.GetJsonData("behavior", id)
	if json == nil {
		log.Error("BehaviorTree load config error, cls=" + ut.Itoa(id))
		return nil
	}
	cls := ut.String(json["cls"])
	tnode, _ := bevTreeFactory.New(cls)
	if tnode == nil {
		log.Error("BehaviorTree load config error, cls=" + cls)
		return nil
	}
	node := tnode.(IBaseNode)
	node.Ctor()
	node.SetBaseNodeWorker(node.(IBaseWorker))
	node.Init(json, target, index)
	// 添加子节点
	if node.GetCategory() == COMPOSITE {
		comp := node.(IBaseComposite)
		childs := ut.StringToInt32s(ut.String(json["children"]), "|")
		for i, child := range childs {
			comp.AddChild(this.loadNode(child, target, int32(i)))
		}
	} else if node.GetCategory() == DECORATOR {
		deco := node.(IBaseDecorator)
		deco.AddChild(this.loadNode(ut.Int32(json["children"]), target, 0))
	}
	return node
}

func (this *BehaviorTree) Tick(dt int32) {
	if this.root != nil {
		this.root.Execute(dt)
	}
}

func (this *BehaviorTree) IsCanRun() bool {
	return this.root != nil
}

func printNode(root IBaseNode, blk int) {
	for i := 0; i < blk; i++ {
		fmt.Print(" ") //缩进
	}
	fmt.Print("|—", root.GetCategory())
	fmt.Println("")
	if root.GetCategory() == DECORATOR {
		dec := root.(IBaseDecorator)
		if dec.GetChild() != nil {
			printNode(dec.GetChild(), blk+3)
			return
		}
	} else if root.GetCategory() == COMPOSITE {
		comp := root.(IBaseComposite)
		for i, l := 0, comp.GetChildrenCount(); i < l; i++ {
			printNode(comp.GetChild(i), blk+3)
		}
	}
}
