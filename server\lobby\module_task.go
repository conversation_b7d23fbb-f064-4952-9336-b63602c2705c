package lobby

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Lobby) InitHDTask() {
	this.GetServer().RegisterGO("HD_GetTasks", this.getTasks)                             //获取所有任务列表
	this.GetServer().RegisterGO("HD_ClaimGeneralTaskReward", this.claimGeneralTaskReward) //领取任务奖励
	this.GetServer().RegisterGO("HD_ClaimAchieveTaskReward", this.claimAchieveTaskReward) //领取成就任务奖励
}

func (this *Lobby) getTasks(session gate.Session, msg *pb.LOBBY_HD_GETTASKS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	ret, _ = pb.ProtoMarshal(&pb.LOBBY_HD_GETTASKS_S2C{
		GeneralTasks: user.GeneralTasks.ToPb(),
		AchieveTasks: user.AchieveTasks.ToPb(),
	})
	return
}

// 领取常规任务奖励
func (this *Lobby) claimGeneralTaskReward(session gate.Session, msg *pb.LOBBY_HD_CLAIMGENERALTASKREWARD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	if user.IsFinishGeneralTask(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	task := user.GetGeneralTask(id)
	if task == nil {
		return nil, ecode.TASK_NOT_EXIST.String()
	} else if !user.checkTaskCondition(this, task) {
		return nil, ecode.TASK_NOT_COMPLETE.String()
	}
	rewards := array.Map(task.GetRewards(), func(m *g.TypeObj, _ int) *g.TypeObj { return m.Clone() })
	// 这里检测是否有自选英雄 替换为残卷
	if reward := array.Find(rewards, func(m *g.TypeObj) bool { return m.Type == ctype.HERO_OPT }); reward != nil {
		if heroId := msg.GetHeroId(); heroId > 0 && lc.CheckHeroOptId(reward.Id, heroId) {
			reward.Type = ctype.HERO_DEBRIS
			reward.Id = heroId
			reward.Count = lc.PORTRAYAL_COMP_NEED_COUNT //固定3个碎片
		}
	}
	// 发放奖励
	s2c := &pb.LOBBY_HD_CLAIMGENERALTASKREWARD_S2C{
		Rewards: this.ChangeUserItems(user, rewards, constant.GOLD_CHANGE_TASK_GET),
	}
	// 如果是邀请好友 这里还要消耗一个
	cond := task.GetCondInfo()
	if cond.Type == tctype.INVITE_FRIEND {
		user.UseInviteFriend(cond.Count, constant.INVITE_FRIEND_USE_TYPE_TASK)
		s2c.IsInviteFriend = true
		s2c.InviteFriends = user.InveteFriendsToPb()
	}
	// 放入已完成列表 并获取下一个任务
	user.FinishGeneralTask(task)
	// 返回
	s2c.Tasks = user.GeneralTasks.ToPb()
	return pb.ProtoMarshal(s2c)
}

// 领取成就任务奖励
func (this *Lobby) claimAchieveTaskReward(session gate.Session, msg *pb.LOBBY_HD_CLAIMACHIEVETASKREWARD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	if user.IsFinishAchieveTask(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	task := user.GetAchieveTask(id)
	if task == nil {
		return nil, ecode.TASK_NOT_EXIST.String()
	} else if cond := task.GetCondInfo(); cond != nil && !user.checkTaskCondition(this, task) {
		return nil, ecode.TASK_NOT_COMPLETE.String()
	}
	// 发放称号
	titleId := task.GetTitleID()
	user.UnlockTitle(titleId)
	// 放入已完成列表 并获取下一个任务
	user.FinishAchieveTask(task)
	// 上报
	ta.Track(user.SID, user.UID, user.DistinctId, 0, "ta_unlockTtile", map[string]interface{}{"title_id": titleId})
	return pb.ProtoMarshal(&pb.LOBBY_HD_CLAIMACHIEVETASKREWARD_S2C{
		Titles: user.TitlesToPb(),
		Tasks:  user.AchieveTasks.ToPb(),
	})
}
