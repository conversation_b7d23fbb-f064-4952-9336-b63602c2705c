package slg

import (
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
)

// 服务器状态
const (
	SERVER_STATUS_STOP     = iota // 停止运行
	SERVER_STATUS_OPEN            // 开放
	SERVER_STATUS_CREATING        // 创建中
	SERVER_STATUS_APPLYING        // 报名中
	SERVER_STATUS_OPENING         // 开启中
	SERVER_STATUS_DELETE          // 已删除
	SERVER_STATUS_PRE             // 预生成
	SERVER_STATUS_CLOSE    = 100  // 游戏已结束
)

// 服务器区域类型
const (
	SERVER_AREA_CHINA = "china" // 国内
	SERVER_AREA_HK    = "hk"    // 香港
	SERVER_AREA_US    = "us"    // 硅谷
)

// 服务器类型
const (
	MACH_SERVER_TYPE_LOGIN = "login" // 登录服
	MACH_SERVER_TYPE_LOBBY = "lobby" // 大厅服
	MACH_SERVER_TYPE_GAME  = "game"  // 游戏服
	MACH_SERVER_TYPE_MATCH = "match" // 匹配服
	MACH_SERVER_TYPE_HTTP  = "http"  // 工具服
	MACH_SERVER_TYPE_CHAT  = "chat"  // 聊天服
	MACH_SERVER_TYPE_GATE  = "gate"  // 连接服
	MACH_SERVER_TYPE_MAIL  = "mail"  // 邮件服

	MACH_SERVER_TYPE_DEVOLOPMENT = "development" // 开发服
)

// 登录类型
const (
	LOGIN_TYPE_GUEST    = "guest"
	LOGIN_TYPE_WX       = "wx"
	LOGIN_TYPE_ACCOUNT  = "account"
	LOGIN_TYPE_GOOGLE   = "google"
	LOGIN_TYPE_APPLE    = "apple"
	LOGIN_TYPE_FACEBOOK = "facebook"
	LOGIN_TYPE_TWITTER  = "twitter"
	LOGIN_TYPE_LINE     = "line"
)

const (
	LOGIN_ID_TYPE_GUEST    = "guest_id"
	LOGIN_ID_TYPE_WX       = "openid"
	LOGIN_ID_TYPE_ACCOUNT  = "account"
	LOGIN_ID_TYPE_GOOGLE   = "google_openid"
	LOGIN_ID_TYPE_APPLE    = "apple_openid"
	LOGIN_ID_TYPE_FACEBOOK = "facebook_openid"
	LOGIN_ID_TYPE_TWITTER  = "twitter_openid"
	LOGIN_ID_TYPE_LINE     = "line_openid"
)

// 免费头像列表
var FREE_HEAD_ICONS = []string{
	"head_icon_free_001",
	"head_icon_free_002",
	"head_icon_free_003",
	"head_icon_free_004",
	"head_icon_free_005",
	"head_icon_free_006",
	"head_icon_free_007",
	"head_icon_free_008",
}

// 支付平台
const (
	PAY_PLATFORM_NONE   = "none"
	PAY_PLATFORM_WX     = "wx"
	PAY_PLATFORM_QQ     = "qq"
	PAY_PLATFORM_APPLE  = "apple"
	PAY_PLATFORM_GOOGLE = "google"
	PAY_PLATFORM_APP_WX = "app_wx"
)

// 登录平台
const (
	LOGIN_PLATFORM_NONE       = "none"
	LOGIN_PLATFORM_IOS_GLOBAL = "ios_global"
	LOGIN_PLATFORM_IOS_INLAND = "ios_inland"
	LOGIN_PLATFORM_GOOGLE     = "google"
	LOGIN_PLATFORM_TAPTAP     = "taptap"
)

const (
	PAY_SHOP_GOLD_CONFIG_NAME      = "recharge"
	PAY_SHOP_GOLD_CONFIG_PRODUCTID = "product_id"
	PAY_SHOP_GOLD_CONFIG_INGOT     = "ingot"
	PAY_SHOP_GOLD_CONFIG_EXTRA     = "extra"

	PAY_SHOP_GOLD_CONFIG_MONEY_CN = "money_china"
	PAY_SHOP_GOLD_CONFIG_MONEY_HK = "money_en"
)

const (
	DB_COLLECTION_NAME_USER               = "user"
	DB_COLLECTION_NAME_USER_GOLD_RECORD   = "user_gold_record"
	DB_COLLECTION_NAME_USER_INGOT_RECORD  = "user_ingot_record"
	DB_COLLECTION_NAME_USER_ITEM_RECORD   = "user_item_record"
	DB_COLLECTION_NAME_ROOM               = "room"
	DB_COLLECTION_NAME_RECHARGE           = "recharge"
	DB_COLLECTION_NAME_SUBSCRIPTION       = "subscription"
	DB_COLLECTION_NAME_GAME_RECORD        = "game_record"
	DB_COLLECTION_NAME_ACCOUNT_DEL_RECORD = "account_del_record"
	DB_COLLECTION_NAME_GIFT               = "gift"
	DB_COLLECTION_NAME_GIFT_CLAIM         = "gift_claim"
	DB_COLLECTION_NAME_MAIL               = "mail"
	DB_COLLECTION_NAME_USER_MAIL          = "user_mail"
	DB_COLLECTION_NAME_FRIEND_CHAT        = "friend_chat"
	DB_COLLECTION_NAME_GALLERY            = "gallery"
	DB_COLLECTION_NAME_GALLERY_COMMENT    = "gallery_comment"
	DB_COLLECTION_NAME_RECORED_BAN        = "record_ban"
	DB_COLLECTION_NAME_TEAM               = "team"
	DB_COLLECTION_NAME_APPLY_INFO         = "apply_info"
	DB_COLLECTION_NAME_MATCH_INFO         = "match_info"
	DB_COLLECTION_NAME_CUSTOM_ROOM        = "custom_room"
	DB_COLLECTION_NAME_WORKSHOP           = "workshop"
	DB_COLLECTION_NAME_GLOBAL             = "global"
	DB_COLLECTION_NAME_APPLE_TRANSFER     = "apple_transfer"
	DB_COLLECTION_NAME_DC_CODE            = "dc_code"
	DB_COLLECTION_NAME_SKIN_ITEM_TRACK    = "skin_item_track"
	DB_COLLECTION_NAME_SKIN_ITEM_COMPOSE  = "skin_item_compose"
	DB_COLLECTION_NAME_SKIN_ITEM_BAN      = "skin_item_ban"
	DB_COLLECTION_NAME_MONTH_CARD_AWARD   = "month_card_award"
	DB_COLLECTION_NAME_USER_UUID_MAP      = "user_uuid_map"

	DB_COLLECTION_NAME_WORLD       = "world"
	DB_COLLECTION_NAME_GAME_PLAYER = "player"
	DB_COLLECTION_NAME_GAME_CHAT   = "chat"
	DB_COLLECTION_NAME_GAME_BAZAAR = "bazaar"
	DB_COLLECTION_NAME_GAME        = "game"
	DB_COLLECTION_NAME_ALLIANCE    = "alliance"

	DB_COLLECTION_NAME_RECORD_ARMY                = "record_army"
	DB_COLLECTION_NAME_RECORD_BATTLE              = "record_battle"
	DB_COLLECTION_NAME_RECORD_BAZAAR              = "record_bazaar"
	DB_COLLECTION_NAME_RECORD_BATTLE_SCORE        = "record_battle_score"
	DB_COLLECTION_NAME_RECORD_BATTLE_SCORE_PLAYER = "record_battle_score_player"
	DB_COLLECTION_NAME_BAZAAR_RECORD              = "bazaar_record"
	DB_COLLECTION_NAME_REQUEST_LOG                = "request_log"
	DB_COLLECTION_NAME_FRIEND_GIFT                = "record_friend_gift"

	DB_COLLECTION_NAME_GM_MAP        = "mapCol"
	DB_COLLECTION_NAME_GAME_MACHS    = "game_machs"
	DB_COLLECTION_NAME_LOBBY_MACHS   = "lobby_machs"
	DB_COLLECTION_NAME_SUPPORT_MACHS = "support_machs"
	DB_COLLECTION_NAME_MAP_USE       = "mapUseCol"
	DB_COLLECTION_NAME_ROOM_GEN_INFO = "room_gen_info"
)

// 账号删除相关
const (
	LOGOUT_MAX_DAY          = ut.TIME_DAY * 7  // 注销账号需要多少天
	LOGOUT_APPLY_TIME       = ut.TIME_DAY * 7  // 申请注销账号需要多少天
	DEL_USER_NOT_LOGIN_DAYS = ut.TIME_DAY * 90 // 多少天未登录删除账号
)

// 区服类型占位
const (
	ROOM_TYPE_FLAG     = 1000000 // 区服类型占位
	ROOM_SUB_TYPE_FLAT = 100000  // 区服子类型占位
)

// 区服类型
const (
	NORMAL_SERVER_TYPE = iota // 普通服
	ROOKIE_SERVER_TYPE        // 新手服
	RANK_SERVER_TYPE          // 排位服
	CUSTOM_SERVER_TYPE        // 自定义服
)

// 参与匹配的区服类型列表
var MATCH_ROOM_TYPE_LIST = []uint8{NORMAL_SERVER_TYPE, RANK_SERVER_TYPE}

// 区服子类型
const (
	NORMAL_SERVER_SUB_TYPE_STAND = 0 // 普通服 标准
	NORMAL_SERVER_SUB_TYPE_FREE  = 1 // 普通服 自由

	ROOKIE_SERVER_SUB_TYPE_STAND = 0 // 新手服 标准

	RANK_SERVER_SUB_TYPE_STAND = 0 // 排位服 标准
)

// 区服相关
const (
	MACH_MAX_SERVER_COUNT    = 5 // 每个物理机最大游戏服数量
	NORMAL_SERVER_COUNT      = 0 // 普通服 报名中
	NORMAL_SERVER_FREE_COUNT = 0 // 普通服 报名中 自由区
	ROOKIE_SERVER_COUNT      = 1 // 新手服 开启中
	RANK_SERVER_COUNT        = 0 // 排位服 报名中

	ROOKIE_PLAY_MAX_COUNT = 2 // 新手服同时游玩上限
	NORMAL_PLAY_MAX_COUNT = 2 // 普通服同时游玩上限
	RANK_PLAY_MAX_COUNT   = 1 // 排位服同时游玩上限

	SHOW_CLOSE_SERVER_NUM_MAX        = 20 // 客户端显示已关闭区服最大数量
	SHOW_USER_SERVER_RECORD_PAGE_NUM = 20 // 个人记录每页数量
	SHOW_HISTORY_SERVER_PAGE_NUM     = 20 // 历史对局每页数量

	AUTO_DELETE_CLOSE_ROOM_DB_DAYS = 30 // 自动清理已结束X天的游戏服数据

	CUSTOM_ROOM_MAX_DELAY_TIME    = ut.TIME_DAY * 3 // 自定义房间最晚开服时间
	CUSTOM_ROOM_MAX_USER_NUM      = 600             // 自定义房间
	CUSTOM_ROOM_CREATE_INGOT_COST = 2000            // 创建自定义房间花费元宝
)

// 区服报名配置 可后台调整
var (
	SERVER_APPLY_FINISH_NUMBER            = 300                                    // 报名达到该人数时截止
	SERVER_APPLY_OPEN_TIME_START          = 6                                      // 报名完成的服开启时间起始
	SERVER_APPLY_OPEN_TIME_END            = 23                                     // 报名完成的服开启时间结束
	SERVER_APPLY_TO_OPEN_TIME       int64 = 10 * ut.TIME_MINUTE                    // 报名完成的服开启时间倒计时
	SERVER_APPLY_CANCEL_CD          int64 = 1 * ut.TIME_MINUTE                     // 报名取消CD 单位毫秒
	ROOKIE_CAP_MIN                  int32 = 150                                    // 新手服最低容量
	SERVER_APPLY_OPEN_TIME                = 10                                     // 报名完成的服开启时间(整点) 单位小时
	INIT_ROOM_MATCH_TIME            int64 = ut.TIME_DAY * 3                        // 初始匹配时间间隔 3天
	ROOM_MATCH_OPEN_FAIL_DELAY_TIME int64 = ut.TIME_DAY * 1                        // 匹配人数不够延迟开服时间 1天
	ROOKIE_SERVER_OPEN_TIME               = ut.TIME_DAY * 3                        // 新手服开服间隔时间 3天
	ROOKIE_SERVER_OPEN_TIME_PRE           = ROOKIE_SERVER_OPEN_TIME - ut.TIME_HOUR // 新手区检测开服时间 提前一小时检测开服
	SERVER_UPDATE_MATCH_PAUSE_TIME        = ut.TIME_MINUTE * 10                    // 自动停服更新前10分钟暂停匹配
	ROOKIE_SERVER_PRE_NUM           int32 = 10                                     // 新手区提前创建数量
	MATCH_DELAY_WAR_TOKEN_NUM       int32 = 5                                      // 匹配推迟补偿兵符数量

	MATCH_NEW_ROOM_MIN_PLAYER_NUM        = 300 // 匹配创建新区的最小人数
	MATCH_NEW_ROOM_MAX_PLAYER_NUM  int32 = 600 // 匹配创建新区的最大人数
	MATCH_NEW_ROOM_PLAYER_NUM      int32 = 600 // 匹配创建新区人数 按报名人数浮动
	MATCH_NEW_ROOM_PLAYER_NUM_STEP int32 = 100 // 匹配创建新区人数每次浮动人数
	MATCH_LAYER_USER_NUM           int32 = 400 // 匹配分层人数
	MATCH_TEAM_NUM_AVG_SCORE             = 10  // 匹配组队人数平均分加成

	// 匹配区服阶梯人数算法相关参数
	MATCH_BALLENCE_PLAYER_NUM_MIN        int32 = 450 // 区服分配最小人数
	MATCH_BALLENCE_PLAYER_NUM_MAX        int32 = 600 // 区服分配最大人数
	MATCH_BALLENCE_PLAYER_NUM_INIT_PARAM       = 1.0 // 匹配人数阶梯算法初始比例
	MATCH_BALLENCE_PLAYER_NUM_STEP_PARAM       = 0.7 // 匹配人数阶梯算法每阶比例
)

// 匹配状态
const (
	ROOM_MATCH_STATE_APPLY    = iota // 报名中
	ROOM_MATCH_STATE_MATCHING        // 匹配中
	ROOM_MATCH_STATE_CREATING        // 创建房间中
)

// 区分类型名
var SERVER_TYPE_NAME = map[int]map[int]map[string]string{
	0: {
		0: {"cn": "普通", "hk": "普通", "tw": "普通", "en": "Normal "},
		1: {"cn": "自由", "hk": "自由", "tw": "自由", "en": "Free "},
	},
	1: {
		0: {"cn": "新手", "hk": "新手", "tw": "新手", "en": "Newbie "},
	},
	2: {
		0: {"cn": "排位", "hk": "排位", "tw": "排位", "en": "Ranked "},
	},
}

// 账号注销状态
const (
	ACCOUNT_DEL_STATE_DONE  = iota // 已注销
	ACCOUNT_DEL_STATE_APPLY        // 已申请未注销
)

// 大转盘随机奖励
var WHEEL_RANDOM_AWARD = []string{"7,0,2", "9,0,2", "13,0,1", "14,0,1"}

const (
	// 月卡订阅
	// SUB_PRODUCTID_AWARD_WEEK   = "jwmCardWeek"
	SUB_PRODUCTID_AWARD_MOUNTH         = "jwmCardMonth"
	SUB_PRODUCTID_AWARD_QUARTER        = "jwmCardQuarter"
	SUB_PRODUCTID_AWARD_GOOGLE         = "jwm_card"         // google月卡商品id
	SUB_PRODUCTID_AWARD_GOOGLE_MONTH   = "jwm-card-month"   // google月卡基础方案id 按月购买
	SUB_PRODUCTID_AWARD_GOOGLE_QUARTER = "jwm-card-quarter" // google月卡基础方案id 按季购买
	SUB_PRODUCTID_AWARD_ONCE           = "jwm_card_once"    // 一次性购买一个月

	// 超级月卡订阅 （4.0之前线上的订阅自动兼容为超级月卡）
	SUB_PRODUCTID_WEEK             = "jwmAdFreeWeek"
	SUB_PRODUCTID_MONTH            = "jwmAdFreeMonth"
	SUB_PRODUCTID_QUARTER          = "jwmSuperCardQuarter"
	SUB_PRODUCTID_GOOGLE           = "jwm_ad_free"            // google超级月卡商品id
	SUB_PRODUCTID_GOOGLE_WEEK      = "jwm-ad-free-week"       // google超级月卡基础方案id 按周购买
	SUB_PRODUCTID_GOOGLE_MONTH     = "jwm-ad-free-month"      // google超级月卡基础方案id 按月购买
	SUB_PRODUCTID_GOOGLE_QUARTER   = "jwm-ad-free-quarter-f3" // google超级月卡基础方案id 按季购买
	SUB_PRODUCTID_SUPER_AWARD_ONCE = "jwm_super_card_once"    // 一次性购买一个月

	SUB_PRODUCTID_TYPE_WEEK    = "week"    // 周订阅
	SUB_PRODUCTID_TYPE_MONTH   = "month"   // 月订阅
	SUB_PRODUCTID_TYPE_QUARTER = "quarter" // 季度订阅

	BATTLE_PASS_PRODUCT_ID = "jwm_up_book"
)

// 订阅功能类型
const (
	// USER_SUBSCRIBE_TYPE_NO_AD       = "sub_no_ad"            // 免广告
	USER_SUBSCRIBE_TYPE_AWARD       = "sub_month_card"       // 奖励月卡
	USER_SUBSCRIBE_TYPE_SUPER_AWARD = "sub_super_month_card" // 超级奖励月卡
)

// 充值价格配置
var RECHARGE_PRICE_USD_CONFIG = map[string]float32{
	"jwm_ingot_1":         0.99,
	"jwm_ingot_2":         4.99,
	"jwm_ingot_3":         9.99,
	"jwm_ingot_4":         19.99,
	"jwm_ingot_5":         49.99,
	"jwm_ingot_6":         99.99,
	"jwm_up_book":         8.99,  // 宝典
	"jwmCardMonth":        1.99,  // 特惠月卡 月度订阅
	"jwmCardQuarter":      4.99,  // 特惠月卡 季度订阅
	"jwmAdFreeMonth":      6.99,  // 超级月卡（兼容线上的）月度订阅
	"jwmSuperCardQuarter": 19.99, // 超级月卡（兼容线上的）季度订阅
}

// 订阅功能类型map
var USER_SUB_TYPE_MAP = map[string]string{
	SUB_PRODUCTID_AWARD_MOUNTH:  USER_SUBSCRIBE_TYPE_AWARD,
	SUB_PRODUCTID_AWARD_QUARTER: USER_SUBSCRIBE_TYPE_AWARD,
	SUB_PRODUCTID_AWARD_ONCE:    USER_SUBSCRIBE_TYPE_AWARD,

	SUB_PRODUCTID_WEEK:             USER_SUBSCRIBE_TYPE_SUPER_AWARD,
	SUB_PRODUCTID_MONTH:            USER_SUBSCRIBE_TYPE_SUPER_AWARD,
	SUB_PRODUCTID_QUARTER:          USER_SUBSCRIBE_TYPE_SUPER_AWARD,
	SUB_PRODUCTID_GOOGLE:           USER_SUBSCRIBE_TYPE_SUPER_AWARD,
	SUB_PRODUCTID_SUPER_AWARD_ONCE: USER_SUBSCRIBE_TYPE_SUPER_AWARD,
}

// 月卡订阅购买奖励
var SUB_BUY_AWARD_MAP = map[string][]int32{
	USER_SUBSCRIBE_TYPE_AWARD:       {ctype.INGOT, 0, 100},
	USER_SUBSCRIBE_TYPE_SUPER_AWARD: {ctype.INGOT, 0, 400},
}

// 月卡订阅每日奖励
var SUB_DAILY_AWARD_MAP = map[string][][]int32{
	USER_SUBSCRIBE_TYPE_AWARD:       {{ctype.GOLD, 0, 30}},
	USER_SUBSCRIBE_TYPE_SUPER_AWARD: {{ctype.GOLD, 0, 80}, {ctype.WAR_TOKEN, 0, 5}},
}

// 免广告订阅商品id
var SUB_PRODUCTID_MAP = map[string]string{
	SUB_PRODUCTID_GOOGLE:        "",
	SUB_PRODUCTID_WEEK:          SUB_PRODUCTID_TYPE_WEEK,
	SUB_PRODUCTID_MONTH:         SUB_PRODUCTID_TYPE_MONTH,
	SUB_PRODUCTID_QUARTER:       SUB_PRODUCTID_TYPE_QUARTER,
	SUB_PRODUCTID_AWARD_MOUNTH:  SUB_PRODUCTID_TYPE_MONTH,
	SUB_PRODUCTID_AWARD_QUARTER: SUB_PRODUCTID_TYPE_QUARTER,
}

// 免广告订阅商品信息
var SUB_PRODUCT_INFO_MAP = map[string]map[string]float64{
	"jwmAdFreeWeek": {
		"storeId":  201,
		"rmb":      15,
		"rmbFirst": 3,
		"us":       1.99,
		"usFirst":  0.59,
	},
	"jwmAdFreeMonth": {
		"storeId":  202,
		"rmb":      48,
		"rmbFirst": 15,
		"us":       6.99,
		"usFirst":  1.99,
	},
}

// 战令商品信息
var BATTLE_PASS_PRODUCT_INFO = map[string]float64{
	"storeId": 301,
	"rmb":     65,
	"us":      8.99,
}

const (
	// 开服脚本
	OPEN_GAME_SERVER_BASH = "cd /projects/slg-server && /projects/slg-server/update_server_config.sh"
	// 关服脚本
	CLOSE_GAME_SERVER_BASH = " cd /projects/slg-server && /projects/slg-server/close_game_server.sh"
	// 关闭进程脚本
	STOP_SERVER_BASH = "/projects/slg-server/pm2_stop_all.sh"
	// 更新代码脚本
	GIT_UPDATE_BASH = " cd /projects/slg-server && /projects/slg-server/update_git.sh"
	// 开启进程脚本
	START_SERVER_BASH = "/projects/slg-server/pm2_start_all.sh"
	// 查询进程脚本
	SHOW_PID_BASH = "/projects/slg-server/show_pid.sh"
	// 进程目录
	PROCESS_DIR = "/projects/slg-server/bin/server/main"
	// 项目目录
	PROJECT_DIR = "/projects/slg-server"
	// 更新代码脚本
	GIT_UPDATE_SHELL = "/projects/slg-server/update_git.sh"
	// 重启服务器脚本
	RESTART_SERVER_BASH = "pm2 restart all"
	// 清理日志脚本
	CLEAN_LOGS_SHELL = "/projects/slg-server/clean_log.sh"
)

// 物理机时间脚本
const (
	GET_MACH_TIME   = "date \"+%Y-%m-%d %H:%M:%S\"" // 获取时间
	SET_MACH_TIME   = "date -s"                     // 设置时间
	RESET_MACH_TIME = "hwclock --hctosys"           // 还原时间
)

// 物理机相关参数
var (
	CLEAN_LOG_DISK_USAGE_THRESHOLD = "70" // 磁盘清理阈值 百分比
	CLEAN_LOG_DAYS_OLD             = "30" // 磁盘清理多少天以上的文件
)

// 好友
var (
	FRIENDS_MAX_NUM        = 100 // 好友数量上限
	FRIENDS_APPLY_MAX_NUM  = 50  // 被申请好友数量上限
	FRIENDS_MIN_LAND_COUNT = 100 // 可申请添加好友最小地块数
	BLACK_LIST_MAX         = 50  // 黑名单上限
	FRIEND_CHAT_SHOW_MAX   = 50  // 好友聊天显示数量上限
)

// 翻译语言map
var TRANS_LANG_MAP = map[string]string{
	"cn":  "zh-CN",
	"hk":  "zh-HK",
	"tw":  "zh-TW",
	"en":  "en-US",
	"jp":  "ja",
	"kr":  "ko",
	"id":  "id",
	"idl": "id",
	"th":  "th",
	"vi":  "vi",

	"EN": "en-US",
	"JA": "ja",
	"KO": "ko",
	"ID": "id",
	"TH": "th",
}

// 术语表支持语言map
var GLOSSARY_LANG_MAP = map[string]bool{
	"zh-CN": true,
	"zh-HK": true,
	"zh-TW": true,
	"en-US": true,
	"ja":    true,
	"ko":    true,
	"id":    true,
	"th":    true,
}

// 语言频道map
var LANG_CHANNEL_MAP = map[string]string{
	"cn":  "zh",
	"hk":  "zh",
	"tw":  "zh",
	"en":  "en",
	"jp":  "jp",
	"kr":  "kr",
	"id":  "idl",
	"idl": "idl",
	"th":  "th",
	"vi":  "vi",
}

const (
	BATTLE_FORECAST_COST       = 30 // 战斗预测费用
	BATTLE_FORECAST_FREE_COUNT = 5  // 战斗免费次数

	SEND_TRUMPET_COST     = 50 // 发送喇叭费用
	SEND_TRUMPET_ACC_COST = 25 // 每次累计花费
)

// 物理机状态
const (
	MACH_STATUS_OPEN     = 0 // 开启
	MACH_STATUS_STOPPING = 1 // 停机中
	MACH_STATUS_STOPPED  = 2 // 已停机
	MACH_STATUS_CLOSING  = 3 // 关闭中
	MACH_STATUS_CLOSE    = 4 // 已关闭
	MACH_STATUS_OPENNING = 5 // 开启中

	MACH_UPDATE_STATUS_UPDATED  = 0 // 已更新
	MACH_UPDATE_STATUS_UPDATING = 1 // 代码更新中
)

// 胜利条件
var (
	WIN_COND_LAND    = []int32{1, 30000, 12, ut.TIME_DAY * 1} // 领地争夺
	WIN_COND_ANCIENT = []int32{2, 20, 12, ut.TIME_DAY * 1}    // 修建遗迹
	WIN_COND_CONQUER = []int32{3, 1, 12, ut.TIME_DAY * 1}     // 血战到底
)

// 自动更新任务状态
const (
	UPDATE_TASK_IDLE         = 0  // 任务未开始
	UPDATE_TASK_UPDATE_CODE  = 1  // 更新代码
	UPDATE_TASK_GAME_STOP    = 2  // 游戏服停机
	UPDATE_TASK_LOBBY_STOP   = 3  // 大厅服停机
	UPDATE_TASK_GAME_CLOSE   = 4  // 游戏服关闭
	UPDATE_TASK_LOBBY_CLOSE  = 5  // 大厅服关闭
	UPDATE_TASK_SUP_CLOSE    = 6  // 辅助服关闭
	UPDATE_TASK_CLOSE_FINISH = 7  // 停机完成
	UPDATE_TASK_LOBBY_OPEN   = 8  // 大厅服开启
	UPDATE_TASK_GAME_OPEN    = 9  // 游戏服开启
	UPDATE_TASK_SUP_OPEN     = 10 // 辅助服开启
	UPDATE_TASK_FINISH       = 11 // 任务完成
)

const (
	AUTO_UPDATE_EXTRA_TIME        = ut.TIME_MINUTE * 10 // 自动停机更新冗余时间
	AUTO_UPDATE_HANDLE_EXTRA_TIME = ut.TIME_SECOND * 10 // 自动停机更新单个阶段冗余时间
)

// 订阅上报状态
const (
	TA_SUB_STATE_NONE     = "none"
	TA_SUB_STATE_FREE     = "free"
	TA_SUB_STATE_PAY      = "pay"
	TA_SUB_STATE_RENEWAL  = "renewal"
	TA_SUB_STATE_DISCOUNT = "discount"
)

// 登录相关
const (
	LOGIN_TOKEN_EXPIRE_TIME = ut.TIME_DAY * 30
)

// 组队相关
var (
	TEAM_USER_NUM_MAX   = 40 // 组队人数上限
	TEAM_CHAT_NUM_MAX   = 50 // 组队聊天数量上限
	TEAM_RANK_LEVEL_MAX = 9  // 组队最大段位差
)

const (
	// 组队系统消息
	TEAM_MSG_TYPE_JOIN         = "ui.team_msg_01" //{0}加入队伍
	TEAM_MSG_TYPE_EXIT         = "ui.team_msg_02" //{0}离开队伍
	TEAM_MSG_TYPE_KICK         = "ui.team_msg_03" //{0}被踢出队伍
	TEAM_MSG_TYPE_REFUSE       = "ui.team_msg_04" //{0}拒绝了邀请
	TEAM_MSG_TYPE_CHANGE_MODE  = "ui.team_msg_05" // 对局类型改为{0}
	TEAM_MSG_TYPE_INVITE       = "ui.team_msg_06" //{0}邀请了{1}
	TEAM_MSG_TYPE_APPLY        = "ui.team_msg_07" // 报名了{0}
	TEAM_MSG_TYPE_CANCEL_APPLY = "ui.team_msg_08" //{0}取消了报名

	TEAM_MSG_CHANGE_MODE_PARAM = "ui.title_server_name_"
)

// 游戏模式状态
const (
	MODE_INFO_STATE_NOT_APPLY = iota
	MODE_INFO_STATE_IN_MATCH
	MODE_INFO_STATE_IN_GAME
	MODE_INFO_STATE_CLOSE    // 维护中
	MODE_INFO_STATE_NOT_OPEN // 未开放
)

// 邮件类型id
const (
	MAIL_DELAY_MATCH_ID                   = 100016 // 延迟匹配
	MAIL_GAME_SETTLEMENT_ID               = 100017 // 对局结算 发放兵符
	MAIL_GAME_DC_GIFT_ID                  = 100022 // dc关注有礼
	MAIL_RANK_SEASON_REWARD_ID            = 100023 // 赛季发放邮件
	MAIL_ANTI_CHEAT_REWARD_ID             = 100024 // 防作弊检测奖励邮件
	MAIL_ALLI_BUILD_CANCEL_ID             = 100025 // 大使馆取消补偿邮件
	MAIL_BATTLE_PASS_TIMEOUT_ID           = 100026 // 战令过期奖励邮件
	MAIL_GAME_SETTLEMENT_NEWBIE_REPEAT_ID = 100027 // 对局结算 发放兵符(新手区已通关减半)
	MAIL_HOSPITAL_ALMOST_FULL_ID          = 100028 // 医馆容量快满邮件
	MAIL_HOSPITAL_FULL_ID                 = 100029 // 医馆容量已满邮件
	MAIL_BAN_SKIN_ID                      = 100030 // 皮肤禁用邮件
	MAIL_OCCUPY_ROBBERY_ID                = 100031 // 攻占掠夺资源邮件
	MAIL_POLICY_SEASON_ITEM_ID            = 100032 // 政策季节资源邮件
	MAIL_POLICY_ADD_ITEM_ID               = 100033 // 政策添加资源邮件
	MAIL_MONTH_CARD_AWARD_ID              = 100034 // 月卡奖励补发邮件
	MAIL_WHEEL_REWARD_FIX_ID              = 100035 // 大转盘奖励兼容邮件
	MAIL_GAME_SETTLEMENT_NO_REWARD_ID     = 100036 // 对局结算 无奖励邮件
	MAIL_SUPER_MONTH_CARD_BUY_AWARD_ID    = 100037 // 超级月卡购买奖励邮件
	MAIL_MONTH_CARD_BUY_AWARD_ID          = 100038 // 月卡购买奖励邮件
)

// 观战相关
const (
	SPECTATE_USER_NUM_MAX = 100 // 观战玩家数量上限
)

// 邮件有效期
const (
	NORMAL_MAIL_EXPIR_TIME = ut.TIME_DAY * 30 // 普通邮件有效期
	ITEM_MAIL_EXPIR_TIME   = ut.TIME_DAY * 60 // 物品邮件有效期
)

// 玩家状态
const (
	USER_STATE_OFFLINE  = iota // 离线
	USER_STATE_LOGIN           // 登陆中
	USER_STATE_ONLINE          // 在线
	USER_STATE_MALLOC          // 离线分配大厅服中
	USER_STATE_IN_CACHE        // 离线但在内存中
)

// 登录限制类型
const (
	LOGIN_LIMIT_TYPE_NULL       = iota // 无限制
	LOGIN_LIMIT_TYPE_WHITE_LIST        // 仅白名单可登录
	LOGIN_LIMIT_TYPE_BLACK_LIST        // 黑名单不可登录
)

// 新手区相关配置
var (
	NEWBIE_PASS_SCORE           int32 = 1000 // 新手区通关分数 1000
	NEWBIE_PASS_RANK            int32 = 100  // 新手区通关排名 100
	NEWBIE_PASS_MIN_SCORE       int32 = 500  // 新手区通关最低分数 500
	NEWBIE_PASS_LAND            int32 = 1000 // 达到多少地块数通关新手区
	NEWBIE_MAX_COUNT            int32 = 5    // 新手区次数上限 包含放弃
	NEWBIE_PASSED_REWARD_REDUCE       = 0.5  // 新手区通关后再玩的奖励系数
)

// AF相关配置
var (
	AF_OPEN = true
)

// DH相关配置
var (
	HD_LOG_OPEN = true
)

// 皮肤礼盒价格
var (
	NORMAL_SKIN_GIFT_BOX_PRICE int32 = 1   // 普通皮肤礼盒价格
	RARE_SKIN_GIFT_BOX_PRICE   int32 = 100 // 隐藏皮肤礼盒价格

	SEND_GIFT_FRIEND_TIME = ut.TIME_DAY * 20 // 赠送礼物需要成为好友时间

	SEND_NORMAL_SKIN_GIFT_TIME_LIMIT int64 = ut.TIME_DAY * 1  // 赠送自己购买普通皮肤需要持有的时间
	SEND_RARE_SKIN_GIFT_TIME_LIMIT   int64 = ut.TIME_DAY * 30 // 赠送自己购买稀有皮肤需要持有的时间

	FRIEND_GIFT_RECORD_PAGE_NUM int32 = 20 // 好友礼物记录查询每页数量
)

// DC关注有礼奖励
var DC_FOLLOW_GIFT = map[int32]int32{
	5:  50, // 金币50
	23: 15, // 兵符15
}

// 封禁类型
const (
	BAN_TYPE_CHAT    = iota // 禁言 0
	BAN_TYPE_ACCOUNT        // 账号封禁 1
	BAN_TYPE_CHEAT          // 作弊封禁 2
	BAN_TYPE_REFUND         // 恶意退款 3
)

// 战令配置
var (
	BATTLE_PASS_TODAY_SCORE_MAX int32 = 100  // 每天任务积分上限
	BATTLE_PASS_BUY_SCORE_COUNT int32 = 1    // 每天购买积分次数上限
	BATTLE_PASS_BUY_SCORE_ADD   int32 = 100  // 购买积分添加的积分数量
	BATTLE_PASS_BUY_SCORE_PRICE int32 = 50   // 购买积分价格
	BATTLE_PASS_SCORE_MAX       int32 = 1500 // 积分上限
	BATTLE_PASS_BUY_GIVE_SCORE  int32 = 300  // 购买进阶奖励赠送积分

	BATTLE_PASS_BUY_SCORE_KILL_MONSTER int32 = 1 // 击杀野怪积分
	BATTLE_PASS_BUY_SCORE_KILL_PAWN    int32 = 1 // 击杀士兵积分
	BATTLE_PASS_BUY_SCORE_OCCUPY       int32 = 1 // 占领地块积分
)

const (
	KICK_NOTIFY_TYPE_NONE  = -1 // 不通知
	KICK_NOTIFY_TYPE_OTHER = 0  // 挤号
	KICK_NOTIFY_TYPE_BAN   = 1  // 封停
	KICK_NOTIFY_TYPE_GM    = 2  // 后台踢人
)

// 公告id配置
const (
	PLAYER_OCCUPY_NOTICE_ID   = 10001 // 攻占玩家主城公告id
	EXCLUSION_EQUIP_NOTICE_ID = 10002 // 专属打造公告id
	TRAIN_PAWN_NOTICE_ID      = 10003 // 训练士兵公告id
	ANCIENT_OCCUPY_NOTICE_ID  = 10004 // 古城攻占公告id
	ANCIENT_LV_UP_NOTICE_ID   = 10005 // 古城升级公告id
	OCCUPY_HELL2_NOTICE_ID    = 10006 // 首次攻占地狱2公告id
	ANCIENT_CREATE_NOTICE_ID  = 10007 // 古城出现公告id

	UPDATE_SERVER_NOTICE_ID  = 200001 // 停机维护公告id
	STOP_SERVER_NOTICE_ID    = 200002 // 停服公告id
	WHEEL_MUL_NOTICE_ID      = 200003 // 大转盘倍数公告id
	COMPOSE_HERO_NOTICE_ID   = 200004 // 修补出天选公告id
	RECOMPOSE_HERO_NOTICE_ID = 200005 // 重绘出天选公告id
	RARE_SKIN_NOTICE_ID      = 200006 // 抽到隐藏皮肤公告id
	POINT_SETS_HERO_MUL_27   = 200007 // 27倍残卷id
	POINT_SETS_HERO_MUL_81   = 200008 // 81倍残卷id
)

// 动态资源相关
var (
	PAWN_COST_LV_LIST    = []int32{1, 2, 4, 6, 8, 10} // 招募动态资源每级系数
	PAWN_COST_LIMIT_LIST = []int32{150, 350}          // 动态资源上下限
	PAWN_COST_TIME_LIST  = []int32{150, 350}          // 动态时间上下限

	PAWN_COST_CAL_MUL          = 2  // 动态资源统计倍率
	PAWN_COST_VALID_ROOM_COUNT = 20 // 动态资源区服采样数量
)

// 英雄相关
const (
	PORTRAYAL_HISTORY_MAX_COUNT = 6 // 画像历史记录上限
)
