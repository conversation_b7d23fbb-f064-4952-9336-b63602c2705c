package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 战令
type BattlePass struct {
	deadlock.RWMutex `bson:"-"`
	Rewarded         []int32                `bson:"rewarded"`     //普通已领奖
	RewardedPay      []int32                `bson:"rewarded_pay"` //付费已领奖
	PayRecords       []*BattlePassPayRecord `bson:"pay_records"`  //购买记录列表

	Id            int32 `bson:"id"`              //id
	Score         int32 `bson:"score"`           //积分
	TodayScore    int32 `bson:"today_score"`     //当天获得积分
	BuyScoreCount int32 `bson:"buy_score_count"` //当天购买积分次数

	BuyPass              bool `bson:"buy_pass"`      //是否购买付费档
	TimeoutCheck         bool `bson:"timeout_check"` //过期检测
	tempIsNotifyHasAward bool //是否通知有奖励了
}

// 战令购买记录
type BattlePassPayRecord struct {
	Id    int32 `bson:"id"`    //id
	State int32 `bson:"state"` //状态
}

// 战令购买状态
const (
	BATTLE_PASS_PAY_STATE_BUY    = iota //已购买未领取
	BATTLE_PASS_PAY_STATE_REWARD        //已购买已领取
	BATTLE_PASS_PAY_STATE_REFUND        //已领取已退款
)

func NewBattlePass() *BattlePass {
	battlePass := &BattlePass{}
	battlePass.SeasonCheck(true)
	return battlePass
}

// 购买战令
func (this *BattlePass) BuyBattlePass(uid string) {
	log.Info("BuyBattlePass uid: %v", uid)
	this.Lock()
	defer this.Unlock()
	isTimeout := !this.SeasonCheck(false)
	payRefund := false
	if this.PayRecords == nil {
		this.PayRecords = []*BattlePassPayRecord{}
	} else {
		// 尝试补齐退款
		for _, v := range this.PayRecords {
			if v.State == BATTLE_PASS_PAY_STATE_REFUND {
				v.State = BATTLE_PASS_PAY_STATE_REWARD
				payRefund = true
				if v.Id == this.Id {
					// 补齐的是当前版本的战令
					this.BuyPass = true
					this.Score += slg.BATTLE_PASS_BUY_GIVE_SCORE
				}
				log.Info("BuyBattlePass pay refund uid: %v", uid)
				break
			}
		}
	}
	if !payRefund {
		// 没有需要补齐的退款 则为购买当前版本的战令
		if isTimeout {
			// 战令过期
			log.Warning("BuyBattlePass season timeout uid: %v", uid)
			return
		}
		isrepeat := false
		if array.Some(this.PayRecords, func(m *BattlePassPayRecord) bool { return m.Id == this.Id }) {
			// 重复购买
			log.Warning("BuyBattlePass reapeat id: %v uid: %v", this.Id, uid)
			isrepeat = true
		}
		// 添加购买记录 重复购买也添加到记录中
		this.PayRecords = append(this.PayRecords, &BattlePassPayRecord{Id: this.Id, State: BATTLE_PASS_PAY_STATE_BUY})
		this.BuyPass = true
		// 购买就送300
		if !isrepeat {
			this.Score += slg.BATTLE_PASS_BUY_GIVE_SCORE
		}
		log.Info("BuyBattlePass finish uid: %v", uid)
	}
}

// 增加积分
func (this *BattlePass) AddScore(score int32) {
	this.Lock()
	defer this.Unlock()
	if score <= 0 {
		return
	}
	if !this.SeasonCheck(false) {
		return
	}
	if this.TodayScore >= slg.BATTLE_PASS_TODAY_SCORE_MAX {
		// 已达到当天积分上限
		return
	}
	if this.Score >= slg.BATTLE_PASS_SCORE_MAX {
		// 积分上限
		return
	}
	if this.TodayScore+score > slg.BATTLE_PASS_TODAY_SCORE_MAX {
		score = slg.BATTLE_PASS_TODAY_SCORE_MAX - this.TodayScore
	}
	this.Score += score
	this.TodayScore += score
}

// 购买积分
func (this *BattlePass) BuyScore(uid string) string {
	this.Lock()
	defer this.Unlock()
	if !this.SeasonCheck(false) {
		log.Error("BuyScore season err uid: %v", uid)
		// 战令过期
		return ecode.BATTLE_PASS_TIMEOUT.String()
	}
	if this.BuyScoreCount >= slg.BATTLE_PASS_BUY_SCORE_COUNT {
		log.Error("BuyScore count limit count: %v, uid: %v", this.BuyScoreCount, uid)
		// 当天次数上限
		return ecode.BATTLE_PASS_BUY_SCORE_LIMIT.String()
	}
	if this.Score >= slg.BATTLE_PASS_SCORE_MAX {
		log.Error("BuyScore score max count: %v, uid: %v", this.Score, uid)
		// 积分已达到上限
		return ecode.BATTLE_PASS_BUY_SCORE_LIMIT.String()
	}
	this.Score += slg.BATTLE_PASS_BUY_SCORE_ADD
	this.BuyScoreCount++
	return ""
}

// 检测是否可以领取了 用于通知
func (this *BattlePass) CheckCanClaimAward() bool {
	if this.tempIsNotifyHasAward || this.Score >= slg.BATTLE_PASS_SCORE_MAX {
		return false
	}
	this.RLock()
	defer this.RUnlock()
	confDatas := config.GetJson("battlePass").Datas
	id := this.getNotClaimId(this.Rewarded, confDatas)
	if this.BuyPass {
		id = ut.MinInt32(id, this.getNotClaimId(this.RewardedPay, confDatas))
	}
	json := config.GetJsonData("battlePass", id)
	this.tempIsNotifyHasAward = json != nil && this.Score >= ut.Int32(json["score"])
	return this.tempIsNotifyHasAward
}

func (this *BattlePass) getNotClaimId(list []int32, confDatas []map[string]interface{}) int32 {
	obj := map[int32]bool{}
	for _, m := range list {
		obj[m] = true
	}
	for _, data := range confDatas {
		id := ut.Int32(data["id"])
		if id/1000 != this.Id {
			continue
		}
		if !obj[id] {
			return id
		}
	}

	return 0
}

// 期数更新数据重置
func (this *BattlePass) SeasonReset(id int32) {
	this.Id = id
	this.Score = 0
	this.TodayScore = 0
	this.BuyScoreCount = 0
	this.BuyPass = false
	this.Rewarded = []int32{}
	this.RewardedPay = []int32{}
	this.TimeoutCheck = false
}

// 过期检测
func (this *BattlePass) SeasonTimeoutCheck(userId string) {
	this.Lock()
	defer this.Unlock()
	curId := GetCurBattlePassId()
	if this.Id != curId {
		log.Info("SeasonTimeoutCheck 11 uid: %v", userId)
		// 期数不一致 发放上一期未领取奖励
		this.TimeoutRewardSend(userId)
		if curId > 0 {
			// 重置数据
			this.SeasonReset(curId)
		}
	}
}

// 检测期数和时间
func (this *BattlePass) SeasonCheck(lock bool) bool {
	if lock {
		this.Lock()
		defer this.Unlock()
	}
	curId := GetCurBattlePassId()
	if this.Id != curId {
		// 期数不一致 重置数据
		if curId > 0 {
			this.SeasonReset(curId)
		}
	}
	return curId != 0
}

// 领取奖励
func (this *BattlePass) GetReward(id int32, isPay bool, heros []int32) (items []*g.TypeObj, err string) {
	this.Lock()
	defer this.Unlock()
	if !this.SeasonCheck(false) {
		// 战令过期
		return nil, ecode.BATTLE_PASS_TIMEOUT.String()
	}
	if isPay && !this.BuyPass {
		// 战令未购买
		return nil, ecode.BATTLE_PASS_NOT_BUY.String()
	}
	season := id / 1000
	if season != this.Id {
		// 奖励id不是当前战令
		return nil, ecode.BATTLE_PASS_TIMEOUT.String()
	}
	cfgJson := config.GetJson("battlePass")
	cfg := cfgJson.GetById(id)
	if cfg == nil {
		return nil, ecode.UNKNOWN.String()
	}
	if this.Score < ut.Int32(cfg["score"]) {
		// 积分不足
		return nil, ecode.BATTLE_PASS_SCORE_NOT_ENOUGH.String()
	}
	if (!isPay && array.Has(this.Rewarded, id)) || (isPay && array.Has(this.RewardedPay, id)) {
		// 已领取
		return nil, ecode.YET_CLAIM.String()
	}
	var rewardCfg interface{}
	if !isPay {
		// 免费奖励
		rewardCfg = cfg["reward_1"]
		this.Rewarded = append(this.Rewarded, id)
	} else {
		// 付费奖励
		rewardCfg = cfg["reward_2"]
		if len(this.RewardedPay) == 0 {
			this.updatePayRecord(this.Id, BATTLE_PASS_PAY_STATE_REWARD) //没领过则更新购买状态
		}
		this.RewardedPay = append(this.RewardedPay, id)
	}
	items = g.StringToTypeObjs(rewardCfg)
	for _, item := range items {
		if item.Type == ctype.HERO_OPT {
			if heros == nil || len(heros) == 0 {
				return nil, ecode.HERO_NOT_EXIST.String() //未选择自选英雄
			}
			if !lc.GetItemByOptHero(item, heros[0]) {
				return nil, ecode.HERO_NOT_EXIST.String() //未选择自选英雄
			}
		}
	}
	this.tempIsNotifyHasAward = false
	return items, ""
}

// 一键领取 指定一键领取免费或付费奖励
func (this *BattlePass) GetAllReward(heros []int32, isPay bool) (items []*g.TypeObj, err string) {
	this.Lock()
	defer this.Unlock()
	if !this.SeasonCheck(false) {
		// 战令过期
		return nil, ecode.BATTLE_PASS_TIMEOUT.String()
	}
	if isPay && !this.BuyPass {
		// 战令未购买
		return nil, ecode.BATTLE_PASS_NOT_BUY.String()
	}
	items = this.GetRewards(heros, isPay)
	this.tempIsNotifyHasAward = false
	return items, ""
}

// 一键领取奖励 (外部加锁)
func (this *BattlePass) GetRewards(heros []int32, isPay bool) (items []*g.TypeObj) {
	if isPay && !this.BuyPass {
		// 战令未购买
		return
	}
	cfgJson := config.GetJson("battlePass").Datas
	items = []*g.TypeObj{}
	var optHeroIndex int32
	for _, v := range cfgJson {
		score := ut.Int32(v["score"])
		if this.Score < score {
			continue
		}
		id := ut.Int32(v["id"])
		season := id / 1000
		if id < 1000 {
			// 第一期id小于1000
			season = 1
		}
		if season != this.Id {
			continue
		}
		if (!isPay && array.Has(this.Rewarded, id)) || (isPay && array.Has(this.RewardedPay, id)) {
			continue
		}
		var addItems []*g.TypeObj
		if !isPay {
			addItems = g.StringToTypeObjs(v["reward_1"])
		} else {
			addItems = g.StringToTypeObjs(v["reward_2"])
		}
		// 自选英雄检测
		optHeroIndex = itemsHeroOptCheck(addItems, heros, optHeroIndex)
		items = append(items, addItems...)
		if !isPay {
			this.Rewarded = append(this.Rewarded, id)
		} else {
			if len(this.RewardedPay) == 0 {
				this.updatePayRecord(this.Id, BATTLE_PASS_PAY_STATE_REWARD) //没领过则更新购买状态
			}
			this.RewardedPay = append(this.RewardedPay, id)
		}
	}
	return
}

// 战令退款 返回true为已领取过付费档奖励 需要封号
func (this *BattlePass) PayRefund(uid string) bool {
	this.Lock()
	defer this.Unlock()
	needBan := false
	if this.PayRecords == nil || len(this.PayRecords) == 0 {
		log.Warning("PayRefund PayRecords nil uid: %v", uid)
		return needBan
	}
	// 先检查是否有重复的 优先退重复的
	var lastId, lastIndex int32 = -1, -1
	for i := len(this.PayRecords) - 1; i >= 0; i-- {
		if this.PayRecords[i].Id == lastId {
			this.PayRecords = append(this.PayRecords[:lastIndex], this.PayRecords[lastIndex+1:]...)
			return needBan
		}
		lastId = this.PayRecords[i].Id
		lastIndex = int32(i)
	}
	lastRecord := this.PayRecords[len(this.PayRecords)-1]
	if lastRecord.Id == this.Id && lastRecord.State != BATTLE_PASS_PAY_STATE_REFUND {
		// 优先退款当前版本的战令
		if lastRecord.State == BATTLE_PASS_PAY_STATE_BUY {
			// 未领取 直接从记录中删除
			array.Delete(this.PayRecords, func(m *BattlePassPayRecord) bool { return m.Id == this.Id })
		} else if lastRecord.State == BATTLE_PASS_PAY_STATE_REWARD {
			// 已领取 改为退款状态
			lastRecord.State = BATTLE_PASS_PAY_STATE_REFUND
			needBan = true
		}
		this.BuyPass = false
		// 扣除赠送经验
		this.Score -= slg.BATTLE_PASS_BUY_GIVE_SCORE
	} else {
		for _, v := range this.PayRecords {
			if v.State == BATTLE_PASS_PAY_STATE_REWARD {
				v.State = BATTLE_PASS_PAY_STATE_REFUND
				// 已领取 改为退款状态
				needBan = true
			}
		}
	}
	return needBan
}

// 更新购买记录
func (this *BattlePass) updatePayRecord(id, state int32) {
	for _, v := range this.PayRecords {
		if v.Id == id {
			v.State = state
			break
		}
	}
}

// 自选英雄检测
func itemsHeroOptCheck(items []*g.TypeObj, heros []int32, optHeroIndex int32) int32 {
	for _, item := range items {
		if item.Type == ctype.HERO_OPT {
			var heroId int32
			if heros == nil || int32(len(heros)) <= optHeroIndex {
				heroId = lc.GetRandomHeroIdByLv(item.Id) // 没有则随机
			} else {
				heroId = heros[optHeroIndex]
			}
			if !lc.GetItemByOptHero(item, heroId) {
				// 根据id获取英雄失败 则随机id
				heroId = lc.GetRandomHeroIdByLv(item.Id)
				// 再次转换为碎片
				lc.GetItemByOptHero(item, heroId)
			}
			optHeroIndex++
		}
	}
	return optHeroIndex
}

// 过期发放奖励 (外部加锁)
func (this *BattlePass) TimeoutRewardSend(userId string) {
	if this.TimeoutCheck {
		return
	}
	log.Info("TimeoutRewardSend userId: %v, this.TimeoutCheck: %v", userId, this.TimeoutCheck)
	this.TimeoutCheck = true
	// 获取免费奖励
	items := this.GetRewards(nil, false)
	if this.BuyPass {
		payItems := this.GetRewards(nil, true)
		if payItems != nil && len(payItems) > 0 {
			items = append(items, payItems...)
		}
	}
	if len(items) > 0 {
		rewards := []*g.TypeObj{}
		// 合并同类型奖励
		for _, v := range items {
			rewards = g.MergeTypeObjsCount(rewards, v)
		}
		// 邮件发放奖励
		lobbyModule.sendMailItemOne(0, slg.MAIL_BATTLE_PASS_TIMEOUT_ID, "", "", "-1", userId, rewards)
	}
}

func (this *BattlePass) ToPb() *pb.BattlePassInfo {
	this.RLock()
	defer this.RUnlock()
	return &pb.BattlePassInfo{
		Id:            this.Id,
		Score:         this.Score,
		BuyPass:       this.BuyPass,
		TodayScore:    this.TodayScore,
		BuyScoreCount: this.BuyScoreCount,
		Rewarded:      array.Clone(this.Rewarded),
		RewardedPay:   array.Clone(this.RewardedPay),
	}
}

func (this *BattlePass) Clone() *BattlePass {
	this.RLock()
	defer this.RUnlock()
	return &BattlePass{
		Id:            this.Id,
		Score:         this.Score,
		BuyPass:       this.BuyPass,
		TodayScore:    this.TodayScore,
		BuyScoreCount: this.BuyScoreCount,
		Rewarded:      array.Map(this.Rewarded, func(m int32, _ int) int32 { return m }),
		RewardedPay:   array.Map(this.RewardedPay, func(m int32, _ int) int32 { return m }),
		PayRecords:    array.Map(this.PayRecords, func(m *BattlePassPayRecord, _ int) *BattlePassPayRecord { return m }),
	}
}

// 第二天
func (this *BattlePass) NextDay() {
	this.Lock()
	defer this.Unlock()
	this.TodayScore = 0
	this.BuyScoreCount = 0
}

// 获取当前战令id
func GetCurBattlePassId() int32 {
	serverArea := ut.If(slg.IsDebug(), "test", slg.SERVER_AREA)
	configDatas := config.GetJson("battlePassBase")
	for _, v := range configDatas.Datas {
		limitTimeArr := strings.Split(ut.String(v["limit_time_"+serverArea]), "|")
		if len(limitTimeArr) == 2 && helper.CheckActivityAutoDate(limitTimeArr[0], limitTimeArr[1]) {
			return ut.Int32(v["id"])
		}
	}
	return 0
}

//todo toDb
// func (this *BattlePass) ToDb() *pb.BattlePassInfo {
// 	this.RLock()
// 	defer this.RUnlock()
// 	return &pb.BattlePassInfo{
// 		Id:            int32(this.Id),
// 		Score:         int32(this.Score),
// 		BuyPass:       this.BuyPass,
// 		TodayScore:    int32(this.TodayScore),
// 		BuyScoreCount: int32(this.BuyScoreCount),
// 		Rewarded:      pb.IntArrayToInt32(this.Rewarded),
// 		RewardedPay:   pb.IntArrayToInt32(this.RewardedPay),
// 	}
// }
