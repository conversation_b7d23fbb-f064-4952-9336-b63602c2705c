package lobby

import (
	"errors"
	slg "slgsrv/server/common"
	"slgsrv/server/common/af"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"
	"strings"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) InitHDRecharge() {
	this.GetServer().RegisterGO("HD_CreatePayOrder", this.createOrder)                //创建订单
	this.GetServer().RegisterGO("HD_VerifyPayOrder", this.verifyOrder)                //验证订单
	this.GetServer().RegisterGO("HD_GetPayRewards", this.getPayRewards)               //领取订单物品
	this.GetServer().RegisterGO("HD_CreateSubOrder", this.createSubOrder)             //创建订阅订单
	this.GetServer().RegisterGO("HD_VerifySubOrder", this.verifySubOrder)             //验证订阅订单
	this.GetServer().RegisterGO("HD_SubOrderCheck", this.subOrderCheck)               //获取验证成功的订阅
	this.GetServer().RegisterGO("HD_GetUserSubInfo", this.getUserSubInfo)             //获取玩家的订阅
	this.GetServer().RegisterGO("HD_CheckInvalidSubOrder", this.checkInvalidSubOrder) //检测订阅是否失效
	this.GetServer().RegisterGO("HD_GetMonthCardAward", this.getMonthCardAward)       //领取月卡每日奖励
}

func (this *Lobby) createOrder(session gate.Session, msg *pb.LOBBY_HD_CREATEPAYORDER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	productId, platform := msg.GetProductId(), msg.GetPlatform()
	orderInfo, e := recharge.RechargeDb.CreateOrder(user.UID, productId, platform)
	if e != "" {
		log.Error("createOrder order fail userId: %v, uid: %v, err: %v", user.UID, e)
		err = ecode.DB_ERROR.String()
		return
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_CREATEPAYORDER_S2C{Uid: orderInfo.UID})
}

func (this *Lobby) verifyOrder(session gate.Session, msg *pb.LOBBY_HD_VERIFYPAYORDER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	uid, orderId, platform, token, price, purchaseTime := msg.GetCpOrderId(), msg.GetOrderId(), msg.GetPlatform(), msg.GetToken(), msg.GetPrice(), msg.GetPurchaseTime()
	productId := msg.GetProductId()
	currencyType, payAmount := msg.GetCurrencyType(), msg.GetPayAmount()
	var orderInfo recharge.RechargeOrderData
	var e string
	quantity := 1 //购买数量默认为1
	log.Info("verifyOrder userId: %v, uid: %v, orderId: %v, platform: %v, productId: %v", user.UID, uid, orderId, platform, productId)
	if slg.SUB_PRODUCT_INFO_MAP[productId] != nil {
		// 该订单是订阅
		return nil, ecode.ORDER_VERIFY_PARAM_ERROR.String()
	}
	if uid != "" {
		// uid不为空通过内部订单号查找
		orderInfo, e = recharge.RechargeDb.FindByUid(uid)
		if e != "" {
			//未查询到订单
			log.Error("verifyOrder order not find userId: %v, uid: %v, err: %v", user.UID, uid, e)
			err = ecode.DB_ERROR.String()
			return
		}
	} else {
		// uid为空则创建订单
		orderData, e := recharge.RechargeDb.CreateOrder(user.UID, productId, platform)
		if e != "" {
			//创建订单错误
			log.Error("verifyOrder FindByUserNotVerifyOrder create err userId: %v, uid: %v, err: %v", user.UID, uid, e)
			err = ecode.DB_ERROR.String()
			return
		}
		orderInfo = *orderData
	}
	if orderInfo.OrderId != "" && orderInfo.OrderId != orderId {
		//订单的orderId与当前传入的orderId不同 传入的orderId对应的订单不应被消费
		log.Warning("verifyOrder orderId exist userId: %v, uid: %v, state: %v, orderInfo.OrderId: %v", user.UID, orderInfo.UID, orderInfo.State, orderInfo.OrderId)
		return nil, ecode.PAY_FAIL.String()
	}
	if orderInfo.State == recharge.ORDER_STATE_PAY {
		//已验证过的的订单直接返回
		log.Warning("verifyOrder order state err userId: %v, uid: %v, state: %v, orderInfo.OrderId: %v", user.UID, orderInfo.UID, orderInfo.State, orderInfo.OrderId)
		return pb.ProtoMarshal(&pb.LOBBY_HD_VERIFYPAYORDER_S2C{CpOrderId: orderInfo.UID})
	}
	if orderInfo.State == recharge.ORDER_STATE_FINISH {
		//已领取过奖励
		log.Warning("verifyOrder order state err userId: %v, uid: %v, state: %v", user.UID, orderInfo.UID, orderInfo.State)
		err = ecode.ORDER_FINISHED.String()
		return
	}
	if orderInfo.State == recharge.ORDER_STATE_REFUND {
		//订单已退款
		log.Warning("verifyOrder order state err userId: %v, uid: %v, state: %v", user.UID, orderInfo.UID, orderInfo.State)
		err = ecode.ORDER_REFUNDED.String()
		return
	}
	if orderInfo.UserId != user.UID || orderInfo.Platform != platform {
		//订单数据不匹配
		log.Error("verifyOrder param err orderInfo: %v, userId: %v, platform: %v", orderInfo, user.UID, platform)
		err = ecode.ORDER_VERIFY_PARAM_ERROR.String()
		return
	}
	switch platform {
	case slg.PAY_PLATFORM_GOOGLE:
		e, q, oid := sdk.GoogleOrderVerify(productId, token)
		if e != nil {
			log.Error("verifyOrder GoogleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, e)
			err = ecode.API_ERR.String()
			return
		}
		orderId = oid
		quantity = q
	case slg.PAY_PLATFORM_APPLE:
		e, q := sdk.AppleOrderVerify(orderId, productId, token)
		if e != nil {
			log.Error("verifyOrder AppleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, e)
			err = ecode.API_ERR.String()
			return
		}
		quantity = q
	}
	quantity = ut.Max(quantity, 1)
	//通过外部订单号查询是否有重复订单
	_, oErr := recharge.RechargeDb.FindByOrderId(orderId, platform)
	if oErr == "" {
		//已验证过 无法在新的订单重复验证
		log.Warning("verifyOrder order repeat err userId: %v, uid: %v, orderId: %v", user.UID, uid, orderId)
		err = ecode.ORDER_VERIFY_REPEAT.String()
		return
	}
	//更新订单状态
	recharge.RechargeDb.UpdateVerifiedOrder(orderInfo.UID, orderId, price, currencyType, int(purchaseTime), payAmount, quantity, token)
	//更新未完成订单列表
	user.NotFinishOrdersLock.Lock()
	user.NotFinishOrders = append(user.NotFinishOrders, &NotFinishOrderInfo{CpOrderId: orderInfo.UID, OrderId: orderId, ProductId: productId, Quantity: quantity, Platform: platform})
	user.NotFinishOrdersLock.Unlock()
	user.FlagUpdateDB()
	totalAmount := payAmount * float32(quantity)
	//数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_purchase", map[string]interface{}{
		"product_id":    productId,
		"currency_type": currencyType,
		"pay_amount":    totalAmount,
		"quantity":      quantity,
		"pay_situation": 1,
		"is_double":     user.IsFirstRechargeByProduct(productId),
		"os":            user.GetOs(),
		"platform":      user.Platform,
		"#ip":           user.Ip,
	})
	//AF上报
	af_revenue := totalAmount
	if productId == slg.BATTLE_PASS_PRODUCT_ID {
		af_revenue = ut.Float32(slg.BATTLE_PASS_PRODUCT_INFO["us"])
	} else if rechargeCfg := config.GetRechargeConfByProductId(productId); rechargeCfg != nil {
		af_revenue = ut.Float32(strings.Replace(ut.String(rechargeCfg["money_en"]), "$", "", 1))
	}
	af.Track(user.DeviceOS, user.DistinctId, user.AdvertisingId, "af_purchase", map[string]interface{}{
		"appsflyer_id": user.AppsflyerId,
		// "eventCurrency": currencyType,
		"eventCurrency": "USD",
		"ip":            user.Ip,
		"eventValue": map[string]interface{}{
			"af_revenue":    af_revenue,
			"af_quantity":   quantity,
			"af_content_id": productId,
		},
		"custom_data": map[string]interface{}{
			"ta_account_id": user.UID,
		},
	})
	// dh上报
	dhPayLog(user.UID, user.SessionId, user.Platform, orderId, platform, productId, false, false, quantity)
	return pb.ProtoMarshal(&pb.LOBBY_HD_VERIFYPAYORDER_S2C{CpOrderId: orderInfo.UID})
}

func (this *Lobby) getPayRewards(session gate.Session, msg *pb.LOBBY_HD_GETPAYREWARDS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	uid := msg.GetUid()
	log.Info("getPayRewards uid: %v, userId: %v", uid, user.UID)

	// 领取订单物品使用redis分布式锁
	orderMutex := rds.RedsyncInst.NewMutex(
		"order_lock:"+uid,
		redsync.WithExpiry(10*time.Second), // 订单验证最长10秒
	)
	lockErr := orderMutex.Lock()
	if lockErr != nil {
		log.Error("getPayRewards orderMutex Lock err: %v", lockErr)
	}
	defer func() {
		ok, unlockErr := orderMutex.Unlock()
		if lockErr != nil || !ok {
			log.Error("getPayRewards orderMutex Unlock err: %v", unlockErr)
		}
	}()

	orderInfo, e := recharge.RechargeDb.FindByUid(uid)
	if e != "" {
		//未查询到订单
		log.Error("getPayRewards order not find userId: %v, uid: %v, err: %v", user.UID, uid, e)
		err = ecode.DB_ERROR.String()
		return
	}
	if orderInfo.State != recharge.ORDER_STATE_PAY {
		//已支付状态的订单才发放物品
		log.Error("getPayRewards order state err userId: %v, uid: %v, state: %v", user.UID, uid, orderInfo.State)
		err = ecode.ORDER_STATE_ERROR.String()
		return
	}

	//更新订单状态
	recharge.RechargeDb.UpdateFinishedOrder(uid)
	//更新未完成订单列表
	user.NotFinishOrdersLock.Lock()
	notfinishIndex := -1
	for i, v := range user.NotFinishOrders {
		if uid == v.CpOrderId {
			notfinishIndex = i
			break
		}
	}
	if notfinishIndex != -1 {
		user.NotFinishOrders = append(user.NotFinishOrders[0:notfinishIndex], user.NotFinishOrders[notfinishIndex+1:]...)
	} else {
		log.Error("getPayRewards remove from NotFinishOrders fail userId: %v, uid: %v, NotFinishOrders: %v", user.UID, uid, user.NotFinishOrders)
	}
	user.NotFinishOrdersLock.Unlock()

	// 战令商品 解锁战令
	if orderInfo.ProductId == slg.BATTLE_PASS_PRODUCT_ID {
		if user.BattlePass == nil {
			user.BattlePass = NewBattlePass()
		}
		user.BattlePass.BuyBattlePass(user.UID)
		user.FlagUpdateDB()
		return pb.ProtoMarshal(&pb.LOBBY_HD_GETPAYREWARDS_S2C{})
	}

	// 根据商品id发放物品
	productInfo := config.GetJson(slg.PAY_SHOP_GOLD_CONFIG_NAME).Get(slg.PAY_SHOP_GOLD_CONFIG_PRODUCTID, orderInfo.ProductId)
	if len(productInfo) == 0 {
		//未找到商品数据 (战令商品无需查询配置表发放)
		log.Error("getPayRewards productInfo nil itemId: %v, userId: %v, platform: %v", orderInfo.ProductId, user.UID, orderInfo.Platform)
		err = ecode.PAY_ITEM_ID_NOT_EXIST.String()
		return
	}
	count, isFirst, firstRechargeId := int32(0), false, orderInfo.ProductId
	subType := GetSubTypeByProductId(orderInfo.ProductId)
	if subType != "" {
		// 月卡商品的首充档位id使用对应的订阅类型 TODO 订阅是否使用单独配置
		firstRechargeId = subType
		// 添加玩家订阅数据
		endTime := ut.Now() + ut.TIME_DAY*30
		user.UpdateUserSubInfo(orderInfo.UID, orderInfo.ProductId, endTime, 0, false, orderInfo.CurrencyType, orderInfo.PayAmount, true, this)
	}
	ingot := ut.Int32(productInfo[0][slg.PAY_SHOP_GOLD_CONFIG_INGOT])
	extra := ut.Int32(productInfo[0][slg.PAY_SHOP_GOLD_CONFIG_EXTRA])
	// 是否首次充值该档位
	if isFirst = user.AddRechargeProductCount(firstRechargeId, 1); isFirst {
		count = (ingot + ingot) //首次双倍
	} else {
		count = (ingot + extra)
	}
	// 多出来的几次 按正常计算
	for i := 0; i < orderInfo.Quantity-1; i++ {
		count += (ingot + extra)
	}
	// 发放奖励
	user.ChangeIngot(count, constant.GOLD_CHANGE_RECHARGE_GET)
	log.Info("getPayRewards userId: %v, goldCount: %v, isFirst: %v", user.UID, count, isFirst)
	// 更新充值次数
	user.TriggerTask(this, tctype.RECHARGE_COUNT, 1, 0)
	user.FlagUpdateDB()
	if subType != "" {
		// 添加月卡奖励记录
		go monthCardDb.InsertMonthCardAwardRecord(orderInfo.UID, user.UID, orderInfo.ProductId, ut.Now(), []*g.TypeObj{{Type: ctype.INGOT, Count: count}}, false)
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETPAYREWARDS_S2C{
		Ingot:    user.Ingot,
		AddCount: count,
	})
}

// 创建订阅订单
func (this *Lobby) createSubOrder(session gate.Session, msg *pb.LOBBY_HD_CREATESUBORDER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	productId, platform, subType, offerId := msg.GetProductId(), msg.GetPlatform(), msg.GetType(), msg.GetOfferId()
	log.Info("createSubOrder offerId: %v", offerId)
	orderInfo, e := recharge.SubscriptionDb.CreateOrder(user.UID, productId, platform, subType)
	if e != "" {
		log.Error("createSubOrder order fail userId: %v, uid: %v, err: %v", user.UID, e)
		err = ecode.DB_ERROR.String()
		return
	}
	s2c := &pb.LOBBY_HD_CREATESUBORDER_S2C{Uid: orderInfo.UID}
	if platform == slg.PAY_PLATFORM_APPLE {
		sign, nounce := sdk.AppleGetSingAndNounce(orderInfo.UID, user.UID, productId, offerId)
		s2c.Sign = sign
		s2c.Nounce = nounce
	}
	return pb.ProtoMarshal(s2c)
}

// 验证订阅订单
func (this *Lobby) verifySubOrder(session gate.Session, msg *pb.LOBBY_HD_VERIFYSUBORDER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	uid, orderId, platform, token := msg.GetCpOrderId(), msg.GetOrderId(), msg.GetPlatform(), msg.GetToken()
	productId := msg.GetProductId()
	price, purchaseTime, currencyType, payAmount := msg.GetPrice(), msg.GetPurchaseTime(), msg.GetCurrencyType(), msg.GetPayAmount()
	log.Info("verifySubOrder uid: %v, userId: %v, productId: %v, token: %v", uid, user.UID, productId, token)
	var orderInfo recharge.SubscriptionData
	var startTime, endTime int64     //订阅时间
	var auto, isApiErr bool          //自动续费, api是否报错
	var originalId, offerType string //apple订阅原始交易id, 订阅优惠类型
	var e error
	switch platform {
	case slg.PAY_PLATFORM_GOOGLE:
		e, startTime, endTime, auto, currencyType, orderId, payAmount, offerType, isApiErr = sdk.GoogleSubVerify(productId, token)
		if e != nil {
			log.Error("verifySubOrder GoogleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, e)
			err = ecode.ORDER_VERIFY_API_ERROR.String()
			if isApiErr {
				err = ecode.API_ERR.String()
			}
			return
		}
	case slg.PAY_PLATFORM_APPLE:
		e, startTime, endTime, auto, originalId, productId, payAmount, offerType, isApiErr = sdk.AppleSubVerify(orderId, false)
		if e != nil {
			log.Error("verifySubOrder AppleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, e)
			err = ecode.ORDER_VERIFY_API_ERROR.String()
			if isApiErr {
				err = ecode.API_ERR.String()
			}
			return
		}
		if originalId != "" {
			// apple订阅原始交易id用作外部订单号
			orderId = originalId
		}
	}

	// 价格统一使用USD 读取配置
	priceInfo := slg.SUB_PRODUCT_INFO_MAP[productId]
	if priceInfo != nil {
		currencyType = "USD"
		if offerType == slg.TA_SUB_STATE_DISCOUNT {
			// 体验价
			payAmount = ut.Float32(priceInfo["usFirst"])
		} else {
			payAmount = ut.Float32(priceInfo["us"])
		}
	}

	// 订阅验证涉及到奖励发放 使用redis分布式锁
	orderMutex := rds.RedsyncInst.NewMutex(
		"sub_order_lock:"+orderId,
		redsync.WithExpiry(10*time.Second), // 订单验证最长10秒
	)
	lockErr := orderMutex.Lock()
	if lockErr != nil {
		log.Error("verifySubOrder orderMutex Lock err: %v", lockErr)
	}
	defer func() {
		ok, unlockErr := orderMutex.Unlock()
		if lockErr != nil || !ok {
			log.Error("verifySubOrder orderMutex Unlock err: %v", unlockErr)
		}
	}()

	if orderId != "" {
		// 通过外部订单号查找
		orderInfo, err = recharge.SubscriptionDb.FindByOrderId(orderId, platform)
		if err != "" {
			// 未查询到订单
			log.Info("verifySubOrder order not find userId: %v, orderId: %v, err: %v", user.UID, orderId, err)
			productType := "none"
			if pt, ok := slg.SUB_PRODUCTID_MAP[productId]; ok {
				productType = pt
			}
			if uid != "" {
				// 通过内部订单号查找
				orderInfo, err = recharge.SubscriptionDb.FindByUid(uid)
				if err != "" {
					//未查询到订单
					log.Error("verifySubOrder order not find userId: %v, uid: %v, err: %v", user.UID, uid, err)
				}
			} else {
				// 没有则创建订单
				orderData, e := recharge.SubscriptionDb.CreateOrder(user.UID, productId, platform, productType)
				if e != "" {
					err = ecode.DB_ERROR.String()
					log.Error("verifySubOrder new order err orderId: %v, err: %v", orderId, e)
					return
				}
				orderInfo = *orderData
			}
		}
	} else {
		// 验证错误 没有外部订单号
		err = ecode.ORDER_VERIFY_API_ERROR.String()
		log.Error("verifySubOrder orderId nil userId: %v", user.UID)
		return
	}
	if orderInfo.Platform != platform {
		//订单数据不匹配
		log.Error("verifySubOrder param err orderInfo: %v, userId: %v, platform: %v", orderInfo, user.UID, platform)
		err = ecode.ORDER_VERIFY_PARAM_ERROR.String()
		return
	}
	now := time.Now().UnixMilli()
	if endTime > now {
		if platform == slg.PAY_PLATFORM_APPLE {
			token = "" //Apple无需记录token
		}
		// 未支付未验证的订单
		if orderInfo.State == recharge.ORDER_SUB_STATE_NOT_PAY {
			subState := slg.TA_SUB_STATE_PAY
			if offerType != "" {
				subState = offerType
			}
			// 数数上报
			ta.Track(0, user.UID, user.DistinctId, 0, "ta_adFreeSub", map[string]interface{}{
				"product_id":    orderInfo.Type,
				"currency_type": currencyType,
				"pay_amount":    payAmount,
				"sub_state":     subState,
				"os":            user.GetOs(),
				"platform":      user.Platform,
				"#ip":           user.Ip,
			})
			// af上报
			af.Track(user.DeviceOS, user.DistinctId, user.AdvertisingId, "af_purchase", map[string]interface{}{
				"appsflyer_id":  user.AppsflyerId,
				"eventCurrency": currencyType,
				"ip":            user.Ip,
				"eventValue": map[string]interface{}{
					"af_revenue":    payAmount,
					"af_quantity":   1,
					"af_content_id": productId,
				},
				"custom_data": map[string]interface{}{
					"ta_account_id": user.UID,
				},
			})
			// dh上报
			dhPayLog(user.UID, user.SessionId, user.Platform, orderId, platform, orderInfo.ProductId, true, offerType == slg.TA_SUB_STATE_DISCOUNT, 1)
			// 更新订单数据
			recharge.SubscriptionDb.UpdateVerifiedOrder(orderInfo.UID, orderId, token, user.UID, startTime, endTime, price, currencyType, purchaseTime, payAmount, auto)

			subType := GetSubTypeByProductId(productId)
			if subType != "" {
				// 第一次验证成功发放首次奖励 检测是否首充 TODO 从配置获取奖励
				var count int32 = 100
				if subType == slg.USER_SUBSCRIBE_TYPE_SUPER_AWARD {
					count = 400
				}
				// 是否首次充值该档位
				isFirst := user.AddRechargeProductCount(subType, 1)
				if isFirst {
					count *= 2 //首次双倍
				}
				// 发放奖励
				user.ChangeIngot(count, constant.GOLD_CHANGE_MONTH_CARD_BUY_GET)
				log.Info("verifySubOrder award userId: %v, count: %v, isFirst: %v", user.UID, count, isFirst)
				// 更新充值次数
				user.TriggerTask(this, tctype.RECHARGE_COUNT, 1, 0)
				user.FlagUpdateDB()
				// 添加月卡奖励记录
				go monthCardDb.InsertMonthCardAwardRecord(orderInfo.UID, user.UID, orderInfo.ProductId, ut.Now(), []*g.TypeObj{{Type: ctype.INGOT, Count: count}}, false)
			}
		} else {
			// 验证过的订单只更新状态
			recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, user.UID, endTime, orderInfo.State, auto)
		}
		lastAwardTime := orderInfo.LastAwardTime
		if orderInfo.UserId != user.UID {
			// 订单中的玩家与当前玩家不一致为恢复购买 删除老玩家的订阅
			log.Info("verifySubOrder remove old user sub uid: %v, oldUserId: %v", orderInfo.UID, orderInfo.UserId)
			rstBytes, err := ut.RpcBytes(this.InvokeUserFunc(orderInfo.UserId, slg.RPC_DEL_REPEAT_USE_SUB, orderInfo.UID))
			if err == "" {
				userSubInfo := &pb.UserSubInfo{}
				e = pb.ProtoUnMarshal(rstBytes, userSubInfo)
				if e == nil {
					// 上一次领奖时间使用上一个玩家的数据
					lastAwardTime = userSubInfo.LastAwardTime
				}
			} else {
				log.Warning("verifySubOrder remove old user sub err: %v", err)
			}

		}
		// 更新玩家订阅状态
		orderInfo.PayAmount = payAmount
		orderInfo.CurrencyType = currencyType
		user.UpdateUserSubInfo(orderInfo.UID, productId, endTime, lastAwardTime, auto, currencyType, payAmount, false, this)
		user.FlagUpdateDB()
	} else {
		log.Error("verifySubOrder timeout orderInfo: %v, endTime: %v", orderInfo, endTime)
		err = ecode.SUBSCRIPTION_TIMEOUT.String()
		return
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_VERIFYSUBORDER_S2C{CpOrderId: orderInfo.UID})
}

// 获取验证成功的订阅
func (this *Lobby) getUserSubInfo(session gate.Session, msg *pb.LOBBY_HD_GETUSERSUBINFO_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	s2c := &pb.LOBBY_HD_GETUSERSUBINFO_S2C{List: user.UserSubInfoToPb()}
	return pb.ProtoMarshal(s2c)
}

// 获取玩家的订阅
func (this *Lobby) subOrderCheck(session gate.Session, msg *pb.LOBBY_HD_SUBORDERCHECK_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	if msg.List == nil {
		return
	}
	s2c := &pb.LOBBY_HD_SUBORDERCHECK_S2C{}
	s2c.List, _ = recharge.SubscriptionDb.FindSubOrderByIdList(msg.GetList())
	return pb.ProtoMarshal(s2c)
}

// 检测订阅是否失效
func (this *Lobby) checkInvalidSubOrder(session gate.Session, msg *pb.LOBBY_HD_CHECKINVALIDSUBORDER_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	list, platform := msg.GetList(), msg.GetPlatform()
	if msg.List == nil {
		return
	}

	s2c := &pb.LOBBY_HD_CHECKINVALIDSUBORDER_S2C{List: []string{}}
	now := ut.Now()
	for _, v := range list {
		var e error
		var endTime int64
		switch platform {
		case slg.PAY_PLATFORM_GOOGLE:
			e, _, endTime, _, _, _, _, _, _ = sdk.GoogleSubVerify(v.ProductId, v.Token)

		case slg.PAY_PLATFORM_APPLE:
			e, _, endTime, _, _, _, _, _, _ = sdk.AppleSubVerify(v.OrderId, true)
		}
		if e != nil {
			s2c.List = append(s2c.List, v.OrderId)
		} else if now >= endTime {
			s2c.List = append(s2c.List, v.OrderId)
		}
	}

	return pb.ProtoMarshal(s2c)
}

// 验证订阅订单
func HandleSubCheck(user *User, uid string) (endTime int64, auto bool, err error, isApiErr bool) {
	orderInfo, e := recharge.SubscriptionDb.FindByUid(uid)
	if e != "" {
		//未查询到订单
		log.Error("HandleSubCheck order not find userId: %v, uid: %v, err: %v", user.UID, uid, e)
		err = errors.New(e)
		return
	}
	switch orderInfo.Platform {
	case slg.PAY_PLATFORM_GOOGLE:
		err, _, endTime, auto, _, _, _, _, isApiErr = sdk.GoogleSubVerify(orderInfo.ProductId, orderInfo.Token)
		if err != nil {
			log.Info("HandleSubCheck GoogleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, err)
		}
	case slg.PAY_PLATFORM_APPLE:
		err, _, endTime, auto, _, _, _, _, isApiErr = sdk.AppleSubVerify(orderInfo.OrderId, true)
		if err != nil {
			log.Info("HandleSubCheck AppleOrderVerify orderInfo: %v, userId: %v, err: %v", orderInfo, user.UID, err)
			if err.Error() == "refund" {
				orderInfo.State = recharge.ORDER_STATE_REFUND
			}
		}
	}
	if orderInfo.EndTime != endTime || orderInfo.AutoRenewing != auto {
		// 更新订单状态
		recharge.SubscriptionDb.UpdateSubRenew(orderInfo.UID, user.UID, endTime, orderInfo.State, auto)
	}
	return
}

// 用户退款
func (this *Lobby) userRefund(user *User, orderUid, productId, currencyType string, payAmount float32, quantity, oldState int) {
	if oldState == recharge.ORDER_STATE_PAY {
		// 退款前的状态是已支付未领取
		user.NotFinishOrdersLock.Lock()
		if user.NotFinishOrders != nil && len(user.NotFinishOrders) > 0 {
			for i := len(user.NotFinishOrders) - 1; i >= 0; i-- {
				if user.NotFinishOrders[i].CpOrderId == orderUid {
					// 退款订单是未领取的订单则从列表中删除
					user.NotFinishOrders = append(user.NotFinishOrders[0:i], user.NotFinishOrders[i+1:]...)
				}

			}
		}
		user.NotFinishOrdersLock.Unlock()
	}

	if oldState == recharge.ORDER_STATE_FINISH {
		// 已正常领取的订单则扣除物品
		if productId == slg.BATTLE_PASS_PRODUCT_ID {
			// 战令商品
			if user.BattlePass == nil {
				log.Error("userRefund BattlePass nil uid: %v, productId: %v", user.UID, productId)
				return
			}
			if user.BattlePass.PayRefund(user.UID) {
				// 已领取过付费档奖励 需要封号
				this.BanUserAccount(user, ut.TIME_DAY*3650, slg.BAN_TYPE_REFUND, false)
			}
		} else if subType := slg.USER_SUB_TYPE_MAP[productId]; subType != "" {
			// 订阅商品
			UserSubRefund(orderUid)
		} else {
			// 元宝商品
			productInfo := config.GetJson(slg.PAY_SHOP_GOLD_CONFIG_NAME).Get(slg.PAY_SHOP_GOLD_CONFIG_PRODUCTID, productId)
			if len(productInfo) == 0 {
				// 未找到商品数据
				log.Error("userRefund uid: %v, productId: %v", user.UID, productId)
				return
			}
			ingot := ut.Int32(productInfo[0][slg.PAY_SHOP_GOLD_CONFIG_INGOT])
			extra := ut.Int32(productInfo[0][slg.PAY_SHOP_GOLD_CONFIG_EXTRA])
			var ingotCount int32
			if user.AddRechargeRefundProductCount(productId, 1) {
				ingotCount = (ingot + ingot) //首次双倍
			} else {
				ingotCount = (ingot + extra)
			}
			// 多出来的几次 按正常计算
			for i := 0; i < quantity-1; i++ {
				ingotCount += (ingot + extra)
			}
			// 扣除元宝
			this.ReduceIngot(user, ingotCount, constant.GOLD_CHANGE_REFUND)
			// 发送邮件
			content := ut.StringJoin("|", ingotCount, user.Ingot)
			this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, 0, 100009, "", content, "-1", user.UID, nil)
		}
	}

	if user.FlagUpdateDB() {
		user.PutNotifyQueue(constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: &pb.UpdateOutPut{Gold: user.Gold, Flag: pb.AddFlags(int64(pb.OutPutFlagEnum_Gold))}})
	}
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_refund", map[string]interface{}{
		"product_id":    productId,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
		"os":            user.GetOs(),
		"platform":      user.Platform,
		"#ip":           user.Ip,
	})
}

// 月卡奖励领取
func (this *Lobby) getMonthCardAward(session gate.Session, msg *pb.LOBBY_HD_GETMONTHCARDAWARD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		err = ecode.NOT_BIND_UID.String()
		return
	}
	items := user.GetMonthCardAward(msg.GetType())
	s2c := &pb.LOBBY_HD_GETMONTHCARDAWARD_S2C{List: user.UserSubInfoToPb(), Rewards: items}
	return pb.ProtoMarshal(s2c)
}

// 用户续订处理
func (this *Lobby) userDidRenew(user *User, orderUid, orderId, productId, subType, currencyType string, payAmount float32, endTime int64, auto bool, offerType string) {
	log.Info("userDidRenew orderUid: %v, endTime: %v", orderUid, endTime)
	// af_revenue := payAmount
	if info := slg.SUB_PRODUCT_INFO_MAP[productId]; info != nil {
		currencyType = "USD"
		if offerType == slg.TA_SUB_STATE_DISCOUNT {
			// 体验价
			payAmount = ut.Float32(info["usFirst"])
		} else {
			payAmount = ut.Float32(info["us"])
		}
	}
	// 更新玩家订阅状态
	user.UpdateUserSubInfo(orderUid, productId, endTime, 0, auto, currencyType, payAmount, false, this)
	user.FlagUpdateDB()
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_adFreeSub", map[string]interface{}{
		"product_id":    subType,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
		"sub_state":     slg.TA_SUB_STATE_RENEWAL,
		"os":            user.GetOs(),
		"platform":      user.Platform,
		"#ip":           user.Ip,
	})
	// AF上报
	af.Track(user.DeviceOS, user.DistinctId, user.AdvertisingId, "af_purchase", map[string]interface{}{
		"appsflyer_id":  user.AppsflyerId,
		"eventCurrency": currencyType,
		"ip":            user.Ip,
		"eventValue": map[string]interface{}{
			"af_revenue":    payAmount,
			"af_quantity":   1,
			"af_content_id": productId,
		},
		"custom_data": map[string]interface{}{
			"ta_account_id": user.UID,
		},
	})
	// dh上报
	dhPayLog(user.UID, user.SessionId, user.Platform, orderId, user.Platform, productId, true, false, 1)
}

// 续订状态更新
func (this *Lobby) userRenewStateChange(user *User, orderUid, productId, subType, currencyType string, payAmount float32, endTime int64, auto bool) {
	log.Info("userRenewStateChange orderUid: %v, auto: %v", orderUid, auto)
	if !auto {
		// 数数上报
		ta.Track(0, user.UID, user.DistinctId, 0, "ta_sub_cancel", map[string]interface{}{
			"product_id":    subType,
			"currency_type": currencyType,
			"pay_amount":    payAmount,
			"os":            user.GetOs(),
			"platform":      user.Platform,
			"#ip":           user.Ip,
		})
	}
	// 更新玩家订阅状态
	user.UpdateUserSubInfo(orderUid, productId, endTime, 0, auto, currencyType, payAmount, false, this)
	user.FlagUpdateDB()
}

// 用户订阅退款处理
func (this *Lobby) userSubRefund(user *User, orderUid, subType, currencyType string, payAmount float32) {
	log.Info("userSubRefund orderUid: %v", orderUid)
	// 扣除已领取物品
	UserSubRefund(orderUid)
	// 移除玩家订阅
	user.DelUserSubInfo(orderUid, this)
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_refund", map[string]interface{}{
		"product_id":    subType,
		"currency_type": currencyType,
		"pay_amount":    payAmount,
		"os":            user.GetOs(),
		"platform":      user.Platform,
		"#ip":           user.Ip,
	})
}

// 付费dh上报
func dhPayLog(userId, sessionId, platform, orderId, payPlatform, productId string, isSub, isfirst bool, quantity int) {
	isSandBox := 0
	if slg.IS_SANDBOX {
		isSandBox = 1
	}
	// 支付渠道其他支付渠道默认为999 微信：1 支付宝：2 IOS：3 GP：4 Xsolla web支付：5 Xsolla 游戏内支付：6
	var payChannel int
	switch payPlatform {
	case slg.PAY_PLATFORM_WX:
		payChannel = 1
	case slg.PAY_PLATFORM_APPLE:
		payChannel = 3
	default:
		payChannel = 999
	}
	var priceUs float64
	var priceRmb float64
	var storeId int
	if isSub {
		info := slg.SUB_PRODUCT_INFO_MAP[productId]
		if info == nil {
			return
		}
		// 获取订阅金额
		storeId = ut.Int(info["storeId"])
		if isfirst {
			priceRmb = info["rmbFirst"]
			priceUs = info["usFirst"]
		} else {
			priceRmb = info["rmb"]
			priceUs = info["us"]
		}
	} else if productId == slg.BATTLE_PASS_PRODUCT_ID {
		storeId = ut.Int(slg.BATTLE_PASS_PRODUCT_INFO["storeId"])
		priceRmb = slg.BATTLE_PASS_PRODUCT_INFO["rmb"]
		priceUs = slg.BATTLE_PASS_PRODUCT_INFO["us"]

	} else {
		datas := config.GetJson("recharge").Datas
		if datas == nil {
			return
		}
		for _, v := range datas {
			if ut.String(v["product_id"]) == productId {
				storeId = ut.Int(v["id"])
				priceRmbStr := ut.String(v["money_china"])
				priceRmbStrArr := strings.Split(priceRmbStr, "¥")
				if len(priceRmbStrArr) >= 2 {
					priceRmb = ut.Float64(priceRmbStrArr[1])
				}
				priceUsStr := ut.String(v["money_en"])
				priceUsStrArr := strings.Split(priceUsStr, "$")
				if len(priceUsStrArr) >= 2 {
					priceUs = ut.Float64(priceUsStrArr[1])
				}
			}
		}
	}
	priceUs *= float64(quantity)
	priceRmb *= float64(quantity)
	dh.Track(dh.DH_EVENT_NAME_PAY, map[string]interface{}{
		"user_info": map[string]interface{}{
			"bundle_id":   dh.APP_BUNDLE_ID, //游戏包名
			"sub_package": "",               //头条分包，没有的传空值
			"server_id":   dh.DhLogServerId, //逻辑服ID， 大于等于10000的server_id会被当作测试环境数据处理
			"user_id":     userId,           //用户游戏角色ID，为8位数字id，全服唯一
			"session_id":  sessionId,        //由客户端初始化时生成（ 建议使用时间戳+设备信息or uuid  md5）一个唯一值，并传给后端
			"account":     userId,
			"platform":    dh.DhPlatformSwitch(platform), //平台，iOS android pc
			"lv":          0,
			"vip":         0,
		},
		"event_info": map[string]interface{}{
			"order_no":      orderId,    //第三方订单号【自研项目接入中台的third_id】
			"pay_price":     priceUs,    //美元计费点
			"pay_price_rmb": priceRmb,   //人民币计费点 市场需求
			"pay_channel":   payChannel, //支付渠道
			"store_id":      storeId,    //礼包ID（数字）
			"is_sandbox":    isSandBox,  //是否为沙盒付费 1沙盒付费 0非沙盒付费,2024.02.07新增
		},
	})
}

// 根据订阅商品id获取类型
func GetSubTypeByProductId(productId string) string {
	return slg.USER_SUB_TYPE_MAP[productId]
}
