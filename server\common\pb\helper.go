package pb

import (
	"slgsrv/server/common/ecode"
	ut "slgsrv/utils"
	"strconv"
	"strings"

	"google.golang.org/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

func ProtoMarshal(m protoreflect.ProtoMessage) (bytes []byte, err string) {
	bytes, e := proto.Marshal(m)
	if e != nil {
		return nil, ecode.UNKNOWN.String()
	}
	return bytes, ""
}

func ProtoUnMarshal(bytes []byte, m protoreflect.ProtoMessage) error {
	return proto.Unmarshal(bytes, m)
}

func Bytes(m protoreflect.ProtoMessage) []byte {
	bytes, _ := proto.Marshal(m)
	return bytes
}

func Int32(val interface{}) int32 {
	switch reply := val.(type) {
	case float32:
		return int32(reply)
	case float64:
		return int32(reply)
	case int32:
		return reply
	case int64:
		return int32(reply)
	case int:
		return int32(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return int32(s)
	case nil:
		return 0
	}
	return 0
}

func Int64(val interface{}) int64 {
	switch reply := val.(type) {
	case float32:
		return int64(reply)
	case float64:
		return int64(reply)
	case int32:
		return int64(reply)
	case int64:
		return reply
	case int:
		return int64(reply)
	case string:
		s, _ := strconv.Atoi(reply)
		return int64(s)
	case nil:
		return 0
	}
	return 0
}

func String(val interface{}) string {
	switch reply := val.(type) {
	case string:
		return reply
	case float64:
		return Itoa(reply)
	case int:
		return Itoa(reply)
	case bool:
		return strconv.FormatBool(reply)
	case nil:
		return ""
	}
	return ""
}

func Bool(val interface{}) bool {
	switch reply := val.(type) {
	case bool:
		return reply
	case nil:
		return false
	}
	return false
}

func Int(val interface{}) int {
	switch reply := val.(type) {
	case float32:
		return int(reply)
	case float64:
		return int(reply)
	case int32:
		return int(reply)
	case int64:
		return int(reply)
	case int:
		return reply
	case string:
		s, _ := strconv.Atoi(reply)
		return s
	case nil:
		return 0
	}
	return 0
}

// int转字符串
func Itoa(val interface{}) string {
	return strconv.Itoa(Int(val))
}

// string转int
func Atoi(val interface{}) int {
	r, _ := strconv.Atoi(String(val))
	return r
}

// string转Float
func Atof(val interface{}) float64 {
	r, _ := strconv.ParseFloat(String(val), 64)
	return r
}

// int数组转int32
func IntArrayToInt32(arr []int) []int32 {
	ret := []int32{}
	for _, v := range arr {
		ret = append(ret, Int32(v))
	}
	return ret
}

func NewVec2(val interface{}) *Vec2 {
	switch reply := val.(type) {
	case *ut.Vec2:
		return &Vec2{
			X: int32(reply.X),
			Y: int32(reply.Y),
		}
	case *Vec2:
		return &Vec2{
			X: int32(reply.X),
			Y: int32(reply.Y),
		}
	case map[string]interface{}:
		return &Vec2{
			X: int32(ut.Int(reply["x"])),
			Y: int32(ut.Int(reply["y"])),
		}
	}
	return &Vec2{}
}

func ToVec2(vec *Vec2) *ut.Vec2 {
	return ut.NewVec2(vec.X, vec.Y)
}

// 转int32数组
func Int32Array(val interface{}) []int32 {
	switch reply := val.(type) {
	case []int32:
		return reply
	case []int:
		arr := []int32{}
		for _, v := range reply {
			arr = append(arr, Int32(v))
		}
		return arr
	case []interface{}:
		arr := []int32{}
		for _, v := range reply {
			arr = append(arr, Int32(v))
		}
		return arr
	case []string:
		arr := []int32{}
		for _, v := range reply {
			arr = append(arr, Int32(v))
		}
		return arr
	case nil:
		return []int32{}
	}
	return []int32{}
}

// 将一个字符串拆分为数组
func StringToInts(val string, separator string) []int32 {
	if val == "" {
		return []int32{}
	}
	arr := strings.Split(val, separator)
	ret := []int32{}
	for _, s := range arr {
		ret = append(ret, int32(Atoi(s)))
	}
	return ret
}

func NewVec2ByString(str, separator string) *Vec2 {
	arr := StringToInts(str, separator)
	return &Vec2{X: arr[0], Y: arr[1]}
}

func AddFlags(flags ...int64) int64 {
	data := int64(0)
	for _, v := range flags {
		data |= (1 << v)
	}
	return data
}

func HasFlag(data int64, flag int64) bool {
	return data&(1<<flag) == (1 << flag)
}

func AddFlagsInt32(flags ...int32) int32 {
	data := int32(0)
	for _, v := range flags {
		data |= (1 << v)
	}
	return data
}

func HasFlagInt32(data int32, flag int32) bool {
	return data&(1<<flag) == (1 << flag)
}

// 将mergeData的数据添加进mapData中
func MapMerge(mapData map[int]int, mergeData map[int]int) {
	for k, v := range mergeData {
		if _, ok := mapData[k]; ok {
			mapData[k] += v
		} else {
			mapData[k] = v
		}
	}
}

// 将mergeData的数据添加进mapData中
func MapMergeInt32(mapData map[int32]int32, mergeData map[int32]int32) {
	for k, v := range mergeData {
		if _, ok := mapData[k]; ok {
			mapData[k] += v
		} else {
			mapData[k] = v
		}
	}
}
