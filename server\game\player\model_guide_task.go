package player

import (
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 刷新可以做的任务
func (this *Model) CheckUpdateCanGuideTask() {
	// 删除没有的
	finishMap := map[int32]bool{}
	for i := len(this.Tasks.Finishs) - 1; i >= 0; i-- {
		id := this.Tasks.Finishs[i]
		if json := config.GetJsonData("guideTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.Tasks.Finishs = append(this.Tasks.Finishs[:i], this.Tasks.Finishs[i+1:]...)
		}
	}
	// 删除已经完成的任务且重复的
	ids := map[int32]bool{}
	for i := len(this.Tasks.List) - 1; i >= 0; i-- {
		m := this.Tasks.List[i]
		prevId := m.GetPrevID()
		if finishMap[m.ID] || ids[m.ID] || (prevId != 0 && !finishMap[prevId]) {
			this.Tasks.List = append(this.Tasks.List[:i], this.Tasks.List[i+1:]...)
		} else {
			ids[m.ID] = true
		}
	}
	// 新增可以做的任务
	isReCreate := this.ReCreateMainCityCount > 0 || this.room.GetType() == slg.CUSTOM_SERVER_TYPE
	datas := config.GetJson("guideTask").Datas
	for _, m := range datas {
		id, prev_id := ut.Int32(m["id"]), ut.Int32(m["prev_id"])
		if ut.Int(m["type"]) == 0 || finishMap[id] || (prev_id != 0 && !finishMap[prev_id]) {
			continue
		} else if !ids[id] {
			var newTask *g.TaskInfo = nil
			if isReCreate {
				newTask = g.NewTaskInfoByReCreate(m)
			} else {
				newTask = g.NewTaskInfo(m)
			}
			this.InitTaskProgress(newTask)
			this.Tasks.List = append(this.Tasks.List, newTask)
		}
	}
}

// 获取任务
func (this *Model) GetGuideTask(id int32) *g.TaskInfo {
	this.Tasks.RLock()
	defer this.Tasks.RUnlock()
	if task := array.Find(this.Tasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 是否已经完成了
func (this *Model) InGuideTaskFinish(id int32) bool {
	this.Tasks.RLock()
	defer this.Tasks.RUnlock()
	return array.Has(this.Tasks.Finishs, id)
}

// 完成一个任务
func (this *Model) FinishGuideTask(task *g.TaskInfo) {
	this.Tasks.Lock()
	defer this.Tasks.Unlock()
	// 先删除任务列表里面的
	this.Tasks.List = array.RemoveItem(this.Tasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	this.Tasks.Finishs = append(this.Tasks.Finishs, task.ID)
	// 添加下一个任务
	this.CheckUpdateCanGuideTask()
	// 如果没有任务了 那么就添加每日任务
	if len(this.Tasks.List) == 0 {
		this.TodayTasks.Lock()
		this.CheckUpdateCanTodayTask()
		this.TodayTasks.Unlock()
	}
}
