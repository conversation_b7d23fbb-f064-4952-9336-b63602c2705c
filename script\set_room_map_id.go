package main

// import (
// 	"context"
// 	slg "slgsrv/server/common"
// 	ut "slgsrv/utils"
// 	"time"

// 	"github.com/huyangv/vmqant/log"
// 	"go.mongodb.org/mongo-driver/bson"
// 	"go.mongodb.org/mongo-driver/mongo"
// 	"go.mongodb.org/mongo-driver/mongo/options"
// )

// const (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slgsrv"                    //大厅服数据库名

// 	gameDbUri  = "mongodb://localhost:27017" //游戏服数据库地址
// 	gameDbName = "slgsrv"                    //游戏服数据库名
// )

// func main() {
// 	log.Info("set mapId start!")
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)

// 	// 连接游戏服数据库
// 	clientGame, err := mongo.NewClient(options.Client().ApplyURI(gameDbUri))
// 	if err != nil {
// 		log.Error("parse gameDbUri err: %v", err)
// 	}
// 	defer cancel()
// 	err = clientGame.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect gameDb err: %v", err)
// 	}
// 	defer clientGame.Disconnect(ctx)
// 	gameDb := clientGame.Database(gameDbName)

// 	// 遍历地图使用表
// 	cur, e := lobbyDb.Collection(slg.DB_COLLECTION_NAME_MAP_USE).Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cur.Close(context.TODO())
// 	}()
// 	if e == nil {
// 		var list []bson.M
// 		cur.All(context.TODO(), &list)
// 		for _, v := range list {
// 			mapId := ut.Int(v["id"])
// 			sidList := ut.IntArray(v["sid_list"])
// 			if sidList != nil {
// 				for _, sid := range sidList {
// 					// 更新room表中的地图id
// 					gameDb.Collection("room").UpdateOne(context.TODO(), bson.M{"id": sid}, bson.M{
// 						"$set": bson.M{"map_id": mapId},
// 					})
// 				}
// 			}
// 		}
// 	}
// 	log.Info("set mapId end!")
// }
