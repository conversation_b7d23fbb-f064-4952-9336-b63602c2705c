package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"

	"github.com/sasha-s/go-deadlock"
)

type PolicyMap struct {
	deadlock.RWMutex
	Map map[int32]*g.PolicyInfo
}

func (this *PolicyMap) GetEffect(tp int32) *g.EffectObj {
	this.RLock()
	defer this.RUnlock()
	for _, m := range this.Map {
		if effect := m.GetEffect(); effect != nil && effect.Type == tp {
			return effect
		}
	}
	return nil
}

func (this *PolicyMap) ToPolicys() map[int32]int32 {
	this.RLock()
	defer this.RUnlock()
	arr := map[int32]int32{}
	for i, m := range this.Map {
		arr[i] = m.ID
	}
	return arr
}

func (this *PolicyMap) ToPolicysPb() map[int32]int32 {
	this.RLock()
	defer this.RUnlock()
	arr := map[int32]int32{}
	for i, m := range this.Map {
		arr[pb.Int32(i)] = pb.Int32(m.ID)
	}
	return arr
}

func (this *Model) ToPolicyMap(uid string) map[int32]*g.PolicyInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.Policys.RLock()
		defer plr.Policys.RUnlock()
		ret := map[int32]*g.PolicyInfo{}
		for k, v := range plr.Policys.Map {
			ret[k] = v
		}
		return ret
	}
	return map[int32]*g.PolicyInfo{}
}

func (this *Model) ToPolicys(uid string) map[int32]int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Policys.ToPolicys()
	}
	return map[int32]int32{}
}

func (this *Model) ToPolicysPb(uid string) map[int32]int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.Policys.ToPolicysPb()
	}
	return map[int32]int32{}
}

func (this *PolicyMap) GetPolicyEffectMapBool() map[int32]bool {
	this.RLock()
	defer this.RUnlock()
	effectMap := map[int32]bool{}
	for _, m := range this.Map {
		if effect := m.GetEffect(); effect != nil {
			effectMap[effect.Type] = true
		}
	}
	return effectMap
}

// 获取政策效果
func (this *Model) GetPlayerPolicyEffectFloat(plr *TempPlayer, tp int32) float64 {
	if plr == nil || plr.Policys == nil || plr.Policys.Map == nil {
		return 0
	} else if plr.IsSpectate {
		return 0
	}
	e1 := plr.Policys.GetEffect(tp)
	e2 := this.GetPlayerAlliPolicyEffect(plr.AllianceUid, tp)
	if e1 != nil && e2 != nil {
		return e1.Param
	} else if e1 != nil {
		return e1.Value
	} else if e2 != nil {
		return e2.Value
	}
	return 0
}

// 获取政策效果
func (this *Model) GetPlayerPolicyEffectFloatByUid(uid string, tp int32) float64 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return this.GetPlayerPolicyEffectFloat(plr, tp)
	}
	return 0
}
func (this *Model) GetPlayerPolicyEffectInt(plr *TempPlayer, tp int32) int32 {
	return int32(this.GetPlayerPolicyEffectFloat(plr, tp))
}
func (this *Model) GetPlayerPolicyEffectIntByUid(uid string, tp int32) int32 {
	return int32(this.GetPlayerPolicyEffectFloatByUid(uid, tp))
}

// 获取玩家联盟的政策效果
func (this *Model) GetPlayerAlliPolicyEffect(alliUid string, tp int32) *g.EffectObj {
	if alli := this.GetAlliance(alliUid); alli != nil {
		return alli.GetPolicyEffectByType(tp)
	}
	return nil
}
