package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"github.com/sasha-s/go-deadlock"
)

// 联盟政策
type PolicyMap struct {
	deadlock.RWMutex
	Map map[int32]*g.PolicyInfo
}

func (this *PolicyMap) GetPolicyEffectMapBool() map[int32]bool {
	this.RLock()
	defer this.RUnlock()
	effectMap := map[int32]bool{}
	for _, m := range this.Map {
		if effectType := m.GetEffect(); effectType > 0 {
			effectMap[effectType] = true
		}
	}
	return effectMap
}

func (this *Model) ToPolicySlotMap(uid string) map[int32]*g.CeriSlotInfo {
	ret := map[int32]*g.CeriSlotInfo{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.PolicySlots.ToCerisDB()
	}
	return ret
}

func (this *Model) ToPolicys(uid string) map[int32]int32 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.PolicySlots.ToPolicys()
	}
	return map[int32]int32{}
}

func (this *Model) ToPolicysPb(uid string) map[int32]*pb.CeriSlotInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.PolicySlots.ToCerisPb()
	}
	return map[int32]*pb.CeriSlotInfo{}
}

// 获取指定政策的等级
func (this *Model) GetPolicyLvById(uid string, policyId int32) int32 {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return 0
	}
	var lv int32
	json := config.GetJsonData("policy", policyId)
	if json == nil {
		return 0
	}
	tp := ut.Int32(json["type"])
	if plr.PolicySlots.GetPolicyIdByEffectType(tp) > 0 {
		lv++
	}
	if this.GetPlayerAlliPolicyEffect(plr.AllianceUid, tp) > 0 {
		lv++
	}
	if this.season.GetPolicyIdByType(tp) > 0 {
		lv++
	}
	return lv
}

// 获取政策效果
func (this *Model) GetPlayerPolicyEffect(plr *TempPlayer, tp int32) (int32, float64) {
	if plr == nil || plr.PolicySlots == nil || plr.PolicySlots.Map == nil {
		return 0, 0
	} else if plr.IsSpectator() {
		return 0, 0
	}
	// 同类型效果等级叠加
	handlePolicyLv := func(id, policyId, lv int32) (int32, int32) {
		if id > 0 {
			policyId = id
			lv++
		}
		return policyId, lv
	}
	var policyId, lv int32
	policyId, lv = handlePolicyLv(plr.PolicySlots.GetPolicyIdByEffectType(tp), policyId, lv)         // 玩家政策
	policyId, lv = handlePolicyLv(this.GetPlayerAlliPolicyEffect(plr.AllianceUid, tp), policyId, lv) // 联盟政策
	policyId, lv = handlePolicyLv(this.season.GetPolicyIdByType(tp), policyId, lv)                   // 季节政策

	if policyId > 0 && lv > 0 {
		json := config.GetJsonData("policy", policyId)
		if json == nil {
			return 0, 0
		}
		// 该数组依次为每级叠加的效果
		effectValueArr := ut.StringToFloats(ut.String(json["value"]), ",")
		if len(effectValueArr) == 0 {
			return 0, 0
		}
		return policyId, effectValueArr[ut.Min(len(effectValueArr)-1, int(lv-1))]
	}
	return 0, 0
}

func (this *Model) GetPlayerPolicyEffectFloat(plr *TempPlayer, tp int32) float64 {
	_, val := this.GetPlayerPolicyEffect(plr, tp)
	return val
}

// 获取政策效果
func (this *Model) GetPlayerPolicyEffectFloatByUid(uid string, tp int32) float64 {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return this.GetPlayerPolicyEffectFloat(plr, tp)
	}
	return 0
}

func (this *Model) GetPlayerPolicyEffectInt(plr *TempPlayer, tp int32) int32 {
	return int32(this.GetPlayerPolicyEffectFloat(plr, tp))
}

func (this *Model) GetPlayerPolicyEffectIntByUid(uid string, tp int32) int32 {
	return int32(this.GetPlayerPolicyEffectFloatByUid(uid, tp))
}

// 获取玩家用于战斗的政策buff
func (this *Model) GetPlayerFightPolicyBuffs(uid string, defender string) [][]int32 {
	buffs := [][]int32{}
	if plr := this.GetTempPlayer(uid); plr != nil {
		addBuffVal := func(tp int32) {
			if id, val := this.GetPlayerPolicyEffect(plr, tp); val > 0 {
				buffs = append(buffs, []int32{id, int32(val)})
			}
		}
		isOneAlliance := this.CheckIsOneAllianceByAlliUid(plr.AllianceUid, this.GetPlayerAlliUid(defender))
		if isOneAlliance {
			// 守卫光环 1026
			addBuffVal(effect.DEFEND_HALO)
		} else {
			// 进攻号角 1027
			addBuffVal(effect.ATTACK_HALO)
			// 摧坚巧工 1043
			addBuffVal(effect.ADD_DMG_TO_BUILD)
			// 野怪克星 1042
			if defender == "" {
				addBuffVal(effect.ADD_DMG_TO_MONSTER)
			}
		}
		// 护甲专精 1028
		addBuffVal(effect.ADD_MAX_HP)
		// 武器专精 1029
		addBuffVal(effect.ADD_ATTACK)
		// 一级之力 1030
		addBuffVal(effect.LV_1_POWER)
		// 生生不息 1044
		addBuffVal(effect.CIRCLE_OF_LIFE)
	}
	return buffs
}

// 获取玩家联盟的政策效果
func (this *Model) GetPlayerAlliPolicyEffect(alliUid string, tp int32) int32 {
	if alli := this.GetAlliance(alliUid); alli != nil {
		return alli.GetPolicyEffectByType(tp)
	}
	return 0
}

// 兼容政策槽位信息
func (this *Model) CheckPolicySlotInfo(uid string, buildLv int32) {
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.PolicySlots.Check(buildLv, constant.POLICY_SLOT_CONF_MAP, nil)
	}
}

// 刷新政策槽位信息
func (this *Model) UpdatePolicySlotInfo(uid string, blv int32, init bool) {
	if _, ok := constant.POLICY_SLOT_CONF_MAP[blv]; !ok {
		return
	}
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	}
	if slot := plr.PolicySlots.GetSlotByLv(blv); slot == nil {
		plr.PolicySlots.AddCeriSlot(blv)
	} else if len(slot.SelectIds) > 0 {
		return
	}
	// 随机政策待选项
	if plr.PolicySlots.CeriRandomSelect(blv, nil) != nil && !init {
		// 通知
		this.room.PutPlayerNotifyQueue(constant.NQ_UPDATE_POLICY_SLOT, uid, &pb.OnUpdatePlayerInfoNotify{Data_85: plr.PolicySlots.ToCerisPb()})
	}
}

// 激活政策获得物品
func (this *Model) ActivePolicyAddItems(policyId int32, userId string) {
	json := config.GetJsonData("policy", policyId)
	if json == nil {
		return
	}
	tp := ut.Int32(json["type"])
	if cfg := constant.POLICY_ADD_ITEM_MAP[tp]; len(cfg) >= 3 {
		items := []*g.TypeObj{}
		items = append(items, &g.TypeObj{Type: cfg[0], Id: cfg[1], Count: cfg[2]})
		textName := "policyText.name_" + ut.String(policyId)
		this.room.SendMailItemOne(slg.MAIL_POLICY_ADD_ITEM_ID, textName, textName, "-1", userId, items)
	}
}

// 季节政策激活获得物品
func (this *Model) AllPlrActivePolicyAddItems(policyId int32) {
	json := config.GetJsonData("policy", policyId)
	if json == nil {
		return
	}
	tp := ut.Int32(json["type"])
	if constant.POLICY_ADD_ITEM_MAP[tp] == nil {
		return
	}
	plrList := []string{}
	this.allTempPlayers.RLock()
	for uid := range this.allTempPlayers.Map {
		plrList = append(plrList, uid)
	}
	this.allTempPlayers.RUnlock()

	for _, uid := range plrList {
		// 激活时发放物品
		this.ActivePolicyAddItems(policyId, uid)
		// 也发放该季节的物品
		// this.AddPolicySeasonItems(this.GetTempPlayer(uid))
	}
}

// 季节政策按季节获得物品
func (this *Model) AllPlrAddPolicySeasonItems() {
	plrList := []*TempPlayer{}
	this.allTempPlayers.RLock()
	for _, plr := range this.allTempPlayers.Map {
		plrList = append(plrList, plr)
	}
	this.allTempPlayers.RUnlock()

	for _, plr := range plrList {
		this.AddPolicySeasonItems(plr)
	}
}

func (this *Model) AddPolicySeasonItems(plr *TempPlayer) {
	for tp, cfg := range constant.POLICY_SEASON_ADD_ITEM_MAP {
		items := []*g.TypeObj{}
		policyId, val := this.GetPlayerPolicyEffect(plr, tp)
		if val > 0 {
			items = append(items, &g.TypeObj{Type: cfg[0], Id: cfg[1], Count: int32(val)})
		}
		if len(items) > 0 {
			textName := "policyText.name_" + ut.String(policyId)
			this.room.SendMailItemOne(slg.MAIL_POLICY_SEASON_ITEM_ID, textName, "", "-1", plr.Uid, items)
		}
	}
}
