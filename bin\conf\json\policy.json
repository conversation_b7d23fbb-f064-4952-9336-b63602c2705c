[{"id": 1001, "type": 12, "value": "100,150,200", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1002, "type": 2, "value": "20,40,60", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1003, "type": 5, "value": "10,20,30", "use_cond": "4,2004,1", "need_build_lv": "", "weight": 100}, {"id": 1004, "type": 1, "value": "1,2,3", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1005, "type": 13, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1006, "type": 14, "value": "30,45,60", "use_cond": "4,2011,1", "need_build_lv": "", "weight": 100}, {"id": 1007, "type": 15, "value": "500,1000,1500", "use_cond": "4,2002,1|4,2003,1", "need_build_lv": "", "weight": 100}, {"id": 1008, "type": 16, "value": "20,30,40", "use_cond": "4,2004,1", "need_build_lv": "", "weight": 80}, {"id": 1009, "type": 8, "value": "3,6,9", "use_cond": "4,2004,1", "need_build_lv": "", "weight": 100}, {"id": 1010, "type": 17, "value": "20,40,60", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1011, "type": 11, "value": "1,2,3", "use_cond": "", "need_build_lv": "", "weight": 50}, {"id": 1012, "type": 18, "value": "4,5,6", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1013, "type": 19, "value": "40,60,80", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1014, "type": 20, "value": "20,40,60", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1015, "type": 21, "value": "20,30,40", "use_cond": "4,2008,1", "need_build_lv": "", "weight": 60}, {"id": 1016, "type": 22, "value": "2,4,8", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1017, "type": 23, "value": "5,10,15", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1018, "type": 24, "value": "3,6,9", "use_cond": "4,2011,1", "need_build_lv": "", "weight": 100}, {"id": 1019, "type": 25, "value": "3,6,9", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1020, "type": 26, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1021, "type": 27, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1022, "type": 28, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1023, "type": 29, "value": "3,6,9", "use_cond": "4,2016,1", "need_build_lv": "", "weight": 100}, {"id": 1024, "type": 31, "value": "2,3,4", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1025, "type": 30, "value": "50,75,100", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1026, "type": 38, "value": "10,15,30", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1027, "type": 39, "value": "10,15,30", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1028, "type": 40, "value": "10,20,40", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1029, "type": 41, "value": "1,2,4", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1030, "type": 42, "value": "20,30,40", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1031, "type": 43, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1032, "type": 44, "value": "60,80,100", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1033, "type": 45, "value": "20,40,60", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1034, "type": 46, "value": "1,2,3", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1035, "type": 37, "value": "10,20,30", "use_cond": "4,2016,1", "need_build_lv": "", "weight": 100}, {"id": 1036, "type": 47, "value": "3,6,9", "use_cond": "4,2016,1", "need_build_lv": "", "weight": 50}, {"id": 1037, "type": 48, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1038, "type": 49, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1039, "type": 50, "value": "10,20,30", "use_cond": "", "need_build_lv": "", "weight": 80}, {"id": 1040, "type": 51, "value": "5,10,15", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1041, "type": 52, "value": "5,10,15", "use_cond": "", "need_build_lv": "", "weight": 100}, {"id": 1042, "type": 53, "value": "20,30,40", "use_cond": "", "need_build_lv": "", "weight": 60}, {"id": 1043, "type": 54, "value": "30,40,50", "use_cond": "", "need_build_lv": "", "weight": 50}, {"id": 1044, "type": 55, "value": "2,3,6", "use_cond": "", "need_build_lv": "", "weight": 60}]