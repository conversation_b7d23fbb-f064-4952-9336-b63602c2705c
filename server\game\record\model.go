package record

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

const BATTLE_RECORD_VERSION = 15 // 当前的战斗记录版本号

// 记录模块
type Model struct {
	room g.Room
	sid  int32
	db   *Mongodb
}

func NewModel(room g.Room) *Model {
	sid := room.GetSID()
	return &Model{
		room: room,
		sid:  sid,
		db:   &Mongodb{sid: sid, sidStr: ut.String(sid)},
	}
}

// 删除所有记录
func (this *Model) RemoveAllRecordByUID(uid string) {
	this.db.DeleteMany(ARMY, uid)
	this.db.DeleteMany(BAZAAR, uid)
}

// 添加军队记录 ---------------------------------------------------------------------------------------------------------------------------------------
func (this *Model) AddArmyMarchRecord(tp int32, owner string, armyUid string, armyName string, armyIndex, targetIndex int32) {
	go this.db.InsertArmyRecord(ArmyRecordData{
		UID:         armyUid,
		SID:         this.sid,
		Owner:       owner,
		Type:        tp,
		ArmyName:    armyName,
		ArmyIndex:   armyIndex,
		TargetIndex: targetIndex,
		Time:        time.Now().UnixMilli(),
	})
}

// 添加军队战斗记录
func (this *Model) AddArmyBattleRecords(battleUid string, armyIdnex int32, datas []map[string]interface{}) {
	arr := []interface{}{}
	for _, m := range datas {
		arr = append(arr, ArmyRecordData{
			UID:       ut.String(m["uid"]),
			BattleUid: battleUid,
			SID:       this.sid,
			Owner:     ut.String(m["owner"]),
			ArmyName:  ut.String(m["name"]),
			ArmyIndex: armyIdnex,
			Time:      ut.Int64(m["beginTime"]),
			Battle: map[string]interface{}{
				"battleInfo": m["battleInfo"],
				"pawns":      m["pawns"],
			},
		})
	}
	go this.db.InsertManyArmyRecord(arr)
}

// 获取军队记录
func (this *Model) GetArmyRecords(uid string, isBattle bool) []*pb.ArmyRecordInfo {
	list := []*pb.ArmyRecordInfo{}
	arr, err := this.db.FindArmyRecordsByOwner(uid, 0, 50, isBattle)
	if err != "" {
		return list
	}
	for _, m := range arr {
		list = append(list, m.ToPb())
	}
	return list
}

// 根据军队uids获取军队记录
func (this *Model) GetArmyBattleRecordsByUids(uids []string, battleUid string) []*pb.ArmyRecordInfo {
	list := []*pb.ArmyRecordInfo{}
	if len(uids) == 0 {
		return list
	} else if len(uids) > 100 {
		uids = uids[:100]
	}
	arr, err := this.db.FindArmyBattleRecordsByUids(uids, battleUid)
	if err != "" {
		return list
	}
	for _, m := range arr {
		list = append(list, m.ToPb())
	}
	return list
}

// 添加战斗记录 ---------------------------------------------------------------------------------------------------------------------------------------
func (this *Model) AddBattleRecord(uid string, index int32, beginTime, endTime int64, frames []*g.BattleRecordData, treasures []*g.TreasureInfo) {
	go this.db.InsertBattleRecord(BattleRecordData{
		UID:       uid,
		SID:       this.sid,
		Version:   BATTLE_RECORD_VERSION,
		Index:     index,
		BeginTime: beginTime,
		EndTime:   endTime,
		Frames:    frames,
		Treasures: treasures,
	})
}

// 获取战斗记录
func (this *Model) GetBattleRecord(uid string) *pb.BattleRecordInfo {
	data, err := this.db.FindBattleRecord(uid)
	if err != "" {
		return nil
	}
	return data.ToPb()
}

// 获取战斗记录
func (this *Model) GetPlayerBattleRecords(uid string) []*pb.BattleScoreRecord {
	list := []*pb.BattleScoreRecord{}
	arr, err := this.db.FindPlayerBattleScoreRecordsByPage(uid, 0, 50)
	if err != "" {
		return list
	}
	for _, m := range arr {
		list = append(list, m.ToPb())
	}
	return list
}

// 添加市场记录 ---------------------------------------------------------------------------------------------------------------------------------------
func (this *Model) AddBazaarRecord(tp int8, owner, target string, res map[string]interface{}, names ...string) {
	go this.db.InsertBazaarRecord(BazaarRecordData{
		UID:    ut.ID(),
		SID:    this.sid,
		Type:   tp,
		Time:   time.Now().UnixMilli(),
		Owner:  owner,
		Target: target,
		Res:    res,
		Names:  names,
	})
}

// 获取市场记录
func (this *Model) GetBazaarRecords(uid string) []*pb.BazaarRecordInfo {
	arr, err := this.db.FindBazaarRecords(uid, 0, 50)
	if err != "" {
		return []*pb.BazaarRecordInfo{}
	}
	list := []*pb.BazaarRecordInfo{}
	for _, m := range arr {
		list = append(list, m.ToPb())
	}
	return list
}

// 添加战斗积分记录 ---------------------------------------------------------------------------------------------------------------------------------------
func (this *Model) AddBattleScoreRecords(recordUid string, startTime, endTime int64, alliUidMap map[string]string, deadInfoMap map[string][]*g.PawmDeadRecord,
	validUserInfoMap map[string]map[int32]int32, invalidUserInfoMap map[string]map[int32]int32, scoreMap map[string]float64,
	pawnStatisticMap map[string]map[int32]map[int32]int32, armyUidListMap map[string][]string, index int32, playerWinMap map[string]bool,
) {
	t := time.UnixMilli(int64(startTime))
	date := t.Format("2006-01-02")
	recordList := []interface{}{}
	for userId, alliUid := range alliUidMap {
		if userId == "" {
			continue
		}
		record := &g.BattleScoreRecordData{
			Uid:           recordUid,
			Owner:         userId,
			AlliUid:       alliUid,
			BeginTime:     startTime,
			EndTime:       endTime,
			Date:          date,
			ValidInfo:     validUserInfoMap[userId],
			InvalidInfo:   invalidUserInfoMap[userId],
			DeadInfo:      deadInfoMap[userId],
			PawnStatistic: pawnStatisticMap[userId],
			ArmyUidList:   armyUidListMap[userId],
			Index:         index,
			Version:       BATTLE_RECORD_VERSION,
			Iswin:         playerWinMap[userId],
		}
		if score, ok := scoreMap[userId]; ok {
			record.Score = score
		} else {
			record.Score = 0
		}
		recordList = append(recordList, record)
	}
	go this.db.InsertManyBattleSocreRecord(recordList)
}

// 获取战斗积分记录
func (this *Model) GetBattleScoreRecordsByDate(uid, date, alli string) (*g.BattleScoreRecordData, bool) {
	// 查询该玩家当天的所有记录
	arr, err := this.db.FindBattleScoreRecords(uid, date, alli)
	if err != nil {
		return nil, false
	}
	if len(arr) == 0 {
		return nil, false
	}
	rst := MergeBattleScoreRecord(uid, date, alli, arr, false)
	return rst, true
}

// 获取玩家所有战斗积分记录
func (this *Model) GetPlayerBattleScoreRecords(userId, date, alli string) (*g.BattleScoreRecordData, bool) {
	// 查询该玩家当天的所有记录
	arr, err := this.db.FindPlayerBattleScoreRecords(userId, alli)
	if err != nil {
		return nil, false
	}
	if len(arr) == 0 {
		return nil, false
	}
	rst := MergeBattleScoreRecord(userId, date, alli, arr, true)
	return rst, true
}

// 整合战斗数据记录
func MergeBattleScoreRecord(userId, date, alli string, arr []g.BattleScoreRecordData, isAll bool) *g.BattleScoreRecordData {
	rst := &g.BattleScoreRecordData{
		Owner:       userId,
		Date:        date,
		ValidInfo:   map[int32]int32{},
		InvalidInfo: map[int32]int32{},
		DeadInfo:    []*g.PawmDeadRecord{},
		AlliUid:     alli,
	}
	// 整合数据
	for _, m := range arr {
		if m.DeadInfo != nil {
			for _, v := range m.DeadInfo {
				rst.DeadInfo = append(rst.DeadInfo, &g.PawmDeadRecord{
					Id:          v.Id,
					Lv:          v.Lv,
					KillerId:    v.KillerId,
					KillerOwner: v.KillerOwner,
				})
			}
		}
		if m.ValidInfo != nil {
			pb.MapMergeInt32(rst.ValidInfo, m.ValidInfo)
		}
		if m.InvalidInfo != nil {
			pb.MapMergeInt32(rst.InvalidInfo, m.InvalidInfo)
		}
		if m.Score > 0 {
			rst.Score += ut.Float64Round(m.Score, 2) // 保留2位小数
		}
		if isAll {
			rst.BattleCount += m.BattleCount
		} else if m.ArmyUidList != nil && len(m.ArmyUidList) > 0 {
			rst.BattleCount++ // 派兵参与的记录战斗场次
		}
	}
	return rst
}

// 添加战斗积分记录玩家每日统计
func (this *Model) AddPlayerBattleScoreRecord(record *g.BattleScoreRecordData) {
	this.db.InsertPlayerBattleScoreRecord(record)
}

// 查询玩家指定日期战斗积分记录
func (this *Model) GetPlayerBattleScoreRecordsByDate(uid, date string) *g.BattleScoreRecordData {
	record, err := this.db.FindPlayerBattleScoreRecord(uid, date)
	if err == nil {
		return &record
	}
	return nil
}

// 查询指定联盟所有玩家指定日期的战斗积分记录
func (this *Model) GetAlliPlayersBattleScoreRecordsByDate(alliUid, date string) []*pb.BattleScoreRecordByDate {
	ret := []*pb.BattleScoreRecordByDate{}
	records, err := this.db.FindAlliPlayersBattleScoreRecords(alliUid, date)
	if err == nil {
		for _, v := range records {
			ret = append(ret, this.BattleScoreRecordToPb(v))
		}
	}
	return ret
}

// BattleScoreRecordData数据转pb
func (this *Model) BattleScoreRecordToPb(record g.BattleScoreRecordData) *pb.BattleScoreRecordByDate {
	ret := &pb.BattleScoreRecordByDate{
		Owner:       record.Owner,
		Date:        record.Date,
		BattleCount: int32(record.BattleCount),
		Score:       pb.Int32(record.Score),
	}
	if record.ValidInfo != nil {
		for k, v := range record.ValidInfo {
			if k == bdtype.KILL_PAWN {
				ret.KillCount += int32(v)
			} else if k == bdtype.TO_PAWN_DAMGE {
				ret.PawnDamage += int32(v)
			} else if k == bdtype.HIT_DAMAGE_MITIGATED {
				ret.PawnHitDamage += int32(v)
			} else if k == bdtype.BUILD_DAMAGE {
				ret.BuildDamage += int32(v)
			} else if k == bdtype.DAMAGE_TO_BUILD {
				ret.DamageToBuild += int32(v)
			}
		}
	}
	if record.InvalidInfo != nil {
		for k, v := range record.InvalidInfo {
			if k == bdtype.KILL_PAWN {
				ret.KillCount += int32(v)
			} else if k == bdtype.TO_PAWN_DAMGE {
				continue // 无效战斗的士兵伤害不计入
			} else if k == bdtype.HIT_DAMAGE_MITIGATED {
				ret.PawnHitDamage += int32(v) * int32(bdtype.ALLI_SCORE_INVALID_BATTLE_PARAM[k])
			} else if k == bdtype.BUILD_DAMAGE {
				ret.BuildDamage += int32(v) * int32(bdtype.ALLI_SCORE_INVALID_BATTLE_PARAM[k])
			} else if k == bdtype.DAMAGE_TO_BUILD {
				ret.DamageToBuild += int32(v) * int32(bdtype.ALLI_SCORE_INVALID_BATTLE_PARAM[k])
			}
		}
	}
	if record.DeadInfo != nil {
		for _, v := range record.DeadInfo {
			if v.Id > 0 {
				ret.DeadCount++
			}
		}
	}
	return ret
}
