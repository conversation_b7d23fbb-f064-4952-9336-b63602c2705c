package lobby

import (
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

var (
	RANK_SEASON int32 = 1 //当前赛季
)

type TempRankInfo struct {
	no   int
	info *pb.UserScoreRankInfo
}

type ScoreRankManager struct {
	deadlock.RWMutex
	ranks          []*pb.UserScoreRankInfo //玩家排行列表
	lastUpdateTime int64                   //最后一次刷新排行榜时间
	tempList       []*pb.UserScoreRankInfo
	UserInfoMap    *ut.MapLock[string, *TempRankInfo]
}

var scoreRankMgr = &ScoreRankManager{UserInfoMap: ut.NewMapLock[string, *TempRankInfo]()}

type UserRankScoreInfo struct {
	user *User
	data map[string]interface{}
	rank int32
}

func (this *User) ToRankRewardRecordPb() []int32 {
	rankRewardRecord := []int32{}
	this.RankRewardRecord.ForEach(func(v bool, k int32) bool {
		rankRewardRecord = append(rankRewardRecord, k)
		return true
	})
	return rankRewardRecord
}

func (this *User) CheckUpdateRankSeason(lobby *Lobby) {
	if this.RankSeason == RANK_SEASON {
		return
	} else if this.RankScore > 0 { //清空段位
		this.SeasonRankScoreHistory = append(this.SeasonRankScoreHistory, map[string]interface{}{
			"season":     0,
			"rankScore":  this.RankScore,
			"rankCounts": this.AccTotalRankCounts,
		})
		pawnSkinList := []int32{3101104, 3102104, 3103103, 3104103, 3105104, 3106102}
		pawnSkinLen := len(pawnSkinList)
		// 发放奖励
		if this.RankSeason > 0 {
			// 0赛季主动发奖励
		} else if this.RankScore >= 2100 { //王者 主城皮肤，4个随机士兵皮肤，头像，表情
			indexs := randomIndexs(pawnSkinLen, 4)
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{
				g.NewTypeObj(ctype.HEAD_ICON, 204, 1),
				g.NewTypeObj(ctype.CHAT_EMOJI, 2042, 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[0]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[1]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[2]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[3]], 1),
				g.NewTypeObj(ctype.CITY_SKIN, 1001101, 1),
			})
		} else if this.RankScore >= 1800 { //大师 3个随机士兵皮肤，头像，表情
			indexs := randomIndexs(pawnSkinLen, 3)
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{
				g.NewTypeObj(ctype.HEAD_ICON, 204, 1),
				g.NewTypeObj(ctype.CHAT_EMOJI, 2042, 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[0]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[1]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[2]], 1),
			})
		} else if this.RankScore >= 1500 { //钻石 3个随机士兵皮肤，头像
			indexs := randomIndexs(pawnSkinLen, 3)
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{
				g.NewTypeObj(ctype.HEAD_ICON, 159, 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[0]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[1]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[2]], 1),
			})
		} else if this.RankScore >= 1200 { //铂金 2个随机士兵皮肤，头像
			indexs := randomIndexs(pawnSkinLen, 2)
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{
				g.NewTypeObj(ctype.HEAD_ICON, 159, 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[0]], 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[indexs[1]], 1),
			})
		} else if this.RankScore >= 900 { //黄金 1个随机士兵皮肤，头像
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{
				g.NewTypeObj(ctype.HEAD_ICON, 159, 1),
				g.NewTypeObj(ctype.PAWN_SKIN, pawnSkinList[ut.Random(0, pawnSkinLen-1)], 1),
			})
		} else { // 头像
			lobby.sendMailItemOne(0, slg.MAIL_RANK_SEASON_REWARD_ID, "", this.Nickname, "-1", this.UID, []*g.TypeObj{g.NewTypeObj(ctype.HEAD_ICON, 158, 1)})
		}
	}
	this.RankScore = 0
	this.HideRankScore = []int32{0, 0}
	this.AccTotalRankCounts = []int32{0, 0}
	this.RankRewardRecord.Clean()
	this.RankSeason = RANK_SEASON
}

func randomIndexs(l int, count int) []int {
	idxs := []int{}
	indexs := []int{}
	for i := 0; i < l; i++ {
		indexs = append(indexs, i)
	}
	for i := 0; i < count; i++ {
		idx := ut.Random(0, len(indexs)-1)
		idxs = append(idxs, indexs[idx])
		indexs = append(indexs[:idx], indexs[idx+1:]...)
	}
	sort.Slice(idxs, func(i, j int) bool { return idxs[i] < idxs[j] })
	return idxs
}

// rank = 当前积分排名 从0开始
// S = 当前排行榜总人数（0分不入排行榜）
// MS = 这局所有人的平均胜点
func SettleUserRankScore(user *User, rank float64, S float64, MS float64) int32 {
	PS, min, max := 0.0, 0.0, 0.0
	// 按照积分排名，阶梯计算胜点
	raito := (rank + 1.0) / S
	if raito <= 0.05 { //前5%  +90~100
		PS, min, max = S*0.05, 90, 10
	} else if raito <= 0.15 { //前15%  +40~50
		PPS := S * 0.05
		PS, min, max = S*0.15-PPS, 40, 10
		rank = math.Max(0, rank-PPS)
	} else if raito <= 0.3 { //前30%  +15~25
		PPS := S * 0.15
		PS, min, max = S*0.3-PPS, 15, 10
		rank = math.Max(0, rank-PPS)
	} else if raito <= 0.5 { //前50%  +8~12
		PPS := S * 0.3
		PS, min, max = S*0.5-S*0.3, 8, 4
		rank = math.Max(0, rank-PPS)
	} else { //后50%
		PS = S * 0.5
		rank = math.Max(0, rank-PS)
		if user.RankScore < 300 { //黑铁  +1~5
			min, max = 1, 4
		} else {
			rank = math.Max(0, PS-rank)
			if user.RankScore < 600 { //青铜  -1~-10
				min, max = -1, -9
			} else if user.RankScore < 900 { //白银  -1~-25
				min, max = -1, -24
			} else { //其他  -1~-50
				min, max = -1, -49
			}
		}
	}
	// 按人数和排名分配
	G := min + max/PS*math.Max(PS-rank, 1)
	// // 修正分数 = G + 40 * Min(Max(Sqrt(MS/A), 0.8), 1.2)
	// // 大概意思就是 如果你赢了比你段位高的，会额外获得分数，如果赢了比你段位低的，那么会减少获得的分数
	// A := float64(user.RankScore)
	// A, MS = math.Max(1, A), math.Max(1, MS)
	// if win {
	// 	G *= math.Min(math.Max(math.Sqrt(MS/A), 0.8), 1.2)
	// } else {
	// 	G *= math.Min(math.Max(math.Sqrt(A/MS), 0.8), 1.2)
	// }
	// log.Info("SettleUserRankScore uid: %v, rank: %v, PS: %v, min: %v, max: %v, MS: %v, G: %v, A: %v", user.UID, rank, PS, min, max, MS, G, A)
	return AddUserRankScore(user, int32(G))
}

// 添加分数
func AddUserRankScore(user *User, G int32) int32 {
	A := user.RankScore        //当前段位分
	T := user.HideRankScore[1] //历史获得最高段位分
	id, winPoint := resolutionRankScore(A)
	if G >= 0 {
	} else if winPoint == 0 {
		if id <= 9 { //黄金及以下段位，不会降段
			G = 0
		} else {
			G = ut.MaxInt32(-25, G) //如果当前段位胜点为0时被扣胜点，最多会被降级到前一个级别的75胜点  也就是最多扣25
		}
	} else {
		G = ut.MaxInt32(-winPoint, G) //如果玩家在当前段位胜点大于0，胜点最多被扣至0
	}
	A += G
	user.RankScore = A
	if A > T {
		user.HideRankScore[1] = A
	}
	// log.Info("AddUserRankScore uid: %v, id: %v, winPoint: %v, G: %v, A: %v", user.UID, id, winPoint, G, A)
	return G
}

// 拆分段位分
func resolutionRankScore(score int32) (int32, int32) {
	if score <= 0 {
		return 0, 0
	}
	list := config.GetJson("seasonReward").Datas
	for i, l := 0, len(list)-1; i < l; i++ {
		m := list[i]
		rankScore := ut.Int32(m["rank_score"])
		if score < rankScore {
			return 0, score
		} else if score < ut.Int32(list[i+1]["rank_score"]) {
			return ut.Int32(m["id"]), score - rankScore
		}
	}
	last := list[len(list)-1]
	return ut.Int32(last["id"]), ut.MaxInt32(0, score-ut.Int32(last["rank_score"]))
}

func checkUpdateScoreRankInfo() {
	now := time.Now().UnixMilli()
	if ut.DateZeroTime(scoreRankMgr.lastUpdateTime)+ut.TIME_DAY+ut.TIME_MINUTE*10 > now && scoreRankMgr.ranks != nil {
		return
	}
	scoreRankMgr.lastUpdateTime = now
	list, err := db.GetUserListByRank()
	if err != nil {
		log.Error("checkUpdateScoreRankInfo err=%v", err.Error())
		return
	}
	scoreRankMgr.Lock()
	defer scoreRankMgr.Unlock()
	scoreRankMgr.ranks = []*pb.UserScoreRankInfo{}
	for _, m := range list {
		if ut.Int32(m["rank_season"]) != RANK_SEASON {
			continue
		}
		rankCount := 0
		arr := ut.IntArray(m["acc_total_rank_counts"])
		if len(arr) == 2 {
			rankCount = arr[1]
		}
		uid := ut.String(m["uid"])
		nickname, headIcon := ut.String(m["nickname"]), ut.String(m["head_icon"])
		if user := GetUser(uid); user != nil {
			nickname = user.Nickname
			headIcon = user.HeadIcon
		}
		scoreRankMgr.ranks = append(scoreRankMgr.ranks, &pb.UserScoreRankInfo{
			Uid:       uid,
			Nickname:  nickname,
			HeadIcon:  headIcon,
			Score:     pb.Int32(m["rank_score"]),
			RankCount: pb.Int32(rankCount),
		})
	}
	scoreRankMgr.tempList = scoreRankMgr.ranks[:ut.Min(len(scoreRankMgr.ranks), 50)]
	scoreRankMgr.UserInfoMap.Clean()
}

// 获取评分排行榜
func GetScoreRanks(uid string) ([]*pb.UserScoreRankInfo, int, *pb.UserScoreRankInfo) {
	checkUpdateScoreRankInfo()
	data := scoreRankMgr.UserInfoMap.Get(uid)
	if data == nil {
		if no := array.FindIndex(scoreRankMgr.ranks, func(m *pb.UserScoreRankInfo) bool { return m.Uid == uid }); no != -1 {
			data = &TempRankInfo{no: no, info: scoreRankMgr.ranks[no]}
		} else {
			data = &TempRankInfo{no: -1, info: nil}
		}
		scoreRankMgr.UserInfoMap.Set(uid, data)
	}
	return scoreRankMgr.tempList, data.no, data.info
}
