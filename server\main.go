package main

import (
	"fmt"
	"math/rand"
	httpNet "net/http"
	_ "net/http/pprof"
	"os"
	"slgsrv/server/chat"
	slg "slgsrv/server/common"
	"slgsrv/server/common/af"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game"
	"slgsrv/server/game/common/config"
	mgate "slgsrv/server/gate"
	"slgsrv/server/http"
	"slgsrv/server/lobby"
	"slgsrv/server/login"
	"slgsrv/server/mail"
	"slgsrv/server/match"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"
	rds "slgsrv/utils/redis"
	"strconv"
	"strings"
	"time"

	mqant "github.com/huyangv/vmqant"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	"github.com/huyangv/vmqant/registry/consul"
	"github.com/huyangv/vmqant/selector"
	"github.com/nats-io/nats.go"
	"github.com/sasha-s/go-deadlock"
	"google.golang.org/protobuf/proto"
)

func main() {
	// slg.PrintBanner()
	slg.InitDeadlockListener()
	rs := consul.NewRegistry(func(options *registry.Options) {
		options.Addrs = []string{slg.GetConsulUrl()}
	})
	nc, err := nats.Connect(slg.GetNatsUrl(), nats.MaxReconnects(10000))
	if err != nil {
		log.Error("nats error %v", err)
		return
	}
	debug := slg.IsDebug()
	rds.DEBUG = debug

	app := mqant.CreateApp(
		module.KillWaitTTL(time.Minute*5),
		module.Debug(debug),                     //只有是在调试模式下才会在控制台打印日志, 非调试模式下只在日志文件中输出日志
		module.Nats(nc),                         //指定nats rpc
		module.Registry(rs),                     //指定服务发现
		module.RegisterTTL(20*time.Second),      //TTL指定在发现之后注册的信息存在多长时间 然后过期并被删除
		module.RegisterInterval(10*time.Second), //时间间隔是服务应该重新注册的时间，以保留其在服务发现中的注册信息
	)

	go func() {
		pid := app.GetProcessID()
		port := "6060"
		gamePid := strings.Replace(pid, "game", "", 1)
		if strings.HasPrefix(pid, "game") && gamePid != "" && len(gamePid) >= 5 {
			sid := ut.Int(gamePid)
			roomType := sid / slg.ROOM_TYPE_FLAG
			pre := gamePid[0:1]
			if roomType == slg.NORMAL_SERVER_TYPE {
				pre = "3"
			}
			port = pre + gamePid[len(gamePid)-4:]
		}
		httpNet.ListenAndServe("0.0.0.0:"+port, nil)
	}()

	//重写返回客户端时的协议
	app.SetProtocolMarshal(func(Trace string, Result interface{}, Error string) (module.ProtocolMarshal, string) {
		// 假设返回客户端的必然是protobuf 此处直接断言处理
		data, _ := Result.([]byte)
		bytes, _ := proto.Marshal(&pb.S2C_RESULT{
			Data:  data,
			Error: Error,
		})
		return app.NewProtocolMarshal(bytes), ""
	})

	// 配置解析完成
	_ = app.OnConfigurationLoaded(func(app module.App) {
		serverType := app.GetSettings().Settings["ServerType"].(string)
		if serverType != slg.MACH_SERVER_TYPE_GATE {
			url := app.GetSettings().Settings["MongodbURL"].(string)
			dbname := app.GetSettings().Settings["MongodbDB"].(string)
			mgo.Init(url, dbname, serverType)
			// 加载配置
			config.Load()
			// 初始化数数上报
			ta.Init()
			// 初始化AF上报
			af.Init()
		}
		// 连接Redis
		rds.Init(app.GetSettings().Settings["Redis"].(string))
		// 如果是测试版本 这里就要通过进程来判断 因为提审版本和测试服在一个物理服务器上面
		if serverType == slg.MACH_SERVER_TYPE_DEVOLOPMENT {
			pid := strconv.Itoa(os.Getpid())
			app.Options().Selector.Init(selector.SetStrategy(func(services []*registry.Service) selector.Next {
				var nodes []*registry.Node
				for _, service := range services {
					for _, node := range service.Nodes {
						if node.Metadata["pid"] == pid {
							nodes = append(nodes, node)
						}
					}
				}

				var mtx deadlock.Mutex
				return func() (*registry.Node, error) {
					mtx.Lock()
					defer mtx.Unlock()
					if len(nodes) == 0 {
						return nil, fmt.Errorf("no node")
					}
					index := rand.Intn(int(len(nodes)))
					return nodes[index], nil
				}
			}))
			// 开发服使用同一个redis
			rds.InitTranslateRds(app.GetSettings().Settings["Redis"].(string))
		}
		if serverType == slg.MACH_SERVER_TYPE_LOBBY {
			// 大厅服连接翻译缓存专用redis
			rds.InitTranslateRds(app.GetSettings().Settings["TranslateRedis"].(string))
		}
	})

	// 应用启动完成
	_ = app.OnStartup(func(app module.App) {
		//log.LogBeego().SetFormatFunc(logs.DefineErrorLogFunc(app.GetProcessID(), 4))
	})

	// 启动
	err = app.Run(
		mgate.Module(),
		game.Module(),
		lobby.Module(),
		login.Module(),
		http.Module(),
		match.Module(),
		chat.Module(),
		mail.Module(),
	)
	if err != nil {
		log.Error(err.Error())
	}
}
