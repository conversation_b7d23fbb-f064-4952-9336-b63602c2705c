package game

import (
	"fmt"
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	r "slgsrv/server/game/room"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var Module = func() module.Module {
	return new(Game)
}

type Game struct {
	basemodule.BaseModule
}

func (this *Game) GetType() string {
	return "game"
}

func (this *Game) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Game) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	appSettings := app.GetSettings()
	serverType := ut.String(appSettings.Settings["ServerType"])
	if serverType == "game" {
		id := app.Options().ProcessID
		log.Info("OnAppConfigurationLoaded, id:%v", id)
		r.LoadRoomById(ut.Int(strings.Replace(id, "game", "", 1)))
	} else if serverType == "development" {
		modules := appSettings.Module[this.GetType()]
		for _, conf := range modules {
			r.LoadRoomById(ut.Int(conf.Settings["Sid"]))
		}
	}
}

func (this *Game) OnInit(app module.App, settings *conf.ModuleSettings) {
	id := settings.ID
	this.BaseModule.OnInit(this, app, settings, server.ID(id))
	this.GetServer().Options().Metadata["sid"] = strings.Replace(id, "game", "", 1) //设置元数据 方便路由的是区分节点
	this.InitRpc()
	this.InitHttp()
	this.InitHDPlayer()
	this.InitHDWorld()
	this.InitHDBuild()
	this.InitHDArea()
	this.InitHDAlliance()
	this.InitHDBazaar()
	this.InitHDChat()
	this.InitHDTask()
	this.InitHDEquip()
	this.InitHDRank()
	this.InitHDShop()
	this.InitHDPolicy()
	this.InitHDCeri()
	this.InitHDAncient()
	this.InitHDHero()
}

func (this *Game) Run(closeSig chan bool) {
	r.RunAllRoom(&this.BaseModule)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	r.SaveAllRoom()
	log.Info("数据保存完毕")
}

func (this *Game) OnDestroy() {
	this.BaseModule.OnDestroy()
}

func checkError(session gate.Session) (string, *r.Model, *player.Model, *world.Model) {
	sid, uid := ut.Int32(session.Get("sid")), session.GetUserID()
	room := r.GetRoomById(sid)
	if room == nil {
		log.Error("game checkError err: ROOM_NOT_EXIST")
		return ecode.ROOM_NOT_EXIST.String(), nil, nil, nil
	} else if room.IsClose() {
		log.Error("game checkError err: ROOM_CLOSE")
		return ecode.ROOM_CLOSE.String(), nil, nil, nil
	}
	plr, ok := room.GetPlayer(uid).(*player.Model) //获取玩家
	if !ok || plr == nil {
		log.Error("game checkError err: NOT_BIND_UID")
		return ecode.NOT_BIND_UID.String(), nil, nil, nil
	}
	return "", room, plr, room.GetWorld().(*world.Model)
}

func checkHttpError(sid string) (error, *r.Model, *world.Model) {
	room := r.GetRoomById(ut.Int32(sid))
	if room == nil {
		return fmt.Errorf("服务器不存在"), nil, nil
	} else if room.IsClose() {
		return fmt.Errorf("服务器已关闭"), nil, nil
	}
	return nil, room, room.GetWorld().(*world.Model)
}

// 发送Rpc到lobby
func (this *Game) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	if id == "" {
		log.Error("game InvokeLobbyRpc not lid, func: %v", _func)
		return
	}
	serverType := slg.MACH_SERVER_TYPE_LOBBY
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	return this.Invoke(serverType+"@"+serverType+id, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Game) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	if id == "" {
		log.Error("game InvokeLobbyRpcNR not lid, func: %v", _func)
		return
	}
	serverType := slg.MACH_SERVER_TYPE_LOBBY
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	this.InvokeNR(serverType+"@"+serverType+id, _func, paramsBytesArr...)
}

// 发送Rpc到chat
func (this *Game) InvokeChatRpc(_func string, params ...interface{}) (result interface{}, err string) {
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	return this.Invoke(slg.MACH_SERVER_TYPE_CHAT, _func, paramsBytesArr...)
}

// 发送Rpc到chat
func (this *Game) InvokeChatRpcNR(_func string, params ...interface{}) {
	paramsBytesArr := array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
	this.InvokeNR(slg.MACH_SERVER_TYPE_CHAT, _func, paramsBytesArr...)
}

// 改变金币
func (this *Game) ChangePlayerGold(plr *player.Model, val, reason int32) int32 {
	data, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHANGEIGOLD, plr.Uid, val, reason))
	if err != "" {
		log.Error("login ChangePlayerGold Error %v", err)
		return -1
	}
	return ut.Int32(data["gold"])
}

// 添加玩家物品到用户服
func (this *Game) AddUserItemsToLobby(plr *player.Model, items []*g.TypeObj, reason int32) *pb.UpdateOutPut {
	if len(items) == 0 {
		return nil
	}
	data, err := ut.RpcBytes(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_ADD_USER_ITEMS, plr.Uid, items, reason))
	if err != "" {
		log.Error("AddUserItemsToLobby Error: %v", err)
		return nil
	}
	output := &pb.UpdateOutPut{}
	if e := pb.ProtoUnMarshal(data, output); e != nil {
		log.Error("AddUserItemsToLobby parseError: %v", e)
		return nil
	}
	return output
}

// 改变玩家物品
func (this *Game) ChangePlayerItems(plr *player.Model, items []*g.TypeObj, reason int32) *pb.UpdateOutPut {
	gameItems, userItems := g.ResolutionItems(items)
	// 发放游戏服道具
	plr.ChangeCostByTypeObjs(gameItems, 1)
	// 发放用户服道具
	output := this.AddUserItemsToLobby(plr, userItems, reason)
	return plr.ToItemByTypeObjsPbOut(gameItems, output)
}

// 是否被禁言
func (this *Game) IsBannedChat(plr *player.Model) bool {
	bannedChatEndTime := 0
	if data, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_USER_BANNED_CHAT_ENDTIME, plr.Uid)); data != nil {
		bannedChatEndTime = ut.Int(data["bannedChatEndTime"])
	}
	return bannedChatEndTime > 0
}
