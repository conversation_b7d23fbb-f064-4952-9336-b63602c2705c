package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	ut "slgsrv/utils"
)

// 检测回合开始回血
type CheckBeginBlood struct {
	BaseAction

	bloodNeedTime int32 //回血需要的时间
}

func (this *CheckBeginBlood) OnInit(conf map[string]interface{}) {
	this.bloodNeedTime = ut.Int32(conf["parameters"])
}

func (this *CheckBeginBlood) OnOpen() {
	isCanBlood := this.target.GetEquipEffectByType(eeffect.BEGIN_BLOOD) != nil || this.target.IsHasBuff(bufftype.TOUGH) || this.target.IsHasStrategys(40204, 50028) || this.target.GetBuffValue(bufftype.TONDEN_RECOVER) > 0
	this.SetBlackboardData("isBeginBlood", !isCanBlood || this.target.IsFullHP())
	this.SetTreeBlackboardData("isBloodAction", false)
}

func (this *CheckBeginBlood) OnLeave(state Status) {
	this.SetBlackboardData("isBeginBlood", state == SUCCESS)
}

func (this *CheckBeginBlood) OnTick(dt int32) Status {
	if ut.Bool(this.GetTreeBlackboardData("isBatter")) || ut.Bool(this.GetBlackboardData("isBeginBlood")) || this.target.IsFullHP() {
		return SUCCESS
	}
	currTime := ut.Int32(this.GetBlackboardData("currTime"))
	if currTime >= this.bloodNeedTime {
		return SUCCESS
	} else if currTime == 0 {
		maxHp := float64(this.target.GetEntity().GetMaxHP())
		// 屯垦令
		val := int32(this.target.GetBuffValue(bufftype.TONDEN_RECOVER))
		// 韬略
		val += this.target.GetStrategyValue(50028)
		// 酒葫芦
		if effect := this.target.GetEquipEffectByType(eeffect.BEGIN_BLOOD); effect != nil {
			val += ut.RoundInt32(maxHp * effect.Value * 0.01)
		}
		// 曹仁 满层坚韧
		if buff := this.target.GetBuff(bufftype.TOUGH); buff != nil {
			if heroSkill := this.target.GetEntity().GetPortrayalSkill(); heroSkill != nil && heroSkill.Id == hero.CAO_REN && buff.GetValueInt() >= heroSkill.Value {
				val += ut.RoundInt32(maxHp * heroSkill.GetParamsFloat64())
			}
		}
		// 韬略 恢复当前生命
		val += ut.RoundInt32(float64(this.target.GetCurHp()) * float64(this.target.GetStrategyValue(40204)) * 0.01)
		if val == 0 {
			return SUCCESS
		}
		this.target.OnHeal(val, false)
		this.target.ChangeState(constant.PAWN_STATE_HEAL)
		this.SetTreeBlackboardData("isBloodAction", true)
	}
	this.SetBlackboardData("currTime", currTime+dt)
	return RUNNING
}
