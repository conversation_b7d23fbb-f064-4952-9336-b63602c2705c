[{"id": 30100001, "type": 1, "cond": "1003,0,1", "prev_id": 0, "reward": "5,0,50", "show_progress": 1, "desc": "taskText.1005", "params": 1}, {"id": 30100002, "type": 1, "cond": "1003,0,2", "prev_id": 30100001, "reward": "5,0,55", "show_progress": 1, "desc": "taskText.1005", "params": 2}, {"id": 30100003, "type": 1, "cond": "1003,0,3", "prev_id": 30100002, "reward": "5,0,60", "show_progress": 1, "desc": "taskText.1005", "params": 3}, {"id": 30100004, "type": 1, "cond": "1003,0,4", "prev_id": 30100003, "reward": "5,0,65", "show_progress": 1, "desc": "taskText.1005", "params": 4}, {"id": 30100005, "type": 1, "cond": "1003,0,5", "prev_id": 30100004, "reward": "5,0,70", "show_progress": 1, "desc": "taskText.1005", "params": 5}, {"id": 30200001, "type": 1, "cond": "1002,0,1", "prev_id": 0, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 1}, {"id": 30200002, "type": 1, "cond": "1002,0,2", "prev_id": 30200001, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 2}, {"id": 30200003, "type": 1, "cond": "1002,0,3", "prev_id": 30200002, "reward": "24,330401,3", "show_progress": 1, "desc": "taskText.1004", "params": 3}, {"id": 30200004, "type": 1, "cond": "1002,0,4", "prev_id": 30200003, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 4}, {"id": 30200005, "type": 1, "cond": "1002,0,5", "prev_id": 30200004, "reward": "23,0,10", "show_progress": 1, "desc": "taskText.1004", "params": 5}, {"id": 30200006, "type": 1, "cond": "1002,0,6", "prev_id": 30200005, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 6}, {"id": 30200007, "type": 1, "cond": "1002,0,7", "prev_id": 30200006, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 7}, {"id": 30200008, "type": 1, "cond": "1002,0,8", "prev_id": 30200007, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 8}, {"id": 30200009, "type": 1, "cond": "1002,0,9", "prev_id": 30200008, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 9}, {"id": 30200010, "type": 1, "cond": "1002,0,10", "prev_id": 30200009, "reward": "5,0,20", "show_progress": 1, "desc": "taskText.1004", "params": 10}, {"id": 30200011, "type": 1, "cond": "1002,0,11", "prev_id": 30200010, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 11}, {"id": 30200012, "type": 1, "cond": "1002,0,12", "prev_id": 30200011, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 12}, {"id": 30200013, "type": 1, "cond": "1002,0,13", "prev_id": 30200012, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 13}, {"id": 30200014, "type": 1, "cond": "1002,0,14", "prev_id": 30200013, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 14}, {"id": 30200015, "type": 1, "cond": "1002,0,15", "prev_id": 30200014, "reward": "25,2,1", "show_progress": 1, "desc": "taskText.1004", "params": 15}, {"id": 30200016, "type": 1, "cond": "1002,0,16", "prev_id": 30200015, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 16}, {"id": 30200017, "type": 1, "cond": "1002,0,17", "prev_id": 30200016, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 17}, {"id": 30200018, "type": 1, "cond": "1002,0,18", "prev_id": 30200017, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 18}, {"id": 30200019, "type": 1, "cond": "1002,0,19", "prev_id": 30200018, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 19}, {"id": 30200020, "type": 1, "cond": "1002,0,20", "prev_id": 30200019, "reward": "23,0,10", "show_progress": 1, "desc": "taskText.1004", "params": 20}, {"id": 30200021, "type": 1, "cond": "1002,0,21", "prev_id": 30200020, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 21}, {"id": 30200022, "type": 1, "cond": "1002,0,22", "prev_id": 30200021, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 22}, {"id": 30200023, "type": 1, "cond": "1002,0,23", "prev_id": 30200022, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 23}, {"id": 30200024, "type": 1, "cond": "1002,0,24", "prev_id": 30200023, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 24}, {"id": 30200025, "type": 1, "cond": "1002,0,25", "prev_id": 30200024, "reward": "23,0,10", "show_progress": 1, "desc": "taskText.1004", "params": 25}, {"id": 30200026, "type": 1, "cond": "1002,0,26", "prev_id": 30200025, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 26}, {"id": 30200027, "type": 1, "cond": "1002,0,27", "prev_id": 30200026, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 27}, {"id": 30200028, "type": 1, "cond": "1002,0,28", "prev_id": 30200027, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 28}, {"id": 30200029, "type": 1, "cond": "1002,0,29", "prev_id": 30200028, "reward": "5,0,5", "show_progress": 1, "desc": "taskText.1004", "params": 29}, {"id": 30200030, "type": 1, "cond": "1002,0,30", "prev_id": 30200029, "reward": "25,3,1", "show_progress": 1, "desc": "taskText.1004", "params": 30}, {"id": 30300001, "type": 1, "cond": "1028,0,1", "prev_id": "", "reward": "5,0,100|11,3101101,1", "show_progress": "", "desc": "taskText.1021", "params": ""}, {"id": 30400001, "type": 1, "cond": "4,2001,10", "prev_id": 0, "reward": "25,1,1", "show_progress": 1, "desc": "taskText.1032", "params": "buildText.name_2001|10"}, {"id": 30400002, "type": 1, "cond": "1033,0,1", "prev_id": 30400001, "reward": "25,2,1", "show_progress": 1, "desc": "taskText.1031", "params": 1}, {"id": 30400003, "type": 1, "cond": "1033,0,2", "prev_id": 30400002, "reward": "23,0,25", "show_progress": 1, "desc": "taskText.1031", "params": 2}, {"id": 30400004, "type": 1, "cond": "1033,0,3", "prev_id": 30400003, "reward": "23,0,30", "show_progress": 1, "desc": "taskText.1031", "params": 3}, {"id": 30400005, "type": 1, "cond": "1033,0,5", "prev_id": 30400004, "reward": "23,0,35", "show_progress": 1, "desc": "taskText.1031", "params": 5}, {"id": 30400006, "type": 1, "cond": "1033,0,10", "prev_id": 30400005, "reward": "23,0,40", "show_progress": 1, "desc": "taskText.1031", "params": 10}, {"id": 30500001, "type": 1, "cond": "1038,0,1", "prev_id": 0, "reward": "23,0,2|5,0,5", "show_progress": 1, "desc": "taskText.1036", "params": 1}, {"id": 30500002, "type": 1, "cond": "1038,0,3", "prev_id": 30500001, "reward": "23,0,3|5,0,8", "show_progress": 1, "desc": "taskText.1036", "params": 3}, {"id": 30500003, "type": 1, "cond": "1038,0,5", "prev_id": 30500002, "reward": "23,0,4|5,0,10", "show_progress": 1, "desc": "taskText.1036", "params": 5}, {"id": 30500004, "type": 1, "cond": "1038,0,10", "prev_id": 30500003, "reward": "23,0,5|5,0,10", "show_progress": 1, "desc": "taskText.1036", "params": 10}, {"id": 30500005, "type": 1, "cond": "1038,0,20", "prev_id": 30500004, "reward": "23,0,6|5,0,12", "show_progress": 1, "desc": "taskText.1036", "params": 20}, {"id": 30500006, "type": 1, "cond": "1038,0,35", "prev_id": 30500005, "reward": "23,0,7|5,0,12", "show_progress": 1, "desc": "taskText.1036", "params": 35}, {"id": 30500007, "type": 1, "cond": "1038,0,55", "prev_id": 30500006, "reward": "23,0,8|5,0,12", "show_progress": 1, "desc": "taskText.1036", "params": 55}, {"id": 30500008, "type": 1, "cond": "1038,0,80", "prev_id": 30500007, "reward": "23,0,9|5,0,12", "show_progress": 1, "desc": "taskText.1036", "params": 80}, {"id": 30500009, "type": 1, "cond": "1038,0,110", "prev_id": 30500008, "reward": "23,0,10|5,0,15", "show_progress": 1, "desc": "taskText.1036", "params": 110}, {"id": 30500010, "type": 1, "cond": "1038,0,150", "prev_id": 30500009, "reward": "23,0,11|5,0,15", "show_progress": 1, "desc": "taskText.1036", "params": 150}, {"id": 30500011, "type": 1, "cond": "1038,0,200", "prev_id": 30500010, "reward": "23,0,12|5,0,15", "show_progress": 1, "desc": "taskText.1036", "params": 200}, {"id": 30500012, "type": 1, "cond": "1038,0,250", "prev_id": 30500011, "reward": "23,0,13|5,0,15", "show_progress": 1, "desc": "taskText.1036", "params": 250}, {"id": 30500013, "type": 1, "cond": "1038,0,300", "prev_id": 30500012, "reward": "23,0,14|5,0,15", "show_progress": 1, "desc": "taskText.1036", "params": 300}, {"id": 30500014, "type": 1, "cond": "1038,0,400", "prev_id": 30500013, "reward": "23,0,15|5,0,20", "show_progress": 1, "desc": "taskText.1036", "params": 400}, {"id": 30500015, "type": 1, "cond": "1038,0,500", "prev_id": 30500014, "reward": "23,0,15|5,0,20", "show_progress": 1, "desc": "taskText.1036", "params": 500}]