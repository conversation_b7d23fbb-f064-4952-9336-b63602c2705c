package bazaar

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"time"

	"github.com/huyangv/vmqant/log"
)

// 市场模块
type Model struct {
	room     g.Room
	db       *Mongodb
	recordDb *Mongodb

	tradingRess *TradingResList //交易资源列表
	dbAddTRes   chan *TRes      //需要添加的
	dbDelTRes   chan string     //需要删除的

	recordMap map[string]*TradRecordList //成功交易记录map

	isRunning     bool
	checkInterval int64
}

func NewModel(room g.Room) *Model {
	return &Model{
		room:      room,
		db:        &Mongodb{"bazaar_" + g.ServerIdToString(room.GetSID())},
		recordDb:  &Mongodb{"bazaar_record_" + g.ServerIdToString(room.GetSID())},
		dbAddTRes: make(chan *TRes, constant.TRADINGRES_MAX_COUNT+10),
		dbDelTRes: make(chan string, constant.TRADINGRES_MAX_COUNT+10),
	}
}

// 返回基础信息
func (this *Model) Strip() map[string]interface{} {
	return map[string]interface{}{
		"tradingRess": this.ToTradingRess(),
	}
}

func (this *Model) Init() *Model {
	this.tradingRess = &TradingResList{List: []*TRes{}}
	this.recordMap = map[string]*TradRecordList{}
	return this
}

func (this *Model) FromDB() *Model {
	arr, _ := this.db.Find()
	this.tradingRess = &TradingResList{List: arr}
	this.recordMap = map[string]*TradRecordList{}
	recordList, _ := this.recordDb.FindRecords()
	if recordList == nil || len(recordList) == 0 {
		// 数据库没有则读取配置
		recordList = this.GetInitPrice()
	}
	for _, record := range recordList {
		key := getTresMapRecordKey(record.SellType, record.BuyType)
		this.recordMap[key] = record
	}
	return this
}

// 获取配置的初始汇率
func (this *Model) GetInitPrice() []*TradRecordList {
	arr := []*TradRecordList{}
	for buyType, confMap := range constant.TRADE_INIT_PRICE_MAP {
		for sellType, price := range confMap {
			// 生成初始记录
			recordData := &TradRecordList{
				SellType: sellType,
				BuyType:  buyType,
				CurPrice: price,
				List: []*TradRecord{{
					Price: price,
					Time:  0,
				}},
				NeedUpdate: true,
			}
			arr = append(arr, recordData)
		}
	}
	return arr
}

func (this *Model) Run() {
	this.isRunning = true
	go func() {
		tiker := time.NewTicker(time.Second * time.Duration(ut.Random(100, 150)))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			if this.isRunning {
				this.UpdateDB()
			}
		}
	}()
}

func (this *Model) Stop() {
	this.isRunning = false
	this.UpdateDB()
	close(this.dbAddTRes)
	close(this.dbDelTRes)
}

func (this *Model) Update() {
	this.CheckUpdate(time.Now().UnixMilli())
}

func (this *Model) ToTradingRess() []map[string]interface{} {
	this.tradingRess.RLock()
	defer this.tradingRess.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.tradingRess.List {
		if m.IsEnd() {
			continue
		}
		arr = append(arr, map[string]interface{}{
			"uid":           m.UID,
			"owner":         m.Owner,
			"sellType":      m.SellType,
			"sellCount":     m.SellCount,
			"buyType":       m.BuyType,
			"buyCount":      m.BuyCount,
			"merchantCount": m.MerchantCount,
			"surplusTime":   int32(ut.MaxInt64(0, m.EndTime-time.Now().UnixMilli())),
		})
	}
	return arr
}

func (this *Model) ToTradingRessPb(plyUid string) []*pb.TradingRessInfo {
	this.tradingRess.RLock()
	defer this.tradingRess.RUnlock()
	arr := []*pb.TradingRessInfo{}
	now := time.Now().UnixMilli()
	wld := this.room.GetWorld()
	for _, m := range this.tradingRess.List {
		if m.IsEnd() {
			continue
		} else if m.OnlyAlli && !wld.CheckIsOneAlliance(plyUid, m.Owner) {
			continue //排除仅联盟可见且不是同一联盟的资源
		}
		cancelTime := ut.MaxInt64(m.Time+constant.CANCEL_SELL_CD_TIME-now, 0)
		if plyUid != m.Owner && m.Time > 0 && cancelTime > 0 {
			// 他人的交易需要过了可下架时间才可见
			continue
		}
		arr = append(arr, &pb.TradingRessInfo{
			Uid:            m.UID,
			Owner:          m.Owner,
			SellType:       int32(m.SellType),
			SellCount:      int32(m.SellCount),
			BuyType:        int32(m.BuyType),
			BuyCount:       int32(m.BuyCount),
			MerchantCount:  int32(m.MerchantCount),
			SurplusTime:    int32(ut.MaxInt64(0, m.EndTime-now)),
			NoticeWaitTime: int32(ut.MaxInt64(0, m.NoticeTime-now)),
			CancelTime:     int32(cancelTime),
		})
	}
	return arr
}

// 保存数据库
func (this *Model) UpdateDB() {
	if len(this.dbAddTRes) > 0 {
		log.Info("bazaar UpdateDB add ", len(this.dbAddTRes))
		list := []interface{}{}
		for len(this.dbAddTRes) > 0 {
			data := <-this.dbAddTRes
			list = append(list, *data)
		}
		this.db.InsertMany(list)
	}
	if len(this.dbDelTRes) > 0 {
		log.Info("bazaar UpdateDB del", len(this.dbDelTRes))
		uids := []string{}
		for len(this.dbDelTRes) > 0 {
			data := <-this.dbDelTRes
			uids = append(uids, data)
		}
		this.db.DeleteMany(uids)
	}
	for _, recordData := range this.recordMap {
		if recordData.NeedUpdate {
			this.recordDb.UpdateTresRecord(recordData)
			recordData.NeedUpdate = false
		}
	}
}

// 检测更新
func (this *Model) CheckUpdate(now int64) {
	internal := now - this.checkInterval
	if internal < 1000 {
		return
	}
	this.checkInterval = now
	tradeMap := map[string]map[string]bool{}
	this.tradingRess.Lock()
	for i := len(this.tradingRess.List) - 1; i >= 0; i-- {
		if m := this.tradingRess.List[i]; now > m.NoticeTime && now >= m.EndTime { //删除
			this.tradingRess.List = append(this.tradingRess.List[:i], this.tradingRess.List[i+1:]...) //删除
			this.dbDelTRes <- m.UID                                                                   //标记删除
			plr := this.room.GetOnlinePlayerOrDB(m.Owner)
			if plr == nil {
				continue
			}
			item := g.NewTypeObjNotId(m.SellType, m.SellCount)
			_, add := plr.ChangeCostByTypeObjOne(item, 1)                                   //添加资源
			plr.ChangeMerchantState(constant.MS_TRADING, constant.MS_NONE, m.MerchantCount) //改变商人状态
			if plr.IsOnline() {
				plr.PutNotifyQueue(constant.NQ_UPDATE_MERCHANT, &pb.OnUpdatePlayerInfoNotify{Data_20: plr.ToMerchantsPb()})
				plr.PutNotifyQueue(constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: plr.ToItemByTypeObjsPb([]*g.TypeObj{item})})
			} else {
				this.room.UpdatePlayerDB(plr)
			}
			// 记录
			this.room.GetRecord().AddBazaarRecord(constant.BAZAAR_RECORD_TYPE_DRES_AUTO, plr.GetUID(), "", map[string]interface{}{
				"resType":  m.SellType,
				"resCount": m.SellCount,
				"actCount": add,
			})
		} else if now > m.NoticeTime && CheckTradeInfluence(m.SellType, m.BuyType, float64(m.SellCount), float64(m.BuyCount)) {
			// 过了公示期且达到量级的交易累计上架时间
			key := getTresMapRecordKey(m.SellType, m.BuyType)
			recordList, ok := this.recordMap[key]
			if ok {
				price := float64(m.SellCount) / float64(m.BuyCount)
				if (recordList.CurPrice >= 1 && price < recordList.CurPrice*constant.TRADE_MIN_PRICE_PARAM) ||
					(recordList.CurPrice < 1 && price >= recordList.CurPrice) {
					// 交易价格小于等于市场价格 加入上架时间计时
					tradeUserMap, ok := tradeMap[key]
					if !ok {
						tradeUserMap = map[string]bool{}
						tradeMap[key] = tradeUserMap
					}
					tradeUserMap[m.Owner] = true
				}
			}
		}
	}
	this.tradingRess.Unlock()
	// // 累计上架时间
	// for key, userMap := range tradeMap {
	// 	recordList, ok := this.recordMap[key]
	// 	if ok {
	// 		recordList.AccTime += len(userMap) * internal
	// 		if recordList.AccTime >= slg.GetTradeNoticeTime()[2] {
	// 			// 达到上架时间上限 降价处理
	// 			if recordList.PriceDiscount() {
	// 				// 通知
	// 				this.room.GetWorld().PutNotifyQueue(constant.NQ_TRADE_PRICE, &pb.OnUpdateWorldInfoNotify{Data_60: recordList.ToPb()})
	// 			}
	// 		}
	// 	}
	// }
}

func (this *Model) GetTradingResByUID(uid string) *TRes {
	this.tradingRess.RLock()
	defer this.tradingRess.RUnlock()
	for _, m := range this.tradingRess.List {
		if m.UID == uid {
			return m
		}
	}
	return nil
}

// 获取当前交易资源数量
func (this *Model) GetTradingResCount() int {
	return len(this.tradingRess.List)
}

// 添加交易资源
func (this *Model) AddTradingRes(sell *g.TypeObj, buy *g.TypeObj, owner string, merchantCount int32, onlyAlli bool) {
	this.tradingRess.Lock()
	defer this.tradingRess.Unlock()
	now := time.Now()
	nowTime := now.UnixMilli()
	// nowHour := now.Hour()
	// timeConfig := slg.GetTradeNoticeTime()
	// tradeNoticeTime := timeConfig[0]
	// if nowHour >= timeConfig[3] && nowHour < timeConfig[4] {
	// 	tradeNoticeTime = timeConfig[1]
	// }
	// tradeNoticeTime += timeConfig[5] // 公示时间需加上可取消时间
	var tradeNoticeTime int64
	tres := &TRes{
		UID:           ut.ID(),
		SellType:      sell.Type, //卖
		SellCount:     sell.Count,
		BuyType:       buy.Type, //买
		BuyCount:      buy.Count,
		Owner:         owner,                                   //拥有者
		MerchantCount: merchantCount,                           //需要的商人数量
		EndTime:       nowTime + ut.TIME_DAY + tradeNoticeTime, //最多持续一天
		NoticeTime:    nowTime + tradeNoticeTime,
		OnlyAlli:      onlyAlli,
		Time:          nowTime,
	}
	this.tradingRess.List = append(this.tradingRess.List, tres)
	this.dbAddTRes <- tres //准备放入数据库
}

// 删除交易资源
func (this *Model) RemoveTradingRes(uid string) {
	this.tradingRess.Lock()
	defer this.tradingRess.Unlock()
	for i := len(this.tradingRess.List) - 1; i >= 0; i-- {
		if m := this.tradingRess.List[i]; m.UID == uid {
			this.tradingRess.List = append(this.tradingRess.List[:i], this.tradingRess.List[i+1:]...) //删除
			// 标记删除
			this.dbDelTRes <- m.UID
		}
	}
}

// 获取交易中的商人数量
func (this *Model) GetTradingMerchantCount(uid string) int32 {
	this.tradingRess.RLock()
	defer this.tradingRess.RUnlock()
	var cnt int32
	for i := len(this.tradingRess.List) - 1; i >= 0; i-- {
		if m := this.tradingRess.List[i]; m.Owner == uid {
			cnt += m.MerchantCount
		}
	}
	return cnt
}

// 添加交易成功记录
func (this *Model) AddTradeRecord(sellType, buyType, sellCount, buyCount int32) {
	key := getTresMapRecordKey(sellType, buyType)
	recordList, ok := this.recordMap[key]
	if !ok {
		log.Error("AddTresRecord record nil key: %v", key)
		return
	}
	price := float64(sellCount) / float64(buyCount)
	if CheckTradeInfluence(sellType, buyType, float64(sellCount), float64(buyCount)) {
		// 达到量级的交易才计入价格记录
		if recordList.InsertRecord(price) {
			// 通知
			// this.room.GetWorld().PutNotifyQueue(constant.NQ_TRADE_PRICE, &pb.OnUpdateWorldInfoNotify{Data_60: recordList.ToPb()})
		}
	}
}

// 获取当前交易市场价
func (this *Model) GetTradeCurPrice(sellType, buyType int32) float64 {
	key := getTresMapRecordKey(sellType, buyType)
	if record, ok := this.recordMap[key]; ok {
		return record.CurPrice
	} else {
		return 0
	}
}

// 清理玩家在市场里面的东西
func (this *Model) CleanPlayerBazaarRes(uid string) {
	this.tradingRess.Lock()
	defer this.tradingRess.Unlock()
	for i := len(this.tradingRess.List) - 1; i >= 0; i-- {
		if m := this.tradingRess.List[i]; m.Owner == uid {
			this.tradingRess.List = append(this.tradingRess.List[:i], this.tradingRess.List[i+1:]...) //删除
			// 标记删除
			this.dbDelTRes <- m.UID
		}
	}
}

// 市场价格转pb
func (this *Model) TradePriceToPb() []*pb.TradePriceInfo {
	arr := []*pb.TradePriceInfo{}
	for _, v := range this.recordMap {
		arr = append(arr, v.ToPb())
	}
	return arr
}

// 修改市场汇率
func (this *Model) ModifyTradePrice(sellType, buyType int32, price float64) {
	key := getTresMapRecordKey(sellType, buyType)
	this.tradingRess.Lock()
	defer this.tradingRess.Unlock()
	record, ok := this.recordMap[key]
	if ok {
		record.CurPrice = price
		// this.room.GetWorld().PutNotifyQueue(constant.NQ_TRADE_PRICE, &pb.OnUpdateWorldInfoNotify{Data_60: record.ToPb()})
	}
}

// 获取交易记录map的key
func getTresMapRecordKey(sellType, buyType int32) string {
	return ut.String(sellType) + ":" + ut.String(buyType)
}

// 判断交易是否达到量级
func CheckTradeInfluence(sellType, buyType int32, sellCount, buyCount float64) bool {
	return constant.TRADE_INFLUENCE_MAP[sellType]*sellCount+constant.TRADE_INFLUENCE_MAP[buyType]*buyCount >= 1
}
