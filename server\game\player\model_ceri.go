package player

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
	"time"
)

// 研究所一个槽位信息
type CeriSlotInfo struct {
	SelectIds []int32 `json:"selectIds" bson:"select_ids"` //选择列表

	StartTime int64 `json:"startTime" bson:"start_time"` //开始研究时间

	Lv         int32 `json:"lv" bson:"lv"`                  //需要研究所等级
	Type       int32 `json:"type" bson:"type"`              //当前槽位类型
	ID         int32 `json:"id" bson:"id"`                  //当前已经研究的id
	ResetCount int32 `json:"resetCount" bson:"reset_count"` //重置次数

	NeedTime int32 //研究需要的时间
	value    int32 //对应Id
}

func (this *CeriSlotInfo) Strip() map[string]interface{} {
	return map[string]interface{}{
		"lv":          this.Lv,
		"type":        this.Type,
		"id":          this.ID,
		"selectIds":   this.SelectIds,
		"surplusTime": this.GetSurplusTime(),
		"resetCount":  this.ResetCount,
	}
}

func (this *CeriSlotInfo) GetSurplusTime() int32 {
	return int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0))
}

func (this *CeriSlotInfo) ToPb() *pb.CeriSlotInfo {
	return &pb.CeriSlotInfo{
		Lv:          int32(this.Lv),
		Type:        int32(this.Type),
		Id:          int32(this.ID),
		SelectIds:   this.SelectIds,
		SurplusTime: this.GetSurplusTime(),
		ResetCount:  int32(this.ResetCount),
	}
}

func (this *CeriSlotInfo) InitJson() {
	if json := config.GetJsonData("ceri", this.ID); json != nil {
		this.NeedTime = ut.Int32(json["time"]) * 1000 * this.Lv / int32(slg.GetCeriSpeedUp())
		this.value = ut.Int32(json["value"])
	}
}

// 是否可以研究
func (this *CeriSlotInfo) IsCanStudy() bool {
	return this.ID == 0 && this.StartTime == 0
}

// 获取对应Id
func (this *CeriSlotInfo) GetActValue() int32 {
	if this.ID > 0 && this.StartTime == 0 {
		return this.value
	}
	return 0
}

func (this *CeriSlotInfo) GetValue() int32 {
	return this.value
}

// 开始研究
func (this *CeriSlotInfo) StartStudy(id int32) {
	this.SelectIds = []int32{}
	this.ID = id
	this.StartTime = time.Now().UnixMilli()
	this.InitJson()
}

func InitCeriSlots(slots []*CeriSlotInfo) []*CeriSlotInfo {
	for _, m := range slots {
		m.InitJson()
	}
	return slots
}

func (this *Model) ToCeriSlots() []map[string]interface{} {
	return array.Map(this.CeriSlots, func(m *CeriSlotInfo, _ int) map[string]interface{} { return m.Strip() })
}

func (this *Model) ToCeriSlotsPb() []*pb.CeriSlotInfo {
	return array.Map(this.CeriSlots, func(m *CeriSlotInfo, _ int) *pb.CeriSlotInfo { return m.ToPb() })
}

// 获取槽位信息
func (this *Model) GetCeriSlotByLv(lv int32) *CeriSlotInfo {
	if lv < 1 {
		return nil
	} else if slot := array.Find(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.Lv == lv }); slot != nil {
		return slot
	}
	return nil
}

// 检测上一个槽位是否研究
func (this *Model) CheckPerCeriSlotIsStudy(lv int32) bool {
	if lv == 1 {
		return true
	}
	obj := map[int32]*CeriSlotInfo{}
	for _, m := range this.CeriSlots {
		obj[m.Lv] = m
	}
	for i := lv - 1; i >= 1; i-- {
		if slot := obj[i]; slot == nil || slot.ID == 0 {
			return false
		}
	}
	return true
}

// 随机槽位选择
func (this *Model) CeriRandomSelect(tp, lv int32) []int32 {
	if tp == 4 {
		return this.CeriRandomExclusiveSelect(lv)
	}
	idMap := map[int32]bool{}
	valueMap := map[int32]bool{}
	preIdMap := map[int32]bool{}
	for _, m := range this.CeriSlots { //当前研究槽位信息
		if m.Type != tp {
			continue
		}
		idMap[m.ID] = true
		if m.Lv == lv {
			for _, id := range m.SelectIds {
				preIdMap[id] = true
			}
		} else if m.ID == 0 {
			for _, id := range m.SelectIds {
				idMap[id] = true
			}
		}
	}
	for _, id := range this.UnlockPolicyIds { //政策
		valueMap[id] = true
	}
	for _, id := range this.UnlockPawnIds { //士兵
		valueMap[id] = true
	}
	for _, id := range this.UnlockEquipIds { //装备
		valueMap[id] = true
	}
	// 删除已经解锁 或者已经随机出来的
	serverType := ut.String(this.room.GetType())
	datas := config.GetJson("ceri").Get("type", float64(tp))
	datas = array.Delete(datas, func(m map[string]interface{}) bool {
		return !strings.Contains(ut.String(m["need_server_type"]), serverType) || idMap[ut.Int32(m["id"])] || valueMap[ut.Int32(m["value"])] || ut.Int32(m["need_lv"]) > lv
	})
	if lv > 1 {
	} else if slot := this.GetCeriSlotByLv(lv); slot == nil || slot.ResetCount == 0 {
		// 第一次默认出 猎人
		datas = array.RemoveItem(datas, func(m map[string]interface{}) bool { return ut.Int(m["id"]) == 51 })
		ids := randomCeriIds(preIdMap, datas, 2)
		return append([]int32{51}, ids...)
	}
	return randomCeriIds(preIdMap, datas, 3)
}

// 随机专属装备
func (this *Model) CeriRandomExclusiveSelect(lv int32) []int32 {
	idMap := map[int32]bool{}
	preIdMap := map[int32]bool{}
	// 已经解锁的士兵
	for _, id := range this.UnlockPawnIds { //士兵
		idMap[id] = true
	}
	// 初始士兵
	pawns := array.Filter(config.GetJson("pawnBase").Datas, func(m map[string]interface{}, _ int) bool {
		return ut.Int(m["spawn_build_id"]) > 0 && ut.Int(m["need_unlock"]) == 0
	})
	for _, m := range pawns {
		idMap[ut.Int32(m["id"])] = true
	}
	// 获取已经有的士兵列表
	for _, m := range this.CeriSlots { //当前研究槽位信息
		if m.Lv == lv {
			for _, id := range m.SelectIds {
				preIdMap[id] = true
			}
		} else if m.Type == 2 && m.value > 0 {
			idMap[m.value] = true
		} else if m.Type == 4 && m.value > 0 { //已经获取的专属装备 要忽略
			if json := config.GetJsonData("equipBase", m.value); json != nil {
				idMap[ut.Int32(json["exclusive_pawn"])] = false
			}
		}
	}
	// 删除已经解锁 或者已经随机出来的
	datas := config.GetJson("ceri").Get("type", float64(4))
	datas = array.Delete(datas, func(m map[string]interface{}) bool {
		if json := config.GetJsonData("equipBase", ut.Int32(m["value"])); json == nil || !idMap[ut.Int32(json["exclusive_pawn"])] {
			return true
		}
		return false
	})
	return randomCeriIds(preIdMap, datas, 3)
}

func randomCeriIds(preIdMap map[int32]bool, datas []map[string]interface{}, count int) []int32 {
	if len(datas) <= count {
		return array.Map(datas, func(m map[string]interface{}, _ int) int32 { return ut.Int32(m["id"]) })
	}
	ids, cnt := []int32{}, 0
	for cnt < 50 {
		cnt += 1
		arr := array.Map(datas, func(m map[string]interface{}, _ int) map[string]interface{} { return m })
		ok := false
		for i := 0; i < count && len(arr) > 0; i++ {
			index := ut.RandomIndexByWeight(arr)
			id := ut.Int32(arr[index]["id"])
			ids = append(ids, id)
			arr = append(arr[:index], arr[index+1:]...)
			if !preIdMap[id] {
				ok = true
			}
		}
		if ok {
			break
		}
		ids = []int32{}
	}
	return ids
}

// 刷新研究所槽位信息
func (this *Model) UpdateCeriSlotInfo(blv int32, init bool) {
	ok := false
	for i, l := 0, len(constant.CERI_SLOT_CONF); i < l; i++ {
		arr := constant.CERI_SLOT_CONF[i]
		lv, tp := arr[0], arr[1]
		preLv := lv - 1
		slot := array.Find(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.Lv == lv })
		if slot != nil {
			if lv > blv {
				slot.SelectIds = []int32{}
			} else if pre := this.GetCeriSlotByLv(preLv); (pre != nil && pre.ID > 0) || lv == 1 {
				if slot.ID > 0 {
					slot.SelectIds = []int32{}
				} else if len(slot.SelectIds) == 0 {
					slot.SelectIds = this.CeriRandomSelect(slot.Type, slot.Lv)
				}
			} else {
				slot.SelectIds = []int32{}
			}
			ok = true
		} else if lv <= blv {
			slot = &CeriSlotInfo{Lv: lv, Type: tp, SelectIds: []int32{}}
			if pre := this.GetCeriSlotByLv(preLv); (pre != nil && pre.ID > 0) || lv == 1 {
				slot.SelectIds = this.CeriRandomSelect(tp, lv)
			}
			this.CeriSlots = append(this.CeriSlots, slot)
			ok = true
		}
	}
	if ok && !init {
		this.PutNotifyQueue(constant.NQ_UPDATE_CERI_SLOT, &pb.OnUpdatePlayerInfoNotify{Data_46: this.ToCeriSlotsPb()})
	}
}

// 检测更新研究
func (this *Model) CheckUpdateStudy(now int64) {
	for _, slot := range this.CeriSlots {
		if slot.StartTime == 0 || slot.NeedTime == 0 {
			continue
		}
		if now-slot.StartTime >= int64(slot.NeedTime) {
			slot.StartTime = 0
			slot.SelectIds = []int32{}
			this.PutNotifyQueue(constant.NQ_CERI_STUDY_DONE, &pb.OnUpdatePlayerInfoNotify{Data_47: slot.ToPb()})
			// 检测是否有下一个槽位 给下一个槽位随机选择 但是目前其实在开始研究的时候就给下一个随机了 这里只是再次判断一下
			if next := this.GetCeriSlotByLv(slot.Lv + 1); next != nil && next.ID == 0 && len(next.SelectIds) == 0 {
				next.SelectIds = this.CeriRandomSelect(next.Type, next.Lv)
				this.PutNotifyQueue(constant.NQ_CERI_STUDY_DONE, &pb.OnUpdatePlayerInfoNotify{Data_47: next.ToPb()})
			}
		}
	}
}

// 是否解锁某个政策
func (this *Model) IsUnlockPolicy(id int32) bool {
	return array.Has(this.UnlockPolicyIds, id) || array.Some(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.GetActValue() == id })
}

// 是否解锁某个士兵
func (this *Model) IsUnlockPawn(id int32) bool {
	return array.Has(this.UnlockPawnIds, id) || array.Some(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.GetActValue() == id })
}

// 是否解锁某个装备
func (this *Model) IsUnlockEquip(id int32) bool {
	return array.Has(this.UnlockEquipIds, id) || array.Some(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.GetActValue() == id })
}
