package player

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 研究所一个槽位信息
type CeriSlotInfo struct {
	SelectIds []int32 `json:"selectIds" bson:"select_ids"` // 选择列表

	StartTime int64 `json:"startTime" bson:"start_time"` // 开始研究时间

	Lv         int32 `json:"lv" bson:"lv"`                  // 需要研究所等级
	Type       int32 `json:"type" bson:"type"`              // 当前槽位类型
	ID         int32 `json:"id" bson:"id"`                  // 当前已经研究的id
	ResetCount int32 `json:"resetCount" bson:"reset_count"` // 重置次数

	NeedTime int32 // 研究需要的时间
	value    int32 // 对应Id
}

func (this *CeriSlotInfo) Strip() map[string]interface{} {
	return map[string]interface{}{
		"lv":          this.Lv,
		"type":        this.Type,
		"id":          this.ID,
		"selectIds":   this.SelectIds,
		"surplusTime": this.GetSurplusTime(),
		"resetCount":  this.ResetCount,
	}
}

func InitCeriSlots(slots []*CeriSlotInfo) []*CeriSlotInfo {
	for _, m := range slots {
		m.InitJson()
	}
	return slots
}

func (this *CeriSlotInfo) GetSurplusTime() int32 {
	return int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0))
}

func (this *CeriSlotInfo) InitJson() {
	if json := config.GetJsonData("ceri", this.ID); json != nil {
		this.NeedTime = ut.Int32(json["time"]) * 1000 * this.Lv / int32(slg.GetCeriSpeedUp())
		this.value = ut.Int32(json["value"])
	}
}

func (this *Model) GetStudySlots(slots *g.CeriSlotMap) []*g.CeriSlotInfo {
	slots.RLock()
	defer slots.RUnlock()
	list := []*g.CeriSlotInfo{}
	for _, m := range slots.Map {
		if m.ID > 0 {
			list = append(list, m)
		}
	}
	return list
}

// 兼容所有槽位信息
func (this *Model) CheckAllSlotInfo() {
	// 政策
	wld := this.room.GetWorld()
	wld.CheckPolicySlotInfo(this.Uid, wld.GetPlayerBuildLvById(this.Uid, constant.MAIN_BUILD_ID))
	// 装备
	this.EquipCeriSlots.Check(wld.GetPlayerBuildLvById(this.Uid, constant.SMITHY_BUILD_ID), constant.EQUIP_SLOT_CONF_MAP, map[string]interface{}{
		"idMap": func() map[int32]bool { return this.GetExtraUnlockEquipIdMap() },
		"pawnMap": func() map[int32]bool {
			// 是专属则获取已解锁士兵map
			pawnMap := this.GetExtraUnlockPawnIdMap()
			// 从已经研究里面选
			this.PawnCeriSlots.GetIdBoolMap(pawnMap)
			return pawnMap
		},
	})
	// 士兵
	this.PawnCeriSlots.Check(wld.GetPlayerBuildLvById(this.Uid, constant.BARRACKS_BUILD_ID), constant.PAWN_SLOT_CONF_MAP, map[string]interface{}{
		"idMap": func() map[int32]bool { return this.GetExtraUnlockPawnIdMap() },
	})
}

// 刷新政策槽位信息
func (this *Model) UpdatePolicySlotInfo(blv int32, isInit bool) {
	this.room.GetWorld().UpdatePolicySlotInfo(this.Uid, blv, isInit)
}

// 刷新装备槽位信息
func (this *Model) UpdateEquipSlotInfo(blv int32, isInit bool) {
	if _, ok := constant.EQUIP_SLOT_CONF_MAP[blv]; !ok {
		return
	} else if slot := this.EquipCeriSlots.GetSlotByLv(blv); slot == nil {
		this.EquipCeriSlots.AddCeriSlot(blv)
	} else if len(slot.SelectIds) > 0 {
		return
	} else if slot.ID > 0 {
		return
	}
	if this.EquipCeriSlots.CeriRandomSelect(blv, map[string]interface{}{
		"idMap": func() map[int32]bool { return this.GetExtraUnlockEquipIdMap() },
		"pawnMap": func() map[int32]bool {
			// 是专属则获取已解锁士兵map
			pawnMap := this.GetExtraUnlockPawnIdMap()
			// 从已经研究里面选
			this.PawnCeriSlots.GetIdBoolMap(pawnMap)
			return pawnMap
		},
	}) != nil && !isInit {
		// 通知
		this.PutNotifyQueue(constant.NQ_UPDATE_EQUIP_SLOT, &pb.OnUpdatePlayerInfoNotify{Data_86: this.EquipCeriSlots.ToCerisPb()})
	}
}

// 刷新士兵槽位信息
func (this *Model) UpdatePawnSlotInfo(blv int32, isInit bool) {
	if _, ok := constant.PAWN_SLOT_CONF_MAP[blv]; !ok {
		return
	} else if slot := this.PawnCeriSlots.GetSlotByLv(blv); slot == nil {
		this.PawnCeriSlots.AddCeriSlot(blv)
	} else if len(slot.SelectIds) > 0 {
		return
	} else if slot.ID > 0 {
		return
	}
	if this.PawnCeriSlots.CeriRandomSelect(blv, map[string]interface{}{"idMap": func() map[int32]bool { return this.GetExtraUnlockPawnIdMap() }}) != nil && !isInit {
		// 通知
		this.PutNotifyQueue(constant.NQ_UPDATE_PAWN_SLOT, &pb.OnUpdatePlayerInfoNotify{Data_87: this.PawnCeriSlots.ToCerisPb()})
	}
}

// 是否解锁某个士兵
func (this *Model) IsUnlockPawn(id int32) bool {
	return this.PawnCeriSlots.IsUnlock(id) || this.IsExtraUnlockPawn(id)
}

// 是否额外解锁某个士兵
func (this *Model) IsExtraUnlockPawn(id int32) bool {
	this.ceriUnlockMutex.RLock()
	defer this.ceriUnlockMutex.RUnlock()
	return array.Has(this.UnlockPawnIds, id)
}

// 是否解锁某个装备
func (this *Model) IsUnlockEquip(uid string) bool {
	arr := ut.StringToInt32s(uid, "_")
	if len(arr) != 2 {
		return false
	}
	id, lv := arr[0], arr[1]
	if slot := this.EquipCeriSlots.GetSlotByLv(lv); slot != nil && slot.ID == id {
		return true
	} else {
		return this.IsExtraUnlockEquip(id)
	}
}

// 是否额外解锁某个装备
func (this *Model) IsExtraUnlockEquip(id int32) bool {
	this.ceriUnlockMutex.RLock()
	defer this.ceriUnlockMutex.RUnlock()
	return array.Has(this.UnlockEquipIds, id)
}

// 额外添加解锁士兵
func (this *Model) AddExtraUnlockPawn(id int32) {
	// 先判断士兵槽位中是否解锁
	if this.PawnCeriSlots.IsUnlock(id) {
		return
	}
	this.ceriUnlockMutex.Lock()
	defer this.ceriUnlockMutex.Unlock()
	if !array.Has(this.UnlockPawnIds, id) {
		this.UnlockPawnIds = append(this.UnlockPawnIds, id)
	}
}

func (this *Model) GetExtraUnlockPawnIdMap() map[int32]bool {
	this.ceriUnlockMutex.Lock()
	defer this.ceriUnlockMutex.Unlock()
	idMap := map[int32]bool{}
	for _, id := range this.UnlockPawnIds {
		idMap[id] = true
	}
	return idMap
}

// 额外添加解锁装备
func (this *Model) AddExtraUnlockEquip(id int32) {
	// 先判断装备槽位中是否解锁
	if this.EquipCeriSlots.IsUnlock(id) {
		return
	}
	this.ceriUnlockMutex.Lock()
	defer this.ceriUnlockMutex.Unlock()
	if !array.Has(this.UnlockEquipIds, id) {
		this.UnlockEquipIds = append(this.UnlockEquipIds, id)
	}
}

func (this *Model) GetExtraUnlockEquipIdMap() map[int32]bool {
	this.ceriUnlockMutex.Lock()
	defer this.ceriUnlockMutex.Unlock()
	idMap := map[int32]bool{}
	for _, id := range this.UnlockEquipIds {
		idMap[id] = true
	}
	equips := this.room.GetWorld().GetPlayerEquips(this.Uid)
	for _, m := range equips {
		idMap[m.ID] = true
	}
	return idMap
}

// 供奉英雄额外解锁士兵
func (this *Model) WorshipHeroUnlockPawn(pawnId int32) {
	if pawnId <= 0 {
		return
	}
	// 先检测槽位中是否已解锁该士兵
	var exist bool
	var existLv int32
	this.PawnCeriSlots.RWMutex.RLock()
	for lv, v := range this.PawnCeriSlots.Map {
		if v.ID == pawnId {
			// 该槽位已解锁该士兵 则重置该槽位
			v.ID = -pawnId
			exist = true
			existLv = lv
			break
		}
	}
	this.PawnCeriSlots.RWMutex.RUnlock()

	// 解锁英雄对应士兵
	this.AddExtraUnlockPawn(pawnId)
	if exist && existLv > 0 {
		// 存在则该槽位重新随机待选项
		this.UpdatePawnSlotInfo(existLv, false)
	}
}
