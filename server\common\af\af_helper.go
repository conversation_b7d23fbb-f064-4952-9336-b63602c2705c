package af

import (
	"encoding/json"
	"net/http"
	slg "slgsrv/server/common"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
)

const (
	AF_APPID_ANDROID           = "twgame.global.acers"
	AF_APPID_APPLE             = "id6446716937"
	AF_APP_URL_HK              = "https://api2.appsflyer.com/inappevent/"
	AF_APP_URL_US              = "https://api2.appsflyer.com/inappevent/"
	AF_BUNDLE_IDENTIFIER       = "twgame.global.acers"
	AF_ADD_TEST_DEVICE_ANDROID = "https://hq1.appsflyer.com/api/test-console/v1.0/app/twgame.global.acers/devices"
	AF_S2S_TOKEN               = "2cWVqPHWQn2Uv9frB4duo7"
)

var (
	afBaseUrl    string
	afAndroidUrl string
	afAppleUrl   string
)

func Init() {
	switch slg.SERVER_AREA {
	case slg.SERVER_AREA_HK:
		afBaseUrl = AF_APP_URL_HK
	case slg.SERVER_AREA_US:
		afBaseUrl = AF_APP_URL_US
		// case "singapore": //TEST
		// 	afBaseUrl = AF_APP_URL_US
	}

	afAndroidUrl = afBaseUrl + AF_APPID_ANDROID
	afAppleUrl = afBaseUrl + AF_APPID_APPLE
}

func Track(deveceOs string, distinctId, adId string, eventName string, params map[string]interface{}) {
	if slg.IsDebug() {
		log.Info("af track eventName: %v, params: %v", eventName, params)
	}
	if !slg.AF_OPEN {
		return
	}
	if afBaseUrl == "" {
		return
	}
	var afUrl string
	osArr := strings.Split(deveceOs, ";")
	if len(osArr) >= 1 && osArr[0] == "iOS" {
		// iOS
		afUrl = afAppleUrl
	} else {
		// Android
		afUrl = afAndroidUrl
		params["bundleIdentifier"] = AF_BUNDLE_IDENTIFIER
		params["advertising_id"] = adId
	}
	go _track(afUrl, distinctId, eventName, params)
}

func _track(afUrl string, distinctId string, eventName string, params map[string]interface{}) {
	client := &http.Client{Timeout: 10 * time.Second}
	params["eventName"] = eventName
	params["app_version_name"] = slg.CLIENT_VERSION
	params["customer_user_id"] = distinctId
	params["eventTime"] = time.Now().UTC().Format("2006-01-02 15:04:05")
	jsonbytes, _ := json.Marshal(params)
	jsonStr := string(jsonbytes)
	req, err := http.NewRequest("POST", afUrl, strings.NewReader(jsonStr))
	if err != nil {
		log.Error("af track http req err: %v", err)
		return
	}
	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/json")
	req.Header.Add("authentication", AF_S2S_TOKEN)
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("af track err: %v, uid: %v, eventName: %v", err, distinctId, eventName)
		return
	}
	if resp.StatusCode != 200 {
		log.Warning("af track err: %v, uid: %v, eventName: %v", err, distinctId, eventName)
	}
}

// func AddTestAndroidDevice(deviceId, deviceIdType, deviceName string) {
// 	client := &http.Client{Timeout: 10 * time.Second}
// 	params := map[string]interface{}{}
// 	jsonStr, _ := json.Marshal(params)
// 	params["device_id"] = deviceId
// 	params["device_id_type"] = deviceIdType
// 	params["device_name"] = deviceName
// 	req, err := http.NewRequest("POST", AF_ADD_TEST_DEVICE_ANDROID, bytes.NewBuffer(jsonStr))
// 	if err != nil {
// 		log.Error("AddTestAndroidDevice http req err: %v", err)
// 		return
// 	}
// 	req.Header.Add("accept", "application/json")
// 	req.Header.Add("content-type", "application/json")
// 	req.Header.Add("authentication", AF_API_TOKEN)
// 	resp, err := client.Do(req)
// 	defer func() {
// 		_ = resp.Body.Close()
// 	}()
// 	if err != nil {
// 		log.Error("AddTestAndroidDevice http err:%v", err)
// 		return
// 	}
// }
