package mail

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"time"

	"github.com/huyangv/vmqant/log"
)

var (
	allUserMailMap    *ut.MapLock[string, *UserMailMap]  //玩家邮件数据map
	baseMailInfoMap   *ut.MapLock[string, *MailBaseInfo] //邮件基础信息map
	userMailUpdateMap *ut.MapLock[string, *UserMailInfo] //玩家邮件数据更新map
	mailDelMap        *ut.MapLock[string, bool]          //删除中的邮件map
	dbChan            = make(chan string, 10000)
)

func (this *Mail) InitMailManager() {
	allUserMailMap = ut.NewMapLock[string, *UserMailMap]()
	baseMailInfoMap = ut.NewMapLock[string, *MailBaseInfo]()
	userMailUpdateMap = ut.NewMapLock[string, *UserMailInfo]()
	mailDelMap = ut.NewMapLock[string, bool]()
	// 从数据库读取群发邮件加载到内存
	mails, err := mailDb.FindAllMassMail()
	if err == nil && mails != nil {
		for _, v := range mails {
			baseMailInfoMap.Set(v.UID, v)
		}
	} else {
		log.Warning("InitMailManager FindAllMassMail err: %v", err)
	}
}

// 获取玩家邮件信息map
func GetUserMailMap(userId string) *UserMailMap {
	userMailMap := allUserMailMap.Get(userId)
	if userMailMap == nil {
		userMailMap = &UserMailMap{datas: map[string]*UserMailInfo{}}
		allUserMailMap.Set(userId, userMailMap)
		userMailMap.InitUserMailMap(userId)
	}
	return userMailMap
}

// 获取玩家邮件列表
func GetUserMailList(userId string, sid int32) []*pb.MailInfo {
	userMailMap := GetUserMailMap(userId)
	// 单人邮件从数据库获取
	mailList, _ := mailDb.FindMailsByUserId(userId, sid)
	if mailList == nil {
		mailList = []*MailBaseInfo{}
	}
	// 群发邮件从内存获取
	baseMailInfoMap.ForEach(func(v *MailBaseInfo, k string) bool {
		if v.ReceiverMap[userId] && (v.SID == 0 || v.SID == sid) && (v.NoReceiverMap == nil || !v.NoReceiverMap[userId]) {
			userMail := userMailMap.GetUserMailInfo(v.UID)
			if userMail == nil || userMail.IsDelete == false {
				mailList = append(mailList, v)
			}
		}
		return true
	})
	arr := []*pb.MailInfo{}
	for _, v := range mailList {
		arr = append(arr, v.ToPb(userMailMap.GetUserMailInfo(v.UID)))
	}
	return arr
}

// 发送邮件 个人 带资源
func (this *Mail) SendMailItemOne(sid, contentId int32, title, content, sender, receiver string, items []*g.TypeObj) string {
	mail := NewMail(ut.ID(), sid, contentId, title, content, sender, receiver)
	mail.Items = items
	err := mailDb.InsertOneMail(mail)
	if err != "" {
		log.Error("SendMailOne error, " + err)
	} else {
		this.SendMailNotify([]string{receiver})
	}
	return err
}

// 发送邮件 多人
func (this *Mail) SendMailMany(sid, contentId int32, title, content, sender string, receivers []string, items []*g.TypeObj) string {
	if len(receivers) == 0 {
		return ""
	}
	mailUid := ut.ID()
	mail := NewMail(mailUid, sid, contentId, title, content, sender, "")
	mail.Items = items
	// 将接收人加入map
	mail.ReceiverMap = map[string]bool{}
	for _, m := range receivers {
		mail.ReceiverMap[m] = true
	}
	err := mailDb.InsertOneMail(mail)
	if err != "" {
		log.Error("SendMailMany error, " + err)
	} else {
		// 多人邮件添加到内存
		baseMailInfoMap.Set(mail.UID, mail)
		// 通知大厅服
		this.SendMailNotify(receivers)
	}
	return err
}

// 发送邮件 所有人
func (this *Mail) SendMailAll(sid, contentId int32, title, content, sender string, items []*g.TypeObj, noreceivers ...string) string {
	mailUid := ut.ID()
	mail := NewMail(mailUid, sid, contentId, title, content, sender, "0")
	mail.Items = items
	mail.NoReceiverMap = map[string]bool{}
	for _, uid := range noreceivers {
		mail.NoReceiverMap[uid] = true
	}
	err := mailDb.InsertOneMail(mail)
	if err != "" {
		log.Error("SendMailAll error, " + err)
	} else {
		// 全服邮件添加到内存
		baseMailInfoMap.Set(mail.UID, mail)
		// 通知大厅服
		this.BroadCastAllLobbyNR(slg.RPC_LOBBY_PUT_ALL_USER_NOTIFY, constant.NQ_NEW_MAIL, &pb.OnUpdatePlayerInfoNotify{Data_33: true}) //通知玩家
	}
	return err
}

// 获取邮件基础信息
func GetMailBaseInfo(uid string) (mail *MailBaseInfo, err string) {
	// 先从内存中获取邮件信息
	mail = baseMailInfoMap.Get(uid)
	if mail == nil {
		// 再从数据库获取
		mailData, e := mailDb.FindOneMailBase(uid)
		if e != nil {
			return nil, ecode.MAIL_NOT_EXIST.String()
		}
		mail = &mailData
		if len(mail.ReceiverMap) > 0 || mail.Receiver == "0" {
			// 多人邮件放入内存
			baseMailInfoMap.Set(mail.UID, mail)
		}
	}
	return
}

// 读邮件
func (this *Mail) ReadMail(userId, uid string) (err string) {
	if !mailDb.FindMailExist(uid) {
		return ecode.MAIL_NOT_EXIST.String()
	}
	userMailMap := GetUserMailMap(userId)
	// 获取玩家对应的邮件信息
	userMail := userMailMap.GetUserMailInfo(uid)
	if userMail == nil {
		userMail = NewUserMailInfo(userId, uid)
	} else if userMail.IsRead || userMail.IsDelete {
		return
	}
	userMail.IsRead = true
	userMailMap.UpdateUserMailInfo(userMail) //更新用户邮件数据
	return ""
}

// 领取邮件附件
func (this *Mail) ClaimMailItem(userId, uid string) ([]*g.TypeObj, string) {
	mail, err := GetMailBaseInfo(uid)
	if err != "" {
		return nil, ecode.MAIL_NOT_EXIST.String() // 邮件不存在
	} else if len(mail.Items) == 0 {
		return nil, ecode.NOT_CAN_CLAIM_ITEM.String() //没有道具
	}

	userMailMap := GetUserMailMap(userId)
	// 获取玩家对应的邮件信息
	userMail := userMailMap.GetUserMailInfo(uid)
	if userMail == nil {
		userMail = NewUserMailInfo(userId, uid)
	}
	userMail.Lock()
	if userMail.IsClaim || len(userMail.ClaimList) >= len(mail.Items) {
		userMail.Unlock()
		return nil, ecode.YET_CLAIM.String() //已经领取了
	}

	items := []*g.TypeObj{}
	for i, m := range mail.Items {
		if !array.Has(userMail.ClaimList, int32(i)) {
			items = append(items, m)
		}
	}
	userMail.IsClaim = true
	userMail.Unlock()
	userMailMap.UpdateUserMailInfo(userMail) //更新用户邮件数据
	return items, ""
}

// 领取单个邮件附件
func (this *Mail) ClaimMailItemOne(userId, uid string, index, heroId int32) (*g.TypeObj, string) {
	mail, err := GetMailBaseInfo(uid)
	if err != "" {
		return nil, ecode.MAIL_NOT_EXIST.String() // 邮件不存在
	} else if len(mail.Items) == 0 {
		return nil, ecode.NOT_CAN_CLAIM_ITEM.String() //没有道具
	}

	userMailMap := GetUserMailMap(userId)
	// 获取玩家对应的邮件信息
	userMail := userMailMap.GetUserMailInfo(uid)
	if userMail == nil {
		userMail = NewUserMailInfo(userId, uid)
	}
	userMail.Lock()
	if userMail.IsClaim || len(userMail.ClaimList) >= len(mail.Items) {
		userMail.Unlock()
		return nil, ecode.YET_CLAIM.String() //已经领取了
	}

	item := mail.Items[index]
	// 自选英雄则转化为碎片
	if item.Type == ctype.HERO_OPT && heroId > 0 && lc.CheckHeroOptId(item.Id, heroId) {
		item.Type = ctype.HERO_DEBRIS
		item.Id = heroId
		item.Count = lc.PORTRAYAL_COMP_NEED_COUNT //固定3个碎片
	}
	userMail.ClaimList = append(userMail.ClaimList, index)
	if len(userMail.ClaimList) >= len(mail.Items) {
		// 已领完
		userMail.ClaimList = []int32{}
		userMail.IsClaim = true
	}
	userMail.Unlock()
	userMailMap.UpdateUserMailInfo(userMail) //更新用户邮件数据
	return item, ""
}

// 玩家删除邮件
func (this *Mail) RemoveMail(userId string, uid string) string {
	mail, err := GetMailBaseInfo(uid)
	if err != "" {
		return ecode.MAIL_NOT_EXIST.String() // 邮件不存在
	}

	userMailMap := GetUserMailMap(userId)
	// 获取玩家对应的邮件信息
	userMail := userMailMap.GetUserMailInfo(uid)
	if userMail == nil {
		userMail = NewUserMailInfo(userId, uid)
	}
	if len(mail.Items) > 0 && !userMail.IsClaim { //有物品还未领取
		return ecode.REMOVE_MAIL_HAS_ITEM.String()
	}
	if userMail.IsDelete {
		// 已删除
		return ""
	}

	if mail.Receiver == "0" || len(mail.ReceiverMap) > 0 {
		// 多人邮件或所有人邮件 更新记录 不删除邮件基础信息
		userMail.IsDelete = true
		userMailMap.UpdateUserMailInfo(userMail) //更新用户邮件数据
	} else {
		// 单人邮件 删除记录和邮件
		userMail.IsDelete = true
		mailDb.DeleteOneMail(mail.UID)
		userMailMap.DeleteUserMailInfo(userMail.UID)
	}
	return ""
}

// 删除已读邮件
func (this *Mail) RemoveAllReadMail(owner string, uids []string) string {
	for _, uid := range uids {
		if err := this.RemoveMail(owner, uid); err == ecode.REMOVE_MAIL_HAS_ITEM.String() {
			return err
		}
	}
	return ""
}

// 发送邮件通知
func (this *Mail) SendMailNotify(uidList []string) {
	msgBytes, err := pb.ProtoMarshal(&pb.OnUpdatePlayerInfoNotify{Data_33: true})
	if err != "" {
		log.Warning("SendMailNotify uids: %v, err: %v", uidList, err)
		return
	}
	lidUserListMap := map[string][]string{}
	for _, uid := range uidList {
		lid, err := rds.RdsHGet(rds.RDS_USER_LID_MAP_KEY, uid)
		if err != nil || lid == "" {
			continue
		}
		if lidUserListMap[lid] == nil {
			lidUserListMap[lid] = []string{}
		}
		lidUserListMap[lid] = append(lidUserListMap[lid], uid)
	}
	for lid, uidList := range lidUserListMap {
		this.InvokeLobbyRpcNR(lid, slg.RPC_LOBBY_PUT_USER_NOTIFY, constant.NQ_NEW_MAIL, uidList, msgBytes)
	}
}

// 系统删除指定邮件 需删除内存和数据库中的邮件基础和玩家邮件信息
func (this *Mail) SysDelMail(mailInfo *MailBaseInfo) {
	// 先添加到删除中的邮件map 避免内存db时保存到已删除的邮件
	mailDelMap.Set(mailInfo.UID, true)
	// 先从数据库删除邮件基础信息和玩家邮件信息
	mailDb.DeleteOneMail(mailInfo.UID)
	userMailDb.DelUserMailByMailUid(mailInfo.UID)
	recieverList := []string{}
	if mailInfo.Receiver == "0" || len(mailInfo.ReceiverMap) > 0 {
		// 群发邮件需要从内存中删除
		baseMailInfoMap.Del(mailInfo.UID)
		if mailInfo.Receiver == "0" {
			// 全服邮件
			allUserMailMap.ForEach(func(v *UserMailMap, k string) bool {
				if mailInfo.NoReceiverMap[k] {
					return true
				}
				recieverList = append(recieverList, k)
				return true
			})
		} else {
			// 多人邮件
			for receiver := range mailInfo.ReceiverMap {
				recieverList = append(recieverList, receiver)
			}
		}
	} else {
		// 单人邮件
		recieverList = append(recieverList, mailInfo.Receiver)
	}
	// 删除玩家邮件信息
	for _, userId := range recieverList {
		userMailMap := allUserMailMap.Get(userId)
		if userMailMap != nil {
			userMailMap.DeleteUserMailInfo(mailInfo.UID + "_" + userId)
		}
	}
	mailDelMap.Del(mailInfo.UID)
}

// 更新玩家邮件信息到数据库
func UpdateMailDb(close bool) {
	sum := len(dbChan)
	if sum == 0 {
		return
	}
	userMailUidMap := map[string]bool{}
	for len(dbChan) > 0 {
		channel := <-dbChan
		if userMailUidMap[channel] {
			continue
		}
		userMailUidMap[channel] = true
		if len(userMailUidMap) >= 1000 {
			log.Warning("UpdateMailDb len over 1000")
			break
		}
	}
	dates := []*UserMailInfo{}
	delList := []string{}
	for userMailUid := range userMailUidMap {
		userMail := userMailUpdateMap.Get(userMailUid)
		if userMail != nil {
			// 系统删除邮件中 无需更新到db
			if mailDelMap.Get(userMail.MailUid) {
				continue
			}
			// 添加到更新
			dates = append(dates, userMail)
		} else {
			// 添加到删除
			delList = append(delList, userMailUid)
		}
	}
	userMailDb.UpdateUserMailList(dates)
	userMailDb.DelUserMailList(delList)
	surplus := len(dbChan)
	if close && surplus > 0 {
		UpdateMailDb(close)
	}
}

// 玩家邮件信息db tick
func (this *Mail) RunMailDbTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 10)
		for isRunning {
			<-tiker.C
			UpdateMailDb(false)
		}
	}()
}

// 定时清理过期的邮件
func (this *Mail) RunDelMailsTick() {
	go func() {
		tiker := time.NewTicker(time.Hour * 1)
		lastTime := time.Now().UnixMilli()
		for isRunning {
			<-tiker.C
			// 每天凌晨5点检查并清理
			timeFive := ut.TodayHourTime(5)
			now := time.Now().UnixMilli()
			if lastTime < timeFive && now > timeFive {
				arr, err := mailDb.FindTimeoutMails(now)
				if err == "" {
					count := len(arr)
					log.Info("RunDelMailsTick start count: %v", count)
					for i, mail := range arr {
						if len(mail.Items) > 0 {
							// 物品邮件的有效期更长
							if mail.CreateTime+slg.ITEM_MAIL_EXPIR_TIME <= now {
								// 物品邮件过期需发放物品再清理邮件
								if len(mail.ReceiverMap) == 0 && mail.Receiver != "0" {
									uid := mail.Receiver
									items, err := this.ClaimMailItem(uid, mail.UID)
									if err == "" {
										// 给过期邮件未领取的玩家发放物品 TODO 补发群发邮件的奖励
										lid := rds.GetUserLid(uid)
										this.InvokeLobbyRpcNR(lid, slg.RPC_ADD_USER_ITEMS, uid, items, constant.GOLD_CHANGE_MAIL_TIMEOUT_GET)
									}
								}
								// 删除邮件
								this.SysDelMail(&mail)
							}
						} else {
							// 普通邮件 直接删除
							this.SysDelMail(&mail)
						}
						if i%100 == 0 {
							log.Info("RunDelMailsTick cur: %v, total: %v", i, count)
						}
					}
					log.Info("RunDelMailsTick finish")
				}
			}
			lastTime = now
		}
	}()
}
