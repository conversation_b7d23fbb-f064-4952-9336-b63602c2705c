package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"time"

	"github.com/sasha-s/go-deadlock"
)

type BTCityQueueList struct {
	deadlock.RWMutex
	List []*BTCityInfo
}

// 建造信息
type BTCityInfo struct {
	StartTime int64 `json:"start_time"` //开始时间
	AIndex    int32 `json:"a_index"`    //区域位置
	CityID    int32 `json:"city_id"`    //城市id
	NeedTime  int32 `json:"need_time"`  //需要时间
}

func NewBTCityInfo(index, id, needTime int32) *BTCityInfo {
	return &BTCityInfo{
		AIndex:    index,
		CityID:    id,
		StartTime: time.Now().UnixMilli(),
		NeedTime:  needTime,
	}
}

func (this *BTCityInfo) Strip() map[string]interface{} {
	return map[string]interface{}{
		"index":       this.AIndex,
		"id":          this.CityID,
		"needTime":    this.NeedTime,
		"surplusTime": int32(ut.Max(int(this.NeedTime)-int(time.Now().UnixMilli()-this.StartTime), 0)),
	}
}

func (this *BTCityInfo) ToPb() *pb.BTCityInfo {
	return &pb.BTCityInfo{
		Index:       this.AIndex,
		Id:          this.CityID,
		NeedTime:    this.NeedTime,
		SurplusTime: int32(ut.Max(int(this.NeedTime)-int(time.Now().UnixMilli()-this.StartTime), 0)),
	}
}

func (this *Model) GetBTCityQueuesClone() []*BTCityInfo {
	this.BTCityQueues.RLock()
	defer this.BTCityQueues.RUnlock()
	return this.BTCityQueues.List[:]
}

func (this *Model) ToBTCityQueues() []map[string]interface{} {
	this.BTCityQueues.RLock()
	defer this.BTCityQueues.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.BTCityQueues.List {
		arr = append(arr, m.Strip())
	}
	return arr
}

func (this *Model) ToBTCityQueuesPb() []*pb.BTCityInfo {
	this.BTCityQueues.RLock()
	defer this.BTCityQueues.RUnlock()
	arr := []*pb.BTCityInfo{}
	for _, m := range this.BTCityQueues.List {
		arr = append(arr, m.ToPb())
	}
	return arr
}

// 添加到建造队列
func (this *Model) PutBTCityQueue(index, id, needTime int32) {
	this.BTCityQueues.Lock()
	defer this.BTCityQueues.Unlock()
	info := NewBTCityInfo(index, id, needTime)
	this.BTCityQueues.List = append(this.BTCityQueues.List, info)
	this.PutNotifyQueue(constant.NQ_ADD_BTCITY, &pb.OnUpdateWorldInfoNotify{Data_27: info.ToPb()}) //通知
}

// 取消修建城市
func (this *Model) RemoveBTCityQueue(index int32) {
	this.BTCityQueues.Lock()
	defer this.BTCityQueues.Unlock()
	// 直接删除
	for i, m := range this.BTCityQueues.List {
		if m.AIndex == index {
			this.BTCityQueues.List = append(this.BTCityQueues.List[:i], this.BTCityQueues.List[i+1:]...)
			this.PutNotifyQueue(constant.NQ_REMOVE_BTCITY, &pb.OnUpdateWorldInfoNotify{
				Data_28: &pb.UpdateBTCityInfo{Index: index},
			}) //通知
			return
		}
	}
}

// 检测更新建造队列
func (this *Model) CheckUpdateBTCityQueue() {
	now := time.Now().UnixMilli()
	this.BTCityQueues.Lock()
	defer this.BTCityQueues.Unlock()
	for i := len(this.BTCityQueues.List) - 1; i >= 0; i-- {
		m := this.BTCityQueues.List[i]
		if int32(now-m.StartTime) < m.NeedTime {
			continue
		}
		// 删除
		this.BTCityQueues.List = append(this.BTCityQueues.List[:i], this.BTCityQueues.List[i+1:]...)
		// 设置地块城市信息
		cell, area := this.GetCell(m.AIndex), this.GetArea(m.AIndex)
		if cell == nil || area == nil {
			continue
		}
		cell.SetCity(m.CityID)
		this.WorldPbChangeCellCityId(cell.Owner, cell.index, cell.CityByteId)
		this.UpdateAreaInfoByBTCity(area, m.CityID) //刷新区域信息
		this.TagUpdateDBByIndex(m.AIndex)
		// 刷新玩家产量
		this.room.PutPlayerUpdateOpSec(cell.Owner)
		// 通知
		this.PutNotifyQueue(constant.NQ_REMOVE_BTCITY, &pb.OnUpdateWorldInfoNotify{
			Data_28: &pb.UpdateBTCityInfo{
				Index:  m.AIndex,
				Cell:   cell.ToPb(),
				Builds: area.ToBuildsPb(),
			},
		})
		// 离线通知
		if m.CityID > 0 {
			this.OfflineNotify(cell.Owner, constant.OFFLINE_MSG_TYPE_CITY, ut.String(m.CityID))
		}
	}
}

// 获取当前某个地块的建筑id
func (this *Model) GetBTCityIdByIndex(index int32) int32 {
	this.BTCityQueues.RLock()
	defer this.BTCityQueues.RUnlock()
	for _, m := range this.BTCityQueues.List {
		if m.AIndex == index {
			return m.CityID
		}
	}
	return -1
}

func (this *Model) CheckCellBTCitying(index int32) bool {
	return this.GetBTCityIdByIndex(index) != -1
}
