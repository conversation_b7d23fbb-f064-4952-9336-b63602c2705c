package lobby

import (
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) initHDShop() {
	this.GetServer().RegisterGO("HD_BuyHeadIcon", this.buyHeadIcon)                 // 购买头像
	this.GetServer().RegisterGO("HD_BuyPawnSkin", this.buyPawnSkin)                 // 购买士兵皮肤
	this.GetServer().RegisterGO("HD_BuyCitySkin", this.buyCitySkin)                 // 购买士兵皮肤
	this.GetServer().RegisterGO("HD_BuyChatEmoji", this.buyChatEmoji)               // 购买聊天表情
	this.GetServer().RegisterGO("HD_ExchangeGold", this.exchangeGold)               // 兑换金币
	this.GetServer().RegisterGO("HD_BuyFreeGold", this.buyFreeGold)                 // 购买免费金币
	this.GetServer().RegisterGO("HD_BuyHero", this.buyHero)                         // 购买英雄
	this.GetServer().RegisterGO("HD_BuySkinBlindBox", this.buySkinBlindBox)         // 购买皮肤盲盒
	this.GetServer().RegisterGO("HD_GetBattlePassInfo", this.getBattlePassInfo)     // 获取战令信息
	this.GetServer().RegisterGO("HD_GetBattlePassReward", this.getBattlePassReward) // 领取战令奖励
	this.GetServer().RegisterGO("HD_BuyBattlePassScore", this.buyBattlePassScore)   // 购买战令积分
	this.GetServer().RegisterGO("HD_GetSkinItems", this.getSkinItems)               // 获取皮肤物品
	this.GetServer().RegisterGO("HD_ComposeSkinItems", this.composeSkinItems)       // 合成皮肤物品

	// this.GetServer().RegisterGO("HD_GetWheelInfo", this.getWheelInfo)               // 获取转盘信息
	// this.GetServer().RegisterGO("HD_WheelBegin", this.wheelBegin)                   // 转动开始
	// this.GetServer().RegisterGO("HD_GetWheelRet", this.getWheelRet)                 // 获取转动结果
	// this.GetServer().RegisterGO("HD_ClaimWheelAward", this.claimWheelAward)         // 领取转盘奖励
}

// 购买头像
func (this *Lobby) buyHeadIcon(session gate.Session, msg *pb.LOBBY_HD_BUYHEADICON_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("headIcon", id)
	if json == nil {
		return nil, ecode.HEAD_ICON_NOT_EXIST.String()
	}
	icon := ut.String(json["icon"])
	if user.HasHeadIcon(icon) {
		return nil, ecode.HEAD_ICON_EXIST.String()
	}
	needGold, needIngot := ut.Int32(json["gold"]), ut.Int32(json["ingot"])
	if needGold > 0 && user.ChangeGold(-needGold, constant.GOLD_CHANGE_BUY_HEADICON_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	} else if needIngot > 0 && user.ChangeIngot(-needIngot, constant.GOLD_CHANGE_BUY_HEADICON_COST) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String() // 元宝不足
	}
	// 添加头像到列表
	user.AddHeadIcon(icon)
	ret, _ = pb.ProtoMarshal(&pb.LOBBY_HD_BUYHEADICON_S2C{
		UnlockHeadIcons: user.UnlockHeadIcons,
		Gold:            user.Gold,
	})
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_shopByHead", map[string]interface{}{
		"head_id":    id,
		"gold_cost":  needGold,
		"ingot_cost": needIngot,
	})
	return
}

// 购买士兵皮肤
func (this *Lobby) buyPawnSkin(session gate.Session, msg *pb.LOBBY_HD_BUYPAWNSKIN_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("pawnSkin", id)
	if json == nil {
		return nil, ecode.SKIN_NOT_EXIST.String()
	} else if user.HasPawnSkin(id) {
		return nil, ecode.PAWN_SKIN_EXIST.String()
	} else if ut.Int(json["cond"]) != ITEM_COND_SHOP {
		return nil, ecode.PAWN_SKIN_NO_BUY.String()
	}
	serverArea := ut.If(slg.IsDebug(), "test", slg.SERVER_AREA)
	limitTimeArr := strings.Split(ut.String(json["limit_time_"+serverArea]), "|")
	if len(limitTimeArr) == 2 && !helper.CheckActivityAutoDate(limitTimeArr[0], limitTimeArr[1]) {
		return nil, ecode.PAWN_SKIN_NO_BUY.String() // 还没到时间
	}
	needGold, needIngot := ut.Int32(json["gold"]), ut.Int32(json["ingot"])
	if needGold > 0 && user.ChangeGold(-needGold, constant.GOLD_CHANGE_BUY_PAWN_SKIN_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	} else if needIngot > 0 && user.ChangeIngot(-needIngot, constant.GOLD_CHANGE_BUY_PAWN_SKIN_COST) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String() // 元宝不足
	}
	// 添加到列表
	user.AddPawnSkin(id)
	ret, _ = pb.ProtoMarshal(&pb.LOBBY_HD_BUYPAWNSKIN_S2C{
		UnlockPawnSkinIds: user.ToUnlockPawnSkinsPb(),
		Gold:              user.Gold,
		Ingot:             user.Ingot,
	})
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_shopByPawnSkin", map[string]interface{}{
		"pawn_skin_id": id,
		"gold_cost":    needGold,
		"ingot_cost":   needIngot,
	})
	return
}

// 购买主城皮肤
func (this *Lobby) buyCitySkin(session gate.Session, msg *pb.LOBBY_HD_BUYCITYSKIN_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("citySkin", id)
	if json == nil {
		return nil, ecode.SKIN_NOT_EXIST.String()
	} else if user.HasPawnSkin(id) {
		return nil, ecode.PAWN_SKIN_EXIST.String()
	} else if ut.Int(json["cond"]) != ITEM_COND_SHOP {
		return nil, ecode.PAWN_SKIN_NO_BUY.String()
	}
	serverArea := ut.If(slg.IsDebug(), "test", slg.SERVER_AREA)
	limitTimeArr := strings.Split(ut.String(json["limit_time_"+serverArea]), "|")
	if len(limitTimeArr) == 2 && !helper.CheckActivityAutoDate(limitTimeArr[0], limitTimeArr[1]) {
		return nil, ecode.PAWN_SKIN_NO_BUY.String() // 还没到时间
	}
	needGold, needIngot := ut.Int32(json["gold"]), ut.Int32(json["ingot"])
	if needGold > 0 && user.ChangeGold(-needGold, constant.GOLD_CHANGE_BUY_PAWN_SKIN_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	} else if needIngot > 0 && user.ChangeIngot(-needIngot, constant.GOLD_CHANGE_BUY_PAWN_SKIN_COST) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String() // 元宝不足
	}
	// 添加到列表
	user.AddCitySkin(id)
	ret, _ = pb.ProtoMarshal(&pb.LOBBY_HD_BUYCITYSKIN_S2C{
		UnlockCitySkinIds: user.ToUnlockCitySkinsToPb(),
		Gold:              user.Gold,
		Ingot:             user.Ingot,
	})
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_shopByCitySkin", map[string]interface{}{
		"city_skin_id": id,
		"gold_cost":    needGold,
		"ingot_cost":   needIngot,
	})
	return
}

// 购买聊天表情
func (this *Lobby) buyChatEmoji(session gate.Session, msg *pb.LOBBY_HD_BUYCHATEMOJI_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("chatEmoji", id)
	if json == nil {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	} else if user.CheckHasEmoji(id) {
		return nil, ecode.CHAT_EMOJI_EXIST.String()
	} else if ut.Int(json["cond"]) != 1 {
		return nil, ecode.CHAT_EMOJI_NO_BUY.String()
	}
	startTime, endTime := ut.String(json["start_time"]), ut.String(json["end_time"])
	if startTime != "" && !helper.CheckActivityAutoDate(startTime, endTime) {
		return nil, ecode.CHAT_EMOJI_NO_BUY.String() // 还没到时间
	}
	needGold, needIngot := ut.Int32(json["gold"]), ut.Int32(json["ingot"])
	if needGold > 0 && user.ChangeGold(-needGold, constant.GOLD_CHANGE_BUY_EMOJI_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	} else if needIngot > 0 && user.ChangeIngot(-needIngot, constant.GOLD_CHANGE_BUY_EMOJI_COST) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String() // 元宝不足
	}
	// 添加到列表
	user.AddEmoji(id)
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_shopByChatEmoji", map[string]interface{}{
		"chat_emoji_id": id,
		"gold_cost":     needGold,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYCHATEMOJI_S2C{
		UnlockChatEmojiIds: user.EmojisToPb(),
		Gold:               user.Gold,
	})
}

// 兑换金币
func (this *Lobby) exchangeGold(session gate.Session, msg *pb.LOBBY_HD_EXCHANGEGOLD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	count := msg.GetCount()
	if count <= 0 || count > user.Ingot {
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	// 扣除元宝
	user.ChangeIngot(-count, constant.GOLD_CHANGE_EXCHANGE_COST)
	// 添加金币
	user.ChangeGold(count, constant.GOLD_CHANGE_EXCHANGE_GET)
	user.FlagUpdateDB()
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_exchangeGold", map[string]interface{}{
		"count": count,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_EXCHANGEGOLD_S2C{
		Gold:  user.Gold,
		Ingot: user.Ingot,
	})
}

// 购买免费金币
func (this *Lobby) buyFreeGold(session gate.Session, msg *pb.LOBBY_HD_BUYFREEGOLD_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.GetBuyFreeGoldSurplusTime() > 0 {
		return nil, ecode.NOT_BUY_FREE_GOLD.String()
	}
	// 添加金币
	user.ChangeGold(lc.TODAY_FREE_GOLD, constant.GOLD_CHANGE_FREE_GET)
	user.TodayBuyFreeGoldCount += 1
	user.FlagUpdateDB()
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_buyFreeGold", map[string]interface{}{
		"gold_cost": lc.TODAY_FREE_GOLD,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYFREEGOLD_S2C{
		Gold:                   user.Gold,
		BuyFreeGoldSurplusTime: int64(user.GetBuyFreeGoldSurplusTime()),
	})
}

// 购买英雄
func (this *Lobby) buyHero(session gate.Session, msg *pb.LOBBY_HD_BUYHERO_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.GetBuyOptionalHeroSurplusTime() > 0 {
		return nil, ecode.NOT_BUY_OPT_HERO.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("portrayalBase", id)
	if json == nil {
		return nil, ecode.HERO_NOT_EXIST.String()
	} else if user.ChangeIngot(-lc.BUY_OPT_HERO_COST, constant.GOLD_CHANGE_BUY_OPT_HERO) == -1 { // 扣除元宝
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	// 添加碎片
	user.AddPortrayalDebrisCount(id, lc.PORTRAYAL_COMP_NEED_COUNT)
	// 记录购买时间
	user.LastBuyOptionalHeroTime = time.Now().UnixMilli()
	user.FlagUpdateDB()
	// 上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_buyHero", map[string]interface{}{
		"id":         id,
		"ingot_cost": lc.BUY_OPT_HERO_COST,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYHERO_S2C{
		Ingot:                      user.Ingot,
		Portrayals:                 user.ToPortrayalsPb(),
		BuyOptionalHeroSurplusTime: user.GetBuyOptionalHeroSurplusTime(),
	})
}

// // 获取转盘信息
// func (this *Lobby) getWheelInfo(session gate.Session, msg *pb.LOBBY_HD_GETWHEELINFO_C2S) (ret []byte, err string) {
// 	user := GetUserByOnline(session.GetUserID())
// 	if user == nil {
// 		return nil, ecode.NOT_BIND_UID.String()
// 	}
// 	return pb.ProtoMarshal(&pb.LOBBY_HD_GETWHEELINFO_S2C{
// 		Info:         user.ToWheelInfo(true),
// 		WheelRecords: user.WheelRecordsToPb(),
// 	})
// }

// // 转动开始
// func (this *Lobby) wheelBegin(session gate.Session, msg *pb.LOBBY_HD_WHEELBEGIN_C2S) (ret []byte, err string) {
// 	user := GetUserByOnline(session.GetUserID())
// 	if user == nil {
// 		return nil, ecode.NOT_BIND_UID.String()
// 	} else if user.GetWheelNotClaimCount() >= 10 {
// 		user.WheelCanGetRet = false
// 		return nil, ecode.HAS_NOT_CLAIM_WHEEL_AWARD.String()
// 	}
// 	user.WheelCanGetRet = false
// 	waitTime, count, freeCount := user.GetWheelNeedWaitTime(true)
// 	if freeCount > 0 {
// 		user.WheelBeginTime = 0
// 		user.WheelCanGetRet = true
// 	} else if waitTime <= 0 {
// 		user.WheelBeginTime = time.Now().UnixMilli()
// 		user.WheelCanGetRet = true
// 	}
// 	return pb.ProtoMarshal(&pb.LOBBY_HD_WHEELBEGIN_S2C{
// 		Info: &pb.WheelInfo{
// 			WheelWaitTime:     waitTime,
// 			WheelResidueCount: count,
// 			WheelCurrCount:    user.WheelCurrCount,
// 			WheelFreeCount:    freeCount,
// 		},
// 	})
// }

// // 获取转动结果
// func (this *Lobby) getWheelRet(session gate.Session, msg *pb.LOBBY_HD_GETWHEELRET_C2S) (ret []byte, err string) {
// 	user := GetUserByOnline(session.GetUserID())
// 	if user == nil {
// 		return nil, ecode.NOT_BIND_UID.String()
// 	} else if !user.WheelCanGetRet {
// 		return nil, ecode.UNKNOWN.String()
// 	}
// 	s2c := &pb.LOBBY_HD_GETWHEELRET_S2C{}
// 	if !msg.GetOk() {
// 		s2c.Info = user.ToWheelInfo(false)
// 		return pb.ProtoMarshal(s2c)
// 	}
// 	var mul int32
// 	s2c.Info, s2c.Id, mul, _ = user.WheelRet()
// 	s2c.WheelRecords = user.WheelRecordsToPb()
// 	s2c.LastMul = mul
// 	s2c.SubData = user.UserSubInfoToPb()
// 	if mul > user.MaxWheelMul {
// 		user.MaxWheelMul = mul
// 	}
// 	// 通知全服
// 	if mul >= 27 {
// 		this.NotifyWheelMulMsg(-1, user.Nickname, mul)
// 	}
// 	return pb.ProtoMarshal(s2c)
// }

// // 领取转盘奖励
// func (this *Lobby) claimWheelAward(session gate.Session, msg *pb.LOBBY_HD_CLAIMWHEELAWARD_C2S) (ret []byte, err string) {
// 	user := GetUserByOnline(session.GetUserID())
// 	if user == nil {
// 		return nil, ecode.NOT_BIND_UID.String()
// 	}
// 	record := user.GetWheelRecord(msg.GetUid())
// 	if record == nil || record.IsClaim {
// 		return nil, ecode.YET_CLAIM.String()
// 	} else if record.NeedSid != 0 && user.SID != ut.AbsInt32(record.NeedSid) {
// 		return nil, ecode.NOT_CUR_SERVER_CLAIM.String()
// 	}
// 	record.IsClaim = true
// 	// 发放奖励
// 	output := this.ChangeUserItems(user, record.Items, constant.GOLD_CHANGE_WHEEL_GET)
// 	return pb.ProtoMarshal(&pb.LOBBY_HD_CLAIMWHEELAWARD_S2C{Rewards: output})
// }

// 购买皮肤盲盒
func (this *Lobby) buySkinBlindBox(session gate.Session, msg *pb.LOBBY_HD_BUYSKINBLINDBOX_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	boxId := msg.GetId()
	blindBox, err := BlindBoxModel.GetBindBox(boxId)
	if err != "" {
		return
	}
	// 扣除元宝
	ingotRst := user.ChangeIngot(-blindBox.Price, constant.GOLD_CHANGE_BUY_SKIN_BLIND_BOX)
	if ingotRst == -1 {
		// 元宝不足
		return nil, ecode.INGOT_NOT_ENOUGH.String()
	}
	// 随机皮肤
	skinId, isRare, err := blindBox.OpenBlindBox()
	if err != "" || skinId == 0 {
		log.Warning("buySkinBlindBox OpenBlindBox err: %v, uid: %v, boxId: %v", err, user.UID, boxId)
		return
	}
	skinItem := user.AddSkinItem("", skinId, false, false)
	// 添加溯源记录
	skinItemTrackDb.InsertSkinItemTrack(&SkinItemTrack{
		UID:      skinItem.UID,
		Owner:    user.UID,
		CurOwner: user.UID,
		Id:       skinId,
	})
	if isRare {
		// 公告
		this.NotifyRareSkin(user.Nickname, boxId)
	}
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_buySkinBlindBox", map[string]interface{}{
		"ingot_cost": blindBox.Price,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYSKINBLINDBOX_S2C{
		SkinId:       skinId,
		SkinItemList: user.ToSkinImtemsPb(),
		Ingot:        ingotRst,
	})
}

// 获取战令信息
func (this *Lobby) getBattlePassInfo(session gate.Session, msg *pb.LOBBY_HD_GETBATTLEPASSINFO_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	if user.BattlePass == nil {
		user.BattlePass = NewBattlePass()
	}
	if !user.BattlePass.SeasonCheck(true) {
		return nil, ecode.BATTLE_PASS_TIMEOUT.String() // 战令过期
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETBATTLEPASSINFO_S2C{Info: user.BattlePass.ToPb()})
}

// 领取战令奖励
func (this *Lobby) getBattlePassReward(session gate.Session, msg *pb.LOBBY_HD_GETBATTLEPASSREWARD_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	if user.BattlePass == nil {
		user.BattlePass = NewBattlePass()
	}
	id, isPay, heros := msg.GetId(), msg.GetIsPay(), msg.GetHeros()
	var items []*g.TypeObj
	if id == -1 {
		// 一键领取
		items, err = user.BattlePass.GetAllReward(heros, isPay)
	} else {
		// 领取指定
		items, err = user.BattlePass.GetReward(id, isPay, heros)
	}
	if err != "" {
		// 领取失败
		return nil, err
	}
	s2c := &pb.LOBBY_HD_GETBATTLEPASSREWARD_S2C{Rewards: &pb.UpdateOutPut{}}
	user.ChangeUserItems(items, constant.GOLD_CHANGE_BATTLE_PASS_GET, s2c.Rewards)
	return pb.ProtoMarshal(s2c)
}

// 购买战令积分
func (this *Lobby) buyBattlePassScore(session gate.Session, msg *pb.LOBBY_HD_BUYBATTLEPASSSCORE_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	if user.BattlePass == nil {
		user.BattlePass = NewBattlePass()
	}
	if !user.BattlePass.SeasonCheck(true) {
		return nil, ecode.BATTLE_PASS_TIMEOUT.String() // 战令过期
	} else if user.BattlePass.BuyScoreCount >= slg.BATTLE_PASS_BUY_SCORE_COUNT {
		return nil, ecode.BATTLE_PASS_BUY_SCORE_LIMIT.String() // 当天购买战令积分次数上限
	} else if user.BattlePass.Score >= slg.BATTLE_PASS_SCORE_MAX {
		return nil, ecode.BATTLE_PASS_BUY_SCORE_LIMIT.String() // 积分已达到上限
	}
	cost := slg.BATTLE_PASS_BUY_SCORE_PRICE
	if user.ChangeIngot(-cost, constant.GOLD_CHANGE_BUY_BATTLE_PASS_SCORE) == -1 {
		return nil, ecode.INGOT_NOT_ENOUGH.String() // 元宝不足
	}
	err = user.BattlePass.BuyScore(user.UID)
	if err != "" {
		return
	}
	// 检测是否可以领取奖励
	if user.BattlePass.CheckCanClaimAward() {
		this.PutUserNotifyToLobby(constant.NQ_BATTLE_PASS_HAS_AWARD, []string{user.UID}, &pb.OnUpdatePlayerInfoNotify{Data_78: true}) // 通知玩家
	}
	// 数数上报
	ta.Track(0, user.UID, user.DistinctId, 0, "ta_buyBattlePassScore", map[string]interface{}{
		"ingot_cost": cost,
	})
	return pb.ProtoMarshal(&pb.LOBBY_HD_BUYBATTLEPASSSCORE_S2C{
		Info:  user.BattlePass.ToPb(),
		Ingot: user.Ingot,
	})
}

// 获取皮肤物品
func (this *Lobby) getSkinItems(session gate.Session, msg *pb.LOBBY_HD_GETSKINITEMS_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}

	return pb.ProtoMarshal(&pb.LOBBY_HD_GETSKINITEMS_S2C{List: user.ToSkinImtemsPb(), UnlockPawnSkinIds: user.PawnSkinsClone()})
}

// 合成皮肤物品
func (this *Lobby) composeSkinItems(session gate.Session, msg *pb.LOBBY_HD_COMPOSESKINITEMS_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	err = user.ComposeSkinItem(msg.GetId())
	if err != "" {
		return
	}

	return pb.ProtoMarshal(&pb.LOBBY_HD_COMPOSESKINITEMS_S2C{List: user.ToSkinImtemsPb(), UnlockPawnSkinIds: user.PawnSkinsClone()})
}
