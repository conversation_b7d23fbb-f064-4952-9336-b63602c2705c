package world

import (
	"fmt"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/fsp"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BuildList struct {
	deadlock.RWMutex
	List []*AreaBuild
}

type ArmyList struct {
	deadlock.RWMutex
	List []*AreaArmy
}

type BattlePreArmy struct {
	UID       string
	Name      string
	PawnCount int32
}

type TreasureWeightInfo struct {
	id     int32
	weight int
}

func (this TreasureWeightInfo) GetWeight() int { return this.weight }

// 一个战场信息
type Area struct {
	world g.World
	Owner string // 拥有者

	Builds *BuildList // 建筑列表
	Armys  *ArmyList  // 军队列表

	// ------------------空闲地块不需要初始化的参数 使用时调用Init(false)初始化------------
	areaSize             *ut.Vec2        // 区域位置大小
	buildSize            *ut.Vec2        // 建筑位置大小
	buildOrigin          *ut.Vec2        // 建造起点
	buildMapPoints       []*ut.Vec2      // 建筑的所有地面点 (延迟初始化)
	battleMapPoints      []*ut.Vec2      // 战斗位置所有地面点 (延迟初始化)
	passPoints           []*ut.Vec2      // 关口位置 固定4个 0.上 1.右 2.下 3.左
	doorPoints           []*ut.Vec2      // 城门口位置 固定4个 0.上 1.右 2.下 3.左
	mainPoints           []*ut.Vec2      // 主位置 用于受攻击的点
	banPlacePawnPointMap map[string]bool // 禁止放置士兵的位置 (延迟初始化)

	// 内存优化相关
	lastAccessTime      int64              // 最后访问时间
	pointTemplate       *AreaPointTemplate // 共享点位模板
	usingSharedTemplate bool               // 是否使用共享模板
	// --------------------------------------------------------------------------------
	fspModel          *fsp.FSPModel          // 当前的战斗帧同步模块
	battlePreArmys    []*g.BattlePreArmyData // 战斗前的军队数据 主要用于战斗结束后的记录
	battlePreMutex    *deadlock.RWMutex
	AddArmyMutex      *deadlock.RWMutex // 添加军队锁 防止同时添加 不好处理战斗
	FspMutex          *deadlock.RWMutex // 战斗帧同步模块锁
	BattleSettleMutex *deadlock.RWMutex // 战斗结算锁

	fspAddArmyChan chan *AreaArmy // 战斗中添加军队chan
	fspAddPawnChan chan *AreaPawn // 战斗中添加士兵chan

	BattleTime int64 // 发起战斗时间

	index           int32 // 位置
	CurHp           int32 // 当前血量 0.表示还没有初始化 -1.表示死亡
	MaxHp           int32 // 最大血量
	CityId          int32 // 城市ID
	maxArmyCount    int32 // 最大军队数量
	maxAddPawnTimes int32 // 最大补兵次数
	State           int8  // 状态 0未初始化 1已初始化 2已清理
}

func NewArea(index int32, wld g.World) *Area {
	area := &Area{
		world:             wld,
		index:             index,
		areaSize:          ut.NewVec2(11, 11),
		buildSize:         ut.NewVec2(1, 1),
		Builds:            &BuildList{List: []*AreaBuild{}},
		Armys:             &ArmyList{List: []*AreaArmy{}},
		maxArmyCount:      5,
		buildMapPoints:    []*ut.Vec2{},
		AddArmyMutex:      new(deadlock.RWMutex),
		battlePreMutex:    new(deadlock.RWMutex),
		FspMutex:          new(deadlock.RWMutex),
		BattleSettleMutex: new(deadlock.RWMutex),
	}
	return area
}

func (this *Area) FromDB(owner string, cityId int32, data map[string]interface{}, armyUidMap map[string]bool) *Area {
	this.Owner = owner
	this.CityId = cityId
	this.Builds.List = this.fromBuildsDB(data["builds"])
	this.Armys.List = this.fromArmysDB(data["armys"], armyUidMap)
	return this
}

func (this *Area) ToDB() map[string]interface{} {
	msg := map[string]interface{}{}
	if len(this.Builds.List) > 0 {
		msg["builds"] = this.ToBuildsDB()
	}
	if len(this.Armys.List) > 0 {
		msg["armys"] = this.ToArmysDB()
	}
	this.FspMutex.RLock()
	if this.IsBattle() {
		msg["battle"] = this.fspModel.ToDB()
		msg["curHp"] = this.CurHp
		msg["maxHp"] = this.MaxHp
		msg["battleTime"] = this.BattleTime
		msg["battlePreArmys"] = this.ToBattlePreArmys()
	}
	this.FspMutex.RUnlock()
	return msg
}

func (this *Area) ToBattlePreArmys() []*g.BattlePreArmyData {
	this.battlePreMutex.RLock()
	defer this.battlePreMutex.RUnlock()
	preArmys := []*g.BattlePreArmyData{}
	for _, m := range this.battlePreArmys {
		data := &g.BattlePreArmyData{
			Uid:       m.Uid,
			Name:      m.Name,
			Owner:     m.Owner,
			Pawns:     array.Clone(m.Pawns),
			BeginTime: m.BeginTime,
			EndTime:   m.EndTime,
			PawnCount: m.PawnCount,
		}
		preArmys = append(preArmys, data)
	}
	return preArmys
}

func (this *Area) ToPb() *pb.AreaInfo {
	msg := &pb.AreaInfo{
		Index:  this.index,
		Owner:  this.Owner,
		CityId: this.CityId,
		Hp:     this.ToHPPb(),
	}
	if len(this.Builds.List) > 0 {
		msg.Builds = this.ToBuildsPb()
	}
	if len(this.Armys.List) > 0 {
		msg.Armys = this.ToArmysPb()
	}
	this.FspMutex.RLock()
	if this.IsBattle() {
		msg.Battle = this.fspModel.ToPb(int32(time.Now().UnixMilli() - this.BattleTime))
	}
	this.FspMutex.RUnlock()
	return msg
}

func (this *Area) ToHPPb() []int32 {
	return []int32{this.CurHp, this.MaxHp}
}

func (this *Area) fromBuildsDB(data interface{}) []*AreaBuild {
	arr := []*AreaBuild{}
	if builds, ok := data.(primitive.A); ok {
		for _, val := range builds {
			m := val.(map[string]interface{})
			id := ut.Int32(m["id"])
			if id == constant.CERI_BUILD_ID || id == constant.STABLE_BUILD_ID {
				// 兼容 研究所和马厩建筑不加载
				continue
			}
			arr = append(arr, NewAreaBuild(this.index, ut.String(m["uid"]), ut.NewVec2ByObj(m["point"]), id, ut.Int32(m["lv"])))
		}
	}
	return arr
}

func (this *Area) ToBuildsDB() []map[string]interface{} {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.Builds.List {
		arr = append(arr, m.ToDB())
	}
	return arr
}

func (this *Area) ToBuildsShortData() []map[string]interface{} {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.Builds.List {
		arr = append(arr, m.ToShortData())
	}
	return arr
}

func (this *Area) ToBuildsShortDataPb() []*pb.AreaBuildInfo {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []*pb.AreaBuildInfo{}
	for _, m := range this.Builds.List {
		arr = append(arr, m.ToShortDataPb())
	}
	return arr
}

func (this *Area) ToBuildsStrip() []*g.BuildStrip {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []*g.BuildStrip{}
	for _, m := range this.Builds.List {
		arr = append(arr, m.Strip())
	}
	return arr
}

func (this *Area) ToBuildsPb() []*pb.AreaBuildInfo {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []*pb.AreaBuildInfo{}
	for _, m := range this.Builds.List {
		arr = append(arr, m.ToPb())
	}
	return arr
}

func (this *Area) fromArmysDB(data interface{}, armyUidMap map[string]bool) []*AreaArmy {
	arr := []*AreaArmy{}
	if armys, ok := data.(primitive.A); ok {
		for _, val := range armys {
			m := val.(map[string]interface{})
			uid, owner := ut.String(m["uid"]), ut.String(m["owner"])
			key := owner + "_" + uid
			if !armyUidMap[key] {
				armyUidMap[key] = true
				army := NewAreaArmy(this.index, uid, owner).FromDB(m, this.world)
				army.Pawns.Lock()
				if cnt := len(army.Pawns.List); cnt > 9 && owner != "" {
					army.Pawns.List = army.Pawns.List[:9]
					log.Error("出现多余的士兵 uid: %v, owner: %v, index: %v, cnt: %v", uid, owner, this.index, cnt)
				}
				army.Pawns.Unlock()
				arr = append(arr, army)
			} else {
				log.Error("出现重复的军队 uid: %v, owner: %v, index: %v", uid, owner, this.index)
			}
		}
	}
	return arr
}

func (this *Area) ToArmysDB() []map[string]interface{} {
	arr := []map[string]interface{}{}
	list := this.GetArmysClone()
	for _, m := range list {
		arr = append(arr, m.ToDB())
	}
	return arr
}

func (this *Area) ToArmysPb() []*pb.AreaArmyInfo {
	list := this.GetArmysClone()
	arr := []*pb.AreaArmyInfo{}
	for _, m := range list {
		// if state := this.ToArmyState(m); state != constant.AS_MARCH { // 如果行军中 就不返回
		// 	arr = append(arr, m.ToPb(state))
		// }
		arr = append(arr, m.ToPb(this.ToArmyState(m)))
	}
	return arr
}

func (this *Area) ToArmysStrip2() []*g.ArmyStrip2 {
	list := this.GetArmysClone()
	arr := []*g.ArmyStrip2{}
	for _, m := range list {
		if state := this.ToArmyState(m); state != constant.AS_MARCH { // 如果行军中 就不返回
			arr = append(arr, m.Strip2(state))
		}
	}
	return arr
}

func (this *Area) ToArmyState(army *AreaArmy) int {
	if this.world.CheckArmyMarchingByUID(army.Uid) {
		return constant.AS_MARCH
	} else if army.AIndex == this.index && this.IsBattle() {
		return constant.AS_FIGHT
	}
	return constant.AS_NONE
}

func (this *Area) Init(force bool) {
	if !force && this.IsInitialized() {
		return
	}
	this.State = constant.AREA_SERVER_STATE_INIT
	isBoss := this.IsBoss()
	if this.CityId > 0 { // 有城市
		data := config.GetJsonData("city", this.CityId)
		this.areaSize = ut.NewVec2ByString(data["area_size"].(string), "x")
		this.buildSize = ut.NewVec2ByString(data["build_size"].(string), "x")
		this.maxArmyCount = ut.Int32(data["max_army"])
		this.maxAddPawnTimes = ut.Int32(data["max_add_pawn"])
	} else { // 没有城市的地块
		this.areaSize = ut.NewVec2(11, 11)
		if isBoss {
			this.buildSize = ut.NewVec2(3, 3)
		} else {
			this.buildSize = ut.NewVec2(1, 1)
		}
		this.maxArmyCount = constant.DEFAULT_MAX_ARMY_COUNT
		this.maxAddPawnTimes = constant.DEFAULT_MAX_ADD_PAWN_TIMES
	}
	// 这里兼容一下建筑 如果一个建筑都没有 那么就添加
	this.UpdateCityBuild()

	// 计算建筑区域的起点 用地图区域 - 建筑区域 / 2
	this.buildOrigin = this.areaSize.Sub(this.buildSize).DivSelf(2)

	// 尝试使用共享模板初始化点位数据
	if this.tryUseSharedTemplate(isBoss) {
		this.usingSharedTemplate = true
	} else {
		// 回退到传统方式
		this.initPointsTraditional(isBoss)
		this.usingSharedTemplate = false
	}

	this.lastAccessTime = ut.Now()
}

// 尝试使用共享模板
func (this *Area) tryUseSharedTemplate(isBoss bool) bool {
	// 检查配置是否启用共享模板
	// TODO: 这里可以添加配置检查
	// if !config.IsSharedTemplateEnabled() { return false }

	// 检查是否满足使用共享模板的条件
	if this.areaSize == nil || this.buildSize == nil {
		return false
	}

	// 如果已经使用了共享模板，先释放旧的引用
	if this.usingSharedTemplate && this.pointTemplate != nil {
		this.releaseCurrentTemplate()
	}

	// 获取模板管理器
	manager := GetAreaPointTemplateManager()
	if manager == nil {
		return false
	}

	// 获取共享模板
	hasOwner := this.Owner != ""
	template := manager.GetTemplate(this.areaSize, this.buildSize, hasOwner, isBoss)
	if template == nil {
		return false
	}

	// 使用共享模板数据
	this.pointTemplate = template
	this.buildMapPoints = template.buildMapPoints
	this.battleMapPoints = template.battleMapPoints
	this.banPlacePawnPointMap = template.banPlacePawnPointMap
	this.passPoints = template.passPoints
	this.doorPoints = template.doorPoints
	this.mainPoints = template.mainPoints

	return true
}

// 释放当前使用的共享模板
func (this *Area) releaseCurrentTemplate() {
	if this.pointTemplate != nil {
		manager := GetAreaPointTemplateManager()
		if manager != nil {
			hasOwner := this.Owner != ""
			isBoss := this.pointTemplate.isBoss
			manager.ReleaseTemplate(this.areaSize, this.buildSize, hasOwner, isBoss)
		}
		this.pointTemplate = nil
		this.usingSharedTemplate = false
	}
}

// 传统方式初始化点位数据
func (this *Area) initPointsTraditional(isBoss bool) {
	// 关口
	this.passPoints = helper.GetPassPoints(this.areaSize)
	// 城门口
	this.doorPoints = helper.GetDoorPoints(this.areaSize, this.buildSize, this.buildOrigin)
	// 主位置
	if isBoss {
		this.mainPoints = helper.GetMainPointsByBoss(this.buildSize, this.buildOrigin)
	} else {
		this.mainPoints = helper.GetMainPoints(this.areaSize, this.buildSize, this.buildOrigin)
	}
	// 建筑的所有地面点
	this.buildMapPoints = helper.GenPointsBySize(ut.NewVec2(this.buildSize.X-2, this.buildSize.Y-2), ut.NewVec2(1, 1))
	// 战斗的所有地面点
	this.battleMapPoints = helper.GetBattlePoints(this.areaSize, this.buildSize, this.buildOrigin, this.passPoints)
	// 不能摆放士兵的位置
	if this.Owner != "" {
		this.banPlacePawnPointMap = helper.GetBanPlacePawnPoints(this.areaSize)
	} else {
		this.banPlacePawnPointMap = map[string]bool{}
	}
}

// 清理空闲状态的地块数据
func (this *Area) Clear() {
	if len(this.Builds.List) > 0 || len(this.Armys.List) > 0 {
		// 有军队或建筑则不能清理
		return
	}
	if !this.IsInitialized() {
		return
	}

	// 如果使用共享模板，需要释放模板引用
	this.releaseCurrentTemplate()

	this.State = constant.AREA_SERVER_STATE_CLEAR
	this.areaSize = nil
	this.buildSize = nil
	this.buildMapPoints = nil
	this.buildOrigin = nil
	this.passPoints = nil
	this.doorPoints = nil
	this.battleMapPoints = nil
	this.mainPoints = nil
	this.banPlacePawnPointMap = nil
	this.lastAccessTime = 0
}

// 智能清理长时间未使用的点位数据
func (this *Area) ClearUnusedPoints(maxIdleTime int64) bool {
	// 检查是否已初始化
	if !this.IsInitialized() {
		return false // 未初始化或已清理状态
	}

	// 如果有军队或建筑，不能清理
	if len(this.Builds.List) > 0 || len(this.Armys.List) > 0 {
		return false
	}

	// 检查是否超过最大空闲时间
	if ut.Now()-this.lastAccessTime > maxIdleTime {
		// 直接调用Clear方法进行清理
		this.Clear()
		return true
	}

	return false
}

// 检查Area是否已初始化
func (this *Area) IsInitialized() bool {
	return this.State == constant.AREA_SERVER_STATE_INIT
}

// 获取Area的内存使用信息
func (this *Area) GetMemoryInfo() map[string]interface{} {
	info := map[string]interface{}{
		"index":                 this.index,
		"owner":                 this.Owner,
		"state":                 this.State,
		"is_points_initialized": this.IsInitialized(),
		"using_shared_template": this.usingSharedTemplate,
		"last_access_time":      this.lastAccessTime,
	}

	if this.IsInitialized() {
		info["build_points_count"] = len(this.buildMapPoints)
		info["battle_points_count"] = len(this.battleMapPoints)
		info["ban_points_count"] = len(this.banPlacePawnPointMap)
		info["pass_points_count"] = len(this.passPoints)
		info["door_points_count"] = len(this.doorPoints)
		info["main_points_count"] = len(this.mainPoints)

		if this.usingSharedTemplate && this.pointTemplate != nil {
			info["template_ref_count"] = this.pointTemplate.refCount
			info["template_is_boss"] = this.pointTemplate.isBoss
		}
	}

	return info
}

// 验证模板引用计数的准确性
func (this *Area) ValidateTemplateRefCount() bool {
	if !this.usingSharedTemplate || this.pointTemplate == nil {
		return true // 不使用模板时总是正确的
	}

	// 检查引用计数是否大于0
	if this.pointTemplate.refCount <= 0 {
		return false
	}

	// 这里可以添加更多的验证逻辑
	return true
}

// 强制使用共享模板（用于测试）
func (this *Area) ForceUseSharedTemplate() bool {
	if this.IsInitialized() {
		// 先清理现有数据
		this.ClearUnusedPoints(0)
	}

	// 重新初始化使用共享模板
	// 需要判断是否为Boss区域，这里简单判断buildSize
	isBoss := this.buildSize != nil && (this.buildSize.X > 5 || this.buildSize.Y > 5)
	return this.tryUseSharedTemplate(isBoss)
}

// 刷新城市
func (this *Area) UpdateCityBuild() {
	this.Builds.Lock()
	if this.IsAncient() {
		if this.Builds.List == nil {
			this.Builds.List = []*AreaBuild{
				NewAreaBuild(
					this.index,
					ut.ID(),
					&ut.Vec2{X: 2, Y: 2},
					this.CityId,
					1,
				),
				NewAreaBuild(
					this.index,
					ut.ID(),
					&ut.Vec2{X: 0, Y: 0},
					3000,
					0,
				),
			}
		}
	} else if ut.AbsInt32(this.CityId) != constant.MAIN_CITY_ID {
		this.Builds.List = []*AreaBuild{}
	}
	this.Builds.Unlock()
}

// 更新血量
func (this *Area) UpdateMaxHP() {
	this.State = constant.AREA_SERVER_STATE_DEFAULT
	// 获取对应建筑
	var pawnId int32 = 0
	var lv int32 = 1
	isAncient := this.IsAncient()
	if this.CityId == constant.MAIN_CITY_ID {
		pawnId = 7003 // 主城
	} else if this.CityId == constant.FORT_CITY_ID {
		pawnId = 7002 // 要塞
	} else if isAncient {
		pawnId = 8001 // 遗迹
	} else if this.Owner != "" {
		pawnId = 7001 // 哨塔
	}
	var json map[string]interface{} = nil
	if pawnId == 0 {
		if this.MaxHp == -1 {
			this.CurHp = 0
			return // 这里表示boss
		}
		lv = 1
		json = config.GetJsonData("landAttr", this.world.GetLandLv(this.index)*1000+lv)
	} else if build := this.GetAncientBuild(); build != nil {
		lv = build.Lv
		json = config.GetJsonData("pawnAttr", pawnId*1000+lv)
	} else {
		lv = this.world.GetPlayerTowerLvByPawn(this.Owner, pawnId)
		json = config.GetJsonData("pawnAttr", pawnId*1000+lv)
	}
	hp := this.MaxHp
	if json != nil {
		this.MaxHp = ut.Int32(json["hp"])
	} else {
		this.MaxHp = constant.DEFAULT_AREA_MAX_HP
	}
	if this.MaxHp > 0 {
		// 遗迹加成
		mul := 1 + this.world.GetAncientEffectFloatByPlayer(this.Owner, effect.TOWER_HP)*0.01
		this.MaxHp = ut.RoundInt32(float64(this.MaxHp) * mul)
		this.FspMutex.RLock()
		if this.IsBattle() {
			this.CurHp = ut.ClampInt32(this.CurHp+(this.MaxHp-hp), this.CurHp, this.MaxHp)
			// 通知
			this.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
				Data: &pb.FrameInfo{
					Type:              int32(constant.FSP_NOTIFY_TYPE_UPDATE_HP),
					CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
					Hp:                []int32{this.CurHp, this.MaxHp},
					BuildInfo:         []int32{pawnId, lv},
					RandSeed:          int32(this.fspModel.GetRandSeed()),
				},
			})
			this.fspModel.AddRecordData(&g.BattleRecordData{
				Type:              constant.FSP_NOTIFY_TYPE_UPDATE_HP,
				CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
				Hp:                []int32{this.CurHp, this.MaxHp},
				BuildInfo:         []int32{pawnId, lv},
			}) // 记录帧信息
			this.fspModel.UpdateBuildInfo(pawnId, lv) // 刷新建筑信息
		} else {
			this.CurHp = this.MaxHp
		}
		this.FspMutex.RUnlock()
	} else {
		this.CurHp = 0
	}
}

// 获取有军队的玩家列表
func (this *Area) GetHasArmyPlayers() []string {
	arr := []string{}
	this.FspMutex.RLock()
	if this.IsBattle() {
		arr = append(arr, this.fspModel.GetAttacker()) // 默认将发起者放到第一个
	}
	this.FspMutex.RUnlock()
	list := this.GetArmysClone()
	for _, m := range list {
		if !this.world.CheckArmyMarchingByUID(m.Uid) && m.Owner != "" && !array.Has(arr, m.Owner) {
			arr = append(arr, m.Owner)
		}
	}
	return arr
}

// 是否有血量
func (this *Area) IsHasHp() bool {
	return this.Owner != "" || this.IsBattle()
}

// 建筑面积
func (this *Area) GetBuildAcreage() int32 {
	size := this.GetBuildSize()
	return size.X * size.Y
}

// 检测是否战斗区域
func (this *Area) CheckIsBattleArea(x, y int32) bool {
	size := this.GetAreaSize()
	if x < 0 || y < 0 || x >= size.X || y >= size.Y {
		return false
	} else if this.CheckIsBuildArea(x, y) {
		return false
	}
	return true
}

// 检测是否在建筑区域
func (this *Area) CheckIsBuildArea(x, y int32) bool {
	this.Init(false)
	return x >= this.buildOrigin.X && x < (this.buildOrigin.X+this.buildSize.X) && y >= this.buildOrigin.Y && y < (this.buildOrigin.Y+this.buildSize.Y)
}

// 是否可以加速的城市
func (this *Area) IsCanUpSpeedMarchCity() bool {
	return this.CityId == constant.MAIN_CITY_ID || this.CityId == constant.FORT_CITY_ID || this.IsAncient()
}

// 是否可以恢复士兵血量
func (this *Area) IsRecoverPawnHP() bool {
	return this.IsCanUpSpeedMarchCity()
}

// 是否遗迹
func (this *Area) IsAncient() bool {
	return this.CityId >= constant.CHANGAN_CITY_ID && this.CityId <= constant.LUOYANG_CITY_ID
}

// 是否Boss
func (this *Area) IsBoss() bool {
	return this.MaxHp == -1
}

func (this *Area) GetBoss() g.Pawn {
	if this.Owner != "" {
		return nil
	}
	this.Armys.RLock()
	defer this.Armys.RUnlock()
	for _, army := range this.Armys.List {
		if pawn := array.Find(army.Pawns.List, func(m *AreaPawn) bool { return m.IsBoss() }); pawn != nil {
			return pawn
		}
	}
	return nil
}

// ///////////////////////////////////////////////////////// 建筑 ////////////////////////////////////////////////////////////////
// 添加建筑
func (this *Area) AddBuild(build *AreaBuild) {
	this.Builds.Lock()
	defer this.Builds.Unlock()
	this.Builds.List = append(this.Builds.List, build)
}

// 删除建筑
func (this *Area) RemoveBuild(uid string) {
	this.Builds.Lock()
	defer this.Builds.Unlock()
	this.Builds.List = array.RemoveItem(this.Builds.List, func(item *AreaBuild) bool { return item.Uid == uid })
}

// 获取建筑根据uid
func (this *Area) GetBuildByUID(uid string) *AreaBuild {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	if build := array.Find(this.Builds.List, func(item *AreaBuild) bool { return item.Uid == uid }); build != nil {
		return build
	}
	return nil
}

// 获取建筑列表
func (this *Area) GetAreaBuildsById(id int32) []g.Build {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	arr := []g.Build{}
	for _, m := range this.Builds.List {
		if m.Id == id {
			arr = append(arr, m)
		}
	}
	return arr
}

// 获取建筑
func (this *Area) GetAreaBuildById(id int32) *AreaBuild {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	if build := array.Find(this.Builds.List, func(item *AreaBuild) bool { return item.Id == id }); build != nil {
		return build
	}
	return nil
}

func (this *Area) GetAreaIBuildById(id int32) g.Build {
	if buld := this.GetAreaBuildById(id); buld != nil {
		return buld
	}
	return nil
}

// 获取主城建筑
func (this *Area) GetMainBuild() *AreaBuild {
	return this.GetAreaBuildById(constant.MAIN_BUILD_ID)
}

// 获取联盟建筑
func (this *Area) GetAllianceBuild() *AreaBuild {
	return this.GetAreaBuildById(constant.ALLI_BUILD_ID)
}

// 获取城墙建筑
func (this *Area) GetWallBuild() *AreaBuild {
	return this.GetAreaBuildById(constant.WALL_BUILD_ID)
}

// 获取所有建筑效果
func (this *Area) GetAllBuildEffect() map[int32]*g.EffectObj {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	effects := map[int32]*g.EffectObj{}
	for _, m := range this.Builds.List {
		if m.Lv == 0 {
			continue
		}
		for _, eff := range m.Effects {
			if e := effects[eff.Type]; e != nil {
				e.Value += eff.Value
			} else {
				effects[eff.Type] = eff.Clone()
			}
		}
	}
	return effects
}

// 获取某个建筑的效果
func (this *Area) GetBuildEffect(uid string) map[int32]*g.EffectObj {
	effects := map[int32]*g.EffectObj{}
	if build := this.GetBuildByUID(uid); build != nil && build.Lv > 0 {
		for _, eff := range build.Effects {
			if e := effects[eff.Type]; e != nil {
				e.Value += eff.Value
			} else {
				effects[eff.Type] = eff.Clone()
			}
		}
	}
	return effects
}

// 建筑是否都满级了
func (this *Area) IsBuildsMaxLv(id int32) bool {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	return !array.Some(this.Builds.List, func(m *AreaBuild) bool { return m.Id == id && !m.IsMaxLv() })
}

// 获取一个可摆放建筑的位置
func (this *Area) GetBuildCanPlacePos(build *AreaBuild) (*ut.Vec2, bool) {
	this.Init(false)
	buildPointMap, buildMap := map[string]bool{}, map[int32]bool{}
	this.Builds.RLock()
	for _, m := range this.Builds.List {
		buildMap[m.Id] = true
		ps := m.GetActPoints()
		for _, p := range ps {
			buildPointMap[p.ID()] = true
		}
	}
	this.Builds.RUnlock()
	buildSizeX, buildSizeY := this.buildSize.X-2, this.buildSize.Y-2
	// 先看默认位置
	if defaultPoint := build.GetDefaultPoint(); defaultPoint != nil && !array.Some(build.GetActPointsByPoint(defaultPoint), func(m *ut.Vec2) bool {
		return buildPointMap[m.ID()] || m.X <= 0 || m.X > buildSizeX || m.Y <= 0 || m.Y > buildSizeY
	}) {
		return defaultPoint, true
	}
	// 没有就随机找一个
	arr, buildPoints := []*ut.Vec2{}, build.GetPoints()
	for _, point := range this.buildMapPoints {
		if !array.Some(buildPoints, func(m *ut.Vec2) bool {
			x, y := m.X+point.X, m.Y+point.Y
			if x <= 0 || x > buildSizeX || y <= 0 || y > buildSizeY {
				return true
			}
			return buildPointMap[ut.Itoa(x)+"_"+ut.Itoa(y)]
		}) {
			arr = append(arr, point)
		}
	}
	cnt := len(arr)
	if cnt == 0 {
		return nil, false
	}
	// 获取配置表里面的位置
	confPointMap, datas := map[string]bool{}, config.GetJson("buildBase").Datas
	for _, m := range datas {
		if buildMap[ut.Int32(m["id"])] { // 如果这个建筑已经有了 就不用取他的位置了
		} else if pos := ut.String(m["pos"]); pos != "" {
			point := ut.NewVec2ByString(pos, ",")
			size := ut.NewVec2ByString(ut.String(m["size"]), "x")
			points := helper.GenPointsBySize(size, point)
			for _, p := range points {
				confPointMap[p.ID()] = true
			}
		}
	}
	arr1 := []*ut.Vec2{}
	for _, m := range arr {
		if !confPointMap[m.ID()] { // 排除配置表里面的位置
			arr1 = append(arr1, m)
		}
	}
	if len(arr1) > 0 {
		return arr1[ut.Random(0, len(arr1)-1)].Clone(), true
	}
	return arr[ut.Random(0, cnt-1)].Clone(), true
}

// 检测位置是否可以摆放
func (this *Area) CheckCanPlace(build *AreaBuild, point *ut.Vec2) bool {
	this.Builds.RLock()
	defer this.Builds.RUnlock()
	points, pointMap := build.GetPoints(), map[string]bool{}
	for _, m := range points {
		id := ut.Itoa(m.X+point.X) + "_" + ut.Itoa(m.Y+point.Y)
		pointMap[id] = true
	}
	for _, m := range this.Builds.List {
		if m.Uid == build.Uid {
			continue
		} else if array.Some(m.GetActPoints(), func(p *ut.Vec2) bool { return pointMap[p.ID()] }) {
			return false
		}
	}
	return true
}

// 获取建筑士兵信息
func (this *Area) GetBuildPawnInfo() (id, lv int32) {
	if this.IsAncient() { // 遗迹
		id = 8001
		if ancient := this.GetAncientBuild(); ancient != nil {
			lv = ancient.Lv
		} else {
			lv = 1
		}
		return
	} else if this.CityId == constant.MAIN_CITY_ID { // 城墙
		id = 7003
	} else if this.CityId == constant.FORT_CITY_ID { // 要塞
		id = 7002
	} else if this.Owner != "" { // 除了要塞其他都是箭塔
		id = 7001
	}
	if id > 0 {
		lv = this.world.GetPlayerTowerLvByPawn(this.Owner, id)
	}
	return
}

func (this *Area) GetBuildSize() *ut.Vec2 {
	this.Init(false)
	return this.buildSize
}

/////////////////////////////////////////////////////////// 军队 ////////////////////////////////////////////////////////////////

// 获取军队
func (this *Area) GetArmyByUid(uid string) *AreaArmy {
	if uid == "" {
		return nil
	}
	this.Armys.RLock()
	defer this.Armys.RUnlock()
	return this.GetArmyByUidNoLock(uid)
}

// 获取军队 没有加锁的
func (this *Area) GetArmyByUidNoLock(uid string) *AreaArmy {
	for _, m := range this.Armys.List {
		if m.Uid == uid {
			return m
		}
	}
	return nil
}

// 获取屯田的军队
func (this *Area) GetTondenArmy() *AreaArmy {
	this.Armys.RLock()
	defer this.Armys.RUnlock()
	for _, m := range this.Armys.List {
		if m.state == constant.AS_TONDEN {
			return m
		}
	}
	return nil
}

// 创建一个空军队
func (this *Area) CreateEmptyArmy(name, uid string) *AreaArmy {
	return NewAreaArmy(this.index, ut.ID(), uid).Init(name)
}

// 编队1:3304,4,1,6001@[0,2,5][1,1,15];3202,5,1
// 创建一个军队
func (this *Area) CreateArmyByString(str string, uid string) *AreaArmy {
	a := strings.Split(str, ":")
	name, b := a[0], a[1]
	army := NewAreaArmy(this.index, ut.ID(), uid).Init(name)
	pawns := []*AreaPawn{}
	arr := strings.Split(b, ";")
	for _, s := range arr {
		p := make([]string, 3)
		if strings.Contains(s, "@") {
			for i := 0; i < 3; i++ {
				cur := strings.Index(s, ",")
				p[i] = fmt.Sprint(s[0:cur])
				s = strings.Replace(s, fmt.Sprintf("%s,", p[i]), "", 1)
			}
			p = append(p, fmt.Sprint(s[0:]))
		} else {
			p = strings.Split(s, ",")
		}
		if len(p) < 3 {
			continue
		}
		id, lv, count := ut.Int32(p[0]), ut.Int32(p[1]), ut.Int32(p[2])
		var equipObj *g.EquipInfo
		if len(p) == 4 {
			equipStr := p[3]
			_equip := strings.Split(equipStr, "@")
			equipObj = g.NewEquip().SetID(ut.Int32(_equip[0]))
			_attrs := _equip[1]
			_attrsArr := strings.Split(_attrs, "[")
			targetAttrs := make([][]int32, 0)
			for _, st := range _attrsArr {
				if st == "" {
					continue
				}
				st = strings.Replace(st, "]", "", -1)
				targetAttrs = append(targetAttrs, ut.StringToInt32s(st, ","))
			}
			equipObj.SetAttr(targetAttrs)
		}
		for i := int32(0); i < count; i++ {
			pawn := NewAreaPawn(this.index, army.EnterDir, ut.ID(), ut.NewVec2(0, 0), id, uid, army.Uid, this.world).Init(lv, 0)
			if equipObj != nil {
				pawn.ChangeEquip(equipObj.UID, equipObj.Attrs, true)
			}
			pawns = append(pawns, pawn)
		}
	}
	army.Pawns.Lock()
	army.Pawns.List = pawns
	army.Pawns.Unlock()
	return army
}

func (this *Area) GetArmysClone() []*AreaArmy {
	this.Armys.RLock()
	defer this.Armys.RUnlock()
	return this.Armys.List[:]
}

// 添加军队 只能在world外层调用
func (this *Area) addArmy(army *AreaArmy, dir int32) {
	this.Init(false)
	if this.GetArmyByUid(army.Uid) == nil {
		this.Armys.Lock()
		this.Armys.List = append(this.Armys.List, army)
		this.Armys.Unlock()
	}
	army.ChangeAIndex(this.index, dir) // 修改所在位置
	this.world.ChangeArmyDistIndex(army.Owner, army.Uid, this.index)
}

// 删除军队
func (this *Area) RemoveArmy(uid string) {
	this.Armys.RLock()
	for i := int32(len(this.Armys.List)) - 1; i >= 0; i-- {
		if army := this.Armys.List[i]; army.Uid == uid {
			this.Armys.RUnlock()
			this.RemoveArmyByIndex(uid, i)
			return
		}
	}
	this.Armys.RUnlock()
}

func (this *Area) RemoveArmyByIndex(uid string, i int32) {
	this.Armys.Lock()
	army := this.Armys.List[i]
	this.Armys.List = append(this.Armys.List[:i], this.Armys.List[i+1:]...)
	this.Clear()
	this.Armys.Unlock()
	// // 刷新玩家英雄殿画像信息
	// if hero := army.GetHero(); hero != nil {
	// 	this.world.UpdatePlayerHeroDie(army.Owner, hero.ID)
	// }
	this.world.ChangeArmyDistIndex(army.Owner, army.Uid, -1)
	this.world.TagUpdateDBByIndex(this.index)
	if !this.IsBattle() { // 战斗中不用通知
		this.world.NotifyAreaUpdateInfo(this.index, constant.NQ_REMOVE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_12: uid})
	}
}

// 删除军队士兵
func (this *Area) RemoveArmyPawn(auid string, uid string, isDead bool) (rst bool, tresureRes map[int32]int32) {
	if auid == "" || uid == "" {
		return false, nil
	}
	army := this.GetArmyByUid(auid)
	if army == nil {
		return false, nil
	}
	pawn := army.RemovePawn(uid)
	if pawn == nil {
		return false, nil
	}
	// 检测是否有宝箱
	if tresures := pawn.GetTreasuresClone(); pawn.Owner != "" && tresures != nil && len(tresures) > 0 {
		if wld := this.GetWorld(); wld.GetPlayerTreasureLostCount(pawn.Owner) <= constant.LOST_TRESURE_COMPENSATE_LIMIT {
			// 未超过补偿限制
			tresureRes = map[int32]int32{}
			mul := wld.GetPlayerTreasureAwardMul(pawn.Owner)
			for _, treasure := range tresures {
				if len(treasure.Rewards) == 0 {
					// 未打开则打开宝箱
					treasure.RandomRewards(mul)
				}
				for _, reward := range treasure.Rewards {
					tresureRes[reward.Type] += reward.Count
				}
			}
		}
	}
	// 刷新玩家产出 这里主要是刷新粮耗
	this.world.UpdatePlayerOutput(army.Owner)
	// 删除在练级里面的
	this.world.RemovePawnLvingPawn(this.index, uid)
	// 刷新玩家英雄殿画像信息
	if pawn.IsHero() {
		this.world.UpdatePlayerHeroDie(army.Owner, army.Uid, pawn.Portrayal.ID)
	}
	// 如果一个士兵都没有了
	if army.GetActPawnCount() == 0 {
		if isDead {
			// 离线通知
			this.world.OfflineNotify(army.Owner, constant.OFFLINE_MSG_TYPE_ARMY_DEAD, army.Name)
		}
		this.RemoveArmy(auid)
		this.RemoveArmyToBattlePreRecord(auid)
		if army.Owner != "" { // 野怪不用通知
			this.world.NotifyPlayerArmyDistInfo(army.Owner)
		}
	} else {
		this.world.TagUpdateDBByIndex(this.index)
		if !this.IsBattle() {
			this.world.NotifyAreaUpdateInfo(this.index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
				Data_19: army.ToPb(this.ToArmyState(army)),
			})
		}
	}
	// 通知是否有新的宝箱
	this.world.SendPlayerHasTreasure(army.Owner)
	if army.Owner != "" {
		// 触发任务
		mostPawnCount := this.GetWorld().GetMaxPawnCountInArmy(army.Owner)
		this.GetWorld().TriggerTask(army.Owner, tctype.ARMY_PAWN_COUNT, mostPawnCount, 0)
		// 添加到医馆
		this.GetWorld().AddInjuryPawn(uid, army.Owner, pawn.Id, pawn.Lv, pawn.SkinId, pawn.CureCount)
	}
	return true, tresureRes
}

// 获取所有士兵的位置
func (this *Area) GetAllPawnPointsToMapBool(ignoreArmy string) map[string]bool {
	data := map[string]bool{}
	list := this.GetArmysClone()
	for _, army := range list {
		if this.world.CheckArmyMarchingByUID(army.Uid) || army.Uid == ignoreArmy {
			continue
		}
		army.Pawns.RLock()
		for _, pawn := range army.Pawns.List {
			data[pawn.Point.Join("_")] = true
		}
		army.Pawns.RUnlock()
	}
	return data
}

// 获取空闲的战斗位置
func (this *Area) GetIdleBattlePointsToMapBool(ignoreArmy string) map[string]bool {
	this.Init(false)
	data := map[string]bool{}
	pawnPointMap := this.GetAllPawnPointsToMapBool(ignoreArmy)
	for _, p := range this.battleMapPoints {
		key := p.Join("_")
		if !pawnPointMap[key] && !this.banPlacePawnPointMap[key] {
			data[key] = true
		}
	}
	return data
}

// 获取训练出来的士兵位置
func (this *Area) GetDrillPawnPoint() *ut.Vec2 {
	var dir int32 = 2 // 城门下面
	points := helper.GetPawnPointsByDoor(this.GetIdleBattlePointsToMapBool(""), this.GetAreaSize(), this.GetDoorPoints(), dir, 1)
	if len(points) == 0 {
		return this.doorPoints[dir]
	}
	return points[0]
}

// 检测士兵位置是否合法
func (this *Area) CheckPawnPoint(point *ut.Vec2) bool {
	list := this.GetArmysClone()
	// 是否已经有士兵在这个位置
	for _, army := range list {
		if this.world.CheckArmyMarchingByUID(army.Uid) { // 行军的跳过
			continue
		} else if army.GetPawnByPoint(point) != nil {
			return false
		}
	}
	// 是否在禁止区域
	return this.CheckIsBattleArea(point.X, point.Y) && !this.banPlacePawnPointMap[point.ID()]
}

// 检测士兵位置是否合法
func (this *Area) CheckPawnPointIgnore(point *ut.Vec2, ignores map[string]bool) bool {
	list := this.GetArmysClone()
	// 是否已经有士兵在这个位置
	for _, army := range list {
		if this.world.CheckArmyMarchingByUID(army.Uid) { // 行军的跳过
			continue
		}
		ok := false
		army.Pawns.RLock()
		for _, pawn := range army.Pawns.List {
			if !ignores[pawn.Uid] && pawn.Point.Equals(point) {
				ok = true
				break
			}
		}
		army.Pawns.RUnlock()
		if ok {
			return false
		}
	}
	// 是否在禁止区域
	return this.CheckIsBattleArea(point.X, point.Y) && !this.banPlacePawnPointMap[point.ID()]
}

// 恢复所有士兵血量 外部调用的时候 必须是没有战斗的情况下
func (this *Area) RecoverAllPawnHP() {
	list := this.GetArmysClone()
	for _, army := range list {
		if !this.world.CheckArmyMarchingByUID(army.Uid) && this.world.CheckIsOneAlliance(army.Owner, this.Owner) {
			army.RecoverAllPawn()
		}
	}
}

// 改变士兵皮肤
func (this *Area) ChangePawnSkin(pawn *AreaPawn, skin, sync int32, outs []g.Pawn) []g.Pawn {
	pawns := this.GetPawnsBySync(pawn, sync)
	for _, p := range pawns {
		if p.Id == pawn.Id {
			p.SetSkinId(skin)
			outs = append(outs, p)
		}
	}
	return outs
}

// 改变士兵装备
func (this *Area) ChangePawnEquip(pawn *AreaPawn, equip *g.EquipInfo, sync int32, outs []g.Pawn) []g.Pawn {
	pawns := this.GetPawnsBySync(pawn, sync)
	for _, p := range pawns {
		if p.Id == pawn.Id {
			p.ChangeEquip(equip.UID, equip.Attrs, this.IsRecoverPawnHP())
			outs = append(outs, p)
		}
	}
	return outs
}

func (this *Area) GetPawnsBySync(pawn *AreaPawn, sync int32) []*AreaPawn {
	pawns := []*AreaPawn{}
	switch sync {
	case 1: // 场景中的
		list := this.GetArmysClone()
		owner := pawn.Owner
		for _, army := range list {
			if owner == army.Owner && !this.world.CheckArmyMarchingByUID(army.Uid) {
				army.Pawns.RLock()
				pawns = append(pawns, army.Pawns.List...)
				army.Pawns.RUnlock()
			}
		}
	case 2: // 军队中的
		if army := this.GetArmyByUid(pawn.ArmyUid); army != nil {
			army.Pawns.RLock()
			pawns = append(pawns, army.Pawns.List...)
			army.Pawns.RUnlock()
		}
	default:
		pawns = append(pawns, pawn)
	}
	return pawns
}

// 创建一个军旗士兵
func (this *Area) CreateBuildFlagPawn(uid string, hp []int32, point *ut.Vec2, owner string) g.Pawn {
	pawn := NewAreaPawn(this.index, 0, uid, point, 3601, owner, "", this.world).Init(1, 0)
	pawn.curHp = hp[0]
	pawn.maxHp = hp[1]
	pawn.State = constant.AS_FIGHT
	return pawn
}

// 创建一个宠物士兵
func (this *Area) CreatePetPawn(uid string, id, lv int32, point *ut.Vec2, owner string) g.Pawn {
	pawn := NewAreaPawn(this.index, 0, uid, point, id, owner, "", this.world).Init(lv, 0)
	pawn.State = constant.AS_FIGHT
	return pawn
}

// 创建一个非战斗单位
func (this *Area) CreateNoncombatPawn(uid string, id, lv int32, point *ut.Vec2, owner string, enterDir int32) g.Pawn {
	pawn := NewAreaPawn(this.index, enterDir, uid, point, id, owner, "", this.world).Init(lv, 0)
	pawn.State = constant.AS_FIGHT
	return pawn
}

// 检测更新时光旗
func (this *Area) CheckUpdateTimeFlagHP(uid string) bool {
	has := false
	list := this.GetArmysClone()
	for _, army := range list {
		if (uid != "" && army.Owner != uid) || army.GetPawnCount() == 0 || army.IsFighting() {
			continue
		}
		army.Pawns.RLock()
		for _, pawn := range army.Pawns.List {
			if pawn.Equip != nil && pawn.Equip.GetEquipEffectByType(eeffect.TODAY_ADD_HP) != nil {
				maxHp := pawn.GetMaxHP()
				pawn.UpdateAttr(false)
				if newMaxHp := pawn.GetMaxHP(); newMaxHp > maxHp {
					pawn.curHp = ut.MinInt32(pawn.curHp+(newMaxHp-maxHp), newMaxHp)
					has = true
				}
			}
		}
		army.Pawns.RUnlock()
	}
	return has
}

/////////////////////////////////////////////////////////// 战斗 ////////////////////////////////////////////////////////////////

func (this *Area) GetIndex() int32   { return this.index }
func (this *Area) GetCityID() int32  { return this.CityId }
func (this *Area) GetOwner() string  { return this.Owner }
func (this *Area) GetCurHP() int32   { return this.CurHp }
func (this *Area) GetMaxHP() int32   { return this.MaxHp }
func (this *Area) GetWorld() g.World { return this.world }
func (this *Area) IsBattle() bool    { return this.fspModel != nil && !this.fspModel.GetFspIsEnd() }

func (this *Area) GetAreaSize() *ut.Vec2 {
	this.Init(false)
	return this.areaSize
}

func (this *Area) GetPassPoints() []*ut.Vec2 {
	this.Init(false)
	return this.passPoints
}

func (this *Area) GetDoorPoints() []*ut.Vec2 {
	this.Init(false)
	return this.doorPoints
}

func (this *Area) GetMainPoints() []*ut.Vec2 {
	this.Init(false)
	return this.mainPoints
}

func (this *Area) GetBanPlacePawnPointMap() map[string]bool {
	this.Init(false)
	return this.banPlacePawnPointMap
}

func (this *Area) GetBattleCurrentFrameIndex() int32 {
	if this.IsBattle() {
		return this.fspModel.GetCurrentFrameIndex()
	}
	return -1
}

// 获取进攻方剩余可以补兵次数
func (this *Area) GetAttackerCanAddPawnTimes() int32 {
	if !this.IsBattle() {
		return 0
	}
	return this.maxAddPawnTimes - this.fspModel.GetAttackerAcc()
}

// 获取防守方剩余可以补兵次数
func (this *Area) GetDefenderCanAddPawnTimes() int32 {
	if !this.IsBattle() {
		return 0
	}
	return this.maxAddPawnTimes - this.fspModel.GetDefenderAcc()
}

// 读取战斗信息
func (this *Area) FromBattle(info map[string]interface{}) bool {
	if info == nil {
		return false
	}
	data := info["battle"]
	if data == nil {
		return false
	}
	// fmt.Println("FromBattle ", this.index)
	this.CurHp = ut.Int32(info["curHp"])
	this.MaxHp = ut.Int32(info["maxHp"])
	if this.MaxHp == -1 {
		this.State = constant.AREA_SERVER_STATE_DEFAULT
	}
	this.BattleTime = ut.Int64(info["battleTime"])
	if preArmyArr, ok := info["battlePreArmys"].(bson.A); ok {
		preArmyDatas := []*g.BattlePreArmyData{}
		for _, v := range preArmyArr {
			preArmyData := &g.BattlePreArmyData{}
			if preArmyBytes, err := bson.Marshal(v); err == nil {
				if err := bson.Unmarshal(preArmyBytes, preArmyData); err == nil {
					preArmyDatas = append(preArmyDatas, preArmyData)
				}
			}
		}
		this.battlePreArmys = preArmyDatas
	}
	// fmt.Println("battlePreArmys", this.battlePreArmys)
	pawns := map[string]g.Pawn{}
	list := this.GetArmysClone()
	for _, army := range list {
		if this.world.CheckArmyMarchingByUID(army.Uid) {
			continue
		}
		army.ReadyBattle() // 标记为战斗状态
		army.Pawns.RLock()
		for _, pawn := range army.Pawns.List {
			pawn.ChangeState(constant.AS_FIGHT)
			pawns[pawn.Uid] = pawn
		}
		army.Pawns.RUnlock()
	}
	this.fspModel = fsp.NewFSPModel().FromDB(this, data.(map[string]interface{}), pawns)
	this.InitFspChan()
	return true
}

// 获取战斗经过的时间
func (this *Area) GetBattleTime() int32 {
	return int32(time.Now().UnixMilli() - this.BattleTime)
}

// 检测是否战斗时间到了
func (this *Area) CheckBattleEndTime() bool {
	return this.BattleTime > 0 && this.GetBattleTime() > constant.BATTLE_MAX_TIME
}

// 战斗开始
func (this *Area) BattleBegin(attacker string, enterPoint *ut.Vec2) {
	this.UpdateMaxHP()
	// 将士兵放到一个队列
	roles := []map[string]interface{}{}
	pawns := []g.Pawn{}
	playerEquipAttrMap := map[string]map[string][][]int32{}
	playerHeroAttrMap := map[string]map[int32][][]int32{}
	policyListMap := map[string][][]int32{}
	var attackerArmyNum int32 = 0
	var defenderArmyNum int32 = 0
	// 获取联盟uid
	alliUidMap := map[string]string{}
	alliUidMap[this.Owner] = this.world.GetPlayerAlliUid(this.Owner)
	alliUidMap[attacker] = this.world.GetPlayerAlliUid(attacker)
	armyList := this.GetArmysClone()
	for _, army := range armyList {
		if this.world.CheckArmyMarchingByUID(army.Uid) {
			continue
		}
		army.ReadyBattle()                                   // 标记为战斗状态
		this.AddArmyToBattlePreRecord(army, this.BattleTime) // 添加记录
		var as int32
		if army.Owner == attacker || this.world.CheckIsOneAlliance(army.Owner, attacker) {
			as = 100000 // 如果是攻击方 就要先手
			attackerArmyNum++
		} else {
			defenderArmyNum++
		}
		equipAttrMap := this.getPlayerEquipAttrMap(army.Owner, playerEquipAttrMap)
		heroAttrMap := this.getPlayerHeroAttrMap(army.Owner, playerHeroAttrMap)
		policyList := this.getPlayerFightPolicys(army.Owner, policyListMap)
		list := army.GetPawnsClone()
		for _, pawn := range list {
			pawn.UpdateRodeleroCadetLv()
			pawn.UpdateAttrBattleBeginAndEnd(equipAttrMap, heroAttrMap) // 刷新装备信息和英雄信息
			pawn.CleanAllBuffs()
			pawn.ChangeState(constant.AS_FIGHT)
			pawn.AddPolicyBuffs(policyList)
			if pawn.Owner == "" { // 如果是野怪 调整攻击顺序 最近的优先
				pawn.attackIndex = pawn.AttackSpeed*100 + (99 - helper.GetPointToPointDis(pawn.Point, enterPoint))
			} else {
				pawn.attackIndex = pawn.AttackSpeed + as
			}
			pawns = append(pawns, pawn)
			roles = append(roles, map[string]interface{}{
				"role_id":     pawn.Id,
				"role_lv":     pawn.Lv,
				"role_count":  1,
				"equip_id":    pawn.Equip.ID,
				"equip_count": 1,
				"army_name":   army.Name,
				"army_uid":    army.Uid,
			})
		}
		if _, ok := alliUidMap[army.Owner]; !ok {
			alliUidMap[army.Owner] = this.world.GetPlayerAlliUid(army.Owner)
		}
	}
	// 上报
	var field_type int32
	if this.Owner == "" {
		field_type = this.world.GetLandType(this.index)
	}
	this.world.TaTrack(attacker, 0, "ta_attackFieldStart", map[string]interface{}{
		"roles": roles,
		"field": map[string]interface{}{
			"field_lv":    this.world.GetLandLv(this.index),
			"field_dis":   this.world.GetToMapCellDis(this.world.GetPlayerMainIndex(attacker), this.index),
			"field_type":  field_type,
			"field_index": this.index,
		},
	})
	// 分配阵营
	campMap := map[int32]string{}
	campMap[1] = this.Owner // 当前区域阵营
	campMap[2] = attacker   // 攻击方阵营
	if this.GetWorld().CheckIsOneAlliance(this.Owner, attacker) {
		return
	}
	// 血战到底 的冬季建筑受到的伤害增加
	environmentBuffs := []*g.BuffObj{}
	if wld := this.GetWorld(); wld.GetWinCondType() == 3 && wld.GetSeason().GetType() == 3 {
		environmentBuffs = append(environmentBuffs, new(g.BuffObj).Init(bufftype.ENVIRONMENT_BUILD_DMG, "", 1, 0))
	}
	// 初始化帧同步
	this.FspMutex.Lock()
	this.fspModel = fsp.NewFSPModel().Init(this, fsp.FSPParam{
		Attacker:         attacker,
		Pawns:            pawns,
		CampMap:          campMap,
		FspMul:           1,
		AlliUidMap:       alliUidMap,
		AttackerArmyNum:  attackerArmyNum,
		DefenderArmyNum:  defenderArmyNum,
		EnvironmentBuffs: environmentBuffs,
	})
	this.InitFspChan()
	this.fspModel.Run()
	this.FspMutex.Unlock()
	log.Info("BattleBegin index:%v, owner:%v, attacker:%v", this.index, this.Owner, attacker)
	// 离线通知
	if this.Owner != "" {
		this.world.OfflineNotify(this.Owner, constant.OFFLINE_MSG_TYPE_BE_ATTACK)
	}
}

// 战斗结束
func (this *Area) BattleEnd(fighters []string) {
	if this.fspModel == nil {
		return
	}
	this.BattleSettleMutex.Lock()
	defer this.BattleSettleMutex.Unlock()
	fighter := ""
	if len(fighters) > 0 {
		fighter = fighters[0]
	}
	this.world.TagBattleSettling()
	owner := this.Owner
	this.FspMutex.Lock()
	this.fspModel.AddRecordData(&g.BattleRecordData{ // 添加结束记录
		Type:              constant.FSP_NOTIFY_TYPE_END,
		CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
	})
	attacker := this.fspModel.GetAttacker()
	datas := this.fspModel.GetRecordDatas()
	battleInfoMap := this.fspModel.GetFighterBattleInfoMap()
	battlePlayerUids := this.fspModel.GetBattlePlayerUids()
	deadInfoMap := this.fspModel.GetFighterDeadInfoMap()
	alliUidMap := this.fspModel.GetAlliUidMap()
	atkReinforceCount := this.fspModel.GetAttackerAcc()
	defReinforceCount := this.fspModel.GetDefenderAcc()
	lostTreasureMap := this.fspModel.GetLostTreasureResMap()
	this.fspModel.Stop()
	this.fspModel = nil
	this.FspMutex.Unlock()
	this.ClearFspChan()
	// 获取这个区域的奖励
	playerEquipAttrMap := map[string]map[string][][]int32{}
	playerHeroAttrMap := map[string]map[int32][][]int32{}
	armyMap, otherPlayerCount := map[string][]*AreaArmy{}, int32(0)
	isTaskTrigger := false
	list := this.GetArmysClone()
	for i := len(list) - 1; i >= 0; i-- {
		army := list[i]
		if army.Owner == "" { // 表示野怪 需要删掉
			list = append(list[:i], list[i+1:]...)
			continue
		} else if this.world.CheckArmyMarchingByUID(army.Uid) {
			continue
		}
		army.EndBattle() // 标记结束战斗
		// 只有是胜利者才有宝箱
		if this.world.CheckIsOneAlliance(fighter, army.Owner) {
			if arr := armyMap[army.Owner]; arr != nil {
				armyMap[army.Owner] = append(arr, army)
			} else {
				armyMap[army.Owner] = []*AreaArmy{army}
				if fighter != army.Owner {
					otherPlayerCount += 1
				}
			}
		}
		equipAttrMap := this.getPlayerEquipAttrMap(army.Owner, playerEquipAttrMap)
		heroAttrMap := this.getPlayerHeroAttrMap(army.Owner, playerHeroAttrMap)
		pawnList := army.GetPawnsClone()
		for _, pawn := range pawnList {
			pawn.ChangeState(constant.AS_NONE)
			pawn.UpdateRodeleroCadetLv()
			// 刷新装备信息
			pawn.UpdateAttrBattleBeginAndEnd(equipAttrMap, heroAttrMap)
			// 记录战斗数据
			pawn.AddBattleRecordData(battleInfoMap[pawn.Uid])
			// 检测是否触发任务进度
			if !isTaskTrigger && pawn.curHp == 1 {
				isTaskTrigger = true
				this.world.TriggerTask(army.Owner, tctype.ONE_BLOOD_SURVIVE, 1, 0)
			}
		}
	}
	this.Armys.Lock()
	this.Armys.List = list
	this.Armys.Unlock()
	// 开始分配宝箱
	treasuresMap, fullLostCount := this.SendTreasures(fighter, owner, armyMap, otherPlayerCount, true)
	this.world.AreaBattleEnd(this.index, attacker, fighters, atkReinforceCount, defReinforceCount, datas, battleInfoMap, battlePlayerUids, treasuresMap, deadInfoMap, alliUidMap, lostTreasureMap, fullLostCount)
	this.world.TagBattleSettleFinish()
	log.Info("BattleEnd index:%v, owner:%v, attacker:%v",
		this.index, owner, fighter,
	)
}

// 宝箱分配
func (this *Area) SendTreasures(fighter, owner string, armyMap map[string][]*AreaArmy, otherPlayerCount int32, isBattle bool) (treasuresMap map[string][]*g.TreasureInfo, fullLostCount int32) {
	var attackTreasureCount int32
	var printInfoMap map[string][]string
	treasureIds, treasureCounts, otherTreasureId, isNotStamina := this.GetBattleTreasureCount(fighter, isBattle)
	if !isNotStamina {
		treasuresMap = map[string][]*g.TreasureInfo{}
		if len(armyMap) > 0 && (treasureIds != nil || otherTreasureId > 0) {
			// otherPlayerRatio := constant.TREASURE_ALLOT_RATIO[otherPlayerCount] //其他玩家的分配比例
			if len(treasureCounts) < 2 || len(treasureIds) == 0 {
				treasureCounts = []int32{0, 0}
			}
			attackTreasureCount = ut.RandomInt32(treasureCounts[0], treasureCounts[1]) // 先随机攻占者的数量
			if extraTondenTreasure := this.world.GetPlayerPolicyEffectIntByUid(fighter, effect.CELL_TONDEN_TREASURE); extraTondenTreasure > 0 {
				// 额外屯田宝箱政策
				attackTreasureCount += extraTondenTreasure
			}
			for uid, armys := range armyMap {
				rcount, treasures := int32(0), []*g.TreasureInfo{}
				var treasureCount, otherTreasureCount int32
				if uid == fighter {
					treasureCount = attackTreasureCount
					otherTreasureCount = 1 // 暂时只有攻击者有
				} else if owner == "" { // 如果是野地 盟友根据攻占者的数量分
					// treasureCount = ut.RoundInt32(float64(attackTreasureCount) * otherPlayerRatio)
					continue
				} else { // 如果是玩家主城 那么每个人都随机
					treasureCount = ut.RandomInt32(treasureCounts[0], treasureCounts[1])
				}
				// 添加特殊资源
				if otherTreasureId > 0 && otherTreasureCount > 0 {
					for _, army := range armys {
						list := army.GetPawnsClone()
						for _, pawn := range list {
							sc, arr := pawn.AddTreasure(otherTreasureCount, otherTreasureId)
							rcount += sc
							treasures = append(treasures, arr...)
							otherTreasureCount -= sc
							if otherTreasureCount <= 0 {
								break
							}
						}
						if otherTreasureCount <= 0 {
							break
						}
					}
					// 记录特殊宝箱遗失数量
					if uid == fighter {
						fullLostCount += otherTreasureCount
					}
				}
				if treasureCount > 0 {
					// 随机分配
					var cnt, min int32 = int32(len(armys)), 0
					length := cnt
					sum := treasureCount - min*length
					counts := make([]int32, cnt)
					var i int32
					for l := cnt - 1; i < l; i++ {
						val := ut.RandomInt32(0, sum/length*2)
						sum -= val
						length -= 1
						counts[i] = val + min
					}
					counts[i] = sum + min
					var count int32
					for i, army := range armys {
						count += counts[i]
						list := army.GetPawnsClone()
						if count <= 0 || len(list) == 0 {
							continue
						}
						for _, pawn := range list {
							if pawn.IsBagFull() {
								continue
							}
							data := treasureIds[ut.RandomIndexByDataWeight(treasureIds)].(TreasureWeightInfo)
							sc, arr := pawn.AddTreasure(count, data.id)
							rcount += sc
							treasures = append(treasures, arr...)
							count -= sc
							if count <= 0 {
								break
							}
						}
					}
					// 随机分配后仍有宝箱未分配 尝试装入空位
					if count > 0 {
						for _, army := range armys {
							if count <= 0 {
								break
							}
							list := army.GetPawnsClone()
							if len(list) == 0 {
								continue
							}
							for _, pawn := range list {
								if pawn.IsBagFull() {
									continue
								}
								data := treasureIds[ut.RandomIndexByDataWeight(treasureIds)].(TreasureWeightInfo)
								sc, arr := pawn.AddTreasure(count, data.id)
								rcount += sc
								treasures = append(treasures, arr...)
								count -= sc
								if count <= 0 {
									break
								}
							}
						}
						// 记录普通宝箱遗失数量
						if uid == fighter {
							fullLostCount += count
						}
					}
				}
				if rcount > 0 {
					treasuresMap[uid] = treasures
				}
			}
			printInfoMap = map[string][]string{}
			// 发送有新宝箱通知
			for uid := range treasuresMap {
				this.world.SendPlayerHasTreasure(uid)
				printInfoMap[uid] = array.Map(treasuresMap[uid], func(m *g.TreasureInfo, _ int) string { return ut.String(m.ID) })
			}
		}
	}
	log.Info("SendTreasures index:%v, owner:%v, attacker:%v, isBattle: %v Treasure ids:%v, counts:%v, otherId:%v, stamina:%v, attackTreasureCount:%v, TreasureMap:%v",
		this.index, owner, fighter, isBattle,
		array.Map(treasureIds, func(m ut.DataWeight, _ int) int32 { return m.(TreasureWeightInfo).id }), treasureCounts, otherTreasureId, !isNotStamina,
		attackTreasureCount, printInfoMap,
	)
	return
}

// 获取玩家装备map
func (this *Area) getPlayerEquipAttrMap(uid string, outEquipMap map[string]map[string][][]int32) map[string][][]int32 {
	if uid == "" {
		return nil
	}
	obj := outEquipMap[uid]
	if obj == nil {
		obj = map[string][][]int32{}
		equips := this.world.GetPlayerEquips(uid)
		for _, equip := range equips {
			obj[equip.UID] = equip.Attrs
		}
		outEquipMap[uid] = obj
	}
	return obj
}

// 获取玩家英雄map
func (this *Area) getPlayerHeroAttrMap(uid string, outHeroMap map[string]map[int32][][]int32) map[int32][][]int32 {
	if uid == "" {
		return nil
	}
	obj := outHeroMap[uid]
	if obj == nil {
		obj = map[int32][][]int32{}
		slots := this.world.GetPlayerHeroSlots(uid)
		for _, slot := range slots {
			if hero := slot.Hero; hero != nil {
				obj[hero.ID] = hero.Attrs
			}
		}
		outHeroMap[uid] = obj
	}
	return obj
}

// 获取玩家战斗政策
func (this *Area) getPlayerFightPolicys(uid string, outList map[string][][]int32) [][]int32 {
	if uid == "" {
		return nil
	}
	list := outList[uid]
	if list == nil {
		list = this.world.GetPlayerFightPolicyBuffs(uid, this.Owner)
		outList[uid] = list
	}
	return list
}

// 获取战斗宝箱数量
func (this *Area) GetBattleTreasureCount(fighter string, isBattle bool) ([]ut.DataWeight, []int32, int32, bool) {
	if fighter == "" {
		return nil, nil, 0, false
	} else if this.Owner == fighter && isBattle {
		return nil, nil, 0, false
	} else if this.Owner != "" && isBattle { // 如果是玩家 就固定奖励
		if index := this.world.GetPlayerMainIndex(this.Owner); this.index == index {
			return []ut.DataWeight{TreasureWeightInfo{id: 1001, weight: 100}}, []int32{5, 10}, 0, false // 主城 3-5个黄金宝箱
		}
		return nil, nil, 0, false
	} else if this.IsAncient() {
		return nil, nil, 0, false // 遗迹
	}
	landLv := this.world.GetLandLv(this.index)
	index := this.world.GetPlayerMainIndex(fighter)
	level := config.GetLandAttrLvByDis(this.world.GetToMapCellDis(this.index, index), landLv)
	json := config.GetJsonData("landAttr", landLv*1000+level)
	if json == nil {
		return nil, nil, 0, false
	} else if this.world.ChangePlayerStaminaAndAddScore(fighter, landLv, ut.Int32(json["uiLv"]), -ut.Int32(json["need_stamina"]), isBattle) == -1 { // 扣除体力
		return nil, nil, 0, true
	}
	landType := this.world.GetLandType(this.index)
	treasureIdPrefix := landType * 100
	weights := ut.StringToInt32s(ut.String(json["treasures_lv"]), ",")
	arr := []ut.DataWeight{}
	for id, w := range weights {
		if w > 0 {
			arr = append(arr, TreasureWeightInfo{id: treasureIdPrefix + (int32(id) + 1), weight: int(w)})
		}
	}
	counts := ut.StringToInt32s(ut.String(json["treasures_count"]), ",")
	otherTreasureId := int32(0)
	if isBattle {
		extraOdds := int32(this.world.GetAncientEffectFloatByPlayer(fighter, effect.OTHER_RES_ODDS))
		// 其他资源 材料
		material_odds := ut.Int32(json["material_odds"])
		if material_odds > 0 && ut.ChanceInt32(material_odds+extraOdds) {
			otherTreasureId = treasureIdPrefix + 11
		}
		// 其他资源 特殊资源
		other_rewards_odds := ut.Int32(json["other_rewards_odds"])
		if other_rewards_odds > 0 && ut.ChanceInt32(other_rewards_odds+extraOdds) {
			otherTreasureId = treasureIdPrefix + 13
		}
		// 检测id是否有
		if otherTreasureId > 0 && config.GetJsonData("treasure", otherTreasureId) == nil {
			log.Error("GetBattleTreasureCount otherTreasureId not exist! id: %v", otherTreasureId)
			otherTreasureId = 0
		}
	} else {
		// 屯田奖励按比例减少
		for i, v := range counts {
			counts[i] = ut.MaxInt32(ut.RoundInt32(float64(v)*constant.CELL_TONDEN_REWARD_PARAM), 1)
		}
	}
	return arr, counts, otherTreasureId, false
}

// 添加军队到战场
func (this *Area) AddArmyToBattle(army *AreaArmy) {
	if this.fspAddArmyChan != nil {
		this.fspAddArmyChan <- army
	}
}

// 添加士兵到战场
func (this *Area) AddPawnToBattle(pawn *AreaPawn) {
	if this.fspAddPawnChan != nil {
		this.fspAddPawnChan <- pawn
	}
}

// 添加军队到战场
func (this *Area) addArmyToBattle(army *AreaArmy) {
	if !this.IsBattle() || army.GetPawnCount() == 0 {
		return
	}
	this.battlePreMutex.RLock()
	isRepeat := array.Some(this.battlePreArmys, func(m *g.BattlePreArmyData) bool { return m.Uid == army.Uid })
	this.battlePreMutex.RUnlock()
	if isRepeat {
		// 军队重复添加
		log.Warning("addArmyToBattle repeat index: %v, auid: %v, owner: %v", this.index, army.Uid, army.Owner)
		return
	}
	// 添加累计增援次数
	if this.world.CheckIsOneAlliance(army.Owner, this.Owner) {
		this.fspModel.AddDefenderAcc(army.Uid)
	} else {
		this.fspModel.AddAttackerAcc(army.Uid)
	}
	currentFrameIndex := this.fspModel.GetCurrentFrameIndex()
	army.ReadyBattle()                                             // 标记为战斗状态
	this.AddArmyToBattlePreRecord(army, time.Now().UnixMilli()+10) // 添加记录
	// 将士兵放到一个队列
	pawns := []g.Pawn{}
	equipAttrMap := this.getPlayerEquipAttrMap(army.Owner, map[string]map[string][][]int32{})
	heroAttrMap := this.getPlayerHeroAttrMap(army.Owner, map[string]map[int32][][]int32{})
	policyList := this.getPlayerFightPolicys(army.Owner, map[string][][]int32{})
	army.Pawns.RLock()
	for _, pawn := range army.Pawns.List {
		pawn.UpdateRodeleroCadetLv()
		pawn.UpdateAttrBattleBeginAndEnd(equipAttrMap, heroAttrMap)
		pawn.CleanAllBuffs()
		pawn.ChangeState(constant.AS_FIGHT)
		pawn.AddPolicyBuffs(policyList)
		pawn.attackIndex = pawn.AttackSpeed
		pawns = append(pawns, pawn)
	}
	army.Pawns.RUnlock()
	fighters := this.fspModel.AddArmy(pawns, army.Owner)
	// 通知
	this.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
		Data: &pb.FrameInfo{
			Type:              int32(constant.FSP_NOTIFY_TYPE_ADD_ARMY),
			CurrentFrameIndex: currentFrameIndex,
			Army:              army.ToPb(this.ToArmyState(army)),
			Fighters:          fsp.FightersJsonToPb(fighters),
			RandSeed:          int32(this.fspModel.GetRandSeed()),
		},
	})
	// 添加记录
	this.fspModel.AddRecordData(&g.BattleRecordData{
		Type:              constant.FSP_NOTIFY_TYPE_ADD_ARMY,
		CurrentFrameIndex: currentFrameIndex,
		Army:              army.Strip2(this.ToArmyState(army)),
		Fighters:          fighters,
	})
	// log.Info("战斗中加入军队" + ut.Itoa(this.index) + ", owner=" + army.Owner + ", army=" + army.Uid)
	slg.Log(ut.Itoa(currentFrameIndex) + " AddArmy " + army.Name)
}

// 添加士兵到战场
func (this *Area) addPawnToBattle(pawn *AreaPawn) {
	if !this.IsBattle() {
		return
	}
	currentFrameIndex := this.fspModel.GetCurrentFrameIndex()
	pawn.CleanAllBuffs()
	pawn.ChangeState(constant.AS_FIGHT)
	pawn.attackIndex = pawn.AttackSpeed
	this.AddPawnToBattlePreRecord(pawn) // 添加记录
	fighters := this.fspModel.AddPawn(pawn, pawn.Owner)
	// 通知
	this.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
		Data: &pb.FrameInfo{
			Type:              int32(constant.FSP_NOTIFY_TYPE_ADD_PAWN),
			CurrentFrameIndex: currentFrameIndex,
			ArmyUid:           pawn.ArmyUid,
			Pawn:              pawn.ToPb(),
			Fighters:          fsp.FightersJsonToPb(fighters),
			RandSeed:          int32(this.fspModel.GetRandSeed()),
		},
	})
	this.fspModel.AddRecordData(&g.BattleRecordData{
		Type:              constant.FSP_NOTIFY_TYPE_ADD_PAWN,
		CurrentFrameIndex: currentFrameIndex,
		ArmyUid:           pawn.ArmyUid,
		Pawn:              pawn.Strip2(),
		Fighters:          fighters,
	}) // 添加记录
	// log.Info("战斗中加入士兵" + ut.Itoa(this.index) + ", owner=" + pawn.Owner + ", pawn=" + pawn.Uid)
	slg.Log(ut.Itoa(currentFrameIndex) + " AddPawn " + ut.Itoa(pawn.Id) + "[" + pawn.Uid + "]")
}

func (this *Area) AddArmyToBattlePreRecord(army *AreaArmy, now int64) {
	if army.Owner == "" {
		return
	}
	pawnList := army.GetPawnsClone()
	this.battlePreMutex.Lock()
	parmys := this.battlePreArmys
	if parmys == nil {
		parmys = []*g.BattlePreArmyData{}
	}
	this.battlePreArmys = append(parmys, &g.BattlePreArmyData{
		Uid:       army.Uid,
		Name:      army.Name,
		Owner:     army.Owner,
		PawnCount: int32(len(pawnList)),
		Pawns:     array.Map(pawnList, func(m *AreaPawn, _ int) *g.PawnStrip3 { return m.Strip3() }),
		BeginTime: now,
		EndTime:   0,
	})
	this.battlePreMutex.Unlock()
}

func (this *Area) AddPawnToBattlePreRecord(pawn *AreaPawn) {
	this.battlePreMutex.Lock()
	defer this.battlePreMutex.Unlock()
	if this.battlePreArmys == nil || pawn.ArmyUid == "" {
		return
	}
	uid := pawn.ArmyUid
	for _, m := range this.battlePreArmys {
		if m.Uid == uid {
			m.PawnCount++ // 这里直接加1
			m.Pawns = append(m.Pawns, pawn.Strip3())
			return
		}
	}
}

func (this *Area) RemoveArmyToBattlePreRecord(uid string) {
	if !this.IsBattle() {
		return
	}
	this.battlePreMutex.Lock()
	defer this.battlePreMutex.Unlock()
	if this.battlePreArmys == nil || uid == "" {
		return
	}
	for _, m := range this.battlePreArmys {
		if m.Uid == uid {
			m.EndTime = time.Now().UnixMilli() // 这里不是真的删除 而是记录结束时间
			return
		}
	}
}

// 删除军队从战场
func (this *Area) RemoveArmyByBattle(army *AreaArmy) {
	uid := army.Uid
	this.RemoveArmy(uid)
	this.RemoveArmyToBattlePreRecord(uid)
	this.fspModel.RemoveArmy(uid) // 删除战斗中的
	// 通知
	this.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
		Data: &pb.FrameInfo{
			Type:              int32(constant.FSP_NOTIFY_TYPE_REMOVE_ARMY),
			CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
			Uid:               uid,
			RandSeed:          int32(this.fspModel.GetRandSeed()),
		},
	})
	this.fspModel.AddRecordData(&g.BattleRecordData{
		Type:              constant.FSP_NOTIFY_TYPE_REMOVE_ARMY,
		CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
		Uid:               uid,
	}) // 添加记录
	log.Info("战斗中删除军队 ", uid)
}

// 遣返战斗中的军队
func (this *Area) RepatriateArmyByBattle(army *AreaArmy) {
	uid := army.Uid
	this.RemoveArmyToBattlePreRecord(uid)
	this.fspModel.RemoveArmy(uid) // 删除战斗中的
	// 更新军队状态
	army.EndBattle() // 标记结束战斗
	army.Pawns.RLock()
	for _, pawn := range army.Pawns.List {
		pawn.ChangeState(constant.AS_NONE)
	}
	army.Pawns.RUnlock()
	// 通知
	this.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
		Data: &pb.FrameInfo{
			Type:              int32(constant.FSP_NOTIFY_TYPE_REMOVE_ARMY),
			CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
			Uid:               uid,
			RandSeed:          int32(this.fspModel.GetRandSeed()),
		},
	})
	// 添加记录
	this.fspModel.AddRecordData(&g.BattleRecordData{
		Type:              constant.FSP_NOTIFY_TYPE_REMOVE_ARMY,
		CurrentFrameIndex: this.fspModel.GetCurrentFrameIndex(),
		Uid:               uid,
	})
	log.Info("战斗中遣返军队 index:%v, armyUid:%v, owner:%v", this.index, uid, army.Owner)
}

// 设置血量
func (this *Area) SetCurHP(val int32) {
	this.CurHp = val
}

// 设置最大血量
func (this *Area) SetMaxHP(val int32) {
	this.MaxHp = val
}

// 记录最后一次攻击的玩家
func (this *Area) RecordLastAttackMainPlayer() {
	if this.IsBattle() {
		this.fspModel.RecordLastAttackMainPlayer()
	}
}

// 同步帧信息
func (this *Area) NotifyCheckFrame(msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY) {
	this.world.NotifyAreaCheckFrame(this.index, msg)
}

// 检测是否有我方或盟友军队在战场
func (this *Area) CheckHaveArmyInBattleArea(uid string) bool {
	if !this.IsBattle() {
		return false
	}
	list := this.GetArmysClone()
	for _, army := range list {
		if this.world.CheckIsOneAlliance(uid, army.Owner) {
			return true
		}
	}
	return false
}

// 获取帧同步信息
func (this *Area) GetFspModel() *fsp.FSPModel {
	return this.fspModel
}

type FspAddPawns struct{}

func (this *Area) AddFightersToBattle() {
	if !this.IsBattle() {
		this.ClearFspChan()
		return
	}
	if len(this.fspAddArmyChan) > 0 {
		army := <-this.fspAddArmyChan
		this.addArmyToBattle(army)
	}
	if len(this.fspAddPawnChan) > 0 {
		pawn := <-this.fspAddPawnChan
		this.addPawnToBattle(pawn)
	}
}

func (this *Area) InitFspChan() {
	this.fspAddArmyChan = make(chan *AreaArmy, 10)
	this.fspAddPawnChan = make(chan *AreaPawn, 10)
}

func (this *Area) ClearFspChan() {
	close(this.fspAddArmyChan)
	this.fspAddArmyChan = nil
	close(this.fspAddPawnChan)
	this.fspAddPawnChan = nil
}

// 获取古城建筑
func (this *Area) GetAncientBuild() *AreaBuild {
	if len(this.Builds.List) == 0 || !this.IsAncient() {
		return nil
	}
	return this.GetAreaBuildById(this.CityId)
}
