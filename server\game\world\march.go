package world

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"time"
)

// 行军信息
type March struct {
	Uid      string `json:"uid"`
	Owner    string `json:"owner"`    //谁的行军
	ArmyUid  string `json:"army_uid"` //军队id
	armyName string //军队名字

	StartTime   int64 `json:"start_time"`   //开始时间
	RoleId      int32 `json:"role_id"`      //行军角色id
	ArmyIndex   int32 `json:"army_index"`   //军队所在的位置
	StartIndex  int32 `json:"start_index"`  //起点位置
	TargetIndex int32 `json:"target_index"` //目标位置
	NeedTime    int32 `json:"need_time"`    //行军时间
	AutoRevoke  bool  `json:"auto_revoke"`  //自动撤回 遣返
	ForceRevoke bool  `json:"force_revoke"` //强制撤离
	CellTonden  bool  `json:"cell_tonden"`  //是否屯田
}

func NewMarch(uid string) *March {
	return &March{Uid: uid, AutoRevoke: false}
}

func (this *March) ToPrintParams() []interface{} {
	return []interface{}{
		this.Owner,
		this.armyName,
		this.ArmyIndex,
		this.StartIndex,
		this.TargetIndex,
		this.AutoRevoke,
		this.NeedTime,
	}
}

func (this *March) Init(army *AreaArmy, sindex, target, needTime, roleId int32) *March {
	this.Owner = army.Owner
	this.ArmyUid = army.Uid
	this.ArmyIndex = army.AIndex
	this.armyName = army.Name
	this.RoleId = roleId
	this.StartIndex = sindex
	this.TargetIndex = target
	this.NeedTime = needTime
	this.StartTime = time.Now().UnixMilli()
	return this
}

func (this *March) Strip() map[string]interface{} {
	return map[string]interface{}{
		"uid":         this.Uid,
		"owner":       this.Owner,
		"armyUid":     this.ArmyUid,
		"armyName":    this.armyName,
		"roleId":      this.RoleId,
		"armyIndex":   this.ArmyIndex,
		"startIndex":  this.StartIndex,
		"targetIndex": this.TargetIndex,
		"needTime":    this.NeedTime,
		"autoRevoke":  this.AutoRevoke,
		"surplusTime": ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0),
	}
}

func (this *March) ToPb() *pb.MarchInfo {
	return &pb.MarchInfo{
		Uid:         this.Uid,
		Owner:       this.Owner,
		ArmyUid:     this.ArmyUid,
		ArmyName:    this.armyName,
		RoleId:      int32(this.RoleId),
		ArmyIndex:   int32(this.ArmyIndex),
		StartIndex:  int32(this.StartIndex),
		TargetIndex: int32(this.TargetIndex),
		NeedTime:    int32(this.NeedTime),
		AutoRevoke:  this.AutoRevoke,
		SurplusTime: int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0)),
		ForceRevoke: this.ForceRevoke,
		CellTonden:  this.CellTonden,
	}
}
