package fsp

type FSPPlayBackController struct {
	FSPBattleController
	fspPlayBack *FSPPlayBack
}

func (this *FSPPlayBackController) Init(area Area, data FSPParam) *FSPPlayBackController {
	this.FSPBattleController.Init(area, data)
	this.FSPBattleController.Run()
	return this
}

// 结束
func (this *FSPPlayBackController) End() bool {
	attacker := ""
	this.cmapMutex.RLock()
	if this.area.GetCurHP() <= 0 && this.campMap != nil {
		if attacker = this.campMap[this.lastAttackMainCamp]; attacker == "" {
			attacker = this.campMap[2] //2默认是攻击方
		}
	}
	this.cmapMutex.RUnlock()
	this.currentFighter = nil
	this.fspPlayBack.Stop()
	return true
}
