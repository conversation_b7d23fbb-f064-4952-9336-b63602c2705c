package slg

// 游戏结束信息
type GameOverInfo struct {
	Winners      [][]string `bson:"winners"`      //胜利的玩家列表 [[uid, nickname]]
	AncientInfo  []int32    `bson:"ancient_info"` //遗迹信息[id,lv]
	WinUID       string     `bson:"win_uid"`
	WinName      string     `bson:"win_name"`
	EndTime      int64      `bson:"end_time"`       //结束时间
	WinType      int32      `bson:"win_type"`       //1.玩家 2.联盟
	WinCellCount int32      `bson:"win_cell_count"` //胜利时的地块数量
}
