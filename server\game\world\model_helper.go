package world

import (
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"sort"
)

type TriggerTaskInfo struct {
	Uid      string
	CondType int32
	Count    int32
	Param    int32
}

// 检测是否可以攻击地块
func (this *Model) CheckCanOccupyCell(index int32, uid string) bool {
	size := ut.GetVec2FromPool(1, 1)
	if cell := this.GetCell(index); cell != nil {
		if this.CheckIsOneAlliance(cell.Owner, uid) {
			return false //如果是一个盟的肯定不可攻击啊
		}
		size.Set(cell.Size)
	}
	indexs := helper.GetPointsOuter(index, size, this.room.GetMapSize())
	for _, idx := range indexs {
		if cell := this.GetCell(idx); cell != nil && this.CheckIsOneAlliance(cell.Owner, uid) {
			return true
		} else if this.GetLandType(idx) == constant.LAND_TYPE_BRIDGE {
			// 相邻地块是桥梁 根据延伸的地块判断是否可攻占
			nextIndex := helper.GetIndexByNextAndDis(index, idx, 1, this.room.GetMapSize())
			bridgeMax := 10 // 桥梁最多占10格
			for bridgeMax > 0 && nextIndex >= 0 {
				bridgeMax--
				if cell := this.GetCell(nextIndex); cell != nil && this.CheckIsOneAlliance(cell.Owner, uid) {
					return true
				} else if this.GetLandType(nextIndex) == constant.LAND_TYPE_BRIDGE {
					// 延伸的下一格还是桥梁
					nextIndex = helper.GetIndexByNextAndDis(index, idx, 1, this.room.GetMapSize())
				} else {
					break
				}
			}
		}
	}
	return false
}

// 检测攻击玩家领地的最小距离
func (this *Model) CheckOccupyPlayerMinDis(index int32, tarea *Area) bool {
	return tarea.Owner == "" || this.FailPlrMap.Get(tarea.Owner) || this.GetToMapCellDis(index, tarea.GetIndex()) <= constant.OCCUPY_PLAYER_CELL_MIN_DIS
}

// 检测非攻占世界内是否可以进攻  非攻占时间内只要有盟军就可以进攻
func (this *Model) CheckNotOccupyTimeCanOccupy(area *Area, uid string) bool {
	return area.Owner == "" || this.FailPlrMap.Get(area.Owner) || helper.IsCanOccupyTime() || area.CheckHaveArmyInBattleArea(uid)
}

// 检测是否可以进攻无主遗迹
func (this *Model) CheckCanOccupyWildAncient(area *Area, uid string) bool {
	return !area.IsAncient() || area.Owner != "" || helper.IsCanOccupyAncientTime() || area.CheckHaveArmyInBattleArea(uid)
}

// 是否可以攻占
func (this *Model) IsCanOccupyArea(index int32, area *Area, uid string) bool {
	return !this.IsAvoidWarArea(area, uid) &&
		this.CheckCanOccupyCell(area.index, uid) &&
		this.CheckOccupyPlayerMinDis(index, area) &&
		this.CheckNotOccupyTimeCanOccupy(area, uid) &&
		!this.IsHasProtect(uid, index) &&
		this.CheckCanOccupyWildAncient(area, uid)
}

// 获取一个地块所拥有的所有地块
func (this *Model) GetCellOwnIndexs(index int32) []*ut.Vec2 {
	cell := this.GetCell(index)
	if cell == nil || cell.GetAcreage() <= 1 {
		return []*ut.Vec2{helper.IndexToPoint(index, this.room.GetMapSize())}
	} else if cell.DependIndex >= 0 {
		cell = this.GetCell(cell.DependIndex)
	}
	return cell.GetOwnPoints(this.room.GetMapSize())
}

// 获取地块于地块的距离
func (this *Model) GetToMapCellDis(sindex, tindex int32) int32 {
	// 重新获取最短的点
	sp, tp := helper.GetMinDisPoint(this.GetCellOwnIndexs(sindex), this.GetCellOwnIndexs(tindex))
	return helper.GetPointToPointDis(sp, tp)
}

// 获取方向
func (this *Model) GetAddArmyDir(sIndex, tIndex int32) int32 {
	sp, tp := helper.GetMinDisPoint(this.GetCellOwnIndexs(sIndex), this.GetCellOwnIndexs(tIndex))
	return helper.GetDirByPoint(sp, tp)
}

// 获取玩家没有满士兵的区域
func (this *Model) GetPlayerNotFullArmyArea(uid string, startIndex int32) *Area {
	if uid == "" {
		return nil
	}
	cells := this.GetPlayerOwnCells(uid)
	size := this.room.GetMapSize()
	// 先检测四周
	areas := []*Area{}
	point := helper.IndexToPoint(startIndex, size)
	areas = this.checkAddNotFullAreaNotAlli(helper.PointToIndex(point.X, point.Y+1, size), uid, areas) //上
	areas = this.checkAddNotFullAreaNotAlli(helper.PointToIndex(point.X, point.Y-1, size), uid, areas) //下
	areas = this.checkAddNotFullAreaNotAlli(helper.PointToIndex(point.X-1, point.Y, size), uid, areas) //左
	areas = this.checkAddNotFullAreaNotAlli(helper.PointToIndex(point.X+1, point.Y, size), uid, areas) //右
	// 筛选出 血量最大的 如果相等就看地块等级
	if len(areas) > 0 {
		sort.Slice(areas, func(i, j int) bool {
			a, b := areas[i], areas[j]
			if a.MaxHp == b.MaxHp {
				return this.GetLandLv(a.index) > this.GetLandLv(b.index)
			}
			return a.MaxHp > b.MaxHp
		})
		return areas[0]
	}
	// 四周没有那就开始找所有地块
	var ret *Area = nil
	hp, dis := int32(0), int32(1000000)
	for _, index := range cells {
		if area := this.GetArea(index); area != nil && !area.IsBattle() && area.CityId >= 0 && !this.IsAreaFullArmyAct(area, uid) {
			d := helper.GetIndexToIndexDis(startIndex, index, size)
			if d > dis {
				continue
			} else if d < dis || area.MaxHp > hp {
				dis = d
				hp = area.MaxHp
				ret = area
			}
		}
	}
	return ret
}

// 获取玩家和盟友没有满士兵的区域
func (this *Model) GetPlayerAndAlliNotFullArmyArea(uid string, startIndex int32) *Area {
	if uid == "" {
		return nil
	}
	size := this.room.GetMapSize()
	// 先检测四周
	point := helper.IndexToPoint(startIndex, size)
	for i := int32(1); i <= constant.CANCEL_ARMY_AREA_CHECK_DIS; i++ {
		areas := []*Area{}
		areas = this.checkAddNotFullArea(helper.PointToIndex(point.X, point.Y+i, size), uid, areas) //上
		areas = this.checkAddNotFullArea(helper.PointToIndex(point.X, point.Y-i, size), uid, areas) //下
		areas = this.checkAddNotFullArea(helper.PointToIndex(point.X-i, point.Y, size), uid, areas) //左
		areas = this.checkAddNotFullArea(helper.PointToIndex(point.X+i, point.Y, size), uid, areas) //右
		// 筛选出 血量最大的 如果相等就看地块等级
		if len(areas) > 0 {
			sort.Slice(areas, func(i, j int) bool {
				a, b := areas[i], areas[j]
				if a.MaxHp == b.MaxHp {
					return this.GetLandLv(a.index) > this.GetLandLv(b.index)
				}
				return a.MaxHp > b.MaxHp
			})
			return areas[0]
		}
	}
	// 四周没有那就开始找所有盟友地块
	cells := []int32{}
	ply := this.GetTempPlayer(uid)
	if ply != nil {
		cells = ply.OwnCells
		alli := this.GetAlliance(ply.AllianceUid)
		if alli != nil {
			alli.Members.RLock()
			for _, v := range alli.Members.List {
				if this.GetPlayerCapture(v.Uid) {
					continue
				}
				aliCells := this.GetPlayerOwnCells(v.Uid)
				cells = append(cells, aliCells...)
			}
			alli.Members.RUnlock()
		}
	}
	var ret *Area = nil
	hp, dis := int32(0), int32(1000000)
	for _, index := range cells {
		if area := this.GetArea(index); area != nil && !area.IsBattle() && area.CityId >= 0 && !this.IsAreaFullArmyAct(area, uid) {
			d := helper.GetIndexToIndexDis(startIndex, index, size)
			if d > dis {
				continue
			} else if d < dis || area.MaxHp > hp {
				dis = d
				hp = area.MaxHp
				ret = area
			}
		}
	}
	return ret
}

func (this *Model) checkAddNotFullArea(index int32, uid string, areas []*Area) []*Area {
	if area := this.GetArea(index); area != nil && this.CheckIsOneAlliance(area.Owner, uid) && !area.IsBattle() && area.CityId >= 0 && !this.IsAreaFullArmyAct(area, uid) {
		areas = append(areas, area)
	}
	return areas
}

// 检测是否可进入 只有自己地 不包含盟友
func (this *Model) checkAddNotFullAreaNotAlli(index int32, uid string, areas []*Area) []*Area {
	if area := this.GetArea(index); area != nil && area.Owner == uid && !area.IsBattle() && area.CityId >= 0 && !this.IsAreaFullArmyAct(area, uid) {
		areas = append(areas, area)
	}
	return areas
}

// 获取最近没有满士兵的要塞或主城
func (this *Model) GetPlayerNotFullArmyMinFortAndMain(uid string, startIndex int32) (ret *Area) {
	if uid == "" {
		return nil
	}
	minDis := int32(100000)
	cells := this.GetPlayerOwnCells(uid)
	for _, index := range cells {
		if area := this.GetArea(index); area != nil && !area.IsBattle() && area.IsRecoverPawnHP() && !this.IsAreaFullArmyAct(area, uid) {
			if dis := this.GetToMapCellDis(startIndex, index); dis < minDis {
				minDis = dis
				ret = area
			}
		}
	}
	return
}

// 获取相邻自动支援的要塞军队
func (this *Model) GetAreaAdjoinAutoFortArmys(area *Area) []*AreaArmy {
	armys := []*AreaArmy{}
	if area.Owner == "" {
		return armys
	}
	index := area.index
	cells := this.GetPlayerOwnCells(area.Owner)
	size := this.room.GetMapSize()
	// 开始搜索
	opened := []*ut.Vec2{helper.IndexToPoint(index, size)}
	closed := map[int32]bool{}
	openedLen := len(opened)
	for openedLen > 0 {
		point := opened[openedLen-1]
		opened = opened[:openedLen-1]
		opened, armys = this.checkAddFortArmys(index, helper.PointToIndex(point.X, point.Y+1, size), closed, cells, opened, armys, size) //上
		opened, armys = this.checkAddFortArmys(index, helper.PointToIndex(point.X, point.Y-1, size), closed, cells, opened, armys, size) //下
		opened, armys = this.checkAddFortArmys(index, helper.PointToIndex(point.X-1, point.Y, size), closed, cells, opened, armys, size) //左
		opened, armys = this.checkAddFortArmys(index, helper.PointToIndex(point.X+1, point.Y, size), closed, cells, opened, armys, size) //右
		openedLen = len(opened)
	}
	return armys
}
func (this *Model) checkAddFortArmys(startIndex, index int32, closed map[int32]bool, cells []int32, opened []*ut.Vec2, armys []*AreaArmy, size *ut.Vec2) ([]*ut.Vec2, []*AreaArmy) {
	if !closed[index] && (array.Has(cells, index) || this.CheckIsOneAllianceByIndex(startIndex, index)) {
		if a := this.GetArea(index); a != nil && a.CityId == constant.FORT_CITY_ID && !a.IsBattle() && this.IsFortAutoSupport(a.Owner, index) {
			opened = append(opened, helper.IndexToPoint(index, size))
			a.Armys.RLock()
			armys = append(armys, a.Armys.List...)
			a.Armys.RUnlock()
		}
	}
	closed[index] = true
	return opened, armys
}

// 主动刷新玩家产出 只针对在线玩家
func (this *Model) UpdatePlayerOutput(uid string) {
	this.room.PutPlayerUpdateOpSec(uid)
}

// 战斗触发任务
func (this *Model) TriggerTask(uid string, condType, count, param int32) {
	this.TriggerTaskChan <- &TriggerTaskInfo{Uid: uid, CondType: condType, Count: count, Param: param}
}

// 扣除玩家奖励点
func (this *Model) ChangePlayerStaminaAndAddScore(uid string, landLv, uiLv, val int32, isBattle bool) int32 {
	if plr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model); plr != nil {
		isFreedom := this.room.GetType() == 0 && this.room.GetSubType() == 1
		stamina := plr.Stamina
		if !isBattle {
			// 屯田先检查次数
			if plr.IsCellTondenLimit() {
				return -1
			}
			plr.AddCellTonden(1)
		}
		// 扣除奖励点
		if !isFreedom {
			if !isBattle {
				// 屯田消耗的奖励点乘倍率
				val *= constant.CELL_TONDEN_STAMINA_PARAM
			}
			stamina = plr.ChangeStamina(val) //不是自由区 才扣
		}
		if isBattle {
			// 添加攻占野地数量
			if score := plr.AddOccupyLandCount(landLv, 1); score > 0 {
				this.AddPlayerLandScore(plr.Uid, score)
			}
			// 记录难度
			difficultyCount := plr.OccupyLandDifficultyMap.Get(uiLv)
			plr.OccupyLandDifficultyMap.Set(uiLv, difficultyCount+1)
			if difficultyCount == 0 && uiLv == 4002 { //首次攻占地狱2
				this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
					Data_54: &pb.SysMsgInfo{
						Id:      int32(slg.OCCUPY_HELL2_NOTICE_ID),
						Parames: []string{plr.Nickname},
					},
				})
			}
		}
		// 保存数据库
		if !plr.IsOnline() {
			this.room.UpdatePlayerDB(plr)
		} else {
			if !isFreedom && stamina == -1 && !isBattle {
				// 屯田扣除奖励点失败 返还添加的屯田次数
				plr.AddCellTonden(-1)
				return stamina
			}
			if isBattle {
				// 战斗通知积分
				plr.PutNotifyQueue(constant.NQ_UPDATE_LAND_SCORE, &pb.OnUpdatePlayerInfoNotify{Data_57: plr.ToLandScorePb()})
			} else {
				// 屯田通知次数
				plr.PutNotifyQueue(constant.NQ_CELL_TONDEN_COUNT, &pb.OnUpdatePlayerInfoNotify{Data_81: plr.CellTondenCount})
			}
			if !isFreedom && stamina != -1 {
				plr.PutNotifyQueue(constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: plr.ToStaminaPb()})
			}
		}
		return stamina
	}
	return -1
}

// 玩家添加领地积分
func (this *Model) PlayerAddLandScore(uid string, landLv, uiLv int32) {
	if plr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model); plr != nil {
		// 添加攻占野地数量
		if score := plr.AddOccupyLandCount(landLv, 1); score > 0 {
			this.AddPlayerLandScore(plr.Uid, score)
		}
		// 记录难度
		difficultyCount := plr.OccupyLandDifficultyMap.Get(uiLv)
		plr.OccupyLandDifficultyMap.Set(uiLv, difficultyCount+1)
		if difficultyCount == 0 && uiLv == 4002 { //首次攻占地狱2
			this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
				Data_54: &pb.SysMsgInfo{
					Id:      int32(slg.OCCUPY_HELL2_NOTICE_ID),
					Parames: []string{plr.Nickname},
				},
			})
		}
		// 保存数据库
		if !plr.IsOnline() {
			this.room.UpdatePlayerDB(plr)
		} else {
			plr.PutNotifyQueue(constant.NQ_UPDATE_LAND_SCORE, &pb.OnUpdatePlayerInfoNotify{Data_57: plr.ToLandScorePb()})
		}
	}
}

// 处理战损补偿
func (this *Model) HandlePlayerCompensate(uid string, deadInfo []*g.PawmDeadRecord, treasures []*g.TreasureInfo) {
	if deadInfo == nil || len(deadInfo) == 0 {
		return
	}
	plr := this.room.GetOnlinePlayerOrDB(uid).(*player.Model)
	if plr == nil {
		return
	}
	lostResMap := map[int32]float64{}
	// 获取士兵损失资源
	// log.Info("阵亡的士兵以及对应资源:")
	for _, v := range deadInfo {
		resArr, _ := this.GetPawnResCost(v.Id, v.Lv)
		// str := ""
		for _, res := range resArr {
			lostResMap[res.Type] += float64(res.Count)
			// if str != "" {
			// 	str += "|"
			// }
			// str += ut.Itoa(res.Type) + "," + ut.Itoa(res.Id) + "," + ut.String(res.Count)
		}
		// log.Info("id: %v, lv: %v, res: %v", v.Id, v.Lv, str)
	}
	// 获取宝箱的期望资源
	// log.Info("获取宝箱的期望资源:")
	mul := this.GetPlayerTreasureAwardMul(plr.Uid)
	treasureResMap := map[int32]float64{}
	if treasures != nil {
		for _, treasure := range treasures {
			tResMap := treasure.GetResExpectation(mul)
			// str1, str2 := "", ""
			for tp, count := range tResMap {
				// if str1 != "" {
				// 	str1 += "|"
				// }
				// str1 += ut.Itoa(tp) + ",0," + ut.String(count)
				// 其他资源转换书
				if transBookParam := constant.COMPENSATE_BOOK_TRANS_MAP[tp]; transBookParam != 0 {
					tp = ctype.EXP_BOOK
					count *= transBookParam
				}
				treasureResMap[tp] += count
				// if str2 != "" {
				// 	str2 += "|"
				// }
				// str2 += ut.Itoa(tp) + ",0," + ut.String(count)
			}
			// log.Info("res1: %v, res2: %v", str1, str2)
		}
	}
	// 计算累计的补偿资源
	// log.Info("计算累计的补偿资源:")
	for tp, count := range lostResMap {
		tr := treasureResMap[tp] * constant.COMPENSATE_TREASURE_PARAM
		add := math.Max(count-tr, 0)
		new := plr.CompensateMap.Get(tp) + add
		plr.CompensateMap.Set(tp, new)
		// log.Info("tp: %v, count: %v, tRes: %v, add: %v, new: %v", tp, count, tr, add, new)
	}
	newUpRecruit := plr.CompensateMap.Get(ctype.UP_RECRUIT) + ut.Float64(len(deadInfo))
	plr.CompensateMap.Set(ctype.UP_RECRUIT, newUpRecruit)
	// 补偿三资达到阈值则进行补偿
	baseResCount := float64(0)
	// compensateStr := ""
	plr.CompensateMap.ForEach(func(v float64, tp int32) bool {
		if tp == ctype.CEREAL || tp == ctype.TIMBER || tp == ctype.STONE {
			baseResCount += v
		}
		// if compensateStr != "" {
		// 	compensateStr += "|"
		// }
		// compensateStr += ut.Itoa(tp) + ",0," + ut.String(v)
		return true
	})
	// log.Info("最终累计值: %v", compensateStr)
	threshold := ut.Min(constant.COMPENSATE_THRESHOLD_INIT+int(plr.CompensateCount)*constant.COMPENSATE_THRESHOLD_ADD, constant.COMPENSATE_THRESHOLD_MAX)
	if baseResCount < ut.Float64(threshold) {
		return
	}
	// 根据主城等级获取补偿系数
	itemParam := 1.0
	mainCityLv := this.GetPlayerMainBuildLv(plr.Uid)
	if (mainCityLv > 10 && mainCityLv <= 15) || (plr.CompensateCount > 10 && plr.CompensateCount <= 20) {
		// 主城等级大于10小于等于15或补偿次数大于10小于等于20
		itemParam = constant.COMPENSATE_MAIN_CITY_LV_PARAMS[0]
	} else if mainCityLv > 15 || plr.CompensateCount > 20 {
		// 主城等级大于15或补偿次数大于20
		itemParam = constant.COMPENSATE_MAIN_CITY_LV_PARAMS[1]
	}
	// 发送补偿
	items := []*g.TypeObj{}
	notifyMap := map[int32]int32{}
	plr.CompensateMap.ForEach(func(v float64, tp int32) bool {
		count := int32(ut.Ceil(v * itemParam))
		if count == 0 {
			return true
		}
		item := g.NewTypeObj(tp, 0, count)
		items = append(items, item)
		notifyMap[int32(tp)] = int32(count)
		return true
	})
	sort.Slice(items, func(i, j int) bool { return items[i].Type < items[j].Type })
	this.room.SendMailItemOne(constant.COMPENSATE_MAIL_ID, "", plr.Nickname, "-1", plr.Uid, items)
	// 补偿数据记录
	plr.CompensateMap.Clean()
	plr.CompensateCount++
	// 通知客户端
	if plr.IsOnline() {
		plr.PutNotifyQueue(constant.NQ_COMPENSATE, &pb.OnUpdatePlayerInfoNotify{Data_74: notifyMap})
	} else {
		this.room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
	}
	// 上报
	this.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_compensate", map[string]interface{}{
		"compensate_type":       2,
		"maincitylevel":         this.GetPlayerBuildLvById(plr.Uid, constant.MAIN_BUILD_ID),
		"landcount":             len(this.GetPlayerOwnCells(plr.Uid)),
		"pawncount":             len(this.GetPlayerPawnTrackInfo(plr.Uid)),
		"compensate_cereal":     notifyMap[ctype.CEREAL],
		"compensate_timber":     notifyMap[ctype.TIMBER],
		"compensate_stone":      notifyMap[ctype.STONE],
		"compensate_expbook":    notifyMap[ctype.EXP_BOOK],
		"compensate_up_recruit": notifyMap[ctype.UP_RECRUIT],
	})
}

// 任务触发处理
func (this *Model) UpdateTriggerTask() {
	if len(this.TriggerTaskChan) == 0 {
		return
	}
	userTaskMap := map[string][]*TriggerTaskInfo{}
	for len(this.TriggerTaskChan) > 0 {
		trigger := <-this.TriggerTaskChan
		if userTaskMap[trigger.Uid] == nil {
			userTaskMap[trigger.Uid] = []*TriggerTaskInfo{}
		}
		userTaskMap[trigger.Uid] = append(userTaskMap[trigger.Uid], trigger)
	}
	for uid, taskArr := range userTaskMap {
		// 触发每个玩家的任务
		ply, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model)
		if ply == nil {
			continue
		}
		rst := false
		for _, triggerTask := range taskArr {
			if ply.TriggerTask(triggerTask.CondType, triggerTask.Count, triggerTask.Param) {
				rst = true
			}
		}
		if rst {
			// 成功触发则保存
			if !ply.IsOnline() {
				this.room.UpdatePlayerDB(ply)
			}
		} else {
			this.room.CheckRemoveOfflinePlayer(ply)
		}
	}
}

// 通知大厅服添加战令积分
func (this *Model) AddBattlePassScore(uid string, score int32) {
	this.room.InvokeLobbyRpcNR(rds.GetUserLid(uid), slg.RPC_ADD_BATTLE_PASS_SCORE, uid, score)
}
