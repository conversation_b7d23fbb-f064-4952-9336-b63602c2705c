package game

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	"time"

	"slgsrv/server/game/fsp"
	"slgsrv/server/game/player"
	r "slgsrv/server/game/room"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"strconv"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Game) InitHDPlayer() {
	this.GetServer().RegisterGO("HD_Entry", this.entry)                                 //进入
	this.GetServer().RegisterGO("HD_CreatePlayer", this.createPlayer)                   //创建角色
	this.GetServer().RegisterGO("HD_ReCreateMainCity", this.reCreateMainCity)           //重新创建主城
	this.GetServer().RegisterGO("HD_GetPlayerArmys", this.getPlayerArmys)               //获取自己的军队
	this.GetServer().RegisterGO("HD_GetArmyRecords", this.getArmyRecords)               //获取军队记录
	this.GetServer().RegisterGO("HD_GetBattleRecord", this.getBattleRecord)             //获取战斗记录
	this.GetServer().RegisterGO("HD_GetBazaarRecords", this.getBazaarRecords)           //获取市场记录
	this.GetServer().RegisterGO("HD_QueryPlayer", this.queryPlayer)                     //查询玩家
	this.GetServer().RegisterGO("HD_ChangeConfigPawnEquip", this.changeConfigPawnEquip) //改变配置士兵的装备
	this.GetServer().RegisterGO("HD_OpenTreasure", this.openTreasure)                   //打开宝箱
	this.GetServer().RegisterGO("HD_OpenArmyTreasure", this.openArmyTreasure)           //打开军队的所有宝箱
	this.GetServer().RegisterGO("HD_ClaimTreasure", this.claimTreasure)                 //领取宝箱
	this.GetServer().RegisterGO("HD_ClaimArmyTreasure", this.claimArmyTreasure)         //领取军队的所有宝箱
	this.GetServer().RegisterGO("HD_GiveupGame", this.giveupGame)                       //放弃对局
	this.GetServer().RegisterGO("HD_SendMail", this.sendMail)                           //发送邮件
	this.GetServer().RegisterGO("HD_BattlePlayBack", this.battlePlayBack)               //战斗回放
	this.GetServer().RegisterGO("HD_Spectate", this.spectate)                           //观战
	this.GetServer().RegisterGO("HD_MapMarkPoint", this.mapMarkPoint)                   //个人标记
	this.GetServer().RegisterGO("HD_RemoveMapMark", this.removeMapMark)                 //删除个人标记
	this.GetServer().RegisterGO("HD_GetAntiCheatQuestion", this.getAntiCheatQuestion)   //获取防作弊题目
	this.GetServer().RegisterGO("HD_AntiCheatAnswer", this.antiCheatAnswer)             //防作弊答题
}

// 绑定
func bindPalyerAndSend(room *r.Model, plr *player.Model, session gate.Session, isReconnect bool) *pb.EnterGameRst {
	// log.Info("entry ip="+ session.GetIP())
	// 绑定会话用于后面通知
	plr.SetSession(session)
	// 记录sid
	// session.SetPush("sid", ut.Itoa(room.Id))
	// 记录登录天数
	now := time.Now().UnixMilli()
	if !plr.IsSpectate {
		t1 := ut.DateZeroTime(plr.LastLoginTime) //最后一次登录时间
		t2 := ut.DateZeroTime(now)               //当前登录时间
		day := (t2 - t1) / ut.TIME_DAY
		if day > 0 {
			plr.LoginDayCount += 1
			// 连续登录天数
			if day == 1 {
				plr.CLoginDayCount += 1
			} else {
				plr.CLoginDayCount = 1
			}
		}
		// 记录登录时间和登录次数
		plr.LastLoginTime = now
		plr.LoginCount += 1
		// 刷新时间 0表示在线
		room.GetWorld().UpdatePlayerOnlineState(plr.Uid, 0)
		// 这里兼容一下 如果主城id为0 就找一下
		if plr.CaptureInfo == nil && plr.MainCityIndex <= 0 {
			plr.MainCityIndex = room.GetWorld().FindPlayerMainIndex(plr.Uid)
			log.Info("plr.MainCityIndex <= 0, uid=" + plr.Uid + ", newMainCityIndex=" + ut.Itoa(plr.MainCityIndex))
		}
	}
	// 世界数据
	worldData := room.GetWorld().ToPb(isReconnect)
	worldData.RunTime = pb.Int64(room.GetRunTime())
	worldData.GameOver = room.ToGameOverInfoPb()
	worldData.WinCond = room.GetWinCondPb()
	worldData.Season = room.GetSeason().ToPb()
	return &pb.EnterGameRst{
		Sid:                pb.Int32(room.Id),
		MapSize:            room.GetMapSizePb(),
		Player:             plr.ToPb(),
		World:              worldData,
		CanBattleTime:      slg.ToCanOccupyTime[int32](),
		GuestCanCreateAlli: slg.GetGuestCreateAlli(),
		AlliPolicySlotConf: slg.GetAlliPolicySlotConf(),
	}
}

// 进入游戏
func (this *Game) entry(session gate.Session, msg *pb.GAME_HD_ENTRY_C2S) (ret []byte, err string) {
	// ut.TraceMemStats()
	sid, token, version, pos, distinctId := msg.GetSid(), msg.GetToken(), msg.GetVersion(), msg.GetPos(), msg.GetDistinctId()
	uid := session.GetUserID()
	log.Info("1 entry game uid: %v, sid: %v, token: %v, version: %v", uid, sid, token, version)
	if uid == "" {
		return nil, ecode.TOKEN_ERROR.String()
	}
	room := r.GetRoomById(sid)
	if room == nil || !room.IsOpen() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	if wld.GetPlayerIsGiveupGame(uid) { //是否放弃本次对局了 放弃了就不能再次进入
		return nil, ecode.ROOM_CLOSE.String()
	} else if room.IsClose() {
		return nil, ecode.ROOM_CLOSE.String()
	} else if err = room.CheckClientVersion(version); err != "" { //这里返回2个版本号
		ret, _ = pb.ProtoMarshal(&pb.GAME_HD_ENTRY_S2C{
			ClientVersion:    room.GetClientVersion(),
			ClientBigVersion: room.GetClientBigVersion(),
		})
		log.Info("2 entry game uid: %v, err: %v", uid, err)
		return
	}
	isReconnect := msg.GetIsReconnect()
	s2c := &pb.GAME_HD_ENTRY_S2C{}
	plr := room.LoginGetPlayerByDB(uid) //获取玩家
	lid := session.Get("lid")
	if plr == nil {
		// 排位服和新手服直接创建玩家
		plr, err = this.HandleCreatePlayer(lid, uid, distinctId, sid, pos, room)
	} else if session.IsGuest() {
		log.Info("2 entry game error, uid: %v, name: %v, isReconnect: %v", plr.GetUID(), plr.Nickname, isReconnect)
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if plr.IsSpectate {
		// 在该区服观战过且观战数据还在内存中
		plr, err = this.HandleCreatePlayer(lid, uid, distinctId, sid, pos, room)
	}
	// 没有位置了 将玩家的sid置0 重新获取对局
	if err == ecode.ROOM_FULL.String() || err == ecode.NOT_CITY_INDEX.String() {
		this.InvokeLobbyRpc(lid, slg.RPC_SET_USER_PLAYSID, uid, 0)
		log.Error("entry error, err: %v, uid: %v, sid: %v", err, uid, sid)
		return
	} else if plr == nil {
		log.Error("entry createPlr err uid: %v, err: %v", uid, err)
		return
	}
	s2c.Rst = bindPalyerAndSend(room, plr, session, isReconnect)
	// 世界数据pb序列化加读锁
	wld.WorldPbRLock()
	ret, _ = pb.ProtoMarshal(s2c)
	wld.WorldPbRUnLock()
	// 记录玩家的访客id
	wld.RecordPlayerDistinctId(plr.Uid, msg.GetDistinctId())
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_entry", map[string]interface{}{
		"is_reconnect":     isReconnect,
		"platform":         msg.GetPlatform(),
		"os":               msg.GetOs(),
		"open_server_time": room.GetCreateTime(),
		"maincitylevel":    wld.GetPlayerMainBuildLv(plr.Uid),
		"cereal":           plr.Cereal.Value,
		"timber":           plr.Timber.Value,
		"stone":            plr.Stone.Value,
		"expbook":          plr.ExpBook,
		"iron":             plr.Iron,
		"scroll":           plr.UpScroll,
		"nail":             plr.Fixator,
		"pawncount":        len(wld.GetPlayerPawnTrackInfo(plr.Uid)),
		"landcount":        len(wld.GetPlayerOwnCells(plr.Uid)),
	})
	log.Info("2 entry game uid: %v, name: %v, isReconnect: %v", plr.GetUID(), plr.Nickname, isReconnect)
	// ut.TraceMemStats()
	return
}

// 创建玩家
func (this *Game) createPlayer(session gate.Session, msg *pb.GAME_HD_CREATEPLAYER_C2S) (ret []byte, err string) {
	sid, pos := msg.GetSid(), msg.GetPos()
	uid := session.GetUserID()
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	room := r.GetRoomById(sid)
	if room == nil || !room.IsOpen() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	lid := session.Get("lid")
	info, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(lid, slg.RPC_GET_USER_BY_CREATE_PLAYER, uid))
	if info == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, nickname, headIcon, personalDesc, title, pawnSkins, regTime, os, osVer, platform, fcmToken, lang, offlineNotifyOpt, mainCitySkin, treasureLostCount, antiCheat := ut.String(info["uid"]), ut.String(info["nickname"]), ut.String(info["headIcon"]),
		ut.String(info["personalDesc"]), ut.Int32(info["title"]), ut.Int32Array(info["pawnSkins"]), ut.Int64(info["registerTime"]), ut.String(info["deviceOS"]), ut.String(info["deviceOSVersion"]), ut.String(info["platform"]), ut.String(info["FCMToken"]),
		ut.String(info["lang"]), ut.Int32Array(info["offlineNotifyOpt"]), ut.Int32(info["mainCitySkin"]), ut.Int32(info["treasureLostCount"]), ut.Int32Array(info["antiCheat"])
	plr, e := room.CreatePlayer(uid, nickname, headIcon, personalDesc, os, osVer, platform, regTime, title, pawnSkins, pos, lang, fcmToken, offlineNotifyOpt, msg.GetDistinctId(), true, mainCitySkin, treasureLostCount, antiCheat)
	s2c := &pb.GAME_HD_CREATEPLAYER_S2C{}
	if e == "" {
	} else if e == ecode.NOT_CITY_INDEX.String() { //这里表示没有位置了 从新将位置发下去
		s2c.CanCreateAreaCount = room.GetCanCreateAreaCount()
		ret, _ = pb.ProtoMarshal(s2c)
		return
	} else {
		return nil, e
	}
	// 设置用户playSid
	_, err = this.InvokeLobbyRpc(lid, slg.RPC_SET_USER_PLAYSID, uid, sid)
	if err != "" {
		return nil, err
	}
	// 是否之前被回收 发送邮件
	recycleInfo := room.RemoveDeletePlayerInfo(uid)
	if recycleInfo != nil && recycleInfo.CellCount > 4 {
		resCount := ut.If(recycleInfo.CellCount > 5, int32(1000), 500)
		room.SendMailItemOne(100007, "", "", "-1", uid, []*g.TypeObj{
			{Type: ctype.CEREAL, Count: resCount},
			{Type: ctype.TIMBER, Count: resCount},
			{Type: ctype.STONE, Count: resCount},
		})
	}
	wld := room.GetWorld()
	s2c.Rst = bindPalyerAndSend(room, plr, session, false)
	// 世界数据pb序列化加读锁
	wld.WorldPbRLock()
	ret, _ = pb.ProtoMarshal(s2c)
	wld.WorldPbRUnLock()
	// 记录玩家的访客id
	wld.RecordPlayerDistinctId(plr.Uid, msg.GetDistinctId())
	return
}

// 重新创建主城
func (this *Game) reCreateMainCity(session gate.Session, msg *pb.GAME_HD_RECREATEMAINCITY_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsSpectate {
		return nil, ecode.NO_RECREATE_MAIN_CITY.String()
	} else if cells := wld.GetPlayerOwnCells(plr.Uid); len(cells) > 0 {
		return nil, ecode.NO_RECREATE_MAIN_CITY.String()
	}
	index := room.ReRandomMainCityIndex()
	if index == -1 {
		return nil, ecode.NOT_CITY_INDEX.String()
	}
	//
	createTime := time.Now().UnixMilli()
	registerTime, platform, mainCitySkin := createTime, "", int32(0)
	if info, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_GET_ONLINE_USER_INFO, plr.Uid)); info != nil {
		registerTime = ut.Int64(info["registerTime"])
		platform = ut.String(info["platform"])
		mainCitySkin = ut.Int32(info["mainCitySkin"])
	}
	// 记录玩家的主城地块
	plr.MainCityIndex = index
	plr.ReCreateMainCityCount += 1
	// 改变玩家的主城位置
	wld.ChangePlayerMainIndex(plr.Uid, index)
	// 默认这4个地方都被占领
	wld.CreateCellByMainCity(index, plr, msg.GetLang(), mainCitySkin)
	// 重新初始化一下
	plr.Reset()
	wld.SetPlayerCapture(plr.Uid, false)
	// 上报
	room.GetWorld().TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_createPlayer", map[string]interface{}{
		// "create_pos": index,
		"create_player_num": plr.ReCreateMainCityCount,
		"open_server_time":  room.GetCreateTime(),
		"platform":          platform,
	})
	room.GetWorld().TaUserSet(plr.Uid, plr.ReCreateMainCityCount, map[string]interface{}{
		"uid":                plr.Uid,
		"sid":                room.Id,
		"uid_register_time":  registerTime,
		"create_player_time": createTime,
		"create_player_num":  plr.ReCreateMainCityCount,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_RECREATEMAINCITY_S2C{PlayerInfo: plr.ToPb()})
}

// 获取自己的军队列表
func (this *Game) getPlayerArmys(session gate.Session, msg *pb.GAME_HD_GETPLAYERARMYS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETPLAYERARMYS_S2C{
		List: wld.GetPlayerArmysPb(plr.Uid, constant.GAT_NONE, -1),
	})
}

// 获取自己的军队记录列表
func (this *Game) getArmyRecords(session gate.Session, msg *pb.GAME_HD_GETARMYRECORDS_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	list := room.GetRecord().GetArmyRecords(plr.Uid, msg.GetIsBattle())
	return pb.ProtoMarshal(&pb.GAME_HD_GETARMYRECORDS_S2C{List: list})
}

// 获取战斗记录
func (this *Game) getBattleRecord(session gate.Session, msg *pb.GAME_HD_GETBATTLERECORD_C2S) (ret []byte, err string) {
	e, room, _, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETBATTLERECORD_S2C{Record: room.GetRecord().GetBattleRecord(msg.GetUid())})
}

// 获取市场记录
func (this *Game) getBazaarRecords(session gate.Session, msg *pb.GAME_HD_GETBAZAARRECORDS_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	list := room.GetRecord().GetBazaarRecords(plr.Uid)
	return pb.ProtoMarshal(&pb.GAME_HD_GETBAZAARRECORDS_S2C{List: list})
}

// 查询玩家
func (this *Game) queryPlayer(session gate.Session, msg *pb.GAME_HD_QUERYPLAYER_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if tplr := wld.QueryTempPlayer(plr.Uid, msg.GetName(), msg.GetIndex()); tplr != nil {
		return pb.ProtoMarshal(&pb.GAME_HD_QUERYPLAYER_S2C{Nickname: tplr.Nickname, Index: int32(tplr.MainCityIndex)})
	} else {
		err = ecode.PLAYER_NOT_EXIST.String()
	}
	return
}

// 改变配置士兵的装备
func (this *Game) changeConfigPawnEquip(session gate.Session, msg *pb.GAME_HD_CHANGECONFIGPAWNEQUIP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	id, equipId, skinId, attackSpeed := msg.GetId(), msg.GetEquipId(), msg.GetSkinId(), msg.GetAttackSpeed()
	// 装备
	if equipId == 0 {
	} else if equip := wld.GetPlayerEquip(plr.Uid, equipId); equip == nil || !equip.CheckExclusivePawn(id) {
		equipId = 0
	}
	// 皮肤
	if skinId != 0 && skinId/1000 != id {
		skinId = 0
	} else if _, err = this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHANGE_PAWN_SKIN, plr.Uid, skinId, id); err != "" {
		skinId = 0
	}
	// 出手速度
	if attackSpeed != 0 {
		attackSpeed = ut.ClampInt32(attackSpeed, 1, 9)
	}
	wld.UpdateConfigPawnInfo(plr.Uid, id, equipId, skinId, attackSpeed)
	return pb.ProtoMarshal(&pb.GAME_HD_CHANGECONFIGPAWNEQUIP_S2C{})
}

// 打开宝箱
func (this *Game) openTreasure(session gate.Session, msg *pb.GAME_HD_OPENTREASURE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	auid, puid, uid := msg.GetAuid(), msg.GetPuid(), msg.GetUid()
	area, army, pawn, treasure := wld.GetPlayerTreasureInfo(plr.Uid, auid, puid, uid)
	if area == nil || army == nil || pawn == nil || treasure == nil {
		return nil, ecode.TREASURE_NOT_EXIST.String()
	} else if pawn.Owner != plr.Uid {
		return nil, ecode.NOT_OPEN_OTHER_TREASURE.String()
	} else if len(treasure.Rewards) > 0 {
		return nil, ecode.TREASURE_YET_OPEN.String() //宝箱已经打开
	}
	// 打开
	treasure.RandomRewards(wld.GetPlayerTreasureAwardMul(plr.Uid))
	wld.TagUpdateDBByIndex(area.GetIndex())

	pbTreasures := pawn.ToTreasuresPb()
	// 通知
	wld.NotifyAreaUpdateInfo(area.GetIndex(), constant.NQ_UPDATE_PAWN_TREASURE, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_39: &pb.AreaPawnInfo{
			ArmyUid:   army.Uid,
			Uid:       pawn.Uid,
			Treasures: pbTreasures,
		},
	})
	return pb.ProtoMarshal(&pb.GAME_HD_OPENTREASURE_S2C{
		Rewards:   treasure.ToRewardsPb(),
		ArmyUid:   army.Uid,
		Uid:       pawn.Uid,
		Treasures: pbTreasures,
	})
}

// 打开军队的所有宝箱
func (this *Game) openArmyTreasure(session gate.Session, msg *pb.GAME_HD_OPENARMYTREASURE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, auid := msg.GetIndex(), msg.GetAuid()
	area, army := wld.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return nil, ecode.TREASURE_NOT_EXIST.String()
	} else if army.Owner != plr.Uid {
		return nil, ecode.NOT_OPEN_OTHER_TREASURE.String()
	}
	mul := wld.GetPlayerTreasureAwardMul(plr.Uid)
	hasTreasure := false
	pawns := []*pb.PawnTreasureInfo{}
	list := army.GetPawnsClone()
	for _, m := range list {
		if len(m.Treasures) > 0 {
			hasTreasure = true
			hasReward := false
			for _, t := range m.Treasures {
				if len(t.Rewards) == 0 {
					hasReward = true
					t.RandomRewards(mul)
				}
			}
			if hasReward {
				pawns = append(pawns, &pb.PawnTreasureInfo{Uid: m.Uid, Treasures: m.ToTreasuresPb()})
			}
		}
	}
	if !hasTreasure {
		return nil, ecode.TREASURE_NOT_EXIST.String()
	} else if len(pawns) == 0 {
		return nil, ecode.TREASURE_YET_OPEN.String() //宝箱已经打开
	}
	wld.TagUpdateDBByIndex(area.GetIndex())
	// 通知
	wld.NotifyAreaUpdateInfo(area.GetIndex(), constant.NQ_UPDATE_ARMY_TREASURES, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_63: &pb.ArmyTreasuresInfo{
			ArmyUid:       army.Uid,
			PawnTreasures: pawns,
		},
	})
	return pb.ProtoMarshal(&pb.GAME_HD_OPENARMYTREASURE_S2C{
		ArmyUid:       army.Uid,
		PawnTreasures: pawns,
	})
}

// 领取宝箱
func (this *Game) claimTreasure(session gate.Session, msg *pb.GAME_HD_CLAIMTREASURE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	auid, puid, uid := msg.GetAuid(), msg.GetPuid(), msg.GetUid()
	area, army, pawn, treasure := wld.GetPlayerTreasureInfo(plr.Uid, auid, puid, uid)
	if area == nil || army == nil || pawn == nil || treasure == nil {
		return nil, ecode.TREASURE_NOT_EXIST.String()
	} else if pawn.Owner != plr.Uid {
		return nil, ecode.NOT_OPEN_OTHER_TREASURE.String()
	} else if len(treasure.Rewards) == 0 {
		return nil, ecode.TREASURE_NOT_OPEN.String() //宝箱还未打开
	}
	// 发放奖励
	plr.ChangeCostByTypeObjs(treasure.Rewards, 1)
	// 删除宝箱
	pawn.RemoveTreasureByUID(uid)
	wld.TagUpdateDBByIndex(area.GetIndex())
	// 通知是否有新的宝箱
	wld.SendPlayerHasTreasure(plr.Uid)

	pbTreasures := pawn.ToTreasuresPb()

	// 通知
	wld.NotifyAreaUpdateInfo(area.GetIndex(), constant.NQ_UPDATE_PAWN_TREASURE, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_39: &pb.AreaPawnInfo{
			ArmyUid:   army.Uid,
			Uid:       pawn.Uid,
			Treasures: pbTreasures,
		},
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMTREASURE_S2C{
		Rewards:   plr.ToItemByTypeObjsPb(treasure.Rewards),
		ArmyUid:   army.Uid,
		Uid:       pawn.Uid,
		Treasures: pbTreasures,
	})
}

// 领取军队宝箱
func (this *Game) claimArmyTreasure(session gate.Session, msg *pb.GAME_HD_CLAIMARMYTREASURE_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, auid := msg.GetIndex(), msg.GetAuid()
	area, army := wld.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return nil, ecode.TREASURE_NOT_EXIST.String()
	} else if army.Owner != plr.Uid {
		return nil, ecode.NOT_OPEN_OTHER_TREASURE.String()
	}
	pawns := []*pb.PawnTreasureInfo{}
	rewards := []*g.TypeObj{}
	list := army.GetPawnsClone()
	for _, m := range list {
		if arr := m.RemoveTreasureByOpen(); len(arr) > 0 {
			rewards = g.MergeTypeObjsCount(rewards, arr...)
			pawns = append(pawns, &pb.PawnTreasureInfo{Uid: m.Uid, Treasures: m.ToTreasuresPb()})
		}
	}
	if len(rewards) == 0 {
		return nil, ecode.TREASURE_NOT_OPEN.String() //宝箱还未打开
	}
	// 标记
	wld.TagUpdateDBByIndex(area.GetIndex())
	// 发放奖励
	plr.ChangeCostByTypeObjs(rewards, 1)
	// 通知是否有新的宝箱
	wld.SendPlayerHasTreasure(plr.Uid)
	// 通知
	wld.NotifyAreaUpdateInfo(area.GetIndex(), constant.NQ_UPDATE_ARMY_TREASURES, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_63: &pb.ArmyTreasuresInfo{
			ArmyUid:       army.Uid,
			PawnTreasures: pawns,
		},
	})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMARMYTREASURE_S2C{
		Rewards:       plr.ToItemByTypeObjsPb(rewards),
		ArmyUid:       army.Uid,
		PawnTreasures: pawns,
	})
}

// 放弃本次对局
func (this *Game) giveupGame(session gate.Session, msg *pb.GAME_HD_GIVEUPGAME_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if room.IsGameOver() {
		return nil, ecode.ROOM_OVER.String()
	}
	if plr.AllianceUid != "" {
		if alli := wld.GetAlliance(plr.AllianceUid); alli != nil && alli.Creater == plr.Uid {
			// 盟主不能放弃游戏
			return nil, ecode.ALLI_CREATOR_CANT_GIVE_UP.String()
		}
	}
	rankScore, passNewbieIndex, err := wld.DeletePlayerByGiveup(plr.Uid, plr.GetLid())
	if err != "" {
		return nil, err
	}
	session.SetPush("sid", "") //在这里设置一下服务器id
	room.PlayerLeave(plr, 3)
	return pb.ProtoMarshal(&pb.GAME_HD_GIVEUPGAME_S2C{
		RankScore:       int32(rankScore),
		PassNewbieIndex: int32(passNewbieIndex),
	})
}

// 发送邮件
func (this *Game) sendMail(session gate.Session, msg *pb.GAME_HD_SENDMAIL_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	title, content, receiverType, receiver := msg.GetTitle(), msg.GetContent(), ut.Int(msg.GetReceiverType()), msg.GetReceiver()
	if title == "" {
		return nil, ecode.PLEASE_INPUT_TITLE.String()
	} else if receiverType == 1 && receiver == "" {
		return nil, ecode.PLEASE_INPUT_RECEIVER.String()
	} else if content == "" {
		return nil, ecode.PLEASE_INPUT_CONTENT.String()
	} else if ut.GetStringLen(title) > 20 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if sensitive.Validate(title) {
		return nil, ecode.TEXT_HAS_SENSITIVE.String()
	}
	content = sensitive.Replace(content)
	if ut.GetStringLen(content) > 200 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if sensitive.Validate(content) {
		return nil, ecode.TEXT_HAS_SENSITIVE.String()
	} else if this.IsBannedChat(plr) {
		return nil, ecode.BAN_OPT.String() //禁言无法操作
	} else if receiverType == 0 { //发给系统
		room.SendMailOne(0, title, content, plr.Uid, "-1")
	} else if receiverType == 1 { //发给玩家
		tplr := wld.GetTempPlayerByNameOrUID(receiver)
		if tplr == nil || tplr.Uid == plr.Uid {
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
		room.SendMailOne(0, title, content, plr.Uid, tplr.Uid)
	} else if receiverType == 2 { //发给联盟所有人员
		alli := wld.GetAlliance(plr.AllianceUid)
		if alli == nil || (alli.Creater != plr.Uid && alli.GetMemberJob(plr.Uid) != constant.CREATER_VICE) {
			return nil, ecode.NOT_OPERATING_AUTH.String()
		}
		uids := []string{}
		alli.Members.RLock()
		for _, m := range alli.Members.List {
			if m.Uid != plr.Uid {
				uids = append(uids, m.Uid)
			}
		}
		alli.Members.RUnlock()
		if len(uids) == 0 {
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
		room.SendMailMany(0, title, content, plr.Uid, uids)
	}
	return
}

// 战斗回放
func (this *Game) battlePlayBack(session gate.Session, msg *pb.GAME_HD_BATTLEPLAYBACK_C2S) (ret []byte, err string) {
	if !slg.DEBUG {
		// 仅开发环境启用
		return
	}
	if len(msg.Record.Frames) == 0 {
		return
	}
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	initFrame := msg.Record.Frames[0]
	if len(initFrame.Hp) < 2 {
		return
	}
	index, cityId, owner := msg.Record.GetIndex(), initFrame.GetCityId(), initFrame.GetOwner()
	area := world.NewArea(index, wld)
	area.CityId = cityId
	area.MaxHp = initFrame.Hp[1]
	area.SetCurHP(initFrame.Hp[0])
	area.Owner = owner
	pawns := []g.Pawn{}
	attacker := ""
	// 初始军队
	initFighterMap := map[string]*pb.FighterInfo{}
	for _, v := range initFrame.Fighters {
		initFighterMap[v.Uid] = v
	}
	for _, army := range initFrame.Armys {
		if army.GetOwner() != owner {
			attacker = army.GetOwner()
		}
		pawns = append(pawns, transArmyPbToPawns(army, initFighterMap)...)
	}
	fightersCampMap := map[string]int32{}
	for _, fighter := range initFrame.Fighters {
		fightersCampMap[fighter.Uid] = fighter.Camp
	}
	framePawnsMap := map[int32][]g.Pawn{}
	// 中途加入军队
	for i := 1; i < len(msg.Record.Frames); i++ {
		frame := msg.Record.Frames[i]
		fighterMap := map[string]*pb.FighterInfo{}
		for _, v := range frame.Fighters {
			fighterMap[v.Uid] = v
		}
		if frame.Type == constant.FSP_NOTIFY_TYPE_ADD_ARMY {
			framePawnsMap[frame.GetCurrentFrameIndex()] = transArmyPbToPawns(frame.GetArmy(), fighterMap)
		} else if frame.Type == constant.FSP_NOTIFY_TYPE_ADD_PAWN {
			var attackIndex int32 = -1
			if fighter, ok := fighterMap[frame.GetPawn().Uid]; ok {
				attackIndex = fighter.AttackIndex
			}
			army := area.GetArmyByUid(frame.GetPawn().GetArmyUid())
			if army != nil {
				framePawnsMap[frame.GetCurrentFrameIndex()] = []g.Pawn{transPawnPbToPawn(frame.GetPawn(), frame.GetArmyUid(), frame.GetOwner(), attackIndex, army.EnterDir)}
			}
		}
	}
	campMap := map[int32]string{}
	campMap[1] = owner    //当前区域阵营
	campMap[2] = attacker //攻击方阵营
	frameMap := map[int32]*pb.FrameInfo{}
	for _, f := range msg.GetRecord().GetFrames() {
		frameMap[f.GetCurrentFrameIndex()] = f
	}
	// 箭塔信息
	var towerId, towerLv int32
	if cityId == constant.MAIN_CITY_ID { //城墙
		towerId = 7003
	} else if cityId == constant.FORT_CITY_ID { //要塞
		towerId = 7002
	} else if owner != "" { //除了要塞其他都是箭塔
		towerId = 7001
	}
	for _, v := range initFrame.Fighters {
		if v.TowerLv != 0 {
			towerLv = v.TowerLv
			if v.TowerId != 0 {
				towerId = v.TowerId
			}
			break
		}
	}
	fspPlayBack := fsp.NewFSPPlayBack().Init(area, fsp.FSPParam{
		Attacker: attacker,
		Pawns:    pawns,
		CampMap:  campMap,
		FspMul:   ut.MaxInt32(msg.GetSpeed(), 1),
	}, plr.Uid, room, frameMap, framePawnsMap, towerId, towerLv, int(initFrame.GetRandSeed()), fightersCampMap)
	fspPlayBack.Run()
	return
}

// 观战
func (this *Game) spectate(session gate.Session, msg *pb.GAME_HD_SPECTATE_C2S) (ret []byte, err string) {
	sid, version, friendUid := msg.GetSid(), msg.GetVersion(), msg.GetFriendUid()
	uid := session.GetUserID()
	log.Info("1 spectate game uid=" + uid + ", sid=" + ut.Itoa(sid))
	if uid == "" {
		return nil, ecode.TOKEN_ERROR.String()
	}
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() || room.IsGameOver() {
		return nil, ecode.ROOM_OVER.String()
	} else if !room.IsOpen() {
		return nil, ecode.ROOM_CLOSE.String()
	}
	s2c := &pb.GAME_HD_SPECTATE_S2C{}
	if err = room.CheckClientVersion(version); err != "" { //这里返回2个版本号
		s2c.ClientVersion = room.GetClientVersion()
		s2c.ClientBigVersion = room.GetClientBigVersion()
		log.Info("2 spectate game uid=" + uid + ", " + err)
		return
	}
	wld := room.GetWorld()
	maincityIndex := wld.GetPlayerMainIndex(friendUid)
	isReconnect := msg.GetIsReconnect()
	plr := room.GetSpectatePlayer(uid) //获取观战玩家
	if plr == nil {
		lid := session.Get("lid")
		info, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(lid, slg.RPC_GET_USER_BY_CREATE_PLAYER, uid))
		if info == nil {
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
		if ut.Int32(info["playSid"]) == room.Id {
			return nil, ecode.UNKNOWN.String()
		}
		uid, nickname, headIcon, personalDesc, title, lang := ut.String(info["uid"]), ut.String(info["nickname"]), ut.String(info["headIcon"]), ut.String(info["personalDesc"]), ut.Int32(info["title"]), ut.String(info["lang"])
		plr, err = room.CreateSpectatePlayer(uid, nickname, headIcon, personalDesc, lang, title, maincityIndex)
		if err != "" {
			return nil, err
		}

	} else {
		if session.IsGuest() {
			log.Info("2 spectate game error, uid=" + plr.GetUID() + ", name=" + plr.Nickname + ", isReconnect=" + strconv.FormatBool(isReconnect))
			return nil, ecode.PLAYER_NOT_EXIST.String()
		} else if !plr.IsSpectate {
			// 玩家已参与对局 无法观战
			return nil, ecode.PLAYER_NOT_EXIST.String()
		}
	}
	// 更新观战者观战主城
	wld.UpdateSepctatorMainIndex(uid, maincityIndex)
	s2c.Rst = bindPalyerAndSend(room, plr, session, isReconnect)
	// 世界数据pb序列化加读锁
	wld.WorldPbRLock()
	ret, _ = pb.ProtoMarshal(s2c)
	wld.WorldPbRUnLock()
	log.Info("2 spectate game uid=" + plr.GetUID() + ", name=" + plr.Nickname + ", isReconnect=" + strconv.FormatBool(isReconnect))
	return
}

// 个人标记
func (this *Game) mapMarkPoint(session gate.Session, msg *pb.GAME_HD_MAPMARKPOINT_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.GetMapMarkCount() >= constant.MAX_MAP_MARK_COUNT {
		return nil, ecode.MAP_MARK_COUNT_LIMIT.String()
	}
	name, point := msg.GetName(), pb.ToVec2(msg.GetPoint())
	if mark := plr.FindMapMark(point); mark != nil {
		mark.Name = name
	} else {
		plr.AddMapMark(name, point)
	}
	return
}

// 删除个人标记
func (this *Game) removeMapMark(session gate.Session, msg *pb.GAME_HD_REMOVEMAPMARK_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	point := pb.ToVec2(msg.GetPoint())
	plr.RemoveMapMark(point)
	return
}

// 获取防作弊题目
func (this *Game) getAntiCheatQuestion(session gate.Session, msg *pb.GAME_HD_GETANTICHEATQUESTION_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	options := plr.GenAntiCheatQuest()
	if options == nil {
		return nil, ecode.UNKNOWN.String()
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETANTICHEATQUESTION_S2C{
		Item:        plr.AntiCheatQuest.AntiCheatQuestItem,
		Options:     pb.Int32Array(options),
		SurplusTime: int32(plr.AntiCheatQuest.GetSurplusTime()),
	})
}

// 防作弊答题
func (this *Game) antiCheatAnswer(session gate.Session, msg *pb.GAME_HD_ANTICHEATANSWER_C2S) (ret []byte, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	if plr.AntiCheatQuest == nil || plr.AntiCheatQuest.AntiCheatQuestOptions == nil || len(plr.AntiCheatQuest.AntiCheatQuestOptions) == 0 {
		// 未生成问题
		return nil, ecode.UNKNOWN.String()
	}
	s2c := &pb.GAME_HD_ANTICHEATANSWER_S2C{}
	answer := ut.Int(msg.GetAnswer())
	if answer != plr.AntiCheatQuest.AntiCheatQuestOptions[0] {
		plr.AntiCheatQuest.WrongCount++
		s2c.Rst = false
		surplusTime := plr.AntiCheatQuest.GetSurplusTime()
		s2c.SurplusTime = int32(surplusTime)
		if surplusTime <= 0 {
			// 倒计时结束 测试未通过
			plr.AntiCheatQuest.NotPassCount++
			s2c.NotPassCount = int32(plr.AntiCheatQuest.NotPassCount)
			if plr.AntiCheatQuest.NotPassCount >= constant.ANTI_CHEAT_QUEST_NOT_PASS_LIMIT {
				// 达到未通过次数上限 测试失败
				this.InvokeLobbyRpcNR(plr.GetLid(), slg.RPC_USER_ANTI_CHEAT_CHECK, plr.Uid, false)
				plr.AntiCheatL = int32(ut.Ceil(float64(plr.AntiCheatL/4) * 3))
				// 重置答题数据
				plr.AntiCheatQuest = nil
			} else {
				// 重置答题数据 但保留未通过次数
				plr.AntiCheatQuest.AntiCheatQuestOptions = nil
				plr.AntiCheatQuest.WrongCount = 0
				plr.AntiCheatQuest.AntiCheatQuestItem = ""
				plr.AntiCheatQuest.Time = 0
			}
		} else {
			// 还有剩余时间 可继续答题
			s2c.WrongCount = int32(plr.AntiCheatQuest.WrongCount)
		}
	} else {
		// 测试通过
		plr.AntiCheatL += constant.ANTI_CHEAT_L_PASS_ADD
		plr.AntiCheatS = ut.MaxInt32(0, plr.AntiCheatS-plr.AntiCheatL)
		s2c.Rst = true
		// 重置答题数据
		plr.AntiCheatQuest = nil
		// 奖励三资
		runDay := ut.MaxInt32(1, room.GetRunDay())
		itemCount := runDay * constant.ANTI_CHEAT_REWARD
		items := []*g.TypeObj{
			{Type: ctype.CEREAL, Count: itemCount},
			{Type: ctype.TIMBER, Count: itemCount},
			{Type: ctype.STONE, Count: itemCount},
		}
		room.SendMailItemOne(slg.MAIL_ANTI_CHEAT_REWARD_ID, "", "", "-1", plr.Uid, items)
	}
	return pb.ProtoMarshal(s2c)
}

// 军队pb转为士兵数据
func transArmyPbToPawns(army *pb.AreaArmyInfo, fighterMap map[string]*pb.FighterInfo) []g.Pawn {
	pawns := []g.Pawn{}
	for _, p := range army.Pawns {
		var attackIndex int32 = -1
		if fighter, ok := fighterMap[p.Uid]; ok {
			attackIndex = fighter.AttackIndex
		}
		pawns = append(pawns, transPawnPbToPawn(p, army.GetUid(), army.GetOwner(), attackIndex, army.GetEnterDir()))
	}
	return pawns
}

func transPawnPbToPawn(p *pb.AreaPawnInfo, armyUid, owner string, attackIndex, enterDir int32) g.Pawn {
	pawn := world.NewAreaPawn(0, enterDir, p.GetUid(), pb.ToVec2(p.GetPoint()), p.GetId(), owner, armyUid, nil)
	pawn.Lv = p.GetLv()
	pawn.SkinId = p.GetSkinId()
	pawn.AttackSpeed = p.GetAttackSpeed()
	pawn.PetId = p.GetPetId()
	if p.Portrayal != nil {
		pawn.Portrayal = g.NewPortrayal(p.Portrayal.Id, p.Portrayal.Debris)
		if p.Portrayal.Attrs != nil {
			for _, attrInfo := range p.Portrayal.Attrs {
				attr := []int32{}
				for _, v := range attrInfo.Attr {
					attr = append(attr, v)
				}
				pawn.Portrayal.Attrs = append(pawn.Portrayal.Attrs, attr)
			}
		}
		pawn.Portrayal.UpdateAttr()
	}
	if attackIndex > 0 {
		pawn.SetAttackIndex(attackIndex)
	}
	pawn.SetCurHP(p.Hp[0])
	if maxHp, ok := p.Hp[1]; !ok {
		pawn.SetMaxHP(p.Hp[0])
	} else {
		pawn.SetMaxHP(maxHp)
	}
	pawn.SetCurAnger(p.GetCurAnger())
	pawn.SetRodeleroCadetLv(p.GetRodeleroCadetLv())
	equip := p.GetEquip()
	pawn.Equip = g.NewEquip(equip.GetId())
	pawn.Equip.Attrs = g.ClonePbToEffectAttrs(equip.GetAttrs())
	pawn.Equip.UpdateAttr()
	if p.Buffs != nil {
		for _, b := range p.Buffs {
			pawn.Buffs = append(pawn.Buffs, g.NewBuffByPb(b))
		}
	}
	pawn.InitJson()
	pawn.UpdateRodeleroCadetLv()
	return pawn
}

func (this *Game) HandleCreatePlayer(lid, uid, distinctId string, sid int32, pos int32, room *r.Model) (plr *player.Model, err string) {
	info, _ := ut.RpcInterfaceMap(this.InvokeLobbyRpc(lid, slg.RPC_GET_USER_BY_CREATE_PLAYER, uid))
	if info == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	// 随机主城区域
	if pos >= 0 {
		pos = room.GetRandMainCityPos()
		if pos < 0 {
			return nil, ecode.ROOM_FULL.String()
		}
	}
	uid, nickname, headIcon, personalDesc, title, pawnSkins, regTime, os, osVer, platform, fcmToken, lang, offlineOpt, mainCitySkin, treasureLostCount, antiCheat := ut.String(info["uid"]), ut.String(info["nickname"]), ut.String(info["headIcon"]),
		ut.String(info["personalDesc"]), ut.Int32(info["title"]), ut.Int32Array(info["pawnSkins"]), ut.Int64(info["registerTime"]), ut.String(info["deviceOS"]), ut.String(info["deviceOSVersion"]), ut.String(info["platform"]), ut.String(info["FCMToken"]),
		ut.String(info["lang"]), ut.Int32Array(info["offlineNotifyOpt"]), ut.Int32(info["mainCitySkin"]), ut.Int32(info["treasureLostCount"]), ut.Int32Array(info["antiCheat"])
	plr, err = room.CreatePlayer(uid, nickname, headIcon, personalDesc, os, osVer, platform, regTime, title, pawnSkins, pos, lang, fcmToken, offlineOpt, distinctId, true, mainCitySkin, treasureLostCount, antiCheat)
	if err != "" {
		return nil, err
	}
	// 设置用户playSid
	_, err = this.InvokeLobbyRpc(lid, slg.RPC_SET_USER_PLAYSID, uid, sid)
	if err != "" {
		return nil, err
	}
	return
}
