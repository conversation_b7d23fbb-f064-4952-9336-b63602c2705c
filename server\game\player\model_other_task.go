package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/utils/array"
)

// 获取任务
func (this *Model) AddOtherTaskAndComplete(id int32) {
	this.OtherTasks.Lock()
	// 是否已经完成了
	if array.Has(this.OtherTasks.Finishs, id) {
		this.OtherTasks.Unlock()
		return
	} else if array.Some(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }) {
		this.OtherTasks.Unlock()
		return // 已经有了
	}
	// 添加
	json := config.GetJsonData("otherTask", id)
	if json == nil {
		this.OtherTasks.Unlock()
		return
	}
	newTask := g.NewTaskInfo(json)
	// 直接完成
	newTask.Progress = newTask.GetCondInfo().Count
	this.OtherTasks.List = append(this.OtherTasks.List, newTask)
	this.OtherTasks.Unlock()
	// 通知客户端
	this.notifyUpdateTasks(&pb.TaskUpdateNotify{OtherTasks: this.OtherTasks.ToPb()})
}

// 获取任务
func (this *Model) GetOtherTask(id int32) *g.TaskInfo {
	this.OtherTasks.RLock()
	defer this.OtherTasks.RUnlock()
	if task := array.Find(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 是否已经完成了
func (this *Model) InOtherTaskFinish(id int32) bool {
	this.OtherTasks.RLock()
	defer this.OtherTasks.RUnlock()
	return array.Has(this.OtherTasks.Finishs, id)
}

// 完成一个任务
func (this *Model) FinishOtherTask(task *g.TaskInfo) {
	this.OtherTasks.Lock()
	defer this.OtherTasks.Unlock()
	// 先删除任务列表里面的
	this.OtherTasks.List = array.RemoveItem(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	this.OtherTasks.Finishs = append(this.OtherTasks.Finishs, task.ID)
	this.CheckUpdatePolicyTask(task.ID)
}

// 接受指定政策任务的后续任务
func (this *Model) CheckUpdatePolicyTask(taskId int32) {
	// 删除没有的
	finishMap := map[int32]bool{}
	for i := len(this.TodayTasks.Finishs) - 1; i >= 0; i-- {
		id := this.TodayTasks.Finishs[i]
		if json := config.GetJsonData("otherTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.TodayTasks.Finishs = append(this.TodayTasks.Finishs[:i], this.TodayTasks.Finishs[i+1:]...)
		}
	}
	// 删除已经完成的任务且重复的
	ids := map[int32]bool{}
	for i := len(this.TodayTasks.List) - 1; i >= 0; i-- {
		m := this.TodayTasks.List[i]
		prevId := m.GetPrevID()
		if finishMap[m.ID] || ids[m.ID] || (prevId != 0 && !finishMap[prevId]) {
			this.TodayTasks.List = append(this.TodayTasks.List[:i], this.TodayTasks.List[i+1:]...)
		} else {
			ids[m.ID] = true
		}
	}

	// 接受当前任务的后续任务
	curJson := config.GetJsonData("otherTask", taskId)
	if curJson == nil {
		return
	}
	// 获取关联的政策id
	var policyId, nextTaskId, taskCnt int32
	for k, taskIdList := range constant.POLICY_TASK_MAP {
		index := -1
		for i, id := range taskIdList {
			if id == taskId {
				policyId = k
				index = i
			}
		}
		if index >= len(taskIdList)-1 {
			// 没有后续任务了
			return
		} else if index >= 0 {
			nextTaskId = taskIdList[index+1]
			taskCnt = int32(index) + 1
			break
		}
	}
	if policyId == 0 { // 不是政策任务
		return
	}
	// 获取当前政策等级
	plv := this.room.GetWorld().GetPolicyLvById(this.Uid, policyId)
	if plv <= taskCnt {
		// 政策等级和已接受过的该系列任务数量相同 无法接受新的
		return
	}

	// 接受下一个任务
	nextJson := config.GetJsonData("otherTask", nextTaskId)
	if nextJson == nil {
		return
	}
	if finishMap[nextTaskId] || ids[nextTaskId] { // 已接受过
		return
	}
	runDay := this.room.GetRunDay()
	newTask := g.NewTaskInfo(nextJson)
	newTask.ServerRunDay = runDay
	this.InitTaskProgress(newTask)
	this.TodayTasks.List = append(this.TodayTasks.List, newTask)
}
