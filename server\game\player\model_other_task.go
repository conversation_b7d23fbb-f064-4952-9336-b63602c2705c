package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	"slgsrv/utils/array"
)

// 获取任务
func (this *Model) AddOtherTaskAndComplete(id int32) {
	this.OtherTasks.Lock()
	// 是否已经完成了
	if array.Has(this.OtherTasks.Finishs, id) {
		this.OtherTasks.Unlock()
		return
	} else if array.Some(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }) {
		this.OtherTasks.Unlock()
		return //已经有了
	}
	// 添加
	json := config.GetJsonData("otherTask", id)
	if json == nil {
		this.OtherTasks.Unlock()
		return
	}
	newTask := g.NewTaskInfo(json)
	// 直接完成
	newTask.Progress = newTask.GetCondInfo().Count
	this.OtherTasks.List = append(this.OtherTasks.List, newTask)
	this.OtherTasks.Unlock()
	// 通知客户端
	this.notifyUpdateTasks(&pb.TaskUpdateNotify{OtherTasks: this.OtherTasks.ToPb()})
}

// 获取任务
func (this *Model) GetOtherTask(id int32) *g.TaskInfo {
	this.OtherTasks.RLock()
	defer this.OtherTasks.RUnlock()
	if task := array.Find(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 是否已经完成了
func (this *Model) InOtherTaskFinish(id int32) bool {
	this.OtherTasks.RLock()
	defer this.OtherTasks.RUnlock()
	return array.Has(this.OtherTasks.Finishs, id)
}

// 完成一个任务
func (this *Model) FinishOtherTask(task *g.TaskInfo) {
	this.OtherTasks.Lock()
	defer this.OtherTasks.Unlock()
	// 先删除任务列表里面的
	this.OtherTasks.List = array.RemoveItem(this.OtherTasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	this.OtherTasks.Finishs = append(this.OtherTasks.Finishs, task.ID)
}
