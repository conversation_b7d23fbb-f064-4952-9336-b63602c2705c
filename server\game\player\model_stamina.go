package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"sync/atomic"
)

// 检测更新体力
func (this *Model) CheckUpdateStamina(now int64, init bool) {
	arr := this.room.GetStaminas()
	if this.LastUpdateStaminaTime == 0 {
		this.Stamina = arr[0]
		this.LastUpdateStaminaTime = this.NextToDayTime
		return
	} else if this.Stamina >= arr[1] {
		this.LastUpdateStaminaTime = this.NextToDayTime
		return
	} else if this.LastUpdateStaminaTime > now {
		return
	}
	this.Stamina += arr[1]
	this.LastUpdateStaminaTime = this.NextToDayTime
	if !init {
		this.PutNotifyQueue(constant.NQ_UPDATE_ITEMS, &pb.OnUpdatePlayerInfoNotify{Data_41: this.ToStaminaPb()})
	}
}

// 改变体力
func (this *Model) ChangeStamina(val int32) int32 {
	if atomic.LoadInt32(&this.Stamina)+val < 0 {
		return -1
	}
	atomic.AddInt32(&this.Stamina, val)
	return atomic.LoadInt32(&this.Stamina)
}

func (this *Model) ToStaminaPb() *pb.UpdateOutPut {
	return &pb.UpdateOutPut{
		Stamina: int32(this.Stamina),
		Flag:    pb.AddFlags(int32(pb.OutPutFlagEnum_Stamina)),
	}
}
