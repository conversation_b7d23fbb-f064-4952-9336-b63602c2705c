package player

import (
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

const VERSION = 23

// 兼容 英雄技能效果
func (this *Model) compatiPortrayalAttr(id int32, oMin, oMax, nMin, nMax float64) {
	wld := this.room.GetWorld()
	slots := wld.GetPlayerHeroSlots(this.Uid)
	slot := array.Find(slots, func(m *g.HeroSlotInfo) bool { return m.GetHeroID() == id })
	if slot != nil {
		portrayal := slot.Hero
		attr := array.Find(portrayal.Attrs, func(arr []int32) bool { return arr[0] == 1 })
		if attr != nil {
			attr[2] = slg.CompatiValueInt32(oMin, oMax, nMin, nMax, attr[2])
			portrayal.UpdateAttr()
			wld.UpdatePlayerHeroAttr(this.Uid, portrayal.ID, portrayal.Attrs)
		}
	}
}

func (this *Model) Compati(ver int32) {
	if ver == 14 {
		wld := this.room.GetWorld()
		slots := wld.GetPlayerHeroSlots(this.Uid)
		for _, m := range slots {
			if m.Hero == nil {
			} else if m.Hero.ID == 330101 { //黄忠
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = ut.RoundInt32(float64(attr[2]) / 20.0 * 30.0)
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			} else if m.Hero.ID == 320401 { //黄盖
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = ut.RoundInt32(float64(attr[2]) / 60.0 * 50.0)
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			}
		}
		// 装备
		equips := wld.GetPlayerEquips(this.Uid)
		for _, m := range equips {
			if attr := array.Find(m.Attrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 35 }); attr != nil { //玉佩
				attr[2] = ut.RandomInt32(5, 10)
				attr = array.Find(m.LastAttrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 35 })
				if attr != nil {
					attr[2] = ut.RandomInt32(5, 10)
				}
				m.UpdateAttr()
				wld.UpdatePlayerPawnEquipInfo(this.Uid, m.ID, m.Attrs)
			} else if attr := array.Find(m.Attrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 18 }); attr != nil { //护心镜
				attr[2] = 20
				attr = array.Find(m.LastAttrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 18 })
				if attr != nil {
					attr[2] = 20
				}
				m.UpdateAttr()
				wld.UpdatePlayerPawnEquipInfo(this.Uid, m.ID, m.Attrs)
			}
		}
	} else if ver == 15 {
		wld := this.room.GetWorld()
		slots := wld.GetPlayerHeroSlots(this.Uid)
		for _, m := range slots {
			if m.Hero == nil {
			} else if m.Hero.ID == 320501 { //曹操
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = 10 + (attr[2] - 5)
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			} else if m.Hero.ID == 330201 { //刘备
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = 10 + (attr[2] - 5)
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			}
		}
	} else if ver == 16 {
		wld := this.room.GetWorld()
		slots := wld.GetPlayerHeroSlots(this.Uid)
		for _, m := range slots {
			if m.Hero == nil {
			} else if m.Hero.ID == 310102 { //赵云
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = slg.CompatiValueInt32(5, 10, 10, 20, attr[2])
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			} else if m.Hero.ID == 310601 { //文鸯
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = slg.CompatiValueInt32(2, 6, 40, 70, attr[2])
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			}
		}
		// 装备
		equips := wld.GetPlayerEquips(this.Uid)
		for _, m := range equips {
			if index := array.FindIndex(m.Attrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 37 }); index != -1 { //焱阳铠
				attr := m.Attrs[index]
				val := slg.CompatiValueInt32(100, 200, 20, 50, attr[2])
				if len(attr) <= 3 {
					attr = append(attr, val)
				} else {
					attr[3] = val
				}
				attr[2] = 0
				m.Attrs[index] = attr
				if index = array.FindIndex(m.LastAttrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 37 }); index != -1 {
					attr = m.LastAttrs[index]
					attr[2] = 0
					if len(attr) <= 3 {
						attr = append(attr, ut.RandomInt32(20, 50))
					} else {
						attr[3] = ut.RandomInt32(20, 50)
					}
					m.LastAttrs[index] = attr
				}
				m.UpdateAttr()
				wld.UpdatePlayerPawnEquipInfo(this.Uid, m.ID, m.Attrs)
			} else if m.ID == 6025 { //时光旗
				attr := array.Find(m.Attrs, func(arr []int32) bool { return arr[0] == 2 })
				if attr[1] == 32 {
					attr[2] = 30
				} else if attr[1] == 33 {
					attr[2] = 3
				}
				attr = array.Find(m.Attrs, func(arr []int32) bool { return arr[0] == 0 })
				if attr[2] == 2 {
					attr[2] = 3
				} else if attr[2] == 20 {
					attr[2] = 30
				}
				m.UpdateAttr()
				wld.UpdatePlayerPawnEquipInfo(this.Uid, m.ID, m.Attrs)
			}
		}
	} else if ver == 17 {
		wld := this.room.GetWorld()
		slots := wld.GetPlayerHeroSlots(this.Uid)
		for _, m := range slots {
			if m.Hero == nil {
			} else if m.Hero.ID == 310502 { //吕蒙
				attr := array.Find(m.Hero.Attrs, func(arr []int32) bool { return arr[0] == 1 })
				if attr != nil {
					attr[2] = slg.CompatiValueInt32(10, 30, 20, 50, attr[2])
					m.Hero.UpdateAttr()
					wld.UpdatePlayerHeroAttr(this.Uid, m.Hero.ID, m.Hero.Attrs)
				}
			}
		}
		// 装备
		equips := wld.GetPlayerEquips(this.Uid)
		for _, m := range equips {
			if index := array.FindIndex(m.Attrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 17 }); index != -1 { //双钩
				attr := m.Attrs[index]
				if len(attr) <= 3 {
					attr = append(attr, ut.RandomInt32(15, 35))
				} else {
					attr[3] = slg.CompatiValueInt32(15, 40, 15, 35, attr[3])
				}
				m.Attrs[index] = attr
				if index = array.FindIndex(m.LastAttrs, func(arr []int32) bool { return arr[0] == 2 && arr[1] == 17 }); index != -1 {
					attr = m.LastAttrs[index]
					if len(attr) <= 3 {
						attr = append(attr, ut.RandomInt32(15, 35))
					} else {
						attr[3] = ut.RandomInt32(15, 35)
					}
					m.LastAttrs[index] = attr
				}
				m.UpdateAttr()
				wld.UpdatePlayerPawnEquipInfo(this.Uid, m.ID, m.Attrs)
			}
		}
	} else if ver == 18 || ver == 19 {
		// 兼容 周瑜
		this.compatiPortrayalAttr(310302, 10, 30, 20, 40)
		// 兼容 秦琼
		this.compatiPortrayalAttr(310603, 50, 100, 20, 30)
		// 兼容 裴行俨
		this.compatiPortrayalAttr(340402, 10, 20, 20, 30)
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 10, 20, 200, 300)
		// 兼容 孙权
		this.compatiPortrayalAttr(330402, 50, 100, 5, 15)
		this.Compati(20)
		return
	} else if ver == 20 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 200, 300, 100, 150)
	} else if ver == 21 {
		// 兼容 养由基
		this.compatiPortrayalAttr(330401, 100, 150, 10, 30)
		// 兼容 邓艾
		this.compatiPortrayalAttr(320502, 5, 10, 10, 30)
	} else if ver == 22 {
		// 兼容 姜维
		this.compatiPortrayalAttr(310501, 5, 15, 5, 10)
		// 兼容 张辽
		this.compatiPortrayalAttr(340101, 10, 30, 20, 50)
		// 兼容 霍去病
		this.compatiPortrayalAttr(340102, 50, 100, 5, 10)
		// 兼容医馆通知
		this.room.GetWorld().SetHospitalFullNoticeFlag(this.Uid, int32(constant.INJURY_PAWN_MAX_COUNT))
	} else {
		this.Version = VERSION
		return
	}
	this.Compati(ver + 1)
}
