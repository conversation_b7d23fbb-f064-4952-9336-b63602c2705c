package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 建筑
type AreaBuild struct {
	AIndex int32 //所在区域位置
	Uid    string
	Point  *ut.Vec2 //位置
	Id     int32
	Lv     int32

	attrId   int32 //属性id
	baseJson map[string]interface{}
	attrJson map[string]interface{}
	size     *ut.Vec2
	points   []*ut.Vec2 //占地位置

	Effects []*g.EffectObj //效果列表
	UpCosts []*g.TypeObj   //升级消耗
}

func NewAreaBuild(index int32, uid string, point *ut.Vec2, id, lv int32) *AreaBuild {
	ab := &AreaBuild{
		AIndex: index,
		Uid:    uid,
		Point:  point,
		Id:     id,
		Lv:     lv,
	}
	ab.InitJson()
	return ab
}

func (this *AreaBuild) ToDB() map[string]interface{} {
	return map[string]interface{}{
		"uid":   this.Uid,
		"point": this.Point.Clone(),
		"id":    this.Id,
		"lv":    this.Lv,
	}
}

func (this *AreaBuild) Strip() *g.BuildStrip {
	return &g.BuildStrip{
		Index: this.AIndex,
		Uid:   this.Uid,
		Point: this.Point.Clone(),
		Id:    this.Id,
		Lv:    this.Lv,
	}
}

func (this *AreaBuild) ToPb() *pb.AreaBuildInfo {
	return &pb.AreaBuildInfo{
		Index: int32(this.AIndex),
		Uid:   this.Uid,
		Point: pb.NewVec2(this.Point),
		Id:    int32(this.Id),
		Lv:    int32(this.Lv),
	}
}

func (this *AreaBuild) ToShortInfo() map[string]interface{} {
	return map[string]interface{}{
		"index": this.AIndex,
		"uid":   this.Uid,
		"lv":    this.Lv,
	}
}

func (this *AreaBuild) ToShortInfoPb() *pb.AreaBuildInfo {
	return &pb.AreaBuildInfo{
		Index: int32(this.AIndex),
		Uid:   this.Uid,
		Lv:    int32(this.Lv),
	}
}

func (this *AreaBuild) ToShortData() map[string]interface{} {
	return map[string]interface{}{
		"uid": this.Uid,
		"id":  this.Id,
		"lv":  this.Lv,
	}
}

func (this *AreaBuild) ToShortDataPb() *pb.AreaBuildInfo {
	return &pb.AreaBuildInfo{
		Uid: this.Uid,
		Id:  int32(this.Id),
		Lv:  int32(this.Lv),
	}
}

func (this *AreaBuild) InitJson() {
	this.baseJson = config.GetJsonData("buildBase", this.Id)
	this.size = ut.NewVec2ByString(ut.String(this.baseJson["size"]), "x")
	this.points = helper.GenPointsBySize(this.size, ut.NewVec2(0, 0))
	this.UpdateAttrJson()
}

// 刷新属性json
func (this *AreaBuild) UpdateAttrJson() {
	this.attrId = this.Id*1000 + this.Lv
	this.attrJson = config.GetJsonData("buildAttr", this.attrId)
	this.UpCosts = g.StringToTypeObjs(this.attrJson["up_cost"])
	this.Effects = g.StringToEffectObjs(this.attrJson["effects"])
}

func (this *AreaBuild) GetUID() string           { return this.Uid }
func (this *AreaBuild) GetID() int32             { return this.Id }
func (this *AreaBuild) GetLV() int32             { return this.Lv }
func (this *AreaBuild) GetPoints() []*ut.Vec2    { return this.points }
func (this *AreaBuild) GetUpCosts() []*g.TypeObj { return this.UpCosts }

// 获取升级条件
func (this *AreaBuild) GetPrepCond() []*g.TypeObj {
	return g.StringToTypeObjs(this.attrJson["prep_cond"])
}

// 是否满级
func (this *AreaBuild) IsMaxLv() bool {
	return len(this.UpCosts) == 0
}

// 获取建造需要的时间
func (this *AreaBuild) GetBTNeedTime() int32 {
	return ut.Int32(this.attrJson["bt_time"])
}

// 获取效果
func (this *AreaBuild) GetEffectFloat(t int32) float64 {
	for _, m := range this.Effects {
		if m.Type == t {
			return m.Value
		}
	}
	return 0
}

// 获取效果
func (this *AreaBuild) GetEffect(t int32) (bool, float64) {
	for _, m := range this.Effects {
		if m.Type == t {
			return true, m.Value
		}
	}
	return false, 0
}

func (this *AreaBuild) GetEffectInt(t int32) int32 {
	return int32(this.GetEffectFloat(t))
}

// 获取关联的士兵id
func (this *AreaBuild) GetBuildPawnId() int32 {
	return ut.Int32(this.baseJson["pawn_id"])
}

// 获取默认位置
func (this *AreaBuild) GetDefaultPoint() *ut.Vec2 {
	if str := ut.String(this.baseJson["pos"]); str != "" {
		return ut.NewVec2ByString(str, ",")
	}
	return nil
}

// 获取建筑实际的点位
func (this *AreaBuild) GetActPoints() []*ut.Vec2 {
	return array.Map(this.points, func(m *ut.Vec2, _ int) *ut.Vec2 { return ut.NewVec2(m.X+this.Point.X, m.Y+this.Point.Y) })
}

// 获取建筑实际的点位
func (this *AreaBuild) GetActPointsByPoint(point *ut.Vec2) []*ut.Vec2 {
	return array.Map(this.points, func(m *ut.Vec2, _ int) *ut.Vec2 { return ut.NewVec2(m.X+point.X, m.Y+point.Y) })
}
