package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 研究槽位信息
type CeriSlotInfo struct {
	SelectIds  []int32 `bson:"select_ids"`  //选择列表
	Lv         int32   `bson:"lv"`          //对应建筑等级
	ID         int32   `bson:"id"`          //当前选择的id
	ResetCount int32   `bson:"reset_count"` //重置次数
}

func NewCeriSlotInfo(lv int32) *CeriSlotInfo {
	if lv <= 0 {
		return nil
	}
	info := &CeriSlotInfo{Lv: lv}
	return info
}

func (this *CeriSlotInfo) ToPb() *pb.CeriSlotInfo {
	return &pb.CeriSlotInfo{
		Id:         this.ID,
		ResetCount: this.ResetCount,
		SelectIds:  this.SelectIds,
		Lv:         this.Lv,
	}
}

func (this *CeriSlotInfo) ToDB() *CeriSlotInfo {
	return &CeriSlotInfo{
		ID:         this.ID,
		ResetCount: this.ResetCount,
		SelectIds:  array.Map(this.SelectIds, func(m int32, _ int) int32 { return m }),
		Lv:         this.Lv,
	}
}

func (this *CeriSlotInfo) GetPolicyEffect() int32 {
	if json := config.GetJsonData("policy", this.ID); json != nil {
		return ut.Int32(json["type"])
	}
	return 0
}

// 研究槽位map
type CeriSlotMap struct {
	deadlock.RWMutex
	Type int32
	Map  map[int32]*CeriSlotInfo // k=>建筑等级 v=>槽位信息
}

func (this *CeriSlotMap) Clean() {
	this.Lock()
	this.Map = map[int32]*CeriSlotInfo{}
	this.Unlock()
}

// 获取指定建筑等级的槽位信息
func (this *CeriSlotMap) GetSlotByLv(lv int32) *CeriSlotInfo {
	this.RLock()
	defer this.RUnlock()
	return this.Map[lv]
}

// 添加槽位
func (this *CeriSlotMap) AddCeriSlot(lv int32) *CeriSlotInfo {
	slot := NewCeriSlotInfo(lv)
	if slot != nil {
		this.Lock()
		this.Map[lv] = slot
		this.Unlock()
	}
	return slot
}

// 兼容下
func (this *CeriSlotMap) Check(buildLv int32, confMap map[int32]int32, opts map[string]interface{}) {
	// 删除没有的等级
	this.Lock()
	for lv := range this.Map {
		if _, ok := confMap[lv]; !ok {
			delete(this.Map, lv)
		}
	}
	this.Unlock()
	// 开始过滤兼容
	hasSelect := false
	cnt := int32(len(confMap))
	lvs := make([]int32, cnt)
	for lv, index := range confMap {
		if index < cnt {
			lvs[index] = lv
		}
	}
	for _, lv := range lvs {
		slot := this.GetSlotByLv(lv)
		if slot != nil {
			if slot.ID > 0 {
				slot.SelectIds = nil
				continue
			}
		} else if lv <= buildLv {
			slot = this.AddCeriSlot(lv)
		} else {
			continue
		}
		if hasSelect {
			slot.SelectIds = nil
			continue
		} else if this.Type == constant.CERI_CONF_TYPE_EQUIP && constant.EXCLUSIVE_EQUIP_SLOT_CONF_MAP[lv] > 0 {
			// 专属跳过兼容
			continue
		} else if slot.SelectIds == nil || len(slot.SelectIds) == 0 {
			this.CeriRandomSelect(lv, opts)
		}
		if this.Type != constant.CERI_CONF_TYPE_PAWN {
			hasSelect = true
		}
	}
}

func (this *CeriSlotMap) GetIdBoolMap(out map[int32]bool) map[int32]bool {
	if out == nil {
		out = map[int32]bool{}
	}
	this.RLock()
	defer this.RUnlock()
	for _, v := range this.Map {
		if v.ID > 0 {
			out[v.ID] = true
		}
	}
	return out
}

// 获取指定效果类型的政策id
func (this *CeriSlotMap) GetPolicyIdByEffectType(tp int32) int32 {
	this.RLock()
	defer this.RUnlock()
	for _, m := range this.Map {
		if m.ID <= 0 {
			continue
		} else if m.GetPolicyEffect() == tp {
			return m.ID
		}
	}
	return 0
}

// 获取生效的政策map
func (this *CeriSlotMap) ToPolicys() map[int32]int32 {
	this.RLock()
	defer this.RUnlock()
	arr := map[int32]int32{}
	for i, m := range this.Map {
		if m.ID > 0 {
			arr[i] = m.ID
		}
	}
	return arr
}

func (this *CeriSlotMap) ToCerisPb() map[int32]*pb.CeriSlotInfo {
	this.RLock()
	defer this.RUnlock()
	arr := map[int32]*pb.CeriSlotInfo{}
	for i, m := range this.Map {
		arr[i] = m.ToPb()
	}
	return arr
}

func (this *CeriSlotMap) ToCerisDB() map[int32]*CeriSlotInfo {
	this.RLock()
	defer this.RUnlock()
	arr := map[int32]*CeriSlotInfo{}
	for i, m := range this.Map {
		arr[i] = m.ToDB()
	}
	return arr
}

func (this *CeriSlotMap) GetPolicyEffectMapBool() map[int32]bool {
	this.RLock()
	defer this.RUnlock()
	effectMap := map[int32]bool{}
	for _, m := range this.Map {
		if m.ID <= 0 {
			continue
		}
		if effectType := m.GetPolicyEffect(); effectType > 0 {
			effectMap[effectType] = true
		}
	}
	return effectMap
}

// 检测上一个槽位是否选择
func (this *CeriSlotMap) CheckLastSlotIsSelect(blv int32) bool {
	if blv == 1 {
		return true
	}
	this.RLock()
	defer this.RUnlock()
	if this.Type == constant.CERI_CONF_TYPE_EQUIP {
		// 装备类型检测上一个槽位 需要区分普通和专属
		var confList []int32
		if isExclusive := constant.EXCLUSIVE_EQUIP_SLOT_CONF_MAP[blv] > 0; isExclusive {
			confList = constant.EQUIP_SLOT_CONF_EXCLUSIVE_LIST
		} else {
			confList = constant.EQUIP_SLOT_CONF_NORMAL_LIST
		}
		index := array.FindIndex(confList, func(m int32) bool { return m == blv })
		if index <= 0 {
			return true
		}
		// 获取对应类型装备的上一个槽位
		lastBlv := confList[index-1]
		if m := this.Map[lastBlv]; m != nil && m.ID > 0 {
			return true
		} else {
			return false
		}
	} else {
		for lv, m := range this.Map {
			if lv < blv && m.ID <= 0 {
				return false
			}
		}
		return true
	}
}

// 玩家随机研究待选项
func (this *CeriSlotMap) CeriRandomSelect(lv int32, opts map[string]interface{}) []int32 {
	if this.GetSlotByLv(lv) == nil {
		return nil
	}
	if this.Type != constant.CERI_CONF_TYPE_PAWN && !this.CheckLastSlotIsSelect(lv) {
		// 之前的槽位还未选择 当前槽位暂时不随机待选项 (士兵除外)
		return nil
	}
	var idMap map[int32]bool
	if opts == nil {
	} else if idMapFunc, ok := opts["idMap"].(func() map[int32]bool); ok {
		idMap = idMapFunc()
	}
	if idMap == nil {
		idMap = map[int32]bool{}
	}
	preIdMap := map[int32]bool{} // 上一次待选项中的id
	this.RLock()
	for _, m := range this.Map {
		if m.ID > 0 {
			idMap[m.ID] = true
		}
		if m.Lv == lv {
			for _, id := range m.SelectIds {
				preIdMap[id] = true
			}
		} else if m.ID <= 0 {
			for _, id := range m.SelectIds {
				idMap[id] = true
			}
		}
	}
	this.RUnlock()
	// 删除已经解锁 或者已经随机出来的
	ids := []int32{}
	switch this.Type {
	case constant.CERI_CONF_TYPE_POLICY:
		// 政策
		datas := array.Filter(config.GetJson("policy").Datas, func(m map[string]interface{}, _ int) bool {
			id := ut.Int32(m["id"])
			return !idMap[id] && !preIdMap[id] && checkRandomSlotNeedLv(m["need_build_lv"], lv)
		})
		ids = RandomSlotSelectIds(datas, 3)
	case constant.CERI_CONF_TYPE_EQUIP:
		_, isExclusive := constant.EXCLUSIVE_EQUIP_SLOT_CONF_MAP[lv]
		if isExclusive {
			// 专属
			var pawnMap = map[int32]bool{}
			if opts == nil {
			} else if pawnMapFunc, ok := opts["pawnMap"].(func() map[int32]bool); ok {
				pawnMap = pawnMapFunc()
			}
			datas := array.Filter(config.GetJson("equipBase").Datas, func(m map[string]interface{}, _ int) bool {
				return pawnMap[ut.Int32(m["exclusive_pawn"])]
			})
			ids = array.Map(datas, func(m map[string]interface{}, _ int) int32 { return ut.Int32(m["id"]) })
		} else {
			// 装备
			datas := array.Filter(config.GetJson("equipBase").Datas, func(m map[string]interface{}, _ int) bool {
				id := ut.Int32(m["id"])
				return !idMap[id] && !preIdMap[id] && ut.Int32(m["exclusive_pawn"]) == 0 && checkRandomSlotNeedLv(m["need_build_lv"], lv)
			})
			ids = RandomSlotSelectIds(datas, 3)
		}
	case constant.CERI_CONF_TYPE_PAWN:
		// 士兵 士兵待选项不用随机 直接从配置中取出对应建筑等级的
		datas := array.Filter(config.GetJson("pawnBase").Datas, func(m map[string]interface{}, _ int) bool {
			id := ut.Int32(m["id"])
			return !idMap[id] && ut.Int32(m["spawn_build_id"]) == constant.BARRACKS_BUILD_ID && ut.Int32(m["need_build_lv"]) == lv
		})
		ids = array.Map(datas, func(m map[string]interface{}, _ int) int32 { return ut.Int32(m["id"]) })
	default:
		log.Error("CeriRandomSelect Not Type, type: %v", this.Type)
		return nil
	}
	this.Lock()
	this.Map[lv].SelectIds = ids
	this.Unlock()
	return ids
}

func checkRandomSlotNeedLv(needLv interface{}, lv int32) bool {
	str := ut.String(needLv)
	return str == "" || array.Has(ut.StringToInt32s(str, ","), lv)
}

// 随机槽位待选项
func RandomSlotSelectIds(datas []map[string]interface{}, count int) []int32 {
	if len(datas) <= count {
		return array.Map(datas, func(m map[string]interface{}, _ int) int32 { return ut.Int32(m["id"]) })
	}
	ids, cnt := []int32{}, 0
	for cnt < 50 {
		cnt += 1
		arr := array.Map(datas, func(m map[string]interface{}, _ int) map[string]interface{} { return m })
		ok := false
		for i := 0; i < count && len(arr) > 0; i++ {
			index := ut.RandomIndexByWeight(arr)
			id := ut.Int32(arr[index]["id"])
			ids = append(ids, id)
			arr = append(arr[:index], arr[index+1:]...)
			ok = true
		}
		if ok {
			break
		}
		ids = []int32{}
	}
	return ids
}

// 获取指定id是否已研究
func (this *CeriSlotMap) IsUnlock(id int32) bool {
	this.RLock()
	defer this.RUnlock()
	for _, m := range this.Map {
		if m.ID == id {
			return true
		}
	}
	return false
}
