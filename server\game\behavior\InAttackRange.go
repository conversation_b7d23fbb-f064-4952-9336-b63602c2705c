package behavior

import (
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
)

// 是否在攻击范围
type InAttackRange struct {
	BaseCondition
}

func (this *InAttackRange) OnTick(dt int32) Status {
	at := this.target.GetAttackTarget()
	if at == nil {
		return FAILURE
	} else if this.checkInAttackRange(at.GetPoint()) {
		return SUCCESS
	}
	return FAILURE
}

func (this *InAttackRange) checkInAttackRange(target *ut.Vec2) bool {
	return helper.GetPointToPointDis(this.target.GetPoint(), target) <= this.target.GetEntity().GetAttackRange()
}
