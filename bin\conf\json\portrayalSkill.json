[{"id": 1, "value": "40,80", "suffix": "%", "target": "", "params": 0.1, "value_max": 1, "weight": 100}, {"id": 2, "value": "10,20", "suffix": "%", "target": "", "params": 5, "value_max": 1, "weight": 100}, {"id": 3, "value": "10,30", "suffix": "%", "target": 4, "params": "", "value_max": 1, "weight": 100}, {"id": 4, "value": "100,150", "suffix": "%", "target": 75, "params": "1.12,0.28", "value_max": 1, "weight": 100}, {"id": 5, "value": "50,100", "suffix": "%", "target": 12, "params": 4, "value_max": 1, "weight": 100}, {"id": 6, "value": "20,40", "suffix": "%", "target": 8, "params": 2, "value_max": 1, "weight": 100}, {"id": 7, "value": "5,15", "suffix": "", "target": 2, "params": 3, "value_max": 1, "weight": 100}, {"id": 8, "value": "50,100", "suffix": "%", "target": "", "params": 0.5, "value_max": 1, "weight": 100}, {"id": 9, "value": "50,100", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 10, "value": "5,10", "suffix": "", "target": 4, "params": 6, "value_max": 1, "weight": 100}, {"id": 11, "value": "40,70", "suffix": "%", "target": 7, "params": "", "value_max": 1, "weight": 100}, {"id": 12, "value": "20,50", "suffix": "", "target": "", "params": "0.1,0.1", "value_max": 1, "weight": 100}, {"id": 13, "value": "10,30", "suffix": "%", "target": 10, "params": "", "value_max": 1, "weight": 100}, {"id": 14, "value": "20,40", "suffix": "%", "target": 80, "params": "", "value_max": 1, "weight": 100}, {"id": 15, "value": "50,100", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 16, "value": "20,50", "suffix": "", "target": 8, "params": 0.01, "value_max": 1, "weight": 100}, {"id": 17, "value": "10,30", "suffix": "%", "target": 5, "params": "", "value_max": 1, "weight": 100}, {"id": 18, "value": "20,50", "suffix": "%", "target": 8, "params": "0.2,0.3", "value_max": 1, "weight": 100}, {"id": 19, "value": "10,30", "suffix": "%", "target": "", "params": "0.6,0.1", "value_max": 1, "weight": 100}, {"id": 20, "value": "20,50", "suffix": "%", "target": 30, "params": 0.2, "value_max": 1, "weight": 100}, {"id": 21, "value": "50,100", "suffix": "%", "target": 12, "params": 25, "value_max": 1, "weight": 100}, {"id": 22, "value": "10,20", "suffix": "", "target": 1, "params": 10, "value_max": 1, "weight": 100}, {"id": 23, "value": "15,30", "suffix": "%", "target": "", "params": 0.6, "value_max": 1, "weight": 100}, {"id": 24, "value": "30,50", "suffix": "", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 25, "value": "10,20", "suffix": "%", "target": "", "params": 630, "value_max": 1, "weight": 100}, {"id": 26, "value": "20,50", "suffix": "%", "target": 10, "params": "", "value_max": 1, "weight": 100}, {"id": 27, "value": "3,10", "suffix": "", "target": 3, "params": 5, "value_max": 1, "weight": 100}, {"id": 28, "value": "40,80", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 29, "value": "10,20", "suffix": "%", "target": 2, "params": 8, "value_max": 1, "weight": 100}, {"id": 30, "value": "50,100", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 31, "value": "10,30", "suffix": "%", "target": 25, "params": "0.9,0.7", "value_max": 1, "weight": 100}, {"id": 32, "value": "5,15", "suffix": "", "target": "", "params": 0.66, "value_max": 1, "weight": 100}, {"id": 33, "value": "10,30", "suffix": "%", "target": "", "params": "650,0.36,5016", "value_max": 1, "weight": 100}, {"id": 34, "value": "10,30", "suffix": "%", "target": 1, "params": 5, "value_max": 1, "weight": 100}, {"id": 35, "value": "20,50", "suffix": "%", "target": 2, "params": 8, "value_max": 1, "weight": 100}, {"id": 36, "value": "5,10", "suffix": "%", "target": 1, "params": "0.33,0.1", "value_max": 1, "weight": 100}, {"id": 37, "value": "150,200", "suffix": "", "target": "", "params": 4, "value_max": 1, "weight": 80}, {"id": 38, "value": "100,150", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 39, "value": "1,5", "suffix": "", "target": "", "params": "", "value_max": 1, "weight": 80}, {"id": 40, "value": "20,50", "suffix": "%", "target": "", "params": 1, "value_max": 1, "weight": 80}, {"id": 41, "value": "50,100", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 42, "value": "100,200", "suffix": "%", "target": "", "params": "0.33,0.44", "value_max": 1, "weight": 100}, {"id": 43, "value": "20,30", "suffix": "%", "target": 35, "params": "0.7,0.3", "value_max": 1, "weight": 100}, {"id": 44, "value": "10,20", "suffix": "", "target": "", "params": 5, "value_max": 1, "weight": 100}, {"id": 45, "value": "20,40", "suffix": "%", "target": "", "params": 0.5, "value_max": 1, "weight": 100}, {"id": 46, "value": "10,20", "suffix": "%", "target": "", "params": 10, "value_max": 1, "weight": 100}, {"id": 47, "value": "20,50", "suffix": "%", "target": 3, "params": 1.32, "value_max": 1, "weight": 100}, {"id": 48, "value": "15,30", "suffix": "%", "target": "", "params": "0.3,0.4", "value_max": 1, "weight": 100}, {"id": 49, "value": "20,50", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 50, "value": "10,30", "suffix": "%", "target": "", "params": "", "value_max": 1, "weight": 100}, {"id": 51, "value": "10,30", "suffix": "", "target": 30, "params": "", "value_max": 1, "weight": 100}, {"id": 52, "value": "10,20", "suffix": "", "target": "", "params": "0.33,0.44", "value_max": 1, "weight": 100}]