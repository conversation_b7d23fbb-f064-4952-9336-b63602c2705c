package config

import (
	"os"
	ut "slgsrv/utils"
	"strconv"
	"strings"

	"github.com/huyangv/vmqant/log"
)

type JsonConfData struct {
	Datas        []map[string]interface{}
	dataIdMap    map[int32]map[string]interface{}
	dataIdStrMap map[string]map[string]interface{}
}

func (this JsonConfData) Get(key string, val interface{}) []map[string]interface{} {
	arr := []map[string]interface{}{}
	for _, data := range this.Datas {
		if data[key] == val {
			arr = append(arr, data)
		}
	}
	return arr
}

func (this JsonConfData) GetById(id int32) map[string]interface{} {
	return this.dataIdMap[id]
}

var jsons = map[string]JsonConfData{}

func Load() {
	files, _ := os.ReadDir(ut.WorkDir() + "/bin/conf/json/")
	for _, f := range files {
		name := strings.Split(f.Name(), ".")[0]
		v := []interface{}{}
		if err := ut.<PERSON><PERSON><PERSON>(name, &v); err == "" {
			datas := []map[string]interface{}{}
			dataIdMap := map[int32]map[string]interface{}{}
			dataIdStrMap := map[string]map[string]interface{}{}
			for _, data := range v {
				item := data.(map[string]interface{})
				datas = append(datas, item)
				if item["id"] != nil {
					id, ok := item["id"].(int32)
					if ok {
						dataIdMap[id] = item
					} else if idFloat, ok := item["id"].(float64); ok {
						dataIdMap[int32(idFloat)] = item
					} else if idStr, ok := item["id"].(string); ok {
						id, e := strconv.Atoi(idStr)
						if e == nil {
							dataIdMap[int32(id)] = item
						} else {
							dataIdStrMap[idStr] = item
						}
					}
				}
			}
			jsons[name] = JsonConfData{
				Datas:        datas,
				dataIdMap:    dataIdMap,
				dataIdStrMap: dataIdStrMap,
			}
		} else {
			log.Error("load conf error! name=" + name + ", err=" + err)
		}
	}
	log.Info("load config done.")
}

func GetJson(name string) JsonConfData {
	return jsons[name]
}

func GetJsonData(name string, id int32) map[string]interface{} {
	return jsons[name].GetById(id)
}

func GetJsonDataByIdStr(name, idStr string) map[string]interface{} {
	return jsons[name].dataIdStrMap[idStr]
}
