package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

type WheelRecord struct {
	UID     string       `json:"uid" bson:"uid"`
	Items   []*g.TypeObj `json:"items" bson:"items"`
	Time    int64        `json:"time" bson:"time"`
	ID      int32        `json:"id" bson:"id"`
	NextMul int32        `json:"nextMul" bson:"next_mul"` // 下一次倍数
	NeedSid int32        `json:"needSid" bson:"need_sid"` // 需要在几区领取
	IsClaim bool         `json:"isClaim" bson:"is_claim"` // 是否领取
}

type WheelDataInfo struct {
	items   interface{}
	id      int32
	weight  int32
	mul     int32
	factor  int32
	needSid bool // 是否需要在对应区服领取
}

type WheelRandomAwardInfo struct {
	Time  int64        `json:"time" bson:"time"`
	Items []*g.TypeObj `json:"items" bson:"items"`
}

const (
	WHEEL_EVERYDAY_MAX_COUNT  = 10                 // 每日转动次数
	WHEEL_EVERYDAY_FREE_COUNT = 1                  // 每日免费转动次数
	WHEEL_INTERVAL_MAX_TIME   = ut.TIME_MINUTE * 5 // 转动间隔时间
)

// 转盘数据转为pb
func (this *User) WheelRecordsToPb() []*pb.WheelRecordInfo {
	return array.Map(this.WheelRecords, func(v *WheelRecord, _ int) *pb.WheelRecordInfo {
		return &pb.WheelRecordInfo{
			Uid:     v.UID,
			Id:      v.ID,
			NextMul: v.NextMul,
			NeedSid: v.NeedSid,
			IsClaim: v.IsClaim,
			Time:    v.Time,
			Items:   array.Map(v.Items, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() }),
		}
	})
}

func (this *User) ToWheelRandomAwardDB() map[int32][]*g.TypeObj {
	this.WheelRandomAwardLock.RLock()
	defer this.WheelRandomAwardLock.RUnlock()
	obj := map[int32][]*g.TypeObj{}
	for k, v := range this.WheelRandomAwardMap {
		obj[k] = array.Map(v, func(m *g.TypeObj, _ int) *g.TypeObj { return m.Clone() })
	}
	return obj
}

func (this *User) ToWheelRandomAwardRecordsDB() map[int32][]*WheelRandomAwardInfo {
	this.WheelRandomAwardRecordLock.RLock()
	defer this.WheelRandomAwardRecordLock.RUnlock()
	obj := map[int32][]*WheelRandomAwardInfo{}
	for k, v := range this.WheelRandomAwardRecords {
		obj[k] = array.Map(v, func(m *WheelRandomAwardInfo, _ int) *WheelRandomAwardInfo {
			return &WheelRandomAwardInfo{Time: m.Time, Items: array.Map(m.Items, func(t *g.TypeObj, _ int) *g.TypeObj { return t.Clone() })}
		})
	}
	return obj
}

// 返回随机记录
func (this *User) ToWheelRandomAwardRecordsPb(sid int32) []*pb.WheelRandomAwardRecordInfo {
	this.WheelRandomAwardRecordLock.RLock()
	defer this.WheelRandomAwardRecordLock.RUnlock()
	arr := []*pb.WheelRandomAwardRecordInfo{}
	if records := this.WheelRandomAwardRecords[sid]; records != nil {
		arr = array.Map(records, func(m *WheelRandomAwardInfo, _ int) *pb.WheelRandomAwardRecordInfo {
			return &pb.WheelRandomAwardRecordInfo{Time: int64(m.Time), Items: array.Map(m.Items, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() })}
		})
	}
	return arr
}

// 返回结果
func (this *User) ToWheelInfo(ok bool) *pb.WheelInfo {
	sid := this.GetPlaySid()
	time, count, freeCount := this.GetWheelNeedWaitTime(ok)
	randomAwards := this.GetWheelRandomAward(sid)
	return &pb.WheelInfo{
		WheelWaitTime:           time,
		WheelCurrCount:          this.WheelCurrCount,
		WheelResidueCount:       count,
		WheelFreeCount:          freeCount,
		WheelRandomAwards:       array.Map(randomAwards, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() }),
		WheelRandomAwardRecords: this.ToWheelRandomAwardRecordsPb(sid),
		WheelInRoomRunDay:       GetRoomRunDayByWheel(sid),
		FreeAdCount:             this.FreeAdCount,
	}
}

func (this *User) GetWheelRecord(uid string) *WheelRecord {
	if m := array.Find(this.WheelRecords, func(m *WheelRecord) bool { return m.UID == uid }); m != nil {
		return m
	}
	return nil
}

// 获取最后一次转动的倍数
func (this *User) GetLastWheelMul() int32 {
	cnt := len(this.WheelRecords)
	if cnt == 0 {
		return 1
	}
	return this.WheelRecords[cnt-1].NextMul
}

// 获取未领取的数量
func (this *User) GetWheelNotClaimCount() int {
	count := 0
	for _, m := range this.WheelRecords {
		this.UpdateWheelNeedSid(m)
		if !m.IsClaim && len(m.Items) > 0 && m.NeedSid >= 0 {
			count += 1
		}
	}
	return count
}

func (this *User) UpdateWheelNeedSid(data *WheelRecord) {
	playSid := this.GetPlaySid()
	if data.NeedSid <= 0 {
	} else if playSid == 0 || playSid != data.NeedSid {
		data.NeedSid = data.NeedSid * -1
	}
}

// 获取转动需要等待的时间
func (this *User) GetWheelNeedWaitTime(ok bool) (waitTime int64, count, freeCount int32) {
	now := time.Now().UnixMilli()
	// 获取剩余次数
	count = ut.MaxInt32(0, WHEEL_EVERYDAY_MAX_COUNT-this.WheelCurrCount)
	freeCount = ut.MaxInt32(0, WHEEL_EVERYDAY_FREE_COUNT-this.WheelDayFreeCount)
	if freeCount > 0 {
		return // 有免费次数 直接返回
	} else if count > 0 {
		if this.FreeAdCount > 0 { // 有免广告订阅 直接返回
			return
		}
		var maxTime int64 = ut.TIME_SECOND * 30
		waitTime = ut.MaxInt64(0, WHEEL_INTERVAL_MAX_TIME-(now-this.WheelBeginTime))
		if !ok && waitTime > maxTime { // 失败就只要30秒
			this.WheelBeginTime = maxTime - WHEEL_INTERVAL_MAX_TIME + now
			waitTime = maxTime
		}
	} else if this.NextToDayTime > now { // 需要明天来了
		waitTime = ut.MaxInt64(0, this.NextToDayTime-now)
	} else {
		this.CheckUpdateNextToDayTime(now, false, nil)
		return this.GetWheelNeedWaitTime(ok)
	}
	return
}

func NewWheelInfo(json map[string]interface{}, lastMul int32) *WheelDataInfo {
	id := ut.Int32(json["id"])
	weight := ut.Int32(json["weight"])
	mul := ut.Int32(json["mul"])
	if mul > 1 && lastMul >= 27 {
		weight = 0
	}
	return &WheelDataInfo{
		id:      id,
		weight:  weight,
		mul:     mul,
		items:   json["award"],
		factor:  ut.Int32(json["factor"]),
		needSid: ut.Int(json["need_sid"]) == 1,
	}
}

// 随机一个转盘奖励
func (this *User) GetWheelRandomAward(sid int32) []*g.TypeObj {
	this.WheelRandomAwardLock.RLock()
	awards := this.WheelRandomAwardMap[sid]
	this.WheelRandomAwardLock.RUnlock()
	if awards == nil {
		awards = g.StringToTypeObjs(slg.WHEEL_RANDOM_AWARD[ut.Random(0, len(slg.WHEEL_RANDOM_AWARD)-1)])
		this.WheelRandomAwardLock.Lock()
		this.WheelRandomAwardMap[sid] = awards
		this.WheelRandomAwardLock.Unlock()
		this.WheelRandomAwardRecordLock.Lock()
		records := this.WheelRandomAwardRecords[sid]
		if len(records) >= 3 {
			records = records[1:]
		}
		this.WheelRandomAwardRecords[sid] = append(records, &WheelRandomAwardInfo{Time: time.Now().UnixMilli(), Items: awards})
		this.WheelRandomAwardRecordLock.Unlock()
	}
	return awards
}

// 获取转动结果
func (this *User) WheelRet() (wheelInfo *pb.WheelInfo, wheelId int32, mul int32, err string) {
	lastMul := this.GetLastWheelMul()
	var totalWeight int32
	arr := []*WheelDataInfo{}
	if this.WheelSumCount == 0 { // 首次给点好的
		ids := []int32{4, 5, 7, 8}
		for _, id := range ids {
			data := NewWheelInfo(config.GetJsonData("wheel", id), lastMul)
			arr = append(arr, data)
			totalWeight += data.weight
		}
	} else {
		datas := config.GetJson("wheel").Datas
		for _, m := range datas {
			data := NewWheelInfo(m, lastMul)
			arr = append(arr, data)
			totalWeight += data.weight
		}
	}
	// fmt.Println(arr)
	info := arr[randomIndexByWeight(totalWeight, arr)]
	// fmt.Println(info.id)
	now := time.Now().UnixMilli()
	// 记录信息
	record := &WheelRecord{
		UID:  ut.ID(),
		ID:   info.id,
		Time: now,
	}
	if info.mul > 1 {
		record.NextMul = info.mul * lastMul
		record.IsClaim = true
	} else {
		record.NextMul = 1
		record.IsClaim = false
	}
	// 添加奖励
	items := this.getWheelItems(info)
	if lastMul > 1 { // 加上倍数
		for _, m := range items {
			m.Count *= lastMul
		}
	}
	record.Items = items
	record.NeedSid = ut.If(info.needSid, this.GetPlaySid(), 0)
	// 最多20条
	this.WheelRecords = append(this.WheelRecords, record)
	this.CheckRecordCount()
	// 记录数据和次数
	this.WheelSumCount += 1
	if this.WheelDayFreeCount < WHEEL_EVERYDAY_FREE_COUNT { // 先扣免费次数
		this.WheelDayFreeCount += 1
	} else {
		this.WheelCurrCount += 1
		if this.FreeAdCount > 0 { // 扣除免广告次数
			this.FreeAdCount--
		}
	}
	this.WheelCanGetRet = false
	// 返回结果
	wheelInfo = this.ToWheelInfo(true)
	wheelId = info.id
	mul = record.NextMul
	// 上报
	ta.Track(this.SID, this.UID, this.DistinctId, 0, "ta_wheelRet", map[string]interface{}{"wheel_id": info.id})
	return
}

func (this *User) getWheelItems(info *WheelDataInfo) []*g.TypeObj {
	if info.factor > 0 { // 根据系数算出来
		runDay := GetRoomRunDayByWheel(this.GetPlaySid())
		count := 30 * ut.MinInt32(runDay, 10) * info.factor
		return []*g.TypeObj{
			{Type: ctype.CEREAL, Count: count},
			{Type: ctype.TIMBER, Count: count},
			{Type: ctype.STONE, Count: count},
		}
	} else if info.factor == -1 { //-1表示随机奖励
		randItems := this.GetWheelRandomAward(this.GetPlaySid())
		items := []*g.TypeObj{}
		for _, v := range randItems {
			items = append(items, &g.TypeObj{Type: v.Type, Id: v.Id, Count: v.Count})
		}
		return items
	}
	return g.StringToTypeObjs(info.items)
}

// 最多20条
func (this *User) CheckRecordCount() {
	count := len(this.WheelRecords) // 防止多余20条
	i := 0
	for len(this.WheelRecords) > 20 && count > 0 {
		count -= 1
		m := this.WheelRecords[i]
		this.UpdateWheelNeedSid(m)
		if m.IsClaim || m.NeedSid < 0 {
			this.WheelRecords = append(this.WheelRecords[:i], this.WheelRecords[i+1:]...)
		} else {
			i += 1
		}
	}
}

func randomIndexByWeight(totalWeight int32, arr []*WheelDataInfo) int {
	cnt := len(arr)
	if cnt == 0 {
		return -1
	}
	offset := ut.RandomInt32(0, totalWeight-1)
	for i := 0; i < cnt; i++ {
		val := arr[i].weight
		if val == 0 {
			continue
		} else if offset < val {
			return i
		} else {
			offset -= val
		}
	}
	return ut.Random(0, cnt-1)
}
