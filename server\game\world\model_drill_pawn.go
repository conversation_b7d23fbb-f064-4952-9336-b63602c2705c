package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"time"

	"github.com/sasha-s/go-deadlock"
)

type DrillPawnQueueMap struct {
	deadlock.RWMutex
	Map    map[int32]*AreaDrillPawnQueue
	RbTree *ut.RbTreeContainer[*DrillPawnInfo]
}

type AreaDrillPawnQueue struct {
	Index int32
	Map   map[string][]*DrillPawnInfo
}

// 招募士兵信息
type DrillPawnInfo struct {
	UID  string       `json:"uid"`
	BUid string       `json:"b_uid"` //建筑uid
	AUid string       `json:"a_uid"` //军队uid
	Cost []*g.TypeObj `json:"cost"`  //需要的费用

	StartTime int64 `json:"start_time"` //开始时间
	Index     int32 `json:"a_index"`    //区域位置
	Id        int32 `json:"id"`         //士兵id
	Lv        int32 `json:"lv"`         //招募等级
	NeedTime  int32 `json:"need_time"`  //需要时间
}

func NewDrillPawnInfo(index int32, buid string, auid string, id, lv int32, needTime int32) *DrillPawnInfo {
	return &DrillPawnInfo{
		UID:       ut.ID(),
		Index:     index,
		BUid:      buid,
		AUid:      auid,
		Id:        id,
		Lv:        lv,
		StartTime: 0,
		NeedTime:  needTime,
	}
}

func NewAareDrillPawnQueue(index int32) *AreaDrillPawnQueue {
	return &AreaDrillPawnQueue{Map: map[string][]*DrillPawnInfo{}, Index: index}
}

func (this *DrillPawnInfo) ToPb() *pb.DrillPawnInfo {
	return &pb.DrillPawnInfo{
		Uid:         this.UID,
		Index:       this.Index,
		Buid:        this.BUid,
		Auid:        this.AUid,
		Id:          this.Id,
		Lv:          this.Lv,
		NeedTime:    this.NeedTime,
		SurplusTime: int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0)),
	}
}

func (this *DrillPawnQueueMap) ToPb(index int32) map[string]*pb.DrillPawnInfoList {
	ret := map[string]*pb.DrillPawnInfoList{}
	obj := this.Map[index]
	if obj == nil {
		return ret
	}
	for key, arr := range obj.Map {
		list := &pb.DrillPawnInfoList{}
		for _, m := range arr {
			list.List = append(list.List, m.ToPb())
		}
		ret[key] = list
	}
	return ret
}

func (this *Model) ToDrillPawnQueueDB() []*DrillPawnInfo {
	this.DrillPawnQueues.RLock()
	defer this.DrillPawnQueues.RUnlock()
	list := []*DrillPawnInfo{}
	for _, v := range this.DrillPawnQueues.Map {
		for _, queue := range v.Map {
			list = append(list, queue...)
		}
	}
	return list
}

func (this *Model) ToDrillPawnQueuePb(index int32) map[string]*pb.DrillPawnInfoList {
	this.DrillPawnQueues.RLock()
	defer this.DrillPawnQueues.RUnlock()
	return this.DrillPawnQueues.ToPb(index)
}

func (this *Model) GetDrillPawnQueue(index int32, buid string) []*DrillPawnInfo {
	this.DrillPawnQueues.RLock()
	defer this.DrillPawnQueues.RUnlock()
	if m := this.DrillPawnQueues.Map[index]; m != nil {
		return m.Map[buid]
	}
	return nil
}

// 队列是否满了
func (this *Model) IsDrillPawnQueueFull(index int32, buid string, drillQueueMaxCnt int32) bool {
	queue := this.GetDrillPawnQueue(index, buid)
	return queue != nil && int32(len(queue)) >= drillQueueMaxCnt
}

// 获取招募信息
func (this *Model) GetDrillPawnQueueInfo(index int32, buid string, uid string) *DrillPawnInfo {
	queue := this.GetDrillPawnQueue(index, buid)
	if queue == nil {
		return nil
	}
	for _, m := range queue {
		if m.UID == uid {
			return m
		}
	}
	return nil
}

// 添加到队列
func (this *Model) PutDrillPawnQueue(index int32, buid string, armyUid string, id, lv, needTime int32, cd float64, cost []*g.TypeObj) {
	this.DrillPawnQueues.Lock()
	defer this.DrillPawnQueues.Unlock()
	m := this.DrillPawnQueues.Map[index]
	if m == nil {
		m = NewAareDrillPawnQueue(index)
		this.DrillPawnQueues.Map[index] = m
	}
	queue := m.Map[buid]
	if queue == nil {
		queue = []*DrillPawnInfo{}
	}
	if eff := this.GetAreaBuildEffect(index, buid)[effect.XL_CD]; eff != nil {
		cd += eff.Value
	}
	needTime = int32(ut.Max(3000, int(float64(needTime)*1000*(1.0-cd*0.01)/float64(slg.GetDrillSpeedUp()))))
	info := NewDrillPawnInfo(index, buid, armyUid, id, lv, needTime)
	info.Cost = cost
	queue = append(queue, info)
	m.Map[buid] = queue
	// 设置第一个的开始时间
	if len(queue) == 1 {
		info.StartTime = time.Now().UnixMilli()
		this.DrillPawnQueues.RbTree.AddElement(int(info.StartTime+int64(info.NeedTime)), info)
	}
}

// 取消招募
func (this *Model) CancelDrillPawnQueue(index int32, buid string, uid string) *DrillPawnInfo {
	this.DrillPawnQueues.Lock()
	defer this.DrillPawnQueues.Unlock()
	obj := this.DrillPawnQueues.Map[index]
	if obj == nil {
		return nil
	}
	queue := obj.Map[buid]
	if queue == nil {
		return nil
	}
	for i, m := range queue {
		if m.UID == uid {
			queue = append(queue[:i], queue[i+1:]...)
			if len(queue) == 0 {
				delete(obj.Map, buid)
			} else if m.StartTime > 0 {
				queue[0].StartTime = time.Now().UnixMilli()
				obj.Map[buid] = queue
			} else {
				obj.Map[buid] = queue
			}
			if m.StartTime > 0 {
				// 取消的是正在招募中的
				this.DrillPawnQueues.RbTree.RemoveElement(int(m.StartTime+int64(m.NeedTime)), m)

				if len(queue) > 0 { //队列非空，继续招募
					next := queue[0]
					this.DrillPawnQueues.RbTree.AddElement(int(next.StartTime+int64(next.NeedTime)), next)
				}
			}
			return m
		}
	}
	return nil
}

// 检测更新士兵招募队列
func (this *Model) CheckUpdateDrillPawnQueue() {
	now := time.Now().UnixMilli()
	// 遍历之前先检测最快的招募是否结束
	this.DrillPawnQueues.RLock()
	minTreeNode := this.DrillPawnQueues.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.DrillPawnQueues.RUnlock()
		return
	} else {
		minEndTime := ut.Int64(minTreeNode.Key)
		if minEndTime > now {
			// 结束时间最快的招募都没结束
			this.DrillPawnQueues.RUnlock()
			return
		}
	}
	this.DrillPawnQueues.RUnlock()

	completeList := []*DrillPawnInfo{}
	notifyMap := map[int32]map[string]*pb.DrillPawnInfoList{}
	queueFinishMap := map[string]int32{} //队列完成map 用于离线消息通知 k=>buid v=>index

	this.DrillPawnQueues.Lock()
	for m := this.DrillPawnQueues.RbTree.FindMinElements(); m != nil; m = this.DrillPawnQueues.RbTree.FindMinElements() {
		endTime := ut.Int64(m.Key)
		if endTime > now { // 结束时间最快的招募都没完成
			break
		}
		this.DrillPawnQueues.RbTree.Remove(m.Key) //先删除红黑树

		infoList := m.Value.([]*DrillPawnInfo)
		for _, info := range infoList {
			index := info.Index
			objMap := this.DrillPawnQueues.Map[index]
			queue := objMap.Map[info.BUid]

			queue = append(queue[:0], queue[1:]...) //删除第一个
			objMap.Map[info.BUid] = queue           //更新数据

			if len(queue) > 0 { //招募队列非空，继续招募
				next := queue[0]
				next.StartTime = info.StartTime + int64(info.NeedTime)
				this.DrillPawnQueues.RbTree.AddElement(int(next.StartTime+int64(next.NeedTime)), next)
			} else {
				queueFinishMap[info.BUid] = index
				delete(objMap.Map, info.BUid)
			}

			notifyMap[index] = this.DrillPawnQueues.ToPb(index)

			// 招募完成 添加到待通知列表
			completeList = append(completeList, info)
		}
	}
	this.DrillPawnQueues.Unlock()

	// 通知招募完成
	if len(notifyMap) > 0 {
		for index, m := range notifyMap {
			if area := this.GetArea(index); area != nil {
				this.room.PutPlayerNotifyQueue(constant.NQ_PAWN_DRILL_QUEUE, area.Owner, &pb.OnUpdatePlayerInfoNotify{
					Data_18: m,
				})
			}
		}
	}

	// 实际进行招募
	if len(completeList) > 0 {
		for _, m := range completeList {
			this.AreaPawnDrillComplete(m.Index, m.AUid, m.Id, m.Lv)
		}
	}

	// 离线消息通知
	if len(queueFinishMap) > 0 {
		for buid, index := range queueFinishMap {
			if area := this.GetArea(index); area != nil {
				if area.Owner == "" {
					continue
				}
				if build := area.GetBuildByUID(buid); build != nil {
					this.OfflineNotify(area.Owner, constant.OFFLINE_MSG_TYPE_DRILL, ut.String(build.Id))
				}
			}
		}
	}
}

// 删除招募士兵
func (this *Model) RemoveDrillPawn(index int32) {
	this.DrillPawnQueues.Lock()
	defer this.DrillPawnQueues.Unlock()
	m := this.DrillPawnQueues.Map[index]
	if m != nil {
		// 从红黑树删除
		for _, queue := range m.Map {
			if len(queue) > 0 {
				first := queue[0]
				this.DrillPawnQueues.RbTree.RemoveElement(int(first.StartTime+int64(first.NeedTime)), first)
			}
		}
		delete(this.DrillPawnQueues.Map, index)
	}
}
