package helper

import (
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
	"time"
)

// 下标转成点 (使用对象池)
func IndexToPoint(index int32, size *ut.Vec2) *ut.Vec2 {
	return ut.GetVec2FromPool(index%size.X, index/size.X)
}

// 点转成下标
func PointToIndex(x, y int32, size *ut.Vec2) int32 {
	return y*size.X + x
}

// 点转成下标
func Vec2ToIndex(p *ut.Vec2, size *ut.Vec2) int32 {
	return PointToIndex(p.X, p.Y, size)
}

// 点是否在地图中内
func IsPointInMap(x, y int32, size *ut.Vec2) bool {
	return x >= 0 && x < size.X && y >= 0 && y < size.Y
}

func arrayHasPoint(list []*ut.Vec2, x, y int32) int {
	for i, point := range list {
		if point.Equal2(x, y) {
			return i
		}
	}
	return -1
}

// 根据网格坐标获取像素坐标
func GetPixelByPoint(point *ut.Vec2) *ut.Vec2 {
	p := point.Mul(constant.TILE_SIZE)
	p.X += constant.TILE_SIZE_HALF
	p.Y += constant.TILE_SIZE_HALF
	return p
}

// 根据像素位置获取网格坐标
func GetPointByPixel(pos *ut.Vec2) *ut.Vec2 {
	return ut.GetVec2FromPool(pos.X/constant.TILE_SIZE, pos.Y/constant.TILE_SIZE)
}

// 获取一个区域的外圈
func GetPointsOuter(index int32, size *ut.Vec2, mapSize *ut.Vec2) []int32 {
	start := IndexToPoint(index, mapSize)
	arr := []int32{}
	for x := 0; int32(x) < size.X; x++ {
		for y := 0; int32(y) < size.Y; y++ {
			// 上方向
			p1 := ut.GetVec2FromPool(int32(x), int32(y+1))
			if p1.Y >= size.Y && (start.Y+p1.Y) < mapSize.Y { //上
				arr = addOuterPoint(arr, p1, start, mapSize)
			}
			ut.PutVec2ToPool(p1)

			// 下方向
			p2 := ut.GetVec2FromPool(int32(x), int32(y-1))
			if p2.Y < 0 && (start.Y+p2.Y) >= 0 { //下
				arr = addOuterPoint(arr, p2, start, mapSize)
			}
			ut.PutVec2ToPool(p2)

			// 左方向
			p3 := ut.GetVec2FromPool(int32(x-1), int32(y))
			if p3.X < 0 && (start.X+p3.X) >= 0 { //左
				arr = addOuterPoint(arr, p3, start, mapSize)
			}
			ut.PutVec2ToPool(p3)

			// 右方向
			p4 := ut.GetVec2FromPool(int32(x+1), int32(y))
			if p4.X >= size.X && (start.X+p4.X) < mapSize.X { //右
				arr = addOuterPoint(arr, p4, start, mapSize)
			}
			ut.PutVec2ToPool(p4)
		}
	}
	ut.PutVec2ToPool(start)
	return arr
}
func addOuterPoint(arr []int32, p, start *ut.Vec2, mapSize *ut.Vec2) []int32 {
	idx := Vec2ToIndex(p.AddSelf(start), mapSize)
	if !array.Has(arr, idx) {
		return append(arr, idx)
	}
	return arr
}

// 获取不规则区域的外圈
func GetIrregularPointsOuter(indexs []int32, mapSize *ut.Vec2) []int32 {
	if len(indexs) == 0 {
		return []int32{}
	}
	size := ut.GetVec2FromPool(1, 1)
	defer ut.PutVec2ToPool(size)

	if len(indexs) == 1 {
		return GetPointsOuter(indexs[0], size, mapSize)
	}
	obj := map[int32]bool{}
	for _, index := range indexs {
		obj[index] = true
	}
	arr := []int32{}
	for _, index := range indexs {
		outers := GetPointsOuter(index, size, mapSize)
		for _, idx := range outers {
			if !obj[idx] {
				obj[idx] = true
				arr = append(arr, idx)
			}
		}
	}
	return arr
}

// 获取指定点位指定所有曼哈顿距离内的所有点
func GetManhattanPoints(index, dis int32, mapSize *ut.Vec2) []int32 {
	var arr []int32
	if dis < 0 {
		return arr // 距离为负时返回空
	}

	point := IndexToPoint(index, mapSize)
	defer ut.PutVec2ToPool(point) // 回收point对象
	px, py := point.X, point.Y

	for d := int32(0); d <= dis; d++ {
		// 遍历 x 方向的偏移量（从 -d 到 d）
		for xDiff := -d; xDiff <= d; xDiff++ {
			yAbs := d - ut.AbsInt32(xDiff) // y 方向的绝对偏移量
			if yAbs < 0 {
				continue // 无效偏移量
			}

			if yAbs == 0 {
				// 添加点 (xDiff, 0)
				p := ut.GetVec2FromPool(px+xDiff, py)
				arr = append(arr, Vec2ToIndex(p, mapSize))
				ut.PutVec2ToPool(p)
			} else {
				// 添加点 (xDiff, yAbs) 和 (xDiff, -yAbs)
				p1 := ut.GetVec2FromPool(px+xDiff, py+yAbs)
				arr = append(arr, Vec2ToIndex(p1, mapSize))
				ut.PutVec2ToPool(p1)

				p2 := ut.GetVec2FromPool(px+xDiff, py-yAbs)
				arr = append(arr, Vec2ToIndex(p2, mapSize))
				ut.PutVec2ToPool(p2)
			}
		}
	}

	return arr
}

// 根据大小生成站位点
func GenPointsBySize(size *ut.Vec2, start *ut.Vec2) []*ut.Vec2 {
	arr := []*ut.Vec2{}
	for x := 0; int32(x) < size.X; x++ {
		for y := 0; int32(y) < size.Y; y++ {
			arr = append(arr, ut.NewVec2(start.X+int32(x), start.Y+int32(y)))
		}
	}
	return arr
}

// 获取两个点最近的
func GetMinDisPoint(sps, tps []*ut.Vec2) (*ut.Vec2, *ut.Vec2) {
	t, s := tps[0].Clone(), sps[0].Clone()
	d := GetPointToPointDis(s, t)
	for _, s1 := range sps {
		d1 := GetPointToPointDis(s1, t)
		if d1 < d {
			d = d1
			s.Set(s1)
		}
	}
	for _, t1 := range tps {
		d1 := GetPointToPointDis(t1, s)
		if d1 < d {
			d = d1
			t.Set(t1)
		}
	}
	return s, t
}

// 获取最近的
func GetMinDisIndex(point *ut.Vec2, points []*ut.Vec2) int32 {
	if len(points) == 0 {
		return -1
	} else if len(points) == 1 {
		return 0
	}
	var idx int32 = 0
	d := GetPointToPointDis(points[idx], point)
	for i, p := range points {
		l := GetPointToPointDis(p, point)
		if l < d {
			d = l
			idx = int32(i)
		}
	}
	return idx
}
func GetMinDis(point *ut.Vec2, points []*ut.Vec2) *ut.Vec2 {
	if i := GetMinDisIndex(point, points); i != -1 {
		return points[i]
	}
	return point
}

// 获取方向 0.上 1.右 2.下 3.左
func GetDirByPoint(spoint, epoint *ut.Vec2) int32 {
	point := epoint.Sub(spoint)
	if point.Y != 0 && ut.AbsInt32(point.Y) >= ut.AbsInt32(point.X) {
		if point.Y >= 0 {
			return 2
		} else {
			return 0
		}
	} else if point.X >= 0 {
		return 3
	} else {
		return 1
	}
}
func GetDirByIndex(sindex, eindex int32, size *ut.Vec2) int32 {
	spoint := IndexToPoint(sindex, size)
	epoint := IndexToPoint(eindex, size)
	defer func() {
		ut.PutVec2ToPool(spoint)
		ut.PutVec2ToPool(epoint)
	}()
	return GetDirByPoint(spoint, epoint)
}

// 获取两点的距离
func GetPointToPointDis(a *ut.Vec2, b *ut.Vec2) int32 {
	return ut.AbsInt32(a.X-b.X) + ut.AbsInt32(a.Y-b.Y)
}
func GetIndexToIndexDis(a, b int32, size *ut.Vec2) int32 {
	pointA := IndexToPoint(a, size)
	pointB := IndexToPoint(b, size)
	defer func() {
		ut.PutVec2ToPool(pointA)
		ut.PutVec2ToPool(pointB)
	}()
	return GetPointToPointDis(pointA, pointB)
}

// 获取指定点的一个相邻点顺延方向x距离的点
func GetIndexByNextAndDis(start, next, dis int32, size *ut.Vec2) int32 {
	startP := IndexToPoint(start, size)
	nextP := IndexToPoint(next, size)
	defer func() {
		ut.PutVec2ToPool(startP)
		ut.PutVec2ToPool(nextP)
	}()

	diffX := nextP.X - startP.X
	diffY := nextP.Y - startP.Y
	if diffX != 0 {
		nextP.X += diffX * dis
	} else {
		nextP.Y += diffY * dis
	}
	if nextP.X >= size.X || nextP.Y >= size.Y {
		return -1
	}
	return PointToIndex(nextP.X, nextP.Y, size)
}

// 获取两个点的距离排序值 值越大越近
func GetPointDisSortVal(point *ut.Vec2, target *ut.Vec2) int32 {
	dis := GetPointToPointDis(target, point)
	mag := point.Sub(target).MagSqr()
	weight := int32(1)
	weight = weight*100 + (99 - dis)
	weight = weight*1000 + (999 - mag)
	weight = weight*100 + target.X
	return weight
}

// 获取主位置
func GetMainPoints(areaSize *ut.Vec2, buildSize *ut.Vec2, buildOrigin *ut.Vec2) []*ut.Vec2 {
	cx, cy := areaSize.X/2, areaSize.Y/2
	if buildSize.X*buildSize.Y < 9 {
		return []*ut.Vec2{ut.NewVec2(cx, cy)}
	}
	points := []*ut.Vec2{ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0)}
	points[0].Init(cx, buildOrigin.Y+buildSize.Y-1) //0.上
	points[1].Init(buildOrigin.X+buildSize.X-1, cy) //1.右
	points[2].Init(cx, buildOrigin.Y)               //2.下
	points[3].Init(buildOrigin.X, cy)               //3.左
	return points
}

// 获取主位置
func GetMainPointsByBoss(buildSize *ut.Vec2, buildOrigin *ut.Vec2) []*ut.Vec2 {
	points := []*ut.Vec2{}
	for x := int32(0); x < buildSize.X; x++ {
		points = append(points, ut.NewVec2(buildOrigin.X+x, buildOrigin.Y))
		points = append(points, ut.NewVec2(buildOrigin.X+x, buildOrigin.Y+buildSize.Y-1))
	}
	for y := int32(1); y < buildSize.Y-1; y++ {
		points = append(points, ut.NewVec2(buildOrigin.X, buildOrigin.Y+y))
		points = append(points, ut.NewVec2(buildOrigin.X+buildSize.X-1, buildOrigin.Y+y))
	}
	return points
}

// 获取关口位置
func GetPassPoints(size *ut.Vec2) []*ut.Vec2 {
	points := []*ut.Vec2{ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0)}
	cx, cy := size.X/2, size.Y/2
	mx, my := size.X-1, size.Y-1
	points[0].Init(cx, my) //0.上
	points[1].Init(mx, cy) //1.右
	points[2].Init(cx, 0)  //2.下
	points[3].Init(0, cy)  //3.左
	return points
}

// 获取城门口
func GetDoorPoints(areaSize *ut.Vec2, buildSize *ut.Vec2, buildOrigin *ut.Vec2) []*ut.Vec2 {
	cx, cy := areaSize.X/2, areaSize.Y/2
	points := []*ut.Vec2{ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0), ut.NewVec2(0, 0)}
	points[0].Init(cx, buildOrigin.Y+buildSize.Y) //0.上
	points[1].Init(buildOrigin.X+buildSize.X, cy) //1.右
	points[2].Init(cx, buildOrigin.Y-1)           //2.下
	points[3].Init(buildOrigin.X-1, cy)           //3.左
	return points
}

// 获取禁止摆放士兵的位置
func GetBanPlacePawnPoints(areaSize *ut.Vec2) map[string]bool {
	cx, cy := areaSize.X/2, areaSize.Y/2
	points := []*ut.Vec2{}
	points = append(points, GenPointsBySize(ut.NewVec2(5, 2), ut.NewVec2(cx-2, areaSize.Y-2))...) //上
	points = append(points, GenPointsBySize(ut.NewVec2(2, 5), ut.NewVec2(areaSize.X-2, cy-2))...) //右
	points = append(points, GenPointsBySize(ut.NewVec2(5, 2), ut.NewVec2(cx-2, 0))...)            //下
	points = append(points, GenPointsBySize(ut.NewVec2(2, 5), ut.NewVec2(0, cy-2))...)            //左
	obj := map[string]bool{}
	for _, point := range points {
		obj[point.ID()] = true
	}
	return obj
}

// 获取战斗区域位置
func GetBattlePoints(areaSize *ut.Vec2, buildSize *ut.Vec2, buildOrigin *ut.Vec2, pass []*ut.Vec2) []*ut.Vec2 {
	arr := []*ut.Vec2{}
	bulidPoints := GenPointsBySize(buildSize, buildOrigin) //建造所有位置
	for x := 0; int32(x) < areaSize.X; x++ {
		for y := 0; int32(y) < areaSize.Y; y++ {
			point := ut.NewVec2(int32(x), int32(y))
			if array.Some(pass, func(m *ut.Vec2) bool { return m.Equals(point) }) || array.Some(bulidPoints, func(m *ut.Vec2) bool { return m.Equals(point) }) {
				continue
			}
			arr = append(arr, point)
		}
	}
	return arr
}

// 获取士兵的位置 根据城门口开始
func GetPawnPointsByDoor(points map[string]bool, size *ut.Vec2, doorPoints []*ut.Vec2, dir, count int32) []*ut.Vec2 {
	i := 0
	list := []*ut.Vec2{}
	for count > 0 && i < 4 {
		arr := GetPawnPointsByDoorOne(points, size, doorPoints[dir], dir, count)
		count -= int32(len(arr))
		list = append(list, arr...)
		dir = int32(ut.LoopValue(int(dir+1), 4))
		i += 1
	}
	return list
}

// 一个方向
func GetPawnPointsByDoorOne(points map[string]bool, size *ut.Vec2, start *ut.Vec2, dir int32, count int32) []*ut.Vec2 {
	arr := []*ut.Vec2{}
	cnt := ut.NewVec2(0, 0)
	half := size.Div(2)
	sd := ut.If(dir >= 2, int32(-1), int32(1))
	if dir == 0 {
		half.Y = size.Y - start.Y
	} else if dir == 1 {
		half.X = size.X - start.X
	} else if dir == 2 {
		half.Y = start.Y
	} else if dir == 3 {
		half.X = start.X
	}
	var o int32 //计数
	var v int32 //变量
	var i, pLen int32
	pLen = size.X*size.Y + 4
	for int32(len(arr)) < count && i < pLen {
		i += 1
		point := start.Add(cnt)
		key := point.Join("_")
		if points[key] {
			points[key] = false
			arr = append(arr, point)
		}
		o += 1
		d := int32(1)
		if o%2 != 0 {
			d = -1
			v += 1
		}
		if dir%2 == 0 {
			if v <= half.X {
				cnt.X = v * d
			} else {
				y := ut.AbsInt32(cnt.Y)
				if y < half.Y {
					cnt.Y = (y + 1) * sd
				} else {
					sd *= -1
					cnt.Y = sd
					half.Y = size.Y - half.Y - 1
				}
				cnt.X, o, v = 0, 0, 0
			}
		} else {
			if v <= half.Y {
				cnt.Y = v * d
			} else {
				x := ut.AbsInt32(cnt.X)
				if x < half.X {
					cnt.X = (x + 1) * sd
				} else {
					sd *= -1
					cnt.X = sd
					half.X = size.X - half.X - 1
				}
				cnt.Y, o, v = 0, 0, 0
			}
		}
	}
	return arr
}

// 获取移动需要的时间
func GetMoveNeedTime(points []*ut.Vec2, velocity int32) int32 {
	if len(points) <= 1 {
		return 0
	}
	// 获取总距离
	point, length := GetPixelByPoint(points[0]), int32(0)
	for i, l := 1, len(points); i < l; i++ {
		next := GetPixelByPoint(points[i])
		length += int32(point.Sub(next).Mag() * 1000.0)
		point = next
	}
	// 计算需要时间
	time := length / velocity
	// cc.log(points.join2(m => m.Join(), '|') + ' = ' + Math.floor(len * 0.001) + ', time=' + time + 'ms')
	return time
}

// 当前是否攻击时间段
func IsCanOccupyTime() bool {
	return checkInTimeRange(slg.GetCanOccupyTime())
}

// 不可加入联盟的时间
func IsNotJoinAlliTime() bool {
	return checkInTimeRange(slg.GetNotJoinAlliTime())
}

// 当前是否无主遗迹攻击时间段
func IsCanOccupyAncientTime() bool {
	return checkInTimeRange(constant.ANCIENT_OCCUPY_TIME)
}

// 检测是否在该时间段内
func checkInTimeRange(arr []int) bool {
	if len(arr) != 4 {
		return false
	}
	now := time.Now()
	hour := now.Hour()
	min := now.Minute()
	nowTotalMin := hour*60 + min
	startTotalMin := arr[0]*60 + arr[1]
	endTotalMin := arr[2]*60 + arr[3]
	return nowTotalMin >= startTotalMin && nowTotalMin <= endTotalMin
}

// 如果只有2个的时候 会自动加上年份
func CheckActivityAutoDate(startTime string, endTime string) bool {
	year := time.Now().Year()
	if startTime != "" && len(strings.Split(startTime, "-")[0]) != 4 {
		startTime = ut.Itoa(year) + "-" + startTime
	}
	if endTime != "" && len(strings.Split(endTime, "-")[0]) != 4 {
		startArr := strings.Split(startTime, "-")
		endArr := strings.Split(endTime, "-")
		if len(startArr) > 1 && ut.Int(endArr[0]) < ut.Int(startArr[1]) {
			year += 1
		}
		endTime = ut.Itoa(year) + "-" + endTime
	}
	// fmt.Println(startTime, endTime)
	return CheckActivityDate(startTime, endTime)
}

// 检查是否在活动时间内，不保证安全
func CheckActivityDate(startTime string, endTime string) bool {
	if startTime == "" || endTime == "" {
		return false
	}
	return CheckCommunityActivityDate(startTime, 0) && !CheckCommunityActivityDate(endTime, 0)
}

func CheckCommunityActivityDate(date string, offset int) bool {
	arr := strings.Split(date, "-")
	cnt := len(arr)
	if cnt < 2 {
		return false
	}
	now := time.Now()
	startYear, startMonth, startDay, h, m, s := arr[0], arr[1], "00", "00", "00", "00"
	if cnt > 2 {
		startDay = arr[2]
	}
	if cnt > 3 {
		h = arr[3]
	}
	if cnt > 4 {
		m = arr[4]
	}
	if cnt > 5 {
		s = arr[5]
	}
	if cnt == 2 {
		startDay = startMonth
		startMonth = startYear
		startYear = ut.Itoa(now.Year())
	} else if startYear == "" || startYear == "y" {
		startYear = ut.Itoa(now.Year())
	}
	date = startYear + "-" + startMonth + "-" + startDay + " " + h + ":" + m + ":" + s
	t, _ := time.ParseInLocation("2006-01-02 15:04:05", date, time.Local)
	// fmt.Println(t)
	nowTime := int(now.UnixNano() / 1e6)
	// fmt.Println(now)
	dateTime := int(t.UnixNano()/1e6) + offset*ut.TIME_DAY
	return nowTime >= dateTime
}

// 改变市场资源数量
func ChangeNumByMerchantCap(res *g.TypeObj) int32 {
	cap := ut.MaxInt32(constant.RES_TRANSIT_CAP[res.Type], 1)
	return res.Count * cap
}

// 检测是否可以召唤宠物
func CheckCanSummonPet(id int32) bool {
	json := config.GetJsonData("pawnBase", id)
	return json != nil && ut.Int(json["velocity"]) > 0
}
