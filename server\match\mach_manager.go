package match

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 物理机信息
type Mach struct {
	Addr string `bson:"ip"`   //物理机地址
	Name string `bson:"name"` //名字

	SidList     []int32 `bson:"sid_list"`     //已开启游戏服sid列表
	ServerTypes []uint8 `bson:"server_types"` //可开启游戏服类型列表 为空则不限制类型
	StoppedList []int32 //已停机的游戏服sid列表

	lock deadlock.RWMutex

	MaxCap       int32 `bson:"max_cap"`       //最大容量
	Status       int8  `bson:"status"`        //状态
	UpdateStatus int8  `bson:"update_status"` //更新状态
}

// 游戏服物理机map
type MachsInfo struct {
	MachsMap *ut.MapLock[string, *Mach]
}

// 大厅服物理机map
type LobbyMachsInfo struct {
	MachsMap *ut.MapLock[string, *LobbyMach]
}

// 辅助服物理机map
type SpuMachsInfo struct {
	MachsMap *ut.MapLock[string, *SupMach]
}

// 大厅服物理机信息
type LobbyMach struct {
	Addr string `bson:"ip"`   // 物理机地址
	Name string `bson:"name"` // 名字

	Id        int32 `bson:"id"` // 服务器id
	UserNum   int32 // 内存中玩家数量
	UserCount int32 // 停机前玩家数量
	SaveCount int32 // 已保存玩家数量

	Status       int8 `bson:"status"`        // 状态
	UpdateStatus int8 `bson:"update_status"` // 更新状态
	Abandon      bool `bson:"abandon"`       // 是否停用
}

// 更新物理机状态
func (this *LobbyMach) UpdateMachStatus(status int8) {
	this.Status = status
	db_lobby_machs.UpdateLobbyMachDb(this)
}

// 更新物理机代码
func (this *LobbyMach) HandleUpdateCode() {
	go func() {
		_, err := ut.SshExcuteShell(this.Addr, slg.GIT_UPDATE_BASH)
		if err != nil {
			log.Error("httpLobbyMachGitUpdate err: %v", err)
		}
		// 更新完成
		this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
		db_lobby_machs.UpdateLobbyMachDb(this)
		log.Info("httpLobbyMachGitUpdate finifsh")
	}()
	this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATING
	db_lobby_machs.UpdateLobbyMachDb(this)
}

var (
	machs      = &MachsInfo{MachsMap: ut.NewMapLock[string, *Mach]()}           //游戏服物理机map
	lobbyMachs = &LobbyMachsInfo{MachsMap: ut.NewMapLock[string, *LobbyMach]()} //大厅服物理机map
	supMachs   = &SpuMachsInfo{MachsMap: ut.NewMapLock[string, *SupMach]()}     //辅助服物理机map
)

// 物理机代码更新
func (this *Mach) HandleCodeUpdate() {
	if this.UpdateStatus == slg.MACH_UPDATE_STATUS_UPDATING {
		return
	}
	go func() {
		_, err := ut.SshExcuteShell(this.Addr, slg.GIT_UPDATE_BASH)
		if err != nil {
			log.Error("MatchGameMachGitUpdate err: %v", err)
		}
		this.UpdateGameMachUpdateStatus(slg.MACH_UPDATE_STATUS_UPDATED)
		log.Info("MatchGameMachGitUpdate finifsh")
	}()
	this.UpdateGameMachUpdateStatus(slg.MACH_UPDATE_STATUS_UPDATING)
}

// 辅助功能物理机信息 (登录、聊天、工具等)
type SupMach struct {
	Addr string `bson:"ip"`   // 物理机地址
	Name string `bson:"name"` // 名字
	Type string `bson:"type"` // 服务类型 (登录、聊天、工具等)

	Id           int32 `bson:"id"`            // 服务器id
	Status       int8  `bson:"status"`        // 状态
	UpdateStatus int8  `bson:"update_status"` // 更新状态
}

// 更新物理机状态
func (this *SupMach) UpdateMachStatus(status int8) {
	this.Status = status
	db_sup_machs.UpdateSupMachDb(this)
}

// 更新物理机代码
func (this *SupMach) HandleUpdateCode() {
	go func() {
		_, err := ut.SshExcuteShell(this.Addr, slg.GIT_UPDATE_BASH)
		if err != nil {
			log.Error("HandleUpdateCode type: %v, err: %v", this.Type, err)
		}
		// 更新完成
		this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATED
		db_sup_machs.UpdateSupMachDb(this)
		log.Info("HandleUpdateCode type: %v, finifsh", this.Type)
	}()
	this.UpdateStatus = slg.MACH_UPDATE_STATUS_UPDATING
	db_sup_machs.UpdateSupMachDb(this)
}

// 关闭进程
func (this *SupMach) CloseProcess() {
	if this.Status != slg.MACH_STATUS_OPEN {
		return
	}
	_, err := ut.SshExcuteShell(this.Addr, slg.STOP_SERVER_BASH)
	if err != nil {
		log.Error("CloseProcess type: %v , err: %v", this.Type, err)
		return
	}
	this.UpdateMachStatus(slg.MACH_STATUS_CLOSING)
}

// 开启进程
func (this *SupMach) StartProcess() {
	if this.Status != slg.MACH_STATUS_CLOSE {
		return
	}
	_, err := ut.SshExcuteShell(this.Addr, slg.START_SERVER_BASH)
	if err != nil {
		log.Error("StartProcess type: %v , err: %v", this.Type, err)
		return
	}
	this.UpdateMachStatus(slg.MACH_STATUS_OPENNING)
}

// 定时清理日志
func RunCleanMachLogsTick() {
	go func() {
		if slg.IsDebug() {
			return
		}
		tiker := time.NewTicker(time.Minute * 1)
		lastTime := ut.Now()
		for isRunning {
			<-tiker.C
			// 每天凌晨4点检查物理机磁盘并清理日志
			timeFour := ut.NowFourTime()
			now := ut.Now()
			if lastTime < timeFour && now > timeFour {
				HandleCleanMachLogs()
			}
			lastTime = now
		}
	}()
}

// 清理物理机日志
func HandleCleanMachLogs() {
	machIpList := []string{}
	// 游戏服
	machs.MachsMap.ForEach(func(v *Mach, k string) bool {
		machIpList = append(machIpList, v.Addr)
		return true
	})
	// 大厅服
	lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
		machIpList = append(machIpList, v.Addr)
		return true
	})
	// 辅助服
	supMachs.MachsMap.ForEach(func(v *SupMach, k string) bool {
		machIpList = append(machIpList, v.Addr)
		return true
	})
	for _, ip := range machIpList {
		_, err := ut.SshExcuteShell(ip, slg.CLEAN_LOGS_SHELL, slg.CLEAN_LOG_DISK_USAGE_THRESHOLD, slg.CLEAN_LOG_DAYS_OLD)
		if err != nil {
			log.Error("HandleCleanMachLogs ip: %v, err: %v", ip, err)
		}
	}
}

// 在线人数上报tick
func (this *Match) RunOnlineUserLogTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		for isRunning {
			<-tiker.C
			now := time.Now().UTC()
			// 每两分钟上报 如果当前分钟是偶数，且秒数是0
			if now.Minute()%2 == 0 && now.Second() == 0 {
				sum := 0
				// 获取每个大厅服的在线人数
				lobbyIdList := []int32{}
				lobbyMachs.MachsMap.ForEach(func(v *LobbyMach, k string) bool {
					lobbyIdList = append(lobbyIdList, v.Id)
					return true
				})
				for _, v := range lobbyIdList {
					rst, err := ut.RpcInterfaceMap(this.InvokeLobbyRpc(ut.String(v), slg.RPC_GET_ONLINE_SUM))
					if err != "" {
						continue
					}
					sum += ut.Int(rst["onlineSum"])
				}
				dateTime := now.Format("2006-01-02 15:04:05")
				// 执行上报操作
				dh.Track(dh.DH_EVENT_NAME_ONLINE, map[string]interface{}{
					"event_info": map[string]interface{}{
						"server_id":  dh.DhLogServerId,
						"platform":   slg.SERVER_AREA, //这里的platform特指服务器部署的集群名；如果没有具体名字，则使用自定义名称：国内外使用同一个，默认为xxx_all（游戏缩写_all），国内海外分开，则使用xxx_unch，xxx_ch，小游戏集群为xx_mini。
						"online_num": sum,             //在线人数
						"dump_time":  dateTime,        //dump_time必须是偶数分钟，秒为00
					},
				})
			}
		}
	}()
}

// 获取空闲的物理机列表 物理机按启动的游戏服数量从小到大排列
func getFreeMachs(roomType uint8) []*Mach {
	arr := []*Mach{}
	machs.MachsMap.ForEach(func(m *Mach, key string) bool {
		if m.Status != slg.MACH_STATUS_OPEN {
			return true
		}
		// 获取可开启该类型游戏服的物理机
		if len(m.SidList) < int(m.MaxCap) && (len(m.ServerTypes) == 0 || array.Has(m.ServerTypes, roomType)) {
			arr = append(arr, m)
		}
		return true
	})
	sort.Slice(arr, func(i, j int) bool {
		return len(arr[i].SidList) < len(arr[j].SidList)
	})
	return arr
}

// 物理机按启动的游戏服数量从小到大排列
func MachsSort(arr []*Mach) []*Mach {
	sort.Slice(arr, func(i, j int) bool {
		return len(arr[i].SidList) < len(arr[j].SidList)
	})
	return arr
}

// 添加区服到物理机
func AddServerToMach(sid int32, roomType uint8) (addr string) {
	machList := getFreeMachs(roomType)
	if len(machList) <= 0 {
		// 物理机负载已满
		log.Error("AddServerToMach machs all full")
		return ""
	}
	mach := machList[0]
	addr = mach.Addr
	mach.lock.Lock()
	mach.SidList = append(mach.SidList, sid)
	mach.lock.Unlock()
	updateMachInfo(mach)
	return
}
