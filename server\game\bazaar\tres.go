package bazaar

import (
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 一个交易资源
type TRes struct {
	UID   string `json:"uid"`
	Owner string `json:"owner"` //拥有者

	EndTime    int64 `json:"end_time"`    //结束时间
	NoticeTime int64 `json:"notice_time"` //公示结束时间
	Time       int64 `json:"time"`        //上架时间

	SellType      int32 `json:"sell_type"` //卖
	SellCount     int32 `json:"sell_count"`
	BuyType       int32 `json:"buy_type"` //买
	BuyCount      int32 `json:"buy_count"`
	MerchantCount int32 `json:"merchant_count"` //商人数量
	OnlyAlli      bool  `json:"only_alli"`      //仅盟友可见
}

type TradingResList struct {
	deadlock.RWMutex
	List []*TRes
}

func (this *TRes) IsEnd() bool {
	return time.Now().UnixMilli() >= this.EndTime
}

// 成功交易记录列表
type TradRecordList struct {
	deadlock.RWMutex
	List       []*TradRecord `bson:"list"`      //记录列表
	CurPrice   float64       `bson:"cur_price"` //市场价
	SellType   int32         `bson:"sell_type"` //卖
	BuyType    int32         `bson:"buy_type"`  //买
	AccTime    int32         `bson:"acc_time"`  //累计上架计时
	NeedUpdate bool          //是否需要更新到数据库
}

// 成功交易记录
type TradRecord struct {
	Price float64 `bson:"uid"`  //单价
	Time  int64   `bson:"time"` //交易时间
}

// 低价交易数据
type LowerPriceData struct {
	PlayerTradeMap map[string][]string //玩家上架的低价交易map
	deadlock.RWMutex
}

// 插入记录
func (this *TradRecordList) InsertRecord(price float64) bool {
	initPrice := this.GetInitPrice()                                      //初始价格
	priceTriggerMax := initPrice * constant.TRADE_MAX_PRICE_TRIGGER_PARAM //触发涨价的最高价格
	priceMax := initPrice * constant.TRADE_MAX_PRICE_UP_PARAM             //最高价格
	priceMin := initPrice * constant.TRADE_MIN_PRICE_DOWN_PARAM           //最低价格
	this.Lock()
	defer this.Unlock()
	updatePrice := false
	if len(this.List) >= constant.TRADE_RECORD_MAX {
		// 记录数量达到上限则移除最早记录
		this.RemoveOldestRecord()
	}
	// 冒泡插入
	index := -1
	for i, v := range this.List {
		if price <= v.Price {
			index = i
			break
		}
	}
	record := &TradRecord{
		Price: price,
		Time:  time.Now().UnixMilli(),
	}
	if index >= 0 {
		newList := make([]*TradRecord, len(this.List)+1)
		// 复制前半部分
		copy(newList[:index], this.List[:index])
		// 插入记录
		newList[index] = record
		// 复制后半部分
		copy(newList[index+1:], this.List[index:])
		this.List = newList
	} else {
		this.List = append(this.List, record)
	}
	if this.CurPrice >= 1 {
		if price > this.CurPrice && price < priceTriggerMax {
			// 新记录的单价更高 则更新市场价
			newPrice := math.Min((price+this.CurPrice)/2, this.CurPrice*constant.TRADE_PRICE_UP_MAX)
			this.CurPrice = math.Min(newPrice, priceMax) //不超过最高价
			updatePrice = true
		}
	} else {
		if price <= this.CurPrice*constant.TRADE_MIN_PRICE_PARAM {
			// 成交价格小于市场价 高估出售价值 需要降价
			newPrice := this.CurPrice * (1 - constant.TRADE_DISCOUNT_PARAM)
			this.CurPrice = math.Max(newPrice, priceMin) //不低于最低价
			updatePrice = true
		} else if price > this.CurPrice && price < priceTriggerMax {
			// 成交价格大于市场价 低估了出售价值，需要进行升价
			newPrice := math.Min((price+this.CurPrice)/2, this.CurPrice*constant.TRADE_PRICE_UP_MAX)
			this.CurPrice = math.Min(newPrice, priceMax) //不超过最高价
			updatePrice = true
		}
	}
	// 更新上架计时
	this.AccTime = int32(-slg.GetTradeNoticeTime()[2])
	this.NeedUpdate = true
	return updatePrice
}

// 移除最早的记录
func (this *TradRecordList) RemoveOldestRecord() {
	var oldTime int64
	index := -1
	for i, v := range this.List {
		if oldTime == 0 || v.Time < oldTime {
			oldTime = v.Time
			index = i
		}
	}
	if index >= 0 {
		this.List = append(this.List[:index], this.List[index+1:]...)
		this.NeedUpdate = true
	} else {
		log.Error("InsertRecord err sellType: %v, buyType: %v", this.SellType, this.BuyType)
	}
}

// 市场价降价处理
func (this *TradRecordList) PriceDiscount() bool {
	initPrice := this.GetInitPrice()                                      //初始价格
	priceTriggerMax := initPrice * constant.TRADE_MAX_PRICE_TRIGGER_PARAM //触发涨价的最高价格
	priceMax := initPrice * constant.TRADE_MAX_PRICE_UP_PARAM             //最高价格
	priceMin := initPrice * constant.TRADE_MIN_PRICE_DOWN_PARAM           //最低价格
	this.RLock()
	defer this.RUnlock()
	// if this.CurPrice <= this.List[0].Price {
	// 	// 不高于初始价格 无法再降价
	// 	this.AccTime = 0
	// 	return false
	// }
	// 二分法查找
	left, right := 0, len(this.List)-1
	var result float64
	for left <= right {
		mid := (left + right) / 2
		if this.CurPrice >= 1 {
			// 市场价大于等于1查找最接近市场价且价格更低的价
			if this.List[mid].Price < this.CurPrice {
				result = this.List[mid].Price
				left = mid + 1
			} else {
				right = mid - 1
			}
		} else {
			// 市场价小于1则取最接近市场价且更高的价格
			if this.List[mid].Price > this.CurPrice && this.List[mid].Price < priceTriggerMax {
				result = this.List[mid].Price
				right = mid - 1
			} else {
				left = mid + 1
			}
		}
	}
	if this.CurPrice >= 1 {
		// 市场价大于1降价
		newPrice := this.CurPrice
		if result > 0 {
			// 最新的市场价为两者平均值 最低降价10%
			newPrice = math.Min((this.CurPrice+result)/2, this.CurPrice*(1-constant.TRADE_DISCOUNT_PARAM))
		} else {
			// 交易记录中没有更低的价格 直接降价10%
			newPrice = this.CurPrice * (1 - constant.TRADE_DISCOUNT_PARAM)
		}
		// 降价不低于最低价
		this.CurPrice = math.Max(newPrice, priceMin)
	} else {
		// 市场价小于1涨价
		newPrice := this.CurPrice
		if result > 0 {
			// 最新的市场价为两者平均值 最低涨价10%
			newPrice = math.Max((this.CurPrice+result)/2, this.CurPrice*(1+constant.TRADE_DISCOUNT_PARAM))
			// 涨幅不超过1.2倍
			newPrice = math.Min(newPrice, this.CurPrice*constant.TRADE_PRICE_UP_MAX)

		} else {
			// 交易记录中没有更低的价格 直接涨价10%
			newPrice = this.CurPrice * (1 + constant.TRADE_DISCOUNT_PARAM)
		}
		// 涨价不超过最高价
		this.CurPrice = math.Min(newPrice, priceMax)
	}
	this.AccTime = 0
	this.NeedUpdate = true
	return true
}

// 转为pb
func (this *TradRecordList) ToPb() *pb.TradePriceInfo {
	return &pb.TradePriceInfo{
		SellType: int32(this.SellType),
		BuyType:  int32(this.BuyType),
		Price:    this.CurPrice,
	}
}

// 获取初始汇率
func (this *TradRecordList) GetInitPrice() float64 {
	confMap, ok := constant.TRADE_INIT_PRICE_MAP[this.BuyType]
	if !ok {
		return this.CurPrice
	}
	initPrice, ok := confMap[this.SellType]
	if ok {
		return initPrice
	}
	return this.CurPrice
}
