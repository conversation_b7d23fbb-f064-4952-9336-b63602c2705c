package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDPolicy() {
	this.GetServer().RegisterGO("HD_SavePolicy", this.savePolicy)   //保存政策
	this.GetServer().RegisterGO("HD_ResetPolicy", this.resetPolicy) //重置政策
}

// 保存政策
func (this *Game) savePolicy(session gate.Session, msg *pb.GAME_HD_SAVEPOLICY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	ids := msg.GetIds()
	if !array.Some(ids, func(m int32) bool { return m > 0 }) {
		return nil, ecode.PLEASE_FITON_POLICY.String()
	}
	tplr := wld.GetTempPlayer(plr.Uid)
	if tplr == nil {
		return nil, ecode.UNKNOWN.String()
	}
	policys := []map[string]interface{}{}
	// 先将之前的记录一下
	effectMap := tplr.Policys.GetPolicyEffectMapBool()
	idCnt := int32(len(ids))
	tplr.Policys.Lock()
	tplr.Policys.Map = map[int32]*g.PolicyInfo{}
	for i, l := int32(0), int32(len(constant.POLICY_SLOT_CONF)); i < l; i++ {
		cond := constant.POLICY_SLOT_CONF[i]
		if i < idCnt && plr.CheckCTypeOne(cond) {
			if info := g.NewPolicyInfo(int32(ids[i])); info != nil && plr.CheckCTypes(g.StringToTypeObjs(info.GetUseCond())) {
				if !info.IsNeedUnlock() || plr.IsUnlockPolicy(info.ID) {
					tplr.Policys.Map[i] = info
					effect := info.GetEffect()
					if effectMap[effect.Type] {
						delete(effectMap, effect.Type) //之前有了 就不用更新了
					} else {
						effectMap[effect.Type] = true          //表示新的
						tplr.AddPolicyUseMap(plr.Uid, info.ID) //添加使用过的政策
					}
					policys = append(policys, map[string]interface{}{
						"policy_id": info.ID,
					})
				}
			}
		}
	}
	tplr.Policys.Unlock()
	plr.UpdatePolicyEffect(effectMap)
	// 通知
	wld.PutNotifyQueue(constant.NQ_PLAYER_POLICY, &pb.OnUpdateWorldInfoNotify{
		Data_53: &pb.UpdatePolicys{
			Uid:     plr.Uid,
			Policys: tplr.Policys.ToPolicysPb(),
		},
	})
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_savePolicy", map[string]interface{}{
		"policys": policys,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_SAVEPOLICY_S2C{
		Policys: tplr.Policys.ToPolicysPb(),
	})
}

// 重置政策
func (this *Game) resetPolicy(session gate.Session, msg *pb.GAME_HD_RESETPOLICY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	tplr := wld.GetTempPlayer(plr.Uid)
	if tplr == nil {
		return nil, ecode.UNKNOWN.String()
	}
	s2c := &pb.GAME_HD_RESETPOLICY_S2C{}
	needGold := plr.ResetPolicyNeedGold
	if needGold > 0 {
		gold := this.ChangePlayerGold(plr, -needGold, constant.GOLD_CHANGE_RESET_POLICY)
		if gold == -1 {
			return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
		}
		s2c.Gold = gold
		s2c.UseGold = true
	}
	effectMap := tplr.Policys.GetPolicyEffectMapBool()
	tplr.Policys.Lock()
	tplr.Policys.Map = map[int32]*g.PolicyInfo{}
	tplr.Policys.Unlock()
	plr.UpdateResetPolicyNeedGold() //刷新下次需要的金币
	plr.UpdatePolicyEffect(effectMap)
	// 返回
	s2c.Policys = tplr.Policys.ToPolicysPb()
	s2c.ResetPolicyNeedGold = plr.ResetPolicyNeedGold
	// 通知
	wld.PutNotifyQueue(constant.NQ_PLAYER_POLICY, &pb.OnUpdateWorldInfoNotify{
		Data_53: &pb.UpdatePolicys{
			Uid:     plr.Uid,
			Policys: tplr.Policys.ToPolicysPb(),
		},
	})
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_resetPolicy", map[string]interface{}{
		"gold_cost": needGold,
	})
	return pb.ProtoMarshal(s2c)
}
