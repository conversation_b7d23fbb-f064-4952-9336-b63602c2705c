package lobby

import (
	"reflect"
	"strings"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/recharge"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var lobbyModule *Lobby

var Module = func() module.Module {
	lobbyModule = new(Lobby)
	return lobbyModule
}

type Lobby struct {
	basemodule.BaseModule

	LID            string
	RpcUserFuncMap map[string]interface{}
	RpcTeamFuncMap map[string]interface{}

	notifyQueue    chan NotifyData // 通知队列
	loginLimitType int             // 登录限制类型
}

func (this *Lobby) GetType() string {
	return "lobby" // 很关键,需要与配置文件中的Module配置对应
}

func (this *Lobby) Version() string {
	return "1.0.0" // 可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Lobby) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "lobby" || serverType == "development" {
		url := app.GetSettings().Settings["RecordMongodbURL"].(string)
		dbname := app.GetSettings().Settings["RecordMongodbDB"].(string)
		InitRecordDB(url, dbname)
		roomDbUrl := app.GetSettings().Settings["GameMongodbURL"].(string)
		roomDbName := app.GetSettings().Settings["GameMongodbDB"].(string)
		InitRoomDB(roomDbUrl, roomDbName)
		DelUserInit()
		sdk.InitAppleRootCerts()
		sdk.InitTranslateClient()
		sdk.FirebaseCloudMessageInit()
	}
}

func (this *Lobby) OnInit(app module.App, settings *conf.ModuleSettings) {
	id := settings.ID
	this.BaseModule.OnInit(this, app, settings, server.ID(id))
	this.LID = strings.Replace(id, "lobby", "", 1) // 设置元数据 方便路由的是区分节点
	this.GetServer().Options().Metadata["lid"] = this.LID
	this.notifyQueue = make(chan NotifyData, 1000)

	this.InitRpc()
	this.InitUserRpc()
	this.InitTeamRpc()

	this.GetServer().RegisterGO("HD_CheckSensitiveName", this.checkSensitiveName) // 检测敏感名字
	this.GetServer().RegisterGO("HD_CheckRealName", this.checkRealName)           // 检测实名
	this.GetServer().RegisterGO("HD_TranslateText", this.translateText)           // 文本翻译

	this.GetServer().RegisterGO("HD_LobbyChat", this.lobbyChat)                       // 大厅服聊天
	this.GetServer().RegisterGO("HD_ApplyNewServer", this.applyNewServer)             // 新区服报名
	this.GetServer().RegisterGO("HD_CancelApplyNewServer", this.cancelApplyNewServer) // 取消报名

	this.GetServer().RegisterGO("HD_SelectGameServer", this.selectGameServer)   // 选择游戏服务器
	this.GetServer().RegisterGO("HD_ResetSelectServer", this.resetSelectServer) // 重置选择的服务器

	this.initHDUser()
	this.initHDShop()
	this.InitHDTask()
	this.initHDPopularity()
	this.InitHDRecharge()
	this.InitHDTeam()
	this.InitHDRank()
	this.InitHDFriend()
	this.InitHDMail()
	this.InitHDPortrayal()
	this.initHDPlant()

	this.InitHttp()
	this.initBallenceLoad()
	this.initLoginLimitType()
	InitBlindBoxModel()
	InitApplyInfo()
	dh.InitDhLog()
	recharge.InitSnowFlakeIdGenerator(this.LID, this.LID)

	// TEST
	// rst, err := rds.RdsHGetAll(rds.RDS_LOBBY_LOAD_MAP_KEY)
	// rst, err := rds.RdsEvalHashByCmd(1, []string{"111"})
	// log.Info("test rst: %v, err: %v", rst, err)
}

func (this *Lobby) Run(closeSig chan bool) {
	RunTick(this)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	StopTick()
	SaveAllUser()
}

func (this *Lobby) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 检测敏感名字
func (this *Lobby) checkSensitiveName(session gate.Session, msg *pb.LOBBY_HD_CHECKSENSITIVENAME_C2S) (bytes []byte, err string) {
	name := msg.GetName()
	if sta := sensitive.CheckName(name); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	}
	return
}

// 检测实名
func (this *Lobby) checkRealName(session gate.Session, msg *pb.LOBBY_HD_CHECKREALNAME_C2S) (bytes []byte, err string) {
	name, email, identity := msg.GetName(), msg.GetEmail(), msg.GetIdentity()
	err = lc.CheckRealName(name, email, identity)
	return
}

// 文本翻译
func (this *Lobby) translateText(session gate.Session, msg *pb.LOBBY_HD_TRANSLATETEXT_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	text, lang, uid := msg.GetText(), msg.GetLang(), msg.GetUid()
	GetTranslateTexts(lang, text, uid, user)
	return pb.ProtoMarshal(&pb.LOBBY_HD_TRANSLATETEXT_S2C{})
}

func (this *Lobby) lobbyChat(session gate.Session, msg *pb.LOBBY_HD_LOBBYCHAT_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	s2c := &pb.LOBBY_HD_LOBBYCHAT_S2C{}
	if s2c.BannedSurplusTime = int64(user.GetBannedChatEndTime()); s2c.BannedSurplusTime > 0 {
		// 禁言未结束
		return
	}
	uid, channel, content, emoji, portrayalId := msg.GetUid(), msg.GetChannel(), msg.GetContent(), msg.GetEmoji(), msg.GetPortrayalId()
	// 检测是否拥有表情
	if !user.CheckHasEmoji(emoji) {
		err = ecode.CHAT_EMOJI_NOT_EXIST.String()
		return
	}
	// 检测画像
	portrayalInfo, err := user.CheckPortrayalByChat(portrayalId)
	if err != "" {
		return nil, err
	}
	chatInfo := &pb.LobbyChatInfo{
		Uid:               uid,
		Channel:           channel,
		Sender:            user.UID,
		SenderNickname:    user.Nickname,
		SenderHeadicon:    user.HeadIcon,
		Content:           content,
		Emoji:             emoji,
		PortrayalInfo:     g.NewPortrayalPbByJson(ut.MapInterface(portrayalInfo)),
		Time:              time.Now().UnixMilli(),
		BannedSurplusTime: user.BannedChatEndTime,
	}
	bytes, _ := pb.ProtoMarshal(chatInfo)
	this.InvokeChatRpcNR(slg.RPC_SEND_CHAT, bytes, msg.GetReplyUid())
	return pb.ProtoMarshal(s2c)
}

// 新区服报名
func (this *Lobby) applyNewServer(session gate.Session, msg *pb.LOBBY_HD_APPLYNEWSERVER_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.IsNovicePlayer() {
		return nil, ecode.NOVICE_PLAYER.String() // 新手玩家不可报名 直接进入游戏
	} else if user.GetPlaySid() != 0 {
		return nil, ecode.IN_GAME.String()
	} else if user.TeamUid != "" && user.TeamUid != user.UID {
		return nil, ecode.NOT_OPERATING_AUTH.String() // 暂时只能队长报名
	}
	roomType := msg.GetRoomType()
	if applyCloseMap.Get(roomType) {
		// 报名关闭
		return nil, ecode.APPLY_CLOSE.String()
	}
	isSolo := false
	if user.TeamUid == "" {
		isSolo = true
		// 单人报名时先设置队伍uid
		user.SetTeamUid(user.UID)
	}
	_, err = this.InvokeTeamFunc(user.UID, slg.RPC_TEAM_APPLY_SERVER, user.TeamUid, user.Nickname, user.HeadIcon, roomType, user.ExpectPosition, user.FarmType)
	// 拒绝其他队伍
	if err == "" {
		for _, v := range user.TeamInviteList {
			this.InvokeTeamFuncNR(v.TeamUid, slg.RPC_TEAM_INVITE_RESPONSE, user.UID, false, user.ExpectPosition, user.FarmType)
		}
		user.TeamInviteList = []*TeamInviteInfo{}
	} else if isSolo {
		// 单人报名失败 清理队伍uid
		user.SetTeamUid("")
	}
	return
}

// 取消报名
func (this *Lobby) cancelApplyNewServer(session gate.Session, msg *pb.LOBBY_HD_CANCELAPPLYNEWSERVER_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.GetPlaySid() != 0 {
		return nil, ecode.IN_GAME.String()
	}
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_CANCEL_APPLY_SERVER, user.UID, user.Nickname)
	return
}

func (this *Lobby) selectGameServer(session gate.Session, msg *pb.LOBBY_HD_SELECTGAMESERVER_C2S) (bytes []byte, err string) {
	user, sid := GetUserByOnline(session.GetUserID()), msg.GetSid()
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.GetPlaySid() != 0 && user.GetPlaySid() != sid {
		return nil, ecode.CANT_ENTER_ROOM.String() // 和正在玩的区不一致
	} else if !user.CheckCanPlayGameBySid(sid) {
		return nil, ecode.CANT_ENTER_ROOM.String()
	}
	log.Info("selectGameServer uid: %v, sid: %v, playSid: %v, selectSid: %v", user.UID, user.SID, user.GetPlaySid(), sid)
	defer log.Info("selectGameServer uid: %v, sid: %v, playSid: %v, done.", user.UID, user.SID, user.GetPlaySid())
	// 获取服务器
	room := GetRoomById(sid)
	if IsRoomClose(sid) {
		user.SID = 0
		user.SetPlaySid(0)
		user.FlagUpdateDB()
		return nil, ecode.ROOM_CLOSE.String()
	} else if room.State != slg.SERVER_STATUS_OPEN {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if _, err := this.InvokeGameRpc(sid, slg.RPC_SELECT_ROOM, user.UID); err != "" {
		if isGiveGame := err == ecode.YET_GIVE_GAME.String(); isGiveGame || err == ecode.ROOM_CLOSE.String() || err == ecode.ROOM_FULL.String() || err == ecode.ROOM_OVER.String() {
			if isGiveGame {
				user.AddGiveupGame(sid)
				// 退出队伍
				this.InvokeTeamFunc(user.TeamUid, slg.RPC_GIVEUP_LEAVE_TEAM, user.UID)
			}
			user.SID = 0
			user.SetPlaySid(0)
			user.FlagUpdateDB()
		}
		return nil, err
	}
	session.SetPush("sid", ut.Itoa(sid)) // 在这里设置一下服务器id
	user.SID = sid
	user.SetPlaySid(sid)
	user.FriendInfoNotify()
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_SELECTGAMESERVER_S2C{Uid: user.UID, Sid: pb.Int32(sid)})
}

// 重置选择的服务器
func (this *Lobby) resetSelectServer(session gate.Session, msg *pb.LOBBY_HD_RESETSELECTSERVER_C2S) (bytes []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		log.Error("not bind uid")
		return nil, ecode.NOT_BIND_UID.String()
	}
	sid := user.SID
	if sid == 0 { // 如果是观战的话 就从session中取
		sid = ut.Int32(session.Get("sid"))
	}
	_, e := this.InvokeGameRpc(sid, slg.RPC_LEAVE_BY_CHANGE_SER, user.UID)
	if e != "" {
		log.Error("resetSelectServer error, err: %v", e)
	}
	if user.GetPlaySid() == 0 {
	} else if room := GetRoomById(user.GetPlaySid()); room == nil || IsGameOver(room) {
		log.Info("resetSelectServer, uid: %v, sid: %v, playSid: %v", user.UID)
		user.SetPlaySid(0)
	}
	// 离开上一个服务器
	user.SID = 0
	user.FriendInfoNotify()
	session.SetPush("sid", "") // 在这里设置一下服务器id
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_RESETSELECTSERVER_S2C{
		PlaySid:          user.GetPlaySid(),
		TotalGameCount:   array.Map(user.AccTotalGameCounts, func(m int32, _ int) int32 { return m }), // 累计游戏次数
		TotalNewbieCount: array.Map(user.AccTotalNewbieCounts, func(m int32, _ int) int32 { return m }),
		PassNewbieIndex:  user.PassNewbieIndex,
		GiveupCount:      int32(len(user.GiveupGames)), // 放弃次数
	})
}

// 更新使用语言
func (this *Lobby) updateUserLanguage(user *User, lang string) {
	oldLang := user.Language
	if oldLang != lang {
		user.Language = lang
		// 通知到玩家游戏服
		if room := GetRoomById(user.GetPlaySid()); room != nil && room.State == 1 {
			this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_LANGUAGE, user.UID, lang)
		}
		// 通知到聊天服
		this.InvokeChatRpcNR(slg.RPC_CHANGE_LANGUAGE, user.UID, oldLang)
	}
}

// 记录邀请信息
func (this *Lobby) updateUserInvite(user *User, inviteUid string) {
	if user.Referrer != "" || inviteUid == "" {
		return
	} else if inviteUid != user.UID {
		user.Referrer = inviteUid
		// 如果是被邀请记录到被邀请的玩家列表
		this.InvokeUserFuncNR(inviteUid, slg.RPC_ADD_INVITE_FRIEND, user.UID)
		//
		log.Info("updateUserInvite uid: %v, inviteUid: %v", user.UID, inviteUid)
	} else {
		log.Info("updateUserInvite same uid: %v", inviteUid)
	}
}

// 更新FCM令牌
func (this *Lobby) updateFCMToken(user *User, token string) {
	oldToken := user.FCMToken
	if oldToken != token {
		user.FCMToken = token
		// 通知到玩家游戏服
		if room := GetRoomById(user.GetPlaySid()); room != nil && room.State == 1 {
			this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_FCM_TOKEN, user.UID, token)
		}
	}
}

// 更新离线推送设置
func (this *Lobby) updateOfflineNotifyOpt(user *User, opt []int32) {
	user.OfflineNotifyOpt = opt
	// 通知到玩家游戏服
	if room := GetRoomById(user.GetPlaySid()); room != nil && room.State == 1 {
		this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_CHANGE_OFFLINE_OPT, user.UID, opt)
	}
}

// 大厅服负载初始化
func (this *Lobby) initBallenceLoad() {
	rst, err := ut.RpcInterfaceMap(this.InvokeMatchRpc(slg.RPC_GET_LOBBY_MACH_INFO, slg.SERVER_IP))
	if err == "" && rst != nil && len(rst) > 0 {
		if ut.Bool(rst["abandon"]) {
			// 已在后台停用该大厅服 则不添加负载到redis
			return
		}
	}
	rds.RdsHSet(rds.RDS_LOBBY_LOAD_MAP_KEY, this.LID, 0)
}

// 初始化登录限制类型
func (this *Lobby) initLoginLimitType() {
	this.loginLimitType = slg.LOGIN_LIMIT_TYPE_NULL
	rst, err := rds.RdsGet(rds.RDS_LOGIN_LIMIT_TYPE_KEY)
	if err == nil {
		this.loginLimitType = ut.Int(rst)
	}
}

// 获取当前大厅服id
func (this *Lobby) GetLid() string {
	return this.LID
}

func (this *Lobby) GetUserByDB(uid string) *User {
	return GetUserByDB(uid, this.LID)
}

func getInvokeRpcInfo(serverType, id string, params []interface{}) (string, []interface{}) {
	moduleType := serverType
	if serverType == slg.MACH_SERVER_TYPE_LOBBY || serverType == slg.MACH_SERVER_TYPE_GAME {
		moduleType = serverType + "@" + serverType + id
	}
	return moduleType, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
}

// 发送Rpc到game
func (this *Lobby) InvokeGameRpc(id int32, _func string, params ...interface{}) (result interface{}, err string) {
	if id == 0 {
		return
	}
	params = append([]interface{}{id}, params...)
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_GAME, ut.String(id), params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到game
func (this *Lobby) InvokeGameRpcNR(id int32, _func string, params ...interface{}) {
	if id == 0 {
		return
	}
	params = append([]interface{}{id}, params...)
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_GAME, ut.String(id), params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Lobby) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Lobby) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 广播发送到其他有负载的大厅服
func (this *Lobby) BroadCastOtherLobby(_func string, params ...interface{}) (resultList []interface{}, errList []string) {
	rdsData, e := rds.RdsHGetAll(rds.RDS_LOBBY_LOAD_MAP_KEY)
	if e != nil {
		return
	}
	for lid := range rdsData {
		if lid == this.GetLid() {
			continue
		}
		moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, lid, params)
		rst, err := this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
		resultList = append(resultList, rst)
		errList = append(errList, err)
	}
	return
}

// 发送Rpc到chat
func (this *Lobby) InvokeChatRpc(_func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_CHAT, "", params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到chat
func (this *Lobby) InvokeChatRpcNR(_func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_CHAT, "", params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到match
func (this *Lobby) InvokeMatchRpc(_func string, params ...interface{}) (result interface{}, err string) {
	return this.InvokeWithCleanup(slg.MACH_SERVER_TYPE_MATCH, _func, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })...)
}

// 发送Rpc到match
func (this *Lobby) InvokeMatchRpcNR(_func string, params ...interface{}) {
	this.InvokeNR(slg.MACH_SERVER_TYPE_MATCH, _func, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })...)
}

// 发送Rpc到mail
func (this *Lobby) InvokeMailRpc(_func string, params ...interface{}) (result interface{}, err string) {
	return this.InvokeWithCleanup(slg.MACH_SERVER_TYPE_MAIL, _func, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })...)
}

// 发送Rpc到mail
func (this *Lobby) InvokeMailRpcNR(_func string, params ...interface{}) {
	this.InvokeNR(slg.MACH_SERVER_TYPE_MAIL, _func, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })...)
}

// 本地调用rpc
func RunLocalRpcFunc(function interface{}, params ...interface{}) (result interface{}, err string) {
	f := reflect.ValueOf(function)
	in := make([]reflect.Value, len(params))
	for i, p := range params {
		in[i] = reflect.ValueOf(p)
	}
	out := f.Call(in)
	if len(out) < 2 {
		return
	}
	rs := make([]any, len(out))
	if len(out) > 0 {
		for i, v := range out {
			rs[i] = v.Interface()
		}
	}
	result, err = rs[0], ut.String(rs[1])
	return
}

// 通知到玩家所在大厅服
func (this *Lobby) PutUserNotifyToLobby(nType int32, uids []string, msg *pb.OnUpdatePlayerInfoNotify) {
	msgBytes, err := pb.ProtoMarshal(msg)
	if err != "" {
		log.Warning("PutUserNotifyToLobby nType: %v, uids: %v, err: %v", nType, uids, err)
		return
	}
	lidUserListMap := map[string][]string{}
	for _, uid := range uids {
		lid, err := rds.RdsHGet(rds.RDS_USER_LID_MAP_KEY, uid)
		if err != nil || lid == "" {
			continue
		}
		if lidUserListMap[lid] == nil {
			lidUserListMap[lid] = []string{}
		}
		lidUserListMap[lid] = append(lidUserListMap[lid], uid)
	}
	for lid, uidList := range lidUserListMap {
		this.InvokeLobbyRpcNR(lid, slg.RPC_LOBBY_PUT_USER_NOTIFY, nType, uidList, msgBytes)
	}
}

// 通知所有人 所有大厅服
func (this *Lobby) PutAllUserNotifyQueueAllLobby(nType int32, msg *pb.OnUpdatePlayerInfoNotify) {
	this.PutAllUserNotifyQueue(nType, msg)
	msgBytes, err := pb.ProtoMarshal(msg)
	if err != "" {
		log.Warning("PutUserNotifyToLobby nType: %v, err: %v", nType, err)
		return
	}
	this.BroadCastOtherLobby(slg.RPC_LOBBY_PUT_ALL_USER_NOTIFY, nType, msgBytes)
}

// 通知 仅当前大厅服
func (this *Lobby) PutUserNotifyQueue(nType int32, uid string, msg *pb.OnUpdatePlayerInfoNotify) {
	if user := GetUserByOnline(uid); user != nil {
		user.PutNotifyQueue(nType, msg)
	} else if nType == constant.NQ_NEW_MAIL {
		// 邮件通知还需要通知离线玩家
		user := this.GetUserByDB(uid)
		user.OfflineNotify(constant.OFFLINE_MSG_TYPE_MAIL)
	}
}

// 通知所有人 仅当前大厅服
func (this *Lobby) PutAllUserNotifyQueue(nType int32, msg *pb.OnUpdatePlayerInfoNotify) {
	users.RLock()
	defer users.RUnlock()
	for _, m := range users.Map {
		if m.IsOnline() {
			m.PutNotifyQueue(nType, msg)
		}
	}
}

// 改变玩家物品
func (this *Lobby) ChangeUserItems(user *User, items []*g.TypeObj, reason int32) *pb.UpdateOutPut {
	gameItems, userItems := g.ResolutionItems(items)
	output := &pb.UpdateOutPut{}
	// 发放游戏服道具
	if user.SID > 0 && len(gameItems) > 0 {
		if data, err := ut.RpcBytes(this.InvokeGameRpc(user.SID, slg.RPC_ADD_PLAYER_ITEMS, user.UID, gameItems)); err == "" {
			pb.ProtoUnMarshal(data, output)
		}
	}
	// 发放用户服道具
	return user.ChangeUserItems(userItems, reason, output)
}

// 生成大厅全服唯一Uid 避免多个大厅服同时生成重复uid
func GenUid() string {
	return ut.ID() + lobbyModule.GetLid()
}
