package behavior

import (
	"slgsrv/server/game/common/enums/bufftype"
	ut "slgsrv/utils"
)

// 是否可以移动
type CanMove struct {
	BaseCondition
}

func (this *CanMove) OnTick(dt int32) Status {
	if ut.Bool(this.GetTreeBlackboardData("isMove")) {
		return FAILURE //如果已经移动过了就不能再移动了
	} else if this.target.IsHasBuffs(bufftype.STAND_SHIELD, bufftype.TIMIDITY, bufftype.IRREMOVABILITY, bufftype.OVERLORD) {
		return FAILURE //立盾状态下 畏惧 限制移动
	}
	return SUCCESS
}
