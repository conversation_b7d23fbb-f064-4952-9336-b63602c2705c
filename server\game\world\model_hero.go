package world

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

func initHeroSlots(datas []map[string]interface{}) []*g.HeroSlotInfo {
	arr := []*g.HeroSlotInfo{}
	cnt := len(datas)
	for i, lv := range constant.HERO_SLOT_LV_COND {
		info := &g.HeroSlotInfo{Lv: lv}
		if i < cnt {
			data := datas[i]
			info.FromHeroJson(data["hero"])
			info.DieTime = ut.Int64(data["dieTime"])
			info.AvatarArmyUID = ut.String(data["avatarArmyUID"])
		}
		arr = append(arr, info)
	}
	return arr
}

// 是否供奉了某个英雄
func (this *TempPlayer) HasWorshipHero(id int32) bool {
	return array.Some(this.HeroSlots, func(m *g.HeroSlotInfo) bool { return m.Hero != nil && m.Hero.ID == id })
}

// 获取供奉的英雄
func (this *TempPlayer) GetWorshipHeroSlotInfo(id int32) *g.HeroSlotInfo {
	if slot := array.Find(this.HeroSlots, func(m *g.HeroSlotInfo) bool { return m.Hero != nil && m.Hero.ID == id }); slot != nil {
		return slot
	}
	return nil
}

// 获取供奉的英雄id列表
func (this *TempPlayer) GetWorshipHeroIdList() []int32 {
	arr := []int32{}
	for _, v := range this.HeroSlots {
		if v.Hero != nil && v.Hero.ID != 0 {
			arr = append(arr, v.Hero.ID)
		}
	}
	return arr
}

// 刷新英雄属性
func (this *TempPlayer) UpdateHeroAttr(id int32, attrs [][]int32) *g.HeroSlotInfo {
	slot := this.GetWorshipHeroSlotInfo(id)
	if slot != nil {
		slot.Hero.SetAttr(attrs)
		this.IsNeedUpdateDB = true
	}
	return slot
}

// 获取英雄槽位
func (this *Model) GetPlayerHeroSlots(uid string) []*g.HeroSlotInfo {
	if plr := this.GetTempPlayer(uid); plr != nil {
		return plr.HeroSlots
	}
	return []*g.HeroSlotInfo{}
}

// 刷新玩家英雄属性
func (this *Model) UpdatePlayerHeroAttr(uid string, id int32, attrs [][]int32) string {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		log.Error("UpdateHeroAttr plr is nil, uid: %v", uid)
		return ecode.PLAYER_NOT_EXIST.String()
	}
	slot := plr.UpdateHeroAttr(id, attrs)
	if slot == nil {
		return ""
	}
	// 刷新士兵
	this.UpdatePlayerPawnHeroInfo(uid, slot.Hero.ID, slot.Hero.Attrs)
	// 通知
	if p := this.room.GetPlayer(uid); p != nil {
		p.PutNotifyQueue(constant.NQ_CHANGE_HERO_SLOT_INFO, &pb.OnUpdatePlayerInfoNotify{Data_70: slot.ToPb()})
	}
	return ""
}

// 玩家的英雄阵亡
func (this *Model) UpdatePlayerHeroDie(uid string, armyUid string, id int32) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		log.Error("UpdatePlayerHeroDie plr is nil, uid: %v", uid)
		return
	}
	slot := plr.GetWorshipHeroSlotInfo(id)
	if slot == nil {
		log.Error("UpdatePlayerHeroDie slot is nil, uid: %v, id: %v", uid, id)
		return
	} else if slot.AvatarArmyUID != armyUid {
		log.Error("UpdatePlayerHeroDie slot.AvatarArmyUID != armyUid, uid: %v, id: %v, avatarArmyUID: %v, armyUid: %v", uid, id, slot.AvatarArmyUID, armyUid)
		return
	}
	slot.AvatarArmyUID = ""
	slot.DieTime = ut.Now() //记录阵亡时间
	plr.IsNeedUpdateDB = true
	log.Info("Hero Die! uid: %v, id: %v", uid, id)
	// 通知
	if p := this.room.GetPlayer(uid); p != nil {
		p.PutNotifyQueue(constant.NQ_CHANGE_HERO_SLOT_INFO, &pb.OnUpdatePlayerInfoNotify{Data_70: slot.ToPb()})
	}
}

// 刷新玩家的英雄所属军队
func (this *Model) UpdatePlayerHeroArmy(uid string, id int32, armyUid string) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		log.Error("UpdatePlayerHeroDie plr is nil, uid: %v", uid)
		return
	}
	slot := plr.GetWorshipHeroSlotInfo(id)
	if slot == nil {
		log.Error("UpdatePlayerHeroArmy slot is nil, uid: %v, id: %v", uid, id)
		return
	}
	slot.AvatarArmyUID = armyUid
	// 通知
	if p := this.room.GetPlayer(uid); p != nil {
		p.PutNotifyQueue(constant.NQ_CHANGE_HERO_SLOT_INFO, &pb.OnUpdatePlayerInfoNotify{Data_70: slot.ToPb()})
	}
}
