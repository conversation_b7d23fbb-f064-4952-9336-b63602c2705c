package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
)

// 将服务器id转换成字符串
func ServerIdToString(id int32) string {
	if id < 0 {
		return ut.Itoa(ut.Abs(int(id))) + "_e"
	}
	return ut.Itoa(id)
}

// 字符串转类型对象
func StringToTypeObjs(val interface{}) []*TypeObj {
	tos := []*TypeObj{}
	str := ut.String(val)
	if str == "" {
		return tos
	}
	arr := strings.Split(str, "|")
	for _, s := range arr {
		if s == "" {
			continue
		}
		datas := strings.Split(s, ",")
		if len(datas) != 3 {
			continue
		}
		t, id, count := datas[0], datas[1], datas[2]
		tos = append(tos, NewTypeObj(ut.Int32(t), ut.Int32(id), ut.Int32(count)))
	}
	return tos
}

// 字符串转效果对象
func StringToEffectObjs(str interface{}) []*EffectObj {
	arr := strings.Split(ut.String(str), "|")
	ret := []*EffectObj{}
	for _, s := range arr {
		if s == "" {
			continue
		}
		datas := strings.Split(s, ",")
		tp, count, param := int32(-1), 0.0, 0.0
		if len(datas) == 2 {
			tp, count = ut.Int32(datas[0]), ut.Float64(datas[1])
		} else if len(datas) == 3 {
			tp, count, param = ut.Int32(datas[0]), ut.Float64(datas[1]), ut.Float64(datas[2])
		}
		if tp != -1 {
			ret = append(ret, NewEffectObj(tp, count, param))
		}
	}
	return ret
}

func CloneAttrs(attrs [][]int32) [][]int32 {
	ret := [][]int32{}
	for i, l := 0, len(attrs); i < l; i++ {
		attr := attrs[i]
		arr := []int32{}
		for i2, l2 := 0, len(attr); i2 < l2; i2++ {
			arr = append(arr, attr[i2])
		}
		ret = append(ret, arr)
	}
	return ret
}

func CloneEffectAttrsToPb(attrs [][]int32) []*pb.AttrArrayInfo {
	arr := []*pb.AttrArrayInfo{}
	for _, attr := range attrs {
		attrInfo := &pb.AttrArrayInfo{}
		for _, val := range attr {
			attrInfo.Attr = append(attrInfo.Attr, val)
		}
		arr = append(arr, attrInfo)
	}
	return arr
}

func ClonePbToEffectAttrs(attrs []*pb.AttrArrayInfo) [][]int32 {
	arr := [][]int32{}
	for _, v := range attrs {
		arr = append(arr, array.Clone(v.Attr))
	}
	return arr
}

// 将用户服和游戏服道具拆分
func ResolutionItems(items []*TypeObj) (gameItems []*TypeObj, userItems []*TypeObj) {
	gameItems = []*TypeObj{} //游戏服道具
	userItems = []*TypeObj{} //用户服道具
	for _, item := range items {
		if item.Type == ctype.GOLD ||
			item.Type == ctype.INGOT || //元宝
			item.Type == ctype.WAR_TOKEN ||
			item.Type == ctype.TITLE || //称号
			item.Type == ctype.PAWN_SKIN ||
			item.Type == ctype.RANK_SCORE ||
			item.Type == ctype.HERO_DEBRIS ||
			item.Type == ctype.HERO_OPT ||
			item.Type == ctype.SKIN_ITEM ||
			item.Type == ctype.CITY_SKIN ||
			item.Type == ctype.RANK_COIN ||
			item.Type == ctype.HEAD_ICON ||
			item.Type == ctype.CHAT_EMOJI ||
			item.Type == ctype.BOTANY ||
			item.Type == ctype.BATTLE_PASS ||
			item.Type == ctype.BATTLE_PASS_SCORE {
			userItems = append(userItems, item)
		} else {
			gameItems = append(gameItems, item)
		}
	}
	return
}
