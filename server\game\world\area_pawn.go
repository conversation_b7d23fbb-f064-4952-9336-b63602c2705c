package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 一个士兵
type AreaPawn struct {
	wld g.World

	Equip            *g.EquipInfo                       //携带的装备
	Portrayal        *g.PortrayalInfo                   //携带的画像
	StrategyBuffMap  *ut.MapLock[int32, *g.StrategyObj] //韬略map
	BattleRecordData map[int32]int32                    //战斗记录
	baseJson         map[string]interface{}
	attrJson         map[string]interface{}
	Point            *ut.Vec2 //位置

	Treasures []*g.TreasureInfo //宝箱列表
	Buffs     []*g.BuffObj      //buff列表
	skills    []*PawnSkill      //技能列表
	upCost    []*g.TypeObj      //升级费用

	treasureMutex     *deadlock.RWMutex
	battleRecordMutex *deadlock.RWMutex
	buffsMutex        *deadlock.RWMutex

	Owner   string
	ArmyUid string //所属队伍uid
	Uid     string
	Name    string //名字
	Desc    string //介绍

	CreateTime int64 //创建时间

	AIndex   int32 //所在区域位置
	EnterDir int32 //进入方向

	Id              int32
	Lv              int32 //等级
	SkinId          int32 //皮肤id
	curHp           int32
	maxHp           int32
	curAnger        int32 //怒气
	maxAnger        int32
	AttackSpeed     int32 //出手速度
	RodeleroCadetLv int32 //见习勇者 当前层数
	PetId           int32 //携带的宠物
	State           int32 //当前状态

	attrId      int32
	pawnType    int32 //士兵类型
	attack      int32 //攻击力
	attackRange int32 //攻击范围
	moveRange   int32 //移动范围
	behaviorId  int32 //行为树id
	marchSpeed  int32 //行军速度 1小时多少格
	velocity    int32 //移动速度
	attackIndex int32 //临时的出手下标
	bagCap      int32 //背包容量
	CureCount   int32 //已治疗次数
}

func NewAreaPawn(index, enterDir int32, uid string, point *ut.Vec2, id int32, owner, armyUid string, wld g.World) *AreaPawn {
	return &AreaPawn{
		wld:               wld,
		AIndex:            index,
		EnterDir:          enterDir,
		Uid:               uid,
		Point:             point,
		Id:                id,
		Owner:             owner,
		ArmyUid:           armyUid,
		Equip:             g.NewEquip(0),
		RodeleroCadetLv:   0,
		PetId:             0,
		Treasures:         []*g.TreasureInfo{},
		Buffs:             []*g.BuffObj{},
		StrategyBuffMap:   ut.NewMapLock[int32, *g.StrategyObj](),
		BattleRecordData:  map[int32]int32{},
		treasureMutex:     new(deadlock.RWMutex),
		battleRecordMutex: new(deadlock.RWMutex),
		buffsMutex:        new(deadlock.RWMutex),
	}
}

func (this *AreaPawn) ToString() string {
	return ut.Itoa(this.Id) + ", lv=" + ut.Itoa(this.Lv) + ", hp=" + ut.Itoa(this.curHp) + "/" + ut.Itoa(this.GetMaxHP())
}

func (this *AreaPawn) Init(lv int32, skinId int32) *AreaPawn {
	this.Lv = lv
	this.SetSkinId(skinId)
	this.UpdateRodeleroCadetLv()
	this.InitJson()
	this.InitAnger()
	this.curHp = this.maxHp
	this.AttackSpeed = ut.Int32(this.baseJson["attack_speed"])
	this.State = constant.AS_NONE
	this.CreateTime = time.Now().UnixMilli()
	return this
}

func (this *AreaPawn) FromData(data map[string]interface{}) *AreaPawn {
	this.Lv = ut.Int32(data["lv"])
	if equipData, ok := data["equip"]; ok {
		this.Equip = g.NewEquipByJson(equipData.(map[string]interface{}))
	}
	if portrayalData, ok := data["portrayal"]; ok {
		this.Portrayal = g.NewPortrayalByJson(portrayalData.(map[string]interface{}))
	}
	this.SetSkinId(ut.Int32(data["skinId"]))
	this.InitJson()
	this.InitAnger()
	this.curHp = this.maxHp
	this.AttackSpeed = ut.Int32(this.baseJson["attack_speed"])
	this.State = constant.AS_NONE
	this.CreateTime = time.Now().UnixMilli()
	return this
}

func (this *AreaPawn) FromPbData(data *pb.AreaPawnInfo) *AreaPawn {
	this.Lv = ut.Int32(data.Lv)
	if data.Equip != nil {
		this.Equip = g.NewEquipByPb(data.Equip)
	}
	if data.Portrayal != nil {
		this.Portrayal = g.NewPortrayalByPb(data.Portrayal)
	}
	this.PetId = data.PetId
	this.SetSkinId(data.SkinId)
	this.InitJson()
	this.InitAnger()
	this.curHp = this.maxHp
	this.AttackSpeed = ut.Int32(this.baseJson["attack_speed"])
	this.State = constant.AS_NONE
	this.CreateTime = time.Now().UnixMilli()
	return this
}

func (this *AreaPawn) FromDB(data map[string]interface{}) *AreaPawn {
	this.Lv = ut.Int32(data["lv"])
	this.SetSkinId(ut.Int32(data["skinId"]))
	this.curHp = ut.Int32(data["curHp"])
	this.curAnger = ut.Int32(data["curAnger"])
	this.CreateTime = ut.Int64(data["createTime"])
	this.AttackSpeed = ut.Int32(data["attackSpeed"])
	this.RodeleroCadetLv = ut.Int32(data["rodeleroCadetLv"])
	this.PetId = ut.Int32(data["petId"])
	this.CureCount = ut.Int32(data["cureCount"])
	if equipData := data["equip"]; equipData != nil {
		this.Equip = g.NewEquipByJson(equipData.(map[string]interface{}))
	}
	if portrayalData := data["portrayal"]; portrayalData != nil {
		this.Portrayal = g.NewPortrayalByJson(portrayalData.(map[string]interface{}))
	}
	if treasures, ok := data["treasures"].(primitive.A); ok {
		for _, val := range treasures {
			this.Treasures = append(this.Treasures, g.NewTreasureByJson(val.(map[string]interface{})))
		}
	}
	this.SetBuffsByDB(data["buffs"])
	if battleRecordData, ok := data["battleRecordData"].(map[string]interface{}); ok && battleRecordData != nil {
		this.battleRecordMutex.Lock()
		for k, v := range battleRecordData {
			this.BattleRecordData[ut.Int32(k)] = ut.Int32(v)
		}
		this.battleRecordMutex.Unlock()
	}
	this.InitJson()
	return this
}

func (this *AreaPawn) ToDB() map[string]interface{} {
	msg := map[string]interface{}{
		"uid":              this.Uid,
		"point":            this.Point.Clone(),
		"id":               this.Id,
		"lv":               this.Lv,
		"skinId":           this.SkinId,
		"curHp":            this.curHp,
		"curAnger":         this.curAnger,
		"createTime":       this.CreateTime,
		"attackSpeed":      this.AttackSpeed,
		"equip":            this.Equip.ToJson(),
		"portrayal":        this.ToPortrayalJson(),
		"rodeleroCadetLv":  this.RodeleroCadetLv,
		"petId":            this.PetId,
		"treasures":        this.ToTreasures(),
		"battleRecordData": this.ToBattleRecordData(),
		"cureCount":        this.CureCount,
		"buffs":            this.ToBuffsDB(),
	}
	return msg
}

func (this *AreaPawn) ToPb() *pb.AreaPawnInfo {
	ret := &pb.AreaPawnInfo{
		Index:            int32(this.AIndex),
		ArmyUid:          this.ArmyUid,
		Uid:              this.Uid,
		Point:            pb.NewVec2(this.Point.Clone()),
		Id:               int32(this.Id),
		Lv:               int32(this.Lv),
		IsFight:          this.State == constant.AS_FIGHT,
		SkinId:           int32(this.SkinId),
		CurAnger:         int32(this.curAnger),
		AttackSpeed:      int32(this.AttackSpeed),
		Equip:            this.Equip.ToPb(),
		Portrayal:        this.ToPortrayalPb(),
		RodeleroCadetLv:  int32(this.RodeleroCadetLv),
		PetId:            int32(this.PetId),
		Treasures:        this.ToTreasuresPb(),
		BattleRecordData: this.ToBattleRecordDataPb(),
		Buffs:            this.ToBuffsPb(),
	}
	ret.Hp = map[int32]int32{}
	ret.Hp[0] = int32(this.curHp)
	return ret
}

// 用于战斗记录的数据
func (this *AreaPawn) Strip2() *g.PawnStrip2 {
	return &g.PawnStrip2{
		Index:           this.AIndex,
		Uid:             this.Uid,
		Point:           this.Point.Clone(),
		Id:              this.Id,
		Lv:              this.Lv,
		SkinId:          this.SkinId,
		CurHp:           this.curHp,
		CurAnger:        this.curAnger,
		AttackSpeed:     this.AttackSpeed,
		Equip:           this.Equip.ToStrip(),
		Portrayal:       this.ToPortrayalStrip(),
		RodeleroCadetLv: this.RodeleroCadetLv,
		PetId:           this.PetId,
	}
}

// // 用于战斗记录的数据
// func (this *AreaPawn) Strip2() map[string]interface{} {
// 	return map[string]interface{}{
// 		"index":           this.AIndex,
// 		"uid":             this.Uid,
// 		"point":           this.Point.Clone(),
// 		"id":              this.Id,
// 		"lv":              this.Lv,
// 		"skinId":          this.SkinId,
// 		"curHp":           this.curHp,
// 		"curAnger":        this.curAnger,
// 		"attackSpeed":     this.AttackSpeed,
// 		"equip":           this.Equip.ToJson(),
// 		"portrayal":       this.ToPortrayalJson(),
// 		"rodeleroCadetLv": this.RodeleroCadetLv,
// 		"petId":           this.PetId,
// 	}
// }

// 用于战斗记录的数据
func (this *AreaPawn) Strip3() *g.PawnStrip3 {
	return &g.PawnStrip3{
		Uid:         this.Uid,
		Id:          this.Id,
		Lv:          this.Lv,
		EquipId:     this.Equip.ID,
		PortrayalId: this.GetPortrayalID(),
	}
}

// func (this *AreaPawn) Strip3() map[string]interface{} {
// 	return map[string]interface{}{
// 		"uid":         this.Uid,
// 		"id":          this.Id,
// 		"lv":          this.Lv,
// 		"equipId":     this.Equip.ID,
// 		"portrayalId": this.GetPortrayalID(),
// 	}
// }

func (this *AreaPawn) ToShortDataPb() *pb.AreaPawnInfo {
	return &pb.AreaPawnInfo{
		Uid:         this.Uid,
		Id:          int32(this.Id),
		Lv:          int32(this.Lv),
		SkinId:      int32(this.SkinId),
		Hp:          map[int32]int32{0: int32(this.curHp), 1: int32(this.GetMaxHP())},
		Treasures:   this.ToTreasuresPb(),
		Equip:       this.Equip.ToPb(),
		Portrayal:   this.ToPortrayalPb(),
		AttackSpeed: int32(this.AttackSpeed),
	}
}

func (this *AreaPawn) ToNotifyPb() *pb.AreaPawnInfo {
	return &pb.AreaPawnInfo{
		ArmyUid:     this.ArmyUid,
		Uid:         this.Uid,
		SkinId:      int32(this.SkinId),
		AttackSpeed: int32(this.AttackSpeed),
		Equip:       this.Equip.ToPb(),
	}
}

func (this *AreaPawn) ToPortrayalJson() map[string]interface{} {
	if this.Portrayal != nil {
		return this.Portrayal.ToJson()
	}
	return nil
}

func (this *AreaPawn) ToPortrayalStrip() *g.PortrayalStrip {
	if this.Portrayal != nil {
		return this.Portrayal.ToStrip()
	}
	return nil
}

func (this *AreaPawn) ToPortrayalPb() *pb.PortrayalInfo {
	if this.Portrayal != nil {
		return this.Portrayal.ToPbForPawn()
	}
	return nil
}

func (this *AreaPawn) InitJson() *AreaPawn {
	this.baseJson = config.GetJsonData("pawnBase", this.Id)
	this.pawnType = ut.Int32(this.baseJson["type"])
	this.marchSpeed = ut.Int32(this.baseJson["march_speed"])
	this.velocity = ut.Int32(this.baseJson["velocity"])
	this.bagCap = ut.Int32(this.baseJson["bag_cap"])
	if this.pawnType == constant.PAWN_TYPE_MACHINE {
		this.Lv = 1 //器械固定1级
	}
	this.UpdateAttrJson(false)
	return this
}

func (this *AreaPawn) UpdateAttrJson(isUp bool) {
	attrId := this.Id*1000 + this.Lv
	this.attrJson = config.GetJsonData("pawnAttr", attrId)
	if this.attrJson == nil {
		log.Error("UpdateAttrJson error. attId: %v", attrId)
		return
	}
	this.attrId = attrId
	this.maxHp = ut.Int32(this.attrJson["hp"])
	this.maxAnger = ut.Int32(this.attrJson["anger"])
	this.attack = ut.Int32(this.attrJson["attack"])
	this.attackRange = ut.Int32(this.attrJson["attack_range"])
	this.moveRange = ut.Int32(this.attrJson["move_range"])
	this.behaviorId = ut.Int32(this.attrJson["behavior_id"])
	ids := ut.StringToInt32s(ut.String(this.attrJson["skill"]), "|")
	this.skills = []*PawnSkill{}
	for _, id := range ids {
		if skill := NewPawnSkill(id); skill != nil {
			this.skills = append(this.skills, skill)
		}
	}
	this.upCost = g.StringToTypeObjs(this.attrJson["lv_cost"])
	this.UpdateAttr(isUp)
}

// 设置装备
func (this *AreaPawn) ChangeEquip(id int32, attrs [][]int32, recover bool) {
	if this.Equip.ID != id {
		this.Equip.SetID(id)
		this.UpdateEquipAttr(id, attrs, recover)
	}
}

// 刷新当前的装备属性
func (this *AreaPawn) UpdateCurrEquipAttr(equipAttrMap map[int32][][]int32) bool {
	if equipAttrMap == nil {
		return false
	} else if this.Equip.IsSmelt() {
		preId := this.Equip.ID
		this.Equip.ID = 0
		for id := range equipAttrMap {
			if id > 10000 {
				if id == preId {
					this.Equip.ID = id
				} else {
					this.Equip.SetID(id)
				}
				break
			}
		}
	}
	return this.setCurrEquipAttr(this.Equip.ID, equipAttrMap[this.Equip.ID])
}

func (this *AreaPawn) setCurrEquipAttr(id int32, attrs [][]int32) bool {
	if this.Equip.ID == 0 || attrs == nil {
		this.Equip.SetAttr([][]int32{})
	} else if this.Equip.ID == id {
		this.setEquipAttr(attrs)
	} else if this.Equip.IsSmelt() && id > 10000 {
		this.Equip.SetID(id)
		this.setEquipAttr(attrs)
	} else {
		return false
	}
	return true
}

// 刷新装备属性
func (this *AreaPawn) UpdateEquipAttr(id int32, attrs [][]int32, recover bool) bool {
	if this.setCurrEquipAttr(id, attrs) {
		this.UpdateAttr(recover)
		return true
	}
	return false
}

// 设置当前装备的属性 如果熔炼是专属装备 那么还得看是否这个士兵携带
func (this *AreaPawn) setEquipAttr(attrs [][]int32) {
	if !this.Equip.IsExclusive() || this.Equip.CheckExclusivePawn(this.Id) {
		this.Equip.SetAttr(attrs)
	} else {
		this.Equip.ID = 0
		this.Equip.SetAttr([][]int32{})
	}
}

// 设置画像
func (this *AreaPawn) ChangePortrayal(id int32, attrs [][]int32, recover bool) {
	this.Portrayal = g.NewPortrayal(id, 0)
	this.Portrayal.SetAttr(attrs)
	this.UpdateAttr(recover)
}

// 刷新英雄属性
func (this *AreaPawn) UpdateHeroAttr(id int32, attrs [][]int32, recover bool) bool {
	if this.Portrayal == nil || attrs == nil || this.Portrayal.ID != id {
		return false
	}
	this.Portrayal.SetAttr(attrs)
	this.UpdateAttr(recover)
	return true
}

// 刷新当前的装备属性
func (this *AreaPawn) UpdateCurrHeroAttr(heroAttrMap map[int32][][]int32) bool {
	if this.Portrayal == nil || heroAttrMap == nil {
		return false
	}
	attrs := heroAttrMap[this.Portrayal.ID]
	if attrs == nil {
		return false
	}
	this.Portrayal.SetAttr(attrs)
	return true
}

func (this *AreaPawn) UpdateAttrBattleBeginAndEnd(equipAttrMap map[int32][][]int32, heroAttrMap map[int32][][]int32) {
	a := this.UpdateCurrEquipAttr(equipAttrMap)
	b := this.UpdateCurrHeroAttr(heroAttrMap)
	if a || b {
		this.UpdateAttr(false)
	}
}

// 刷新属性
func (this *AreaPawn) UpdateAttr(isUp bool) {
	if this.State == constant.AS_FIGHT {
		return //战斗中不能刷新属性
	}
	// 基础属性
	maxHp, attack, attackRange, moveRange := ut.Int32(this.attrJson["hp"]), ut.Int32(this.attrJson["attack"]), ut.Int32(this.attrJson["attack_range"]), ut.Int32(this.attrJson["move_range"])
	this.maxHp = maxHp
	this.attack = attack
	this.attackRange = attackRange
	this.moveRange = moveRange
	var runDay int32 = 1
	if this.wld != nil {
		runDay = this.wld.GetServerRunDay()
	}
	addHpRatio, addAttackRatio := 0.0, 0.0
	// 装备属性
	equipEffects := this.Equip.GetEquipEffects()
	for _, m := range equipEffects {
		if m.Type == eeffect.ADD_BASE_ATTACK || (m.Type >= eeffect.ADD_BASE_ATTACK_1 && m.Type <= eeffect.ADD_BASE_ATTACK_4) { //提高基础攻击力
			this.attack += int32(ut.Round(float64(attack) * m.Value * 0.01))
		} else if m.Type == eeffect.ADD_BASE_HP || (m.Type >= eeffect.ADD_BASE_HP_1 && m.Type <= eeffect.ADD_BASE_HP_4) { //提高基础生命
			this.maxHp += int32(ut.Round(float64(maxHp) * m.Value * 0.01))
		} else if m.Type == eeffect.TODAY_ADD_HP { //根据服务器运行时间加生命
			this.maxHp += int32(m.Value) * runDay
		} else if m.Type == eeffect.TODAY_ADD_ATTACK { //根据服务器运行时间加攻击
			this.attack += int32(m.Value) * runDay
		} else if m.Type == eeffect.CENTERING_HELMET { //提高基础生命比例
			addHpRatio += (m.Value * 0.01)
		} else if m.Type == eeffect.NOT_DODGE { //攻击范围+1
			this.attackRange += 1
		} else if m.Type == eeffect.ADD_MOVE_RANGE { //移动范围+1
			this.moveRange += 1
		}
	}
	this.maxHp += this.Equip.GetHP()
	this.attack += this.Equip.GetAttack()
	// 画像属性
	if this.Portrayal != nil {
		this.maxHp += this.Portrayal.GetHP()
		this.attack += this.Portrayal.GetAttack()
		// 裴行俨 自带攻击力
		if this.Portrayal.GetSkillID() == hero.PEI_XINGYAN {
			addAttackRatio += this.Portrayal.GetSkill().GetValue() * 0.01
		}
	}
	// 见习勇者
	if skill := this.GetSkillByType(constant.PAWN_SKILL_TYPE_CADET); skill != nil {
		cadetLv := ut.MinInt32(this.RodeleroCadetLv, int32(skill.GetValue()))
		this.maxHp += cadetLv * 5
		this.attack += ut.RoundInt32(float64(cadetLv) * 0.5)
	}
	// 生命 提升比列
	if addHpRatio > 0 {
		this.maxHp += ut.RoundInt32(float64(this.maxHp) * addHpRatio)
	}
	// 攻击 提升比例
	if addAttackRatio > 0 {
		this.attack += ut.RoundInt32(float64(this.attack) * addAttackRatio)
	}
	// 是否有技能强化
	if skillIntensify := this.Equip.GetSkillIntensify(); skillIntensify != nil && len(skillIntensify) == 2 {
		id, tp := skillIntensify[0], skillIntensify[1]
		if skill := array.Find(this.skills, func(m *PawnSkill) bool { return m.BaseId == id }); skill != nil {
			skill.SetIntensifyType(tp)
		}
	} else {
		for _, skill := range this.skills {
			skill.SetIntensifyType(0)
		}
	}
	// 如果满血或者是升级 就直接满血
	if isUp {
		this.curHp = this.GetMaxHP()
	} else {
		this.curHp = ut.MinInt32(this.curHp, this.GetMaxHP())
	}
}

// 回复血量
func (this *AreaPawn) RecoverHP() {
	this.curHp = this.GetMaxHP()
}

// 刷新 见习勇者 层数
func (this *AreaPawn) UpdateRodeleroCadetLv() {
	if this.Id == 3205 && this.wld != nil { //剑盾兵
		this.RodeleroCadetLv = this.wld.GetPlayerRodeleroCadetLv(this.Owner)
	} else {
		this.RodeleroCadetLv = 0
	}
}

func (this *AreaPawn) SetRodeleroCadetLv(lv int32) {
	this.RodeleroCadetLv = lv
}

// 记录战斗数据
func (this *AreaPawn) AddBattleRecordData(data map[int32]int32) {
	if data == nil {
		return
	}
	this.battleRecordMutex.Lock()
	defer this.battleRecordMutex.Unlock()
	for k, v := range data {
		this.BattleRecordData[k] += v
	}
}

func (this *AreaPawn) ToBattleRecordData() map[int32]int32 {
	t := ut.Now()
	this.battleRecordMutex.RLock()
	if dt := ut.Now() - t; dt > 100 {
		log.Info("AreaPawn.ToBattleRecordData battleRecordMutex.RLock uid: %v, owner: %v, armyUid: %v, time: %v", this.Uid, this.Owner, this.ArmyUid, dt)
	}
	defer this.battleRecordMutex.RUnlock()
	data := map[int32]int32{}
	for k, v := range this.BattleRecordData {
		data[k] = v
	}
	return data
}

func (this *AreaPawn) ToBattleRecordDataPb() map[int32]int32 {
	this.battleRecordMutex.RLock()
	defer this.battleRecordMutex.RUnlock()
	data := map[int32]int32{}
	for k, v := range this.BattleRecordData {
		data[int32(k)] = int32(v)
	}
	return data
}

func (this *AreaPawn) GetEnterDir() int32     { return this.EnterDir }
func (this *AreaPawn) GetID() int32           { return this.Id }
func (this *AreaPawn) GetUID() string         { return this.Uid }
func (this *AreaPawn) GetLV() int32           { return this.Lv }
func (this *AreaPawn) GetSkinID() int32       { return this.SkinId }
func (this *AreaPawn) GetOwner() string       { return this.Owner }
func (this *AreaPawn) GetArmyUid() string     { return this.ArmyUid }
func (this *AreaPawn) GetPawnType() int32     { return this.pawnType }
func (this *AreaPawn) GetCerealCost() float64 { return ut.Float64(this.baseJson["cereal_cost"]) }

func (this *AreaPawn) GetViewID() int32 {
	if portrayalID := this.GetPortrayalID(); portrayalID != 0 {
		return portrayalID
	} else if this.SkinId != 0 {
		return this.SkinId
	}
	return this.Id
}

// 设置皮肤
func (this *AreaPawn) SetSkinId(id int32) {
	if id != 0 && id/1000 != this.Id {
		id = 0
	}
	this.SkinId = id
}

func (this *AreaPawn) GetPoint() *ut.Vec2 { return this.Point }
func (this *AreaPawn) SetPoint(point *ut.Vec2) {
	if point != nil {
		this.Point.Set(point)
	}
}

func (this *AreaPawn) IsDie() bool {
	return this.curHp <= 0 && this.maxHp > 0
}

func (this *AreaPawn) GetCurHP() int32 { return this.curHp }
func (this *AreaPawn) SetCurHP(val int32) {
	this.curHp = val
}
func (this *AreaPawn) SetMaxHP(val int32) {
	this.maxHp = val
}

func (this *AreaPawn) GetCurAnger() int32 { return this.curAnger }
func (this *AreaPawn) GetMaxAnger() int32 {
	if this.maxAnger == 0 {
		return 0
	}
	maxAnger := this.maxAnger
	// 吕蒙 -50%怒气
	if heroSkill := this.GetPortrayalSkill(); heroSkill != nil && heroSkill.Id == hero.LV_MENG {
		maxAnger = int32(ut.Round(float64(this.maxAnger) * 0.5))
	}
	//韬略 怒气-1
	if this.IsHasStrategys(40102, 40201, 40303, 40401) {
		maxAnger = ut.MaxInt32(1, maxAnger-1)
	}
	return maxAnger
}

func (this *AreaPawn) SetCurAnger(val int32) {
	this.curAnger = val
}
func (this *AreaPawn) IsHasAnger() bool { return this.maxAnger > 0 }

func (this *AreaPawn) InitAnger() {
	if this.attrJson != nil {
		this.curAnger = ut.Int32(this.attrJson["init_anger"])
	} else {
		this.curAnger = 0
	}
}

func (this *AreaPawn) GetBaseJson() map[string]interface{}  { return this.baseJson }
func (this *AreaPawn) GetAttrJson() map[string]interface{}  { return this.attrJson }
func (this *AreaPawn) GetAttackSpeed() int32                { return this.AttackSpeed }
func (this *AreaPawn) GetBehaviorId() int32                 { return this.behaviorId }
func (this *AreaPawn) GetAttack() int32                     { return this.attack }
func (this *AreaPawn) GetAttackIndex() int32                { return this.attackIndex }
func (this *AreaPawn) SetAttackIndex(val int32)             { this.attackIndex = val }
func (this *AreaPawn) GetUpCost() []*g.TypeObj              { return this.upCost }
func (this *AreaPawn) GetEquip() *g.EquipInfo               { return this.Equip }
func (this *AreaPawn) GetEquipEffects() []*g.EquipEffectObj { return this.Equip.GetEquipEffects() }
func (this *AreaPawn) GetRodeleroCadetLv() int32            { return this.RodeleroCadetLv }
func (this *AreaPawn) GetMarchSpeed() int32                 { return this.marchSpeed }
func (this *AreaPawn) IsHero() bool                         { return this.Portrayal != nil } //是否英雄
func (this *AreaPawn) GetPortrayal() *g.PortrayalInfo       { return this.Portrayal }
func (this *AreaPawn) GetPetId() int32                      { return this.PetId }

func (this *AreaPawn) GetAttackRange() int32 {
	return this.attackRange + +this.GetStrategyValue(40101) + this.GetStrategyValue(40302) + this.GetStrategyValue(50013)
}

func (this *AreaPawn) GetMoveRange() int32 {
	return this.moveRange + this.GetStrategyValue(31901)
}

// 获取移动速度 每秒移动距离
func (this *AreaPawn) GetMoveVelocity() int32 {
	if this.Portrayal != nil {
		return this.Portrayal.GetMoveVelocity()
	}
	return this.velocity
}

// 获取最大攻击力 目前只用于陌刀
func (this *AreaPawn) GetInstabilityMaxAttack() int32 {
	skill := this.GetSkillByType(constant.PAWN_SKILL_TYPE_INSTABILITY_ATTACK)
	if skill == nil {
		return this.attack
	}
	attack := int32(skill.GetValue()) + ut.MaxInt32(0, this.attack-ut.Int32(this.attrJson["attack"]))
	return this.AmendAttack(attack, 0)
}

func (this *AreaPawn) AmendAttack(attack, ignoreBuffType int32) int32 {
	addAttackRatio, lowAttackRatio := 0.0, 0.0
	// buff
	this.buffsMutex.RLock()
	for _, buff := range this.Buffs {
		if buff.Type == ignoreBuffType {
			continue
		} else if buff.Type == bufftype.LOW_HP_ADD_ATTACK || buff.Type == bufftype.KUROU_ADD_ATTACK {
			addAttackRatio += buff.Value //提升攻击力百分比
		} else if buff.Type == bufftype.INSPIRE || buff.Type == bufftype.WORTHY_MONARCH || buff.Type == bufftype.ADD_EXECUTE_ATTACK || buff.Type == bufftype.KILL_ADD_ATTACK || buff.Type == bufftype.GOD_WAR || buff.Type == bufftype.DDIE_ADD_ATTACK {
			attack += buff.GetValueInt() //增加攻击力
		} else if buff.Type == bufftype.WISDOM_COURAGE { //姜维 智勇
			if skill := this.GetPortrayalSkill(); skill != nil && skill.Id == hero.JIANG_WEI {
				attack += (int32(buff.Value) * skill.GetTarget())
			}
		} else if buff.Type == bufftype.VALOR { //李嗣业 勇猛
			if skill := this.GetPortrayalSkill(); skill != nil && skill.Id == hero.LI_SIYE {
				attack += (int32(buff.Value) * skill.GetTarget())
			}
		} else if buff.Type == bufftype.MORALE { //曹操 士气
			if json := config.GetJsonData("portrayalSkill", hero.CAO_CAO); json != nil {
				attack += (int32(buff.Value) * ut.Int32(json["target"]))
			}
		} else if buff.Type == bufftype.TIGER_MANIA { //许褚 虎痴
			addAttackRatio += (buff.Value * 0.01)
		} else if buff.Type == bufftype.DESTROY_WEAPONS { //摧毁武器 降低攻击力
			lowAttackRatio += buff.Value
		} else if buff.Type == bufftype.LOW_HP_ADD_ATTACK_S { //韬略 加攻击力
			addAttackRatio += (buff.Value * 0.01)
		} else if buff.Type == bufftype.THOUSAND_UMBRELLA { //千机伞
			if effect := this.GetEquipEffectByType(eeffect.THOUSAND_UMBRELLA); effect != nil {
				val := ut.If(buff.Value == 0, effect.Value*2, effect.Value)
				addAttackRatio += (val * 0.01)
			}
		} else if buff.Type == bufftype.BREAK_ENEMY_RANKS { //高顺 陷阵
			addAttackRatio += (buff.Value * 0.01)
		} else if buff.Type == bufftype.FEED_INTENSIFY { //养由基 投喂强化
			addAttackRatio += (buff.Value * 0.06)
		} else if buff.Type == bufftype.COURAGEOUSLY { //典韦 奋勇
			attack += int32(buff.Value)
		}
	}
	this.buffsMutex.RUnlock()
	// 韬略
	this.StrategyBuffMap.ForEach(func(v *g.StrategyObj, k int32) bool {
		if v.Type == 20001 || v.Type == 40301 || v.Type == 50010 {
			attack += v.Value //加攻击力
		} else if v.Type == 20003 {
			addAttackRatio += (v.GetValue() * 0.01) //加攻击力%
		}
		return true
	})
	// 提升比列
	if addAttackRatio > 0 {
		attack += int32(ut.Round(float64(attack) * addAttackRatio))
	}
	// 降低比列
	if lowAttackRatio > 0 {
		attack = ut.MaxInt32(0, attack-int32(ut.Round(float64(attack)*lowAttackRatio)))
	}
	return attack
}

func (this *AreaPawn) GetMaxHP() int32 {
	return this.AmendMaxHp(0)
}

func (this *AreaPawn) AmendMaxHp(ignoreBuffType int32) int32 {
	hp := this.maxHp
	addHpRatio, lowHpRatio := 0.0, 0.0
	// buff
	this.buffsMutex.RLock()
	for _, buff := range this.Buffs {
		if buff.Type == ignoreBuffType {
			continue
		} else if buff.Type == bufftype.MORALE { //曹操 士气
			if json := config.GetJsonData("portrayalSkill", hero.CAO_CAO); json != nil {
				hp += (int32(buff.Value) * ut.Int32(json["params"]))
			}
		} else if buff.Type == bufftype.TOUGH { //曹仁 坚韧
			if skill := this.GetPortrayalSkill(); skill != nil && skill.Id == hero.CAO_REN {
				hp += (int32(buff.Value) * skill.GetTarget())
			}
		} else if buff.Type == bufftype.BREAK_ENEMY_RANKS { //高顺 陷阵
			addHpRatio += 0.1
		} else if buff.Type == bufftype.FEED_INTENSIFY { //养由基 投喂强化
			addHpRatio += (buff.Value * 0.04)
		} else if buff.Type == bufftype.TYRANNICAL { //董卓 暴虐
			lowHpRatio += (buff.Value * 0.04)
		}
	}
	this.buffsMutex.RUnlock()
	// 韬略
	this.StrategyBuffMap.ForEach(func(v *g.StrategyObj, k int32) bool {
		if v.Type == 20002 {
			hp += v.Value
		} else if v.Type == 50010 {
			hp += v.Params
		} else if v.Type == 20004 {
			addHpRatio += (v.GetValue() * 0.01)
		}
		return true
	})
	// 提升比列
	if addHpRatio > 0 {
		hp += int32(ut.Round(float64(hp) * addHpRatio))
	}
	// 降低比列
	if lowHpRatio > 0 {
		hp = ut.MaxInt32(1, hp-int32(ut.Round(float64(hp)*lowHpRatio)))
	}
	return hp
}

// 获取画像id
func (this *AreaPawn) GetPortrayalID() int32 {
	if this.Portrayal != nil {
		return this.Portrayal.ID
	}
	return 0
}

// 获取画像技能
func (this *AreaPawn) GetPortrayalSkill() *g.PortrayalSkill {
	if this.Portrayal != nil {
		return this.Portrayal.GetSkill()
	}
	return nil
}

func (this *AreaPawn) GetEquipEffectByType(tp int32) *g.EquipEffectObj {
	return this.Equip.GetEquipEffectByType(tp)
}

// 切换状态
func (this *AreaPawn) ChangeState(state int32) {
	this.State = state
	if state == constant.AS_NONE {
		this.InitAnger()
		this.CleanAllBuffs()
	}
}

func (this *AreaPawn) GetSkillByType(tp int32) g.PawnSkill {
	if skill := array.Find(this.skills, func(m *PawnSkill) bool { return m.Type == tp }); skill != nil {
		return skill
	}
	return nil
}

// 获取主动技能
func (this *AreaPawn) GetActiveSkill() g.PawnSkill {
	if skill := array.Find(this.skills, func(m *PawnSkill) bool {
		return m.UseType == 1 || m.Type == constant.PAWN_SKILL_TYPE_FULL_STRING || m.Type == constant.PAWN_SKILL_TYPE_LONGITUDINAL_CLEFT
	}); skill != nil {
		return skill //目前满弦、顺劈也算主动
	}
	return nil
}

func (this *AreaPawn) ToBuffsDB() []map[string]interface{} {
	this.buffsMutex.RLock()
	defer this.buffsMutex.RUnlock()
	return array.Map(this.Buffs, func(m *g.BuffObj, _ int) map[string]interface{} { return m.ToJson() })
}

func (this *AreaPawn) BuffsClone() []*g.BuffObj {
	this.buffsMutex.RLock()
	defer this.buffsMutex.RUnlock()
	return array.Clone(this.Buffs)
}

func (this *AreaPawn) ToBuffsPb() []*pb.BuffInfo {
	this.buffsMutex.RLock()
	defer this.buffsMutex.RUnlock()
	return array.Map(this.Buffs, func(m *g.BuffObj, _ int) *pb.BuffInfo { return m.ToPb() })
}

func (this *AreaPawn) GetBuffValue(tp int32) float64 {
	this.buffsMutex.RLock()
	defer this.buffsMutex.RUnlock()
	if buff := array.Find(this.Buffs, func(m *g.BuffObj) bool { return m.Type == tp }); buff != nil {
		return buff.Value
	}
	return 0
}

func (this *AreaPawn) SetBuffsByDB(data interface{}) {
	this.Buffs = []*g.BuffObj{}
	if buffs, ok := data.(primitive.A); ok {
		for _, val := range buffs {
			this.Buffs = append(this.Buffs, g.NewBuffByJson(val.(map[string]interface{})))
		}
	}
}

func (this *AreaPawn) CleanAllBuffs() {
	this.CleanBuffs()
	this.CleanStrategyBuffs()
}

// buff
func (this *AreaPawn) CleanBuffs() {
	this.buffsMutex.Lock()
	defer this.buffsMutex.Unlock()
	this.Buffs = []*g.BuffObj{}
}
func (this *AreaPawn) GetBuffs() []*g.BuffObj { return this.Buffs }

func (this *AreaPawn) AddBuff(buff *g.BuffObj) {
	this.buffsMutex.Lock()
	defer this.buffsMutex.Unlock()
	this.Buffs = append(this.Buffs, buff)
}

func (this *AreaPawn) RemoveBuffByIndex(i int32) {
	this.buffsMutex.Lock()
	defer this.buffsMutex.Unlock()
	this.Buffs = append(this.Buffs[:i], this.Buffs[i+1:]...)
}

// 外部加锁
func (this *AreaPawn) RemoveBuffByIndexNoLock(i int32) {
	this.Buffs = append(this.Buffs[:i], this.Buffs[i+1:]...)
}

func (this *AreaPawn) BuffsLock() {
	this.buffsMutex.Lock()
}
func (this *AreaPawn) BuffsUnlock() {
	this.buffsMutex.Unlock()
}
func (this *AreaPawn) BuffsRLock() {
	this.buffsMutex.RLock()
}
func (this *AreaPawn) BuffsRUnlock() {
	this.buffsMutex.RUnlock()
}

// 韬略
func (this *AreaPawn) CleanStrategyBuffs() {
	this.StrategyBuffMap.Clean()
}

func (this *AreaPawn) AddStrategyBuff(strategy *g.StrategyObj) {
	this.StrategyBuffMap.Set(strategy.Type, strategy)
}

func (this *AreaPawn) GetStrategyBuff(tp int32) *g.StrategyObj {
	return this.StrategyBuffMap.Get(tp)
}

func (this *AreaPawn) GetStrategyValue(tp int32) int32 {
	if buff := this.StrategyBuffMap.Get(tp); buff != nil {
		return buff.Value
	}
	return 0
}

// 是否有某个韬略
func (this *AreaPawn) IsHasStrategys(tps ...int32) bool {
	this.StrategyBuffMap.RLock()
	defer this.StrategyBuffMap.RUnlock()
	for _, m := range tps {
		if this.StrategyBuffMap.Map[m] != nil {
			return true
		}
	}
	return false
}

// 是否满级
func (this *AreaPawn) IsMaxLv() bool {
	return this.attrJson == nil || this.upCost == nil || len(this.upCost) == 0
}

func (this *AreaPawn) SetLv(lv int32) {
	this.Lv = lv
}

// 更新等级
func (this *AreaPawn) UpdateLv(lv int32) {
	if lv > 0 && this.Lv != lv && !this.IsMaxLv() {
		this.Lv = lv
		this.UpdateAttrJson(true)
	}
}

// 更新属性 在战斗中
func (this *AreaPawn) UpdateAttrByBattle(id, lv int32) {
	if this.Id != id || (lv > 0 && this.Lv != lv && !this.IsMaxLv()) {
		this.Id = id
		this.Lv = lv
		this.UpdateAttrJson(true)
	}
}

func (this *AreaPawn) ToTreasures() []map[string]interface{} {
	t := ut.Now()
	this.treasureMutex.RLock()
	if dt := ut.Now() - t; dt > 100 {
		log.Info("AreaPawn.ToTreasures treasureMutex.RLock uid: %v, owner: %v, armyUid: %v, time: %v", this.Uid, this.Owner, this.ArmyUid, dt)
	}
	defer this.treasureMutex.RUnlock()
	return array.Map(this.Treasures, func(m *g.TreasureInfo, _ int) map[string]interface{} { return m.ToJson() })
}

func (this *AreaPawn) ToTreasuresPb() []*pb.TreasureInfo {
	t := ut.Now()
	this.treasureMutex.RLock()
	if dt := ut.Now() - t; dt > 100 {
		log.Info("AreaPawn.ToTreasuresPb treasureMutex.RLock uid: %v, owner: %v, armyUid: %v, time: %v", this.Uid, this.Owner, this.ArmyUid, dt)
	}
	defer this.treasureMutex.RUnlock()
	return array.Map(this.Treasures, func(m *g.TreasureInfo, _ int) *pb.TreasureInfo { return m.ToPb() })
}

func (this *AreaPawn) GetTreasuresClone() []*g.TreasureInfo {
	this.treasureMutex.RLock()
	defer this.treasureMutex.RUnlock()
	return this.Treasures[:]
}

func (this *AreaPawn) GetTreasureCount() int32 {
	this.treasureMutex.RLock()
	defer this.treasureMutex.RUnlock()
	return int32(len(this.Treasures))
}

// 添加宝箱
func (this *AreaPawn) AddTreasure(cnt, id int32) (int32, []*g.TreasureInfo) {
	if cnt <= 0 || id <= 0 || this.IsBagFull() {
		return 0, []*g.TreasureInfo{}
	}
	count := this.bagCap - this.GetTreasureCount()
	count = ut.MinInt32(count, cnt)
	arr := []*g.TreasureInfo{}
	this.treasureMutex.Lock()
	for i := int32(0); i < count; i++ {
		treasure := g.NewTreasure(id)
		arr = append(arr, treasure)
		this.Treasures = append(this.Treasures, treasure)
	}
	this.treasureMutex.Unlock()
	return count, arr
}

// 背包是否满了
func (this *AreaPawn) IsBagFull() bool {
	return this.GetTreasureCount() >= this.bagCap
}

// 获取宝箱
func (this *AreaPawn) GetTreasureByUID(uid string) *g.TreasureInfo {
	this.treasureMutex.RLock()
	defer this.treasureMutex.RUnlock()
	if treasure := array.Find(this.Treasures, func(m *g.TreasureInfo) bool { return m.UID == uid }); treasure != nil {
		return treasure
	}
	return nil
}

// 删除宝箱
func (this *AreaPawn) RemoveTreasureByUID(uid string) {
	this.treasureMutex.Lock()
	defer this.treasureMutex.Unlock()
	this.Treasures = array.RemoveItem(this.Treasures, func(m *g.TreasureInfo) bool {
		log.Info("RemoveTreasureByUID userId: %v, id: %v", this.Owner, m.ID)
		return m.UID == uid
	})
}

// 删除已经打开的宝箱
func (this *AreaPawn) RemoveTreasureByOpen() []*g.TypeObj {
	this.treasureMutex.Lock()
	defer this.treasureMutex.Unlock()
	rewards := []*g.TypeObj{}
	for i := len(this.Treasures) - 1; i >= 0; i-- {
		data := this.Treasures[i]
		if len(data.Rewards) > 0 {
			rewards = g.MergeTypeObjsCount(rewards, data.Rewards...)
			this.Treasures = append(this.Treasures[:i], this.Treasures[i+1:]...)
			log.Info("RemoveTreasureByOpen userId: %v, id: %v", this.Owner, data.ID)
		}
	}
	return rewards
}
