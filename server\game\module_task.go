package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDTask() {
	this.GetServer().RegisterGO("HD_ClaimTaskReward", this.claimTaskReward)           //领取任务奖励
	this.GetServer().RegisterGO("HD_ClaimTodayTaskReward", this.claimTodayTaskReward) //领取每日任务奖励
	this.GetServer().RegisterGO("HD_ClaimOtherTaskReward", this.claimOtherTaskReward) //领取其他任务奖励
}

// 领取任务奖励
func (this *Game) claimTaskReward(session gate.Session, msg *pb.GAME_HD_CLAIMTASKREWARD_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	id := msg.GetId()
	if plr.InGuideTaskFinish(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	task := plr.GetGuideTask(id)
	if task == nil {
		return nil, ecode.TASK_NOT_EXIST.String()
	} else if !plr.CheckTaskConditionOne(task) {
		return nil, ecode.TASK_NOT_COMPLETE.String()
	}
	// 发放奖励
	items := task.GetRewards()
	rewards := this.ChangePlayerItems(plr, items, constant.GOLD_CHANGE_TASK_GET)
	// 放入已完成列表 并获取下一个任务
	plr.FinishGuideTask(task)
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_rookieTask", map[string]interface{}{"rookie_id": id})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMTASKREWARD_S2C{
		Rewards:    rewards,
		Tasks:      plr.Tasks.ToPb(),
		TodayTasks: plr.TodayTasks.ToPb(),
	})
}

// 领取每日任务奖励
func (this *Game) claimTodayTaskReward(session gate.Session, msg *pb.GAME_HD_CLAIMTODAYTASKREWARD_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	id := msg.GetId()
	if plr.InTodayTaskFinish(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	task := plr.GetTodayTask(id)
	if task == nil {
		return nil, ecode.TASK_NOT_EXIST.String()
	} else if !plr.CheckTaskConditionOne(task) {
		return nil, ecode.TASK_NOT_COMPLETE.String()
	}
	items := task.GetRewards()
	treasureIndex, selectIndex := int(msg.GetTreasureIndex()), int(msg.GetSelectIndex())
	if treasureIndex > 0 {
		items = task.GetTreasureRewards(treasureIndex - 1)
	} else if selectIndex > 0 {
		items = task.GetSelectRewards(selectIndex - 1)
	}
	// 发放道具
	plr.ChangeCostByTypeObjs(items, 1)
	// 放入已完成列表 并获取下一个任务 宝箱奖励都领完了 再放入完成
	if len(task.TreasureRewards) == 0 {
		plr.FinishTodayTask(task)
	}
	// 上报
	// wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_rookieTask", map[string]interface{}{"rookie_id": id})
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMTODAYTASKREWARD_S2C{
		Rewards:    plr.ToItemByTypeObjsPb(items),
		TodayTasks: plr.TodayTasks.ToPb(),
	})
}

// 领取其他任务奖励
func (this *Game) claimOtherTaskReward(session gate.Session, msg *pb.GAME_HD_CLAIMOTHERTASKREWARD_C2S) (ret []byte, err string) {
	e, _, plr, _ := checkError(session)
	if e != "" {
		return nil, e
	}
	id := msg.GetId()
	if plr.InOtherTaskFinish(id) {
		return nil, ecode.YET_CLAIM.String()
	}
	task := plr.GetOtherTask(id)
	if task == nil {
		return nil, ecode.TASK_NOT_EXIST.String()
	} else if !plr.CheckTaskConditionOne(task) {
		return nil, ecode.TASK_NOT_COMPLETE.String()
	}
	items := task.GetRewards()
	treasureIndex, selectIndex := int(msg.GetTreasureIndex()), int(msg.GetSelectIndex())
	if treasureIndex > 0 {
		items = task.GetTreasureRewards(treasureIndex - 1)
	} else if selectIndex > 0 {
		items = task.GetSelectRewards(selectIndex - 1)
	}
	// 发放道具
	plr.ChangeCostByTypeObjs(items, 1)
	// 放入已完成列表 并获取下一个任务 宝箱奖励都领完了 再放入完成
	if len(task.TreasureRewards) == 0 {
		plr.FinishOtherTask(task)
	}
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMOTHERTASKREWARD_S2C{
		Rewards:    plr.ToItemByTypeObjsPb(items),
		OtherTasks: plr.OtherTasks.ToPb(),
	})
}
