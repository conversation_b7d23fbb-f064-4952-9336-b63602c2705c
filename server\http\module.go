package http

import (
	"context"
	"encoding/json"
	"net/http"
	slg "slgsrv/server/common"
	"slgsrv/server/common/sdk"
	"slgsrv/server/http/db"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	mqrpc "github.com/huyangv/vmqant/rpc"
)

var Module = func() module.Module {
	return new(Http)
}

type Http struct {
	basemodule.BaseModule
}

func (this *Http) GetType() string {
	return "http" //很关键,需要与配置文件中的Module配置对应
}

func (this *Http) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

func (this *Http) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "http" || serverType == "development" {
		//this.webManager = &DbManager{
		//	Cols: make(map[string]*mongo.Collection),
		//}
		db.GlobalGetTableManager()
		db.InsertDefaultUser()
		sdk.InitAppleRootCerts()
	}
}

func (this *Http) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	// 客户端
	this.GetServer().RegisterGO("/http/getHotUpdateInfo", this.httpGetHotUpdateInfo)
	this.GetServer().RegisterGO("/http/getServerInfo", this.httpGetServerInfo)
	this.GetServer().RegisterGO("/http/errorReport", this.httpErrorReport)
	this.GetServer().RegisterGO("/http/feedback", this.httpFeedback)
	this.GetServer().RegisterGO("/http/hotUpdateStart", this.httpHotUpdateStart)
	this.GetServer().RegisterGO("/http/hotUpdateEnd", this.httpHotUpdateEnd)
	this.GetServer().RegisterGO("/http/getUpdateMaintainTime", this.httpGetUpdateMaintainTime)
	this.GetServer().RegisterGO("/http/getServerLoad", this.httpGetServerLoad)
	this.GetServer().RegisterGO("/http/getNotice", this.httpGetNotice)

	// 后台管理
	this.GetServer().RegisterGO("/http/login", this.httpLogin)
	this.GetServer().RegisterGO("/http/info", this.httpInfo)
	this.GetServer().RegisterGO("/http/createRole", this.httpCreateRole)
	this.GetServer().RegisterGO("/http/getRoles", this.httpGetRoles)
	this.GetServer().RegisterGO("/http/queryOperateLog", this.httpQueryOperateLog)
	this.GetServer().RegisterGO("/http/changePassword", this.httpChangePassword)

	// 后台接口
	this.GetServer().RegisterGO("/http/addUpdateUrl", this.httpAddUpdateUrl)
	this.GetServer().RegisterGO("/http/delUpdateUrl", this.httpDelUpdateUrl)
	this.GetServer().RegisterGO("/http/getUpdateUrls", this.httpGetUpdateUrls)
	this.GetServer().RegisterGO("/http/getErrorReport", this.httpGetErrorReport)
	this.GetServer().RegisterGO("/http/getErrorReportVersions", this.httpGetErrorReportVersions)
	this.GetServer().RegisterGO("/http/getFeedback", this.httpGetFeedback)
	this.GetServer().RegisterGO("/http/responseFeedback", this.httpResponseFeedback)
	this.GetServer().RegisterGO("/http/getFeedbackVersions", this.httpGetFeedbackVersions)
	this.GetServer().RegisterGO("/http/saveGoCode", this.httpSaveGoCode)
	this.GetServer().RegisterGO("/http/getGoCode", this.httpGetGoCode)
	this.GetServer().RegisterGO("/http/delGoCode", this.httpDelGoCode)
	this.GetServer().RegisterGO("/http/getTime", this.httpGetTime)
	this.GetServer().RegisterGO("/http/setVersion", this.httpSetVersion)
	this.GetServer().RegisterGO("/http/setExtraParams", this.httpSetExtraParams)
	this.GetServer().RegisterGO("/http/getHotUpdateReportInfo", this.httpGetHotUpdateReportInfo)
	this.GetServer().RegisterGO("/http/setServerStopTime", this.httpSetServerStopTime)
	this.GetServer().RegisterGO("/http/getServerStopTime", this.httpGetServerStopTime)
	this.GetServer().RegisterGO("/http/getCdkModel", this.httpGetCdkModel)
	this.GetServer().RegisterGO("/http/addCdkModel", this.httpAddCdkModel)
	this.GetServer().RegisterGO("/http/notice", this.httpNotice)
	this.GetServer().RegisterGO("/http/getReqLogs", this.httpGetReqLogs)
	this.GetServer().RegisterGO("/http/setNotice", this.httpSetNotice)
	this.GetServer().RegisterGO("/http/getUserRacharge", this.httpGetUserRecharge)
	this.GetServer().RegisterGO("/http/testOrderRefund", this.httpTestOrderRefund)
	this.GetServer().RegisterGO("/http/getGoogleGlossryParam", this.httpGetGoogleGlossryParam)
	this.GetServer().RegisterGO("/http/createGoogleGlossry", this.httpCreateGoogleGlossry)
	this.GetServer().RegisterGO("/http/delGoogleGlossry", this.httpDelGoogleGlossry)

	// 支付
	this.GetServer().RegisterGO("/http/appleRefundNotify", this.httpAppleNotify)
	this.GetServer().RegisterGO("/http/googleNotify", this.httpGoogleNotify)

	// 活动
	this.GetServer().RegisterGO("/http/getDcFollowGift", this.httpGetDcFollowGift)

	this.InitRpc()

	InitNotify()
	//this.SetListener(this)
}

func (this *Http) Run(closeSig chan bool) {
	RunTick(this)
	srv := this.startHttpServer()
	<-closeSig
	if err := srv.Shutdown(context.Background()); err != nil {
		panic(err) // failure/timeout shutting down the server gracefully
	}
	StopTick()
	log.Info("web: done. exiting")
}

func (this *Http) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 启动http服务器
func (this *Http) startHttpServer() *http.Server {
	port := ut.String(this.GetModuleSettings().Settings["Port"])
	srv := &http.Server{
		Addr:         ":" + port,
		WriteTimeout: time.Second * 15, // 写超时
	}
	this.InitHandleFunc()
	log.Info("starting HTTP server " + srv.Addr)
	// 监听
	go func() {
		if err := srv.ListenAndServe(); err != nil {
			log.Info("Httpserver: ListenAndServe() error: %s", err)
		}
	}()
	return srv
}

func (this *Http) httpHandler(_ http.ResponseWriter, serverType string, topic string, params mqrpc.ParamOption) []byte {
	ctx, cancel := context.WithTimeout(context.TODO(), time.Second*15)
	defer cancel()
	body, err := mqrpc.InterfaceMap(this.Call(
		ctx,
		serverType,
		"/http/"+topic,
		params,
	))
	if err != nil {
		body = slg.HttpResponseErrorNoDataWithDesc(err.Error())
	}
	bytes, _ := json.Marshal(body)
	return bytes
}

func getInvokeRpcInfo(serverType, id string, params []interface{}) (string, []interface{}) {
	moduleType := serverType
	if serverType == slg.MACH_SERVER_TYPE_LOBBY || serverType == slg.MACH_SERVER_TYPE_GAME {
		moduleType = serverType + "@" + serverType + id
	}
	return moduleType, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
}

// 发送Rpc到lobby
func (this *Http) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Http) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到邮件服
func (this *Http) InvokeMailRpc(_func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_MAIL, "", params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到邮件服
func (this *Http) InvokeMailRpcNR(_func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_MAIL, "", params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}
