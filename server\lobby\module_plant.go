package lobby

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"time"

	"github.com/huyangv/vmqant/gate"
)

func (this *Lobby) initHDPlant() {
	this.GetServer().RegisterGO("HD_Planting", this.planting)         //种植
	this.GetServer().RegisterGO("HD_Watering", this.watering)         //浇水
	this.GetServer().RegisterGO("HD_GatherBotany", this.gatherBotany) //采集
}

// 种植
func (this *Lobby) planting(session gate.Session, msg *pb.LOBBY_HD_PLANTING_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.PlantData != nil && user.PlantData.ID != 0 {
		return nil, ecode.UNKNOWN.String()
	}
	id := msg.GetId()
	json := config.GetJsonData("botany", id)
	if json == nil {
		return nil, ecode.UNKNOWN.String()
	} else if gold := ut.Int32(json["gold"]); gold > 0 && user.ChangeGold(-gold, constant.GOLD_CHANGE_PLANTING_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
	}
	user.PlantData = &PlantInfo{ID: id, CompleteTime: ut.Int64(json["time"])*ut.TIME_DAY + time.Now().UnixMilli()}
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_PLANTING_S2C{
		Gold:      user.Gold,
		PlantData: user.ToPlantDataPb(),
	})
}

// 浇水
func (this *Lobby) watering(session gate.Session, msg *pb.LOBBY_HD_WATERING_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.PlantData == nil || user.PlantData.ID == 0 {
		return nil, ecode.UNKNOWN.String()
	} else if user.PlantData.CompleteTime == 0 {
		return nil, ecode.YET_BLOOM.String() //已经开花了
	} else if user.PlantData.IsWatering() {
		return nil, ecode.TODAY_YET_WATERING.String() //今天已经浇过水了
	}
	json := config.GetJsonData("botany", user.PlantData.ID)
	if json == nil {
		return nil, ecode.UNKNOWN.String()
	}
	tp := int(msg.GetType())
	if tp == 2 && user.ChangeGold(-lc.PLANT_FERTILIZE_COST, constant.GOLD_CHANGE_WATERING_COST) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
	}
	// 减去时间
	reduceTime := ut.If(tp == 1, int64(5), 20) * ut.TIME_HOUR
	user.PlantData.CompleteTime = ut.MaxInt64(0, user.PlantData.CompleteTime-reduceTime)
	// 记录浇水时间
	user.PlantData.WateringTime = time.Now().UnixMilli()
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_WATERING_S2C{
		Gold:      user.Gold,
		PlantData: user.ToPlantDataPb(),
	})
}

// 采集
func (this *Lobby) gatherBotany(session gate.Session, msg *pb.LOBBY_HD_GATHERBOTANY_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.PlantData == nil || user.PlantData.ID == 0 {
		return nil, ecode.UNKNOWN.String()
	} else if user.PlantData.CompleteTime > 0 {
		return nil, ecode.BOTANY_NOT_DONE.String() //还未开花
	}
	// 添加
	user.ChangeBotany(user.PlantData.ID, 1, "")
	user.PlantData.Clean()
	user.FlagUpdateDB()
	return pb.ProtoMarshal(&pb.LOBBY_HD_GATHERBOTANY_S2C{
		UnlockBotanys: user.ToUnlockBotanysPb(),
		PlantData:     user.ToPlantDataPb(),
	})
}
