package fsp

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
)

type FSPModel struct {
	battleController *FSPBattleController

	attacker         string                //发起者
	battlePlayerUids []string              //参与战斗的玩家列表
	recordDatas      []*g.BattleRecordData //战斗记录数据

	attackerArmyAccMap map[string]bool //进攻方累计军队uidMap
	defenderArmyAccMap map[string]bool //防守方累计军队uidMap

	recordDataMutex *deadlock.RWMutex
	armyAccMutex    *deadlock.RWMutex

	fps               int32 //帧率
	mul               int32 //加速倍数
	dt                int32
	upSpeedFrame      int32 //多少帧开始加速
	currUpSpeedFrame  int32
	checkFrameCount   int32 //服务器多少帧向客户端同步一次
	currentFrameIndex int32 //当前帧
	attackerArmyAcc   int32 //进攻方累计军队数量
	defenderArmyAcc   int32 //防守方累计军队数量
	isRunning         bool
}

func NewFSPModel() *FSPModel {
	var fps int32 = 20
	var dt int32 = 1000 / fps
	upSpeedFrame := ut.TIME_MINUTE * 30 / dt
	return &FSPModel{
		fps:              fps,
		checkFrameCount:  100,
		mul:              1,
		dt:               dt,
		upSpeedFrame:     upSpeedFrame,
		currUpSpeedFrame: upSpeedFrame,
		recordDataMutex:  new(deadlock.RWMutex),
		armyAccMutex:     new(deadlock.RWMutex),
	}
}

func (this *FSPModel) ToPb(time int32) *pb.BattleInfo {
	msg := this.battleController.ToPb()
	msg.Fps = int32(this.fps)
	msg.CheckFrameCount = int32(this.checkFrameCount)
	msg.CurrentFrameIndex = int32(this.currentFrameIndex)
	msg.Mul = int32(this.mul)
	msg.UpSpeedFrame = int32(this.upSpeedFrame)
	msg.BattleTime = int32(time)
	msg.AttackerArmyAcc = int32(this.attackerArmyAcc)
	msg.DefenderArmyAcc = int32(this.defenderArmyAcc)
	return msg
}

func (this *FSPModel) ToDB() map[string]interface{} {
	return map[string]interface{}{
		"mul":                this.mul,
		"currentFrameIndex":  this.currentFrameIndex,
		"battle":             this.battleController.ToDB(),
		"recordDatas":        this.ToRecordDatas(),
		"attacker":           this.attacker,
		"battlePlayerUids":   array.Map(this.battlePlayerUids, func(m string, _ int) string { return m }),
		"attackerArmyAcc":    this.attackerArmyAcc,
		"defenderArmyAcc":    this.defenderArmyAcc,
		"attackerArmyAccMap": this.GetAttackerArmyMap(),
		"defenderArmyAccMap": this.GetDefenderArmyMap(),
	}
}

func (this *FSPModel) ToRecordDatas() []*g.BattleRecordData {
	this.recordDataMutex.RLock()
	defer this.recordDataMutex.RUnlock()
	datas := []*g.BattleRecordData{}
	for _, m := range this.recordDatas {
		data := m.Copy()
		datas = append(datas, data)
	}
	return datas
}

func (this *FSPModel) FromDB(area Area, data map[string]interface{}, pawns map[string]g.Pawn) *FSPModel {
	this.mul = ut.ClampInt32(ut.Int32(data["mul"]), 1, 3)
	this.currentFrameIndex = ut.Int32(data["currentFrameIndex"])
	this.attacker = ut.String(data["attacker"])
	this.battlePlayerUids = ut.StringArray(data["battlePlayerUids"])
	this.currUpSpeedFrame = this.upSpeedFrame * ((this.mul + 1) * this.mul / 2)
	this.attackerArmyAcc = ut.Int32(data["attackerArmyAcc"])
	this.defenderArmyAcc = ut.Int32(data["defenderArmyAcc"])
	this.attackerArmyAccMap = ut.MapStringBool(data["attackerArmyAccMap"])
	this.defenderArmyAccMap = ut.MapStringBool(data["defenderArmyAccMap"])
	// 初始化战斗记录
	if recordArr, ok := data["recordDatas"].(bson.A); ok {
		recordDatas := []*g.BattleRecordData{}
		for _, v := range recordArr {
			recordData := &g.BattleRecordData{}
			if recordBytes, err := bson.Marshal(v); err == nil {
				if err = bson.Unmarshal(recordBytes, recordData); err == nil {
					recordDatas = append(recordDatas, recordData)
				}
			}
			this.recordDatas = recordDatas

		}
	}
	// 初始化战斗逻辑控制
	this.battleController = new(FSPBattleController).FromDB(area, data["battle"].(map[string]interface{}), pawns)
	return this
}

func (this *FSPModel) Init(area Area, data FSPParam) *FSPModel {
	this.mul = ut.MaxInt32(1, data.FspMul)
	this.currentFrameIndex = 0
	this.attacker = data.Attacker
	this.attackerArmyAcc = data.AttackerArmyNum
	this.defenderArmyAcc = data.DefenderArmyNum
	uids := map[string]bool{}
	for _, p := range data.Pawns {
		owner := p.GetOwner()
		if !uids[owner] {
			uids[owner] = true
			this.battlePlayerUids = append(this.battlePlayerUids, owner)
		}
	}
	// 初始化战斗逻辑控制
	this.battleController = new(FSPBattleController).Init(area, data)
	frame := this.battleController.GetInitData()
	frame.Fps = this.fps
	frame.Owner = area.GetOwner()
	frame.CityId = area.GetCityID()
	frame.Hp = []int32{area.GetCurHP(), area.GetMaxHP()}
	frame.Builds = area.ToBuildsStrip()
	frame.Armys = area.ToArmysStrip2()
	buildPawnId, buildLv := area.GetBuildPawnInfo()
	frame.BuildInfo = []int32{buildPawnId, buildLv}
	this.recordDatas = []*g.BattleRecordData{frame}
	this.attackerArmyAccMap = map[string]bool{}
	this.defenderArmyAccMap = map[string]bool{}
	this.battleController.Run()
	return this
}

func (this *FSPModel) Run() {
	this.isRunning = true
	go func() {
		tiker := time.NewTicker(time.Millisecond * time.Duration(this.dt))
		defer tiker.Stop()
		for this.isRunning {
			<-tiker.C
			this.update()
		}
	}()
}

func (this *FSPModel) Stop() {
	this.isRunning = false
	if this.battleController != nil {
		this.battleController.isEnd = true
	}
	this.battleController = nil
}

func (this *FSPModel) Pause(isPause bool) {
	this.isRunning = !isPause
}

func (this *FSPModel) update() {
	for i := int32(0); i < this.mul; i++ {
		this.tick()
	}
	// 加速
	if this.currentFrameIndex >= this.currUpSpeedFrame && this.mul < 3 {
		this.mul += 1
		this.currUpSpeedFrame += this.upSpeedFrame * this.mul
	}
}

// 每帧执行
func (this *FSPModel) tick() {
	if this.battleController == nil || this.battleController.isEnd {
		return
	}
	this.currentFrameIndex += 1
	this.battleController.area.AddFightersToBattle()
	isEnd := this.battleController.UpdateFrame(this.dt)
	// 同步帧信息
	if !isEnd && this.currentFrameIndex%this.checkFrameCount == 0 {
		this.battleController.NotifyCheckFrame(this.currentFrameIndex)
	}
}

func (this *FSPModel) GetCurrentFrameIndex() int32           { return this.currentFrameIndex }
func (this *FSPModel) GetRecordDatas() []*g.BattleRecordData { return this.ToRecordDatas() }
func (this *FSPModel) GetAttacker() string                   { return this.attacker }
func (this *FSPModel) GetBattlePlayerUids() []string         { return this.battlePlayerUids }
func (this *FSPModel) GetMul() int32                         { return this.mul }

func (this *FSPModel) GetAttackerAcc() int32 {
	this.armyAccMutex.RLock()
	defer this.armyAccMutex.RUnlock()
	return this.attackerArmyAcc
}
func (this *FSPModel) GetDefenderAcc() int32 {
	this.armyAccMutex.RLock()
	defer this.armyAccMutex.RUnlock()
	return this.defenderArmyAcc
}

func (this *FSPModel) GetRandSeed() int {
	return this.battleController.GetRandom().GetSeed()
}

func (this *FSPModel) GetFighterBattleInfoMap() map[string]map[int32]int32 {
	return this.battleController.ToFighterBattleInfoMap()
}
func (this *FSPModel) GetFighterDeadInfoMap() map[string][]*g.PawmDeadRecord {
	return this.battleController.ToFighterDeadInfoMap()
}

func (this *FSPModel) GetAlliUidMap() map[string]string {
	return this.battleController.ToAlliUidMap()
}

func (this *FSPModel) GetLostTreasureResMap() map[string]map[int32]int32 {
	return this.battleController.ToLostTreasureResMap()
}

func (this *FSPModel) AddAttackerAcc(uid string) {
	this.armyAccMutex.Lock()
	defer this.armyAccMutex.Unlock()
	if _, ok := this.attackerArmyAccMap[uid]; ok {
		return
	}
	this.attackerArmyAccMap[uid] = true
	this.attackerArmyAcc++
	this.NotifyArmyAcc()
}

func (this *FSPModel) AddDefenderAcc(uid string) {
	this.armyAccMutex.Lock()
	defer this.armyAccMutex.Unlock()
	if _, ok := this.defenderArmyAccMap[uid]; ok {
		return
	}
	this.defenderArmyAccMap[uid] = true
	this.defenderArmyAcc++
	this.NotifyArmyAcc()
}

func (this *FSPModel) DecDefenderAcc(uid string) {
	this.armyAccMutex.Lock()
	defer this.armyAccMutex.Unlock()
	if _, ok := this.defenderArmyAccMap[uid]; !ok {
		return
	}
	delete(this.defenderArmyAccMap, uid)
	this.defenderArmyAcc--
	this.NotifyArmyAcc()
}

func (this *FSPModel) GetAttackerArmyMap() map[string]bool {
	this.armyAccMutex.RLock()
	defer this.armyAccMutex.RUnlock()
	ret := map[string]bool{}
	for k, v := range this.attackerArmyAccMap {
		ret[k] = v
	}
	return ret
}

func (this *FSPModel) GetDefenderArmyMap() map[string]bool {
	this.armyAccMutex.RLock()
	defer this.armyAccMutex.RUnlock()
	ret := map[string]bool{}
	for k, v := range this.defenderArmyAccMap {
		ret[k] = v
	}
	return ret
}

func (this *FSPModel) NotifyArmyAcc() {
	this.battleController.area.NotifyCheckFrame(&pb.GAME_ONFSPCHECKFRAME_NOTIFY{
		Data: &pb.FrameInfo{
			Type:              int32(constant.FSP_NOTIFY_TYPE_ARMY_ACC),
			CurrentFrameIndex: int32(this.GetCurrentFrameIndex()),
			Hp:                []int32{int32(this.defenderArmyAcc), int32(this.attackerArmyAcc)},
		},
	})
}

// 添加记录信息
func (this *FSPModel) AddRecordData(data *g.BattleRecordData) {
	this.recordDataMutex.Lock()
	defer this.recordDataMutex.Unlock()
	this.recordDatas = append(this.recordDatas, data)
}

// 添加军队
func (this *FSPModel) AddArmy(pawns []g.Pawn, owner string) []*g.FigherStrip {
	if !array.Has(this.battlePlayerUids, owner) {
		this.battlePlayerUids = append(this.battlePlayerUids, owner)
	}
	arr, _ := this.battleController.AddFighters(pawns, owner, true)
	return arr
}

// 添加士兵
func (this *FSPModel) AddPawn(pawn g.Pawn, owner string) []*g.FigherStrip {
	if !array.Has(this.battlePlayerUids, owner) {
		this.battlePlayerUids = append(this.battlePlayerUids, owner)
	}
	arr, _ := this.battleController.AddFighters([]g.Pawn{pawn}, owner, true)
	return arr
}

// 删除军队
func (this *FSPModel) RemoveArmy(uid string) {
	this.battleController.RemoveFightersByArmyUid(uid)
}

// 刷新建筑信息
func (this *FSPModel) UpdateBuildInfo(buildPawnId, buildPawnLv int32) {
	this.battleController.UpdateBuildInfo(buildPawnId, buildPawnLv)
}

// 记录最后一次攻击中心的玩家
func (this *FSPModel) RecordLastAttackMainPlayer() {
	this.battleController.RecordLastAttackMainPlayer()
}

// 获取Fsp是否结束
func (this *FSPModel) GetFspIsEnd() bool {
	if this.battleController == nil {
		return true
	}
	return this.battleController.isEnd
}
