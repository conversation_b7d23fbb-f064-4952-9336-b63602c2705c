package season

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
)

const DEBUG = false
const SEASON_COUNT = 4                       //一共4个季节
const SEASON_DURATION_TIME = 3 * ut.TIME_DAY //一个季节持续的天数

// 季节模块
type Model struct {
	room g.Room

	Type           int   //当前的季节类型 0.春 1.夏 2.秋 3.冬
	NextSeasonTime int64 //下一个季节时间

	effectMap *ut.MapLock[int, float64] //当前季节的效果
}

func NewModel(room g.Room) *Model {
	return &Model{
		room:      room,
		effectMap: ut.NewMapLock[int, float64](),
	}
}

func (this *Model) ToPb() *pb.SeasonInfo {
	return &pb.SeasonInfo{
		Type:                  int32(this.Type),
		NextSeasonSurplusTime: ut.MaxInt64(0, this.NextSeasonTime-time.Now().UnixMilli()),
	}
}

func (this *Model) GetType() int                  { return this.Type }
func (this *Model) GetEffectFloat(tp int) float64 { return this.effectMap.Get(tp) }
func (this *Model) IsEnd() bool                   { return this.NextSeasonTime == 0 }

func (this *Model) Init() *Model {
	createTime := this.room.GetCreateTime()
	if createTime == 0 {
		return this
	}
	nowZeroTime := ut.NowZeroTime()
	createZeroTime := ut.DateZeroTime(createTime)
	// 运行时间
	runTime := ut.MaxInt64(0, nowZeroTime-createZeroTime)
	// 定位季节 和 计算下一个季节来临时间
	if DEBUG {
		this.Type = 0
		this.NextSeasonTime = time.Now().UnixMilli() + ut.TIME_MINUTE
	} else {
		this.Type = ut.Min(SEASON_COUNT-1, int(runTime/SEASON_DURATION_TIME))
		this.NextSeasonTime = createZeroTime + int64(this.Type*SEASON_DURATION_TIME) + SEASON_DURATION_TIME
	}
	// 刷新效果
	this.UpdateEffect(true)
	this.createAncientCities()
	log.Info("Season Init, RunTime: %v, Type: %v, NextTime: %v", runTime, this.Type, ut.DateFormat(this.NextSeasonTime, "2006-01-02 15:04:05"))
	return this
}

func (this *Model) Update(now int64) {
	this.createAncientCities()
	if now < this.NextSeasonTime {
		return
	} else if this.Type >= SEASON_COUNT-1 { //最后一个季节
		this.NextSeasonTime = 0 //强制结束游戏
		return
	}
	this.Type += 1
	if DEBUG {
		this.NextSeasonTime += ut.TIME_MINUTE
	} else {
		this.NextSeasonTime += SEASON_DURATION_TIME
	}
	this.UpdateEffect(false)
	// 通知
	this.room.GetWorld().PutNotifyQueue(constant.NQ_UPDATE_SEASON, &pb.OnUpdateWorldInfoNotify{Data_65: this.ToPb()})
	log.Info("ChangeSeason Type: %v, NextTime: %v", this.Type, ut.DateFormat(this.NextSeasonTime, "2006-01-02 15:04:05"))
}

// 刷新效果
func (this *Model) UpdateEffect(init bool) {
	wld := this.room.GetWorld()
	this.effectMap.Clean()
	switch this.Type {
	case 0: //春
		this.effectMap.Set(effect.RES_OUTPUT_PERCENT, 0.5) //基础产量增加50%
	case 1: //夏
		this.effectMap.Set(effect.TREASURE_AWARD, 0.25) //宝箱奖励提高25%
		this.effectMap.Set(effect.MARCH_CD, 0.05)       //行军速度提高5%
	case 2: //秋
		this.effectMap.Set(effect.UPLVING_CD, 0.2) //训练士兵时间减少20%
		this.effectMap.Set(effect.MARCH_CD, 0.1)   //行军速度提高10%
		// 清理免战
		wld.CleanGeneralAvoidWarArea()
	case 3: //冬
		this.effectMap.Set(effect.RES_OUTPUT_PERCENT, -0.5) //基础产量减少50%
		this.effectMap.Set(effect.RECRUIT_COST, 2.0)        //招募士兵费用提高100%
		this.effectMap.Set(effect.UPLVING_COST, 2.0)        //训练士兵费用提高100%
		this.effectMap.Set(effect.UPLVING_CD, 0.2)          //训练士兵时间减少20%
		// 清理免战
		wld.CleanGeneralAvoidWarArea()
	}
	if !init {
		// 刷新玩家产出
		this.room.NotifyAllPlayerUpdateOpSec()
		// 通知盟主坐标
		if this.Type >= 3 {
			wld.NotifyAllAlliBaseInfo()
		}
	}
}

// 改变基础资源费用
func (this *Model) ChangeBaseResCost(list []*g.TypeObj, tp int) []*g.TypeObj {
	up := this.GetEffectFloat(tp)
	if up > 0 {
		list = array.Map(list, func(m *g.TypeObj, _ int) *g.TypeObj {
			v := m.Clone()
			if v.Type <= ctype.STONE {
				v.Count = int32(ut.Round(float64(v.Count) * up))
			}
			return v
		})
	} else {
		list = array.Map(list, func(m *g.TypeObj, _ int) *g.TypeObj { return m.Clone() })
	}
	return list
}

// 创建古城
func (this *Model) createAncientCities() {
	wld := this.room.GetWorld()
	if this.Type >= 1 && !wld.GetHasCreateAncient() {
		createTime := this.room.GetCreateTime()
		if createTime == 0 {
			return
		}
		createZeroTime := ut.DateZeroTime(createTime)
		// 计算夏季开始时间
		summerStartTime := createZeroTime + SEASON_DURATION_TIME
		creaeteAncientTime := summerStartTime + int64(constant.ANCIENT_CITY_CREATE_TIME*ut.TIME_HOUR)
		// if DEBUG {
		// 	creaeteAncientTime = createTime + ut.TIME_MINUTE
		// }
		if time.Now().UnixMilli() >= creaeteAncientTime {
			// 进入夏季且10点之后才生成古城
			go wld.CreateAncientCities()
		}
	}
}
