package behavior

import (
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
)

type IBaseNode interface {
	Ctor()
	GetCategory() string
	Init(conf map[string]interface{}, target g.<PERSON>ighter, index int32)
	Execute(dt int32) Status
	SetBaseNodeWorker(worker IBaseWorker)
}

type BaseNode struct {
	IBaseWorker

	id       int32
	index    int32
	category string
	target   g.IFighter //目标
	json     map[string]interface{}
	key      int32 //唯一标识
}

func (this *BaseNode) SetBaseNodeWorker(worker IBaseWorker) {
	this.IBaseWorker = worker
}

func (this *BaseNode) Ctor() {
}

func (this *BaseNode) Init(conf map[string]interface{}, target g.IFighter, index int32) {
	this.json = conf
	this.id = ut.Int32(conf["id"])
	this.index = index
	this.target = target
	this.key = this.id*100 + index
	this.OnInit(conf)
}

func (this *BaseNode) Execute(dt int32) Status {
	//fmt.Println("node execute [" + this.json["cls"].(string) + "]")
	if !ut.Bool(this.GetBlackboardData("isOpen")) {
		this.SetBlackboardData("isOpen", true)
		this.OnOpen()
	}
	this.OnEnter()
	state := this.OnTick(dt)
	this.OnLeave(state)
	return state
}

func (this *BaseNode) GetCategory() string { return this.category }

func (this *BaseNode) GetCtrl() g.BattleCtrl {
	return this.target.GetCtrl()
}

func (this *BaseNode) GetBlackboardData(key string) interface{} {
	return this.target.GetBlackboardData(this.key, key)
}

func (this *BaseNode) SetBlackboardData(key string, val interface{}) {
	this.target.SetBlackboard(this.key, key, val)
}

// 全局的交互数据
func (this *BaseNode) GetTreeBlackboardData(key string) interface{} {
	return this.target.GetBlackboardData(0, key)
}

func (this *BaseNode) SetTreeBlackboardData(key string, val interface{}) {
	this.target.SetBlackboard(0, key, val)
}
