package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
)

// 寻找最近目标
type SearchTarget struct {
	BaseAction
}

func (this *SearchTarget) OnTick(dt int32) Status {
	if ut.Bool(this.GetTreeBlackboardData("isSearchTarget")) && this.target.GetAttackTarget() != nil {
		return FAILURE
	}
	this.SetTreeBlackboardData("isSearchTarget", true) //记录是否已经搜索过了
	rst := this.setAttackTarget()
	return ut.If(rst, SUCCESS, FAILURE)
}

func (this *SearchTarget) setAttackTarget() bool {
	targets := this.target.GetCanAttackTargets()
	// oldAttackTarget := this.target.GetAttackTarget()
	if len(targets) > 0 {
		// if oldAttackTarget != targets[0].Target {
		// 	this.target.SetAttackTargetWeight(targets[0].Weight)
		// 	//目标fighter添加该单位到锁定列表
		// 	targets[0].Target.BeTargetedAddFighter(this.target)
		// 	if oldAttackTarget != nil {
		// 		//若更换目标 则原目标fighter将该单位从锁定列表移除
		// 		oldAttackTarget.BeTargetedRemoveFighter(this.target)
		// 	}
		// }
		if targets[0].AttackTargetPoint != nil {
			targetPoint := targets[0].AttackTargetPoint
			movePoint := this.target.GetPoint()
			pathLen := len(targets[0].Paths)
			if pathLen > 0 {
				movePoint = targets[0].Paths[pathLen-1]
			}
			attackTargetPoint := targets[0].Target.GetPoint()
			enemyCount := this.GetCtrl().GetFighterCountByPoint(attackTargetPoint)
			if targets[0].Target.GetPawnType() == constant.PAWN_TYPE_BUILD || enemyCount == 1 {
				//该点位只有一个敌人时 设置锁定目标点
				this.GetCtrl().SetLockMovePointFighter(targetPoint, this.target, targets[0].MoveWeight, movePoint)
			}
		}
		this.target.ChangeAttackTarget(targets[0].Target)
		// log.Info("setAttackTarget entity uid: %v, point: %v, pawnType: %v, tUid: %v, tPoint: %v, tType: %v",
		// 	this.target.GetUID(), this.target.GetPoint(), this.target.GetPawnType(), this.target.GetAttackTarget().GetUID(), this.target.GetAttackTarget().GetPoint(), this.target.GetAttackTarget().GetPawnType())
		// for _, p := range targets[0].Paths {
		// 	log.Info("setAttackTarget move: %v", p)
		// }
		return true
	}
	this.target.SetAttackTarget(nil)
	return false
	// point := this.target.GetPoint()
	// as := this.target.GetAstar()
	// camp := this.target.GetCamp()
	// data := array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool {
	// 	if this.checkInAttackRange(m.Point) {
	// 		return true
	// 	} else if len(m.Points) == 0 {
	// 		return false
	// 	}
	// 	return array.Some(m.Points, func(p *ut.Vec2) bool {
	// 		obj := map[string]bool{}
	// 		aPaths := as.Search(p, point, 20, -1, camp)
	// 		for _, p1 := range aPaths {
	// 			obj[p1.ID()] = true
	// 		}
	// 		bPaths := as.Search(point, p, 20, -1, camp)
	// 		return array.Some(bPaths, func(p1 *ut.Vec2) bool { return obj[p1.ID()] })
	// 	})
	// })
	// return data.Target
}

func (this *SearchTarget) checkInAttackRange(target *ut.Vec2) bool {
	return helper.GetPointToPointDis(this.target.GetPoint(), target) <= this.target.GetEntity().GetAttackRange()
}
