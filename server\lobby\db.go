package lobby

import (
	"context"
	"fmt"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/geoip"
	mgo "slgsrv/utils/mgodb"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

// 玩家数据库
var db = &Mongodb{slg.DB_COLLECTION_NAME_USER}

type TableData struct {
	Uid           string `bson:"uid"`
	Nickname      string `bson:"nickname"`
	HeadIcon      string `bson:"head_icon"`
	PersonalDesc  string `bson:"personal_desc"`  //个人简介
	GuestId       string `bson:"guest_id"`       //游客标识
	Openid        string `bson:"openid"`         //微信openid
	SessionKey    string `bson:"session_key"`    //会话密钥
	Account       string `bson:"account"`        //账号
	Password      string `bson:"password"`       //密码
	LoginType     string `bson:"login_type"`     //登录类型
	DistinctId    string `bson:"distinct_id"`    //访客id
	DeviceOS      string `bson:"device_os"`      //设备操作系统
	Platform      string `bson:"platform"`       //平台
	Ip            string `bson:"ip"`             //ip
	AppsflyerId   string `bson:"appsflyer_id"`   //AF id
	AdvertisingId string `bson:"advertising_id"` //AD id
	Referrer      string `bson:"referrer"`       //邀请者

	GoogleOpenid   string `bson:"google_openid"`   //google openid
	AppleOpenid    string `bson:"apple_openid"`    //apple openid
	FacebookOpenid string `bson:"facebook_openid"` //facebook openid
	TwitterOpenid  string `bson:"twitter_openid"`  //twitter openid
	LineOpenid     string `bson:"line_openid"`     //line openid
	Language       string `bson:"language"`        //使用语言
	FCMToken       string `bson:"fcm_token"`       //FCM令牌
	TeamUid        string `bson:"team_uid"`        //所在队伍uid

	Portrayals           []*g.PortrayalInfo  `bson:"portrayals"`              //拥有的画像
	AccTotalGameCounts   []int32             `bson:"acc_total_game_counts"`   //累计游戏次数
	AccTotalNewbieCounts []int32             `bson:"acc_total_newbie_counts"` //累计新手次数
	AccTotalRankCounts   []int32             `bson:"acc_total_rank_counts"`   //累计排位次数
	AccTotalFreeCounts   []int32             `bson:"acc_total_free_counts"`   //累计自由次数
	PopularityList       [][]int32           `bson:"popularity_list"`         //人气记录
	PopularityRecords    []*PopularityRecord `bson:"popularity_records"`      //人气记录
	UnlockHeadIcons      []string            `bson:"unlock_head_icons"`       //已经解锁的头像列表
	UnlockPawnSkinIds    []int32             `bson:"unlock_pawn_skin_ids"`    //解锁的士兵皮肤列表
	UnlockChatEmojiIds   []int32             `bson:"unlock_chat_emoji_ids"`   //解锁的聊天表情列表
	UnlockCitySkinIds    []int32             `bson:"unlock_city_skin_ids"`    //解锁城市皮肤列表
	UnlockBotanys        []*BotanyInfo       `bson:"unlock_botanys"`          //解锁植物列表
	Titles               []*TitleInfo        `bson:"titles"`                  //解锁的称号列表
	InviteFriends        []*InviteFriendInfo `bson:"invite_friends"`          //邀请列表
	Guides               []*GuideInfo        `bson:"guides"`                  //新手引导列表
	SkinItemList         []*SkinItem         `bson:"skin_item_list"`          //皮肤物品列表
	WheelRecords         []*WheelRecord      `bson:"wheel_records"`           //转动记录
	GeneralTasks         []*g.TaskInfo       `bson:"general_tasks"`           //常规任务列表
	GeneralFinishs       []int32             `bson:"general_finishs"`
	GeneralToDayFinishs  []int32             `bson:"general_today_finishs"` //常规每日完成列表
	AchieveTasks         []*g.TaskInfo       `bson:"achieve_tasks"`         //成就任务列表
	AchieveFinishs       []int32             `bson:"achieve_finishs"`

	ApplySevers            []*ApplyInfo             `bson:"apply_info"`                //已报名区服
	HideRankScore          []int32                  `bson:"hide_rank_score"`           //评分[隐藏分，历史最高段位分]
	SeasonRankScoreHistory []map[string]interface{} `bson:"season_rank_score_history"` //赛季历史段位
	GiveupGames            []int32                  `bson:"giveup_games"`              //已放弃的对局
	NotFinishOrders        []*NotFinishOrderInfo    `bson:"not_finish_orders"`         //已支付未完成的订单
	LastPawnSkins          []int32                  `bson:"last_pawn_skins"`           //最后一次使用的士兵皮肤
	LastCitySkins          []int32                  `bson:"last_city_skins"`           //最后一次使用的城市皮肤
	OfflineNotifyOpt       []int32                  `bson:"offline_notify_opt"`        //离线推送设置
	SubscriptionInfo       []*UserSubInfo           `bson:"subscription_info"`         //订阅信息
	Blacklists             []*BlacklistInfo         `bson:"blacklists"`                //黑名单
	FriendsList            []*Friend                `bson:"friends_list"`              //好友列表
	FriendsApplyList       []*ApplyFriend           `bson:"friends_apply_list"`        //好友申请列表
	BanRecordList          []*BanRecord             `bson:"ban_record_list"`           //禁言/封禁记录
	TeamInviteList         []*TeamInviteInfo        `bson:"team_invite_list"`          //收到邀请的队伍uid列表
	AntiCheatData          []int32                  `bson:"anti_cheat_data"`           //防作弊信息[L,PASS,FAIL]

	RechargeCountRecord     map[string]int32                  `bson:"recharge_count_record"`      //充值次数记录
	RechargeRefundRecord    map[string]int32                  `bson:"recharge_refund_record"`     //充值退款记录
	ActivityRecord          map[int32]int32                   `bson:"activity_record"`            //活动记录map
	RankRewardRecord        map[int32]bool                    `bson:"rank_reward_record"`         //排位奖励领取记录
	WheelRandomAwardMap     map[int32][]*g.TypeObj            `bson:"wheel_random_award_map"`     //转盘随机奖励
	WheelRandomAwardRecords map[int32][]*WheelRandomAwardInfo `bson:"wheel_random_award_records"` //转盘随机奖励记录
	PreferenceMap           map[string]interface{}            `bson:"preference_map"`             //偏好设置
	NicknameMap             map[int32]string                  `bson:"nickname_map"`               //昵称map k=>区服id v=>昵称

	BattlePass *BattlePass `bson:"battle_pass"` //战令
	PlantData  *PlantInfo  `bson:"plant_data"`  //种植信息

	CreateTime              int64 `bson:"create_time"`                 //创建时间
	LastLoginTime           int64 `bson:"last_login_time"`             //最后登录时间
	LastOfflineTime         int64 `bson:"last_offline_time"`           //最后离线时间
	NextToDayTime           int64 `bson:"next_today_time"`             //明日开始时间
	LastOfflineTaTrackTime  int64 `bson:"last_offline_tatrack_time"`   //最后一次离线上报时间
	BannedChatEndTime       int64 `bson:"banned_chat_end_time"`        //禁言结束时间
	LogoutApplyTime         int64 `bson:"logout_apply_time"`           //注销申请时间
	BanAccountEndTime       int64 `bson:"ban_account_end_time"`        //封禁账号结束时间
	LastBuyOptionalHeroTime int64 `bson:"last_buy_optional_hero_time"` //最后一次购买自选礼包时间
	WheelBeginTime          int64 `bson:"wheel_begin_time"`            //转动开始时间
	SumOnlineTime           int64 `bson:"sum_online_time"`             //累计在线时长
	AccLikeJwmCount         int64 `bson:"acc_like_jwm_count"`          //累计点赞九万亩次数

	Version          int32 `bson:"version"`
	LoginDayCount    int32 `bson:"login_day_count"`    //登陆天数
	CLoginDayCount   int32 `bson:"clogin_day_count"`   //连续登陆天数
	Sid              int32 `bson:"sid"`                //选择的游戏服务器
	CheckPraiseCount int32 `bson:"check_praise_count"` //记录触发好评弹窗x值

	Gold              int32 `bson:"gold"`                 //拥有的金币
	AccTotalGold      int32 `bson:"acc_total_gold"`       //累计拥有金币
	Ingot             int32 `bson:"ingot"`                //拥有的元宝
	AccTotalIngot     int32 `bson:"acc_total_ingot"`      //累计拥有元宝
	WarToken          int32 `bson:"war_token"`            //拥有的兵符
	AccTotalWarToken  int32 `bson:"acc_total_war_token"`  //累计拥有兵符
	AccTotalWinCount  int32 `bson:"acc_total_win_count"`  //累计获胜次数
	AccTotalGameCount int32 `bson:"acc_total_game_count"` //累计对局次数
	ModifyNameCount   int32 `bson:"modify_name_count"`    //修改昵称次数

	TodaySendTrumpetCount int32 `bson:"today_send_trumpet_count"` //每日发送喇叭次数
	PassNewbieIndex       int32 `bson:"pass_newbie_index"`        //第X局通关了新手区
	TreasureLostCount     int32 `bson:"treasure_lost_count"`      //宝箱损失补偿次数
	WheelSumCount         int32 `bson:"wheel_sum_count"`          //当前一共转动的次数
	WheelCurrCount        int32 `bson:"wheel_curr_count"`         //每日已转动次数
	WheelDayFreeCount     int32 `bson:"wheel_day_free_count"`     //每日免费次数
	MaxLandCount          int32 `bson:"max_land_count"`           //最大地块数
	MaxWheelMul           int32 `bson:"max_wheel_mul"`            //历史最大转动倍数

	PlaySID          int32 `bson:"play_sid"`            //正在玩的服务器id
	RankScore        int32 `bson:"rank_score"`          //段位分
	RankSeason       int32 `bson:"rank_season"`         //当前赛季
	RankCoin         int32 `bson:"rank_coin"`           //段位积分
	AccTotalRankCoin int32 `bson:"acc_total_rank_coin"` //段位积分 累计获得

	FixedFlag             int32 `bson:"fixed_flag"`                //兼容处理位标记
	LastTitle             int32 `bson:"last_title"`                //最后一次使用的称号
	BanAccountType        int32 `bson:"ban_account_type"`          //封禁账号类型
	BattleForecastCount   int32 `bson:"battle_forecast_count"`     //战斗预测免费次数
	ExpectPosition        int32 `bson:"expect_position"`           //期望位置
	TodayBuyFreeGoldCount int32 `bson:"today_buy_free_gold_count"` //每日购买免费金币次数

	CarryNoviceData bool `bson:"carry_novice_data"` //是否携带新手村数据
	WheelCanGetRet  bool `bson:"wheel_can_get_ret"` //是否可以获取转动结果
}

func NewTableData(uid, loginType, guestId, openid, sessionKey, nickname, headicon, distinctId, platform, os string) TableData {
	UnlockHeadIcons := []string{}
	if headicon == "" || strings.Contains(headicon, "head_icon_") {
		UnlockHeadIcons = []string{}
	} else {
		UnlockHeadIcons = []string{headicon}
	}
	tableData := TableData{
		Version:             VERSION,
		Uid:                 uid,
		CreateTime:          time.Now().UnixMilli(),
		LastLoginTime:       0,
		Sid:                 0,
		GuestId:             guestId,
		DistinctId:          distinctId,
		Platform:            platform,
		DeviceOS:            os,
		SessionKey:          sessionKey,
		LoginType:           loginType,
		PreferenceMap:       map[string]interface{}{},
		Gold:                constant.INIT_GOLD,
		Nickname:            nickname,
		HeadIcon:            headicon,
		ModifyNameCount:     0,
		PopularityList:      [][]int32{},
		PopularityRecords:   []*PopularityRecord{},
		UnlockHeadIcons:     UnlockHeadIcons,
		UnlockPawnSkinIds:   []int32{},
		UnlockChatEmojiIds:  []int32{},
		Titles:              []*TitleInfo{},
		Referrer:            "",
		InviteFriends:       []*InviteFriendInfo{},
		Guides:              []*GuideInfo{},
		CarryNoviceData:     true,
		WheelRecords:        []*WheelRecord{},
		GeneralTasks:        []*g.TaskInfo{},
		GeneralFinishs:      []int32{},
		GeneralToDayFinishs: []int32{},
		AchieveTasks:        []*g.TaskInfo{},
		AchieveFinishs:      []int32{},
		NicknameMap:         map[int32]string{},
		ApplySevers:         []*ApplyInfo{},
	}
	switch loginType {
	case slg.LOGIN_TYPE_WX:
		tableData.Openid = openid
	case slg.LOGIN_TYPE_GOOGLE:
		tableData.GoogleOpenid = openid
	case slg.LOGIN_TYPE_APPLE:
		tableData.AppleOpenid = openid
	case slg.LOGIN_TYPE_FACEBOOK:
		tableData.FacebookOpenid = openid
	case slg.LOGIN_TYPE_TWITTER:
		tableData.TwitterOpenid = openid
	case slg.LOGIN_TYPE_LINE:
		tableData.LineOpenid = openid
	}
	// 查询是否有注销信息
	delData, err := accountDelCol.FindDelUserInfo(loginType, openid)
	if err == "" {
		tableData.MaxLandCount = delData.MaxLandCount
		tableData.BanAccountType = delData.BanAccountType
		tableData.BanAccountEndTime = delData.BanAccountEndTime
		tableData.BannedChatEndTime = delData.BannedChatEndTime
		tableData.AccTotalNewbieCounts = []int32{0, delData.NewbieCount}
		tableData.PassNewbieIndex = delData.PassNewbieIndex
	}
	// 查询该设备是否被封禁
	otherUserData, err := db.FindByDistinctId(distinctId)
	if err == "" {
		tableData.BanAccountType = otherUserData.BanAccountType
		tableData.BanAccountEndTime = otherUserData.BanAccountEndTime
		tableData.BannedChatEndTime = otherUserData.BannedChatEndTime
	}
	return tableData
}

// 插入单个
func (this *Mongodb) InsertOne(data TableData) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
		log.Error("InsertOne", err)
	}
	return
}

// 查询单个
func (this *Mongodb) FindByUid(uid string) (data TableData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
		// log.Error("FindByUid", err, uid)
	}
	return
}

// 根据用户名查询
func (this *Mongodb) FindUserByName(name string) (data TableData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"nickname": name}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询多个
func (this *Mongodb) FindByUidList(uidList []string) (datas []TableData, err string) {
	filter := bson.M{"uid": bson.M{"$in": uidList}}
	if cur, e := this.getCollection().Find(context.TODO(), filter); e != nil {
		err = e.Error()
	} else {
		defer cur.Close(context.TODO())
		cur.All(context.TODO(), &datas)
	}
	return
}

// 根据不同登录方式的openid查询账号
func (this *Mongodb) FindByLoginTypeOpenid(openidField string, openid string) (data TableData, err string) {
	if openidField == "" {
		err = "FindByLoginTypeOpenid loginType err"
	} else if e := this.getCollection().FindOne(context.TODO(), bson.M{openidField: openid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 更新一条
func (this *Mongodb) UpdateOne(uid string, key string, value interface{}) (err string) {
	defer func() {
		if r := recover(); r != nil {
			// 捕获 panic，并记录错误
			err = fmt.Sprintf("panic occurred: %v", r)
			log.Error("Recover from panic in UpdateOne field: %v, uid: %v, key: %v, value: %v", err, uid, key, value)
		}
	}()
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{key: value}}); e != nil {
		err = e.Error()
		log.Error("user UpdateOne", err)
	}
	return
}

func (this *Mongodb) UpdateData(uid string, data bson.M) (err string) {
	defer func() {
		if r := recover(); r != nil {
			// 捕获 panic，并记录错误
			err = fmt.Sprintf("panic occurred: %v", r)
			log.Error("Recover from panic in UpdateData: %v, uid: %v, data: %v", err, uid, data)
		}
	}()
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": data}); e != nil {
		err = e.Error()
		log.Error("UpdateOne", err)
	} else {
		// log.Info("UpdateUserDB uid: %v", uid)
	}
	return
}

// 更新账号绑定信息
func (this *Mongodb) UpdateAccountBind(uid, loginType, loginTypeField, openid string) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{loginTypeField: openid, "login_type": loginType, slg.LOGIN_ID_TYPE_GUEST: ""}}); e != nil {
		err = e.Error()
		log.Error("UpdateOne", err)
	}
	return
}

// 更新同一设备的封禁信息
func (this *Mongodb) UpdateBanByDistinct(distinctId string, banEndTime int64, banType int32) (err string) {
	if _, e := this.getCollection().UpdateMany(context.TODO(), bson.M{"distinct_id": distinctId}, bson.M{"$set": bson.M{
		"ban_account_end_time": banEndTime,
		"ban_account_type":     banType}}); e != nil {
		err = e.Error()
		log.Error("user UpdateBanByDistinct", err)
	}
	return
}

// 更新同一设备的禁言信息
func (this *Mongodb) UpdateBanChatByDistinct(distinctId string, banEndTime int64) (err string) {
	if _, e := this.getCollection().UpdateMany(context.TODO(), bson.M{"distinct_id": distinctId}, bson.M{"$set": bson.M{
		"banned_chat_end_time": banEndTime}}); e != nil {
		err = e.Error()
		log.Error("user UpdateBanByDistinct", err)
	}
	return
}

// 根据设备id查询
func (this *Mongodb) FindByDistinctId(distinctId string) (data TableData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"distinct_id": distinctId}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

var accountDelCol = &Mongodb{slg.DB_COLLECTION_NAME_ACCOUNT_DEL_RECORD} //账号注销记录集合

// 申请删除账号
func (this *Mongodb) ApplyDelUser(delInfo *DelUserInfo) (err string) {
	if _, e := accountDelCol.getCollection().InsertOne(context.TODO(), delInfo); e != nil {
		err = e.Error()
		log.Error("ApplyDelUser", err)
	}
	return
}

// 删除账号
func (this *Mongodb) DelUser(uid string) (err string) {
	if _, e := this.getCollection().DeleteOne(context.TODO(), bson.M{"uid": uid}); e != nil {
		err = e.Error()
		log.Error("DelUser err: %v", err)
	}
	if _, e := accountDelCol.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": bson.M{
		"time":  time.Now().UnixMilli(),
		"state": slg.ACCOUNT_DEL_STATE_DONE,
	}}); e != nil {
		err = e.Error()
		log.Error("DelUser record err: %v", err)
	}
	return
}

// 撤回删除账号
func (this *Mongodb) CancelDelUser(uid string) (err string) {
	if _, e := accountDelCol.getCollection().DeleteOne(context.TODO(), bson.M{"uid": uid}); e != nil {
		err = e.Error()
		log.Error("CancelDelUser record err: %v", err)
	}
	return
}

// 获取账号注销信息
func (this *Mongodb) FindDelUserInfo(loginType, openId string) (data DelUserInfo, err string) {
	opt := &options.FindOneOptions{Sort: bson.M{"time": -1}}
	if e := accountDelCol.getCollection().FindOne(context.TODO(), bson.M{"loginType": loginType, "openid": openId}, opt).Decode(&data); e != nil {
		err = e.Error()
		if err != mongo.ErrNoDocuments.Error() {
			log.Error("FindDelUserInfo err: %v", err)
		}
	}
	return
}

// 是否有相同的uid
func (this *Mongodb) HasUid(uid string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// 是否有相同昵称
func (this *Mongodb) HasNickname(nickname string) bool {
	if _, e := this.getCollection().FindOne(context.TODO(), bson.M{"nickname": nickname}).DecodeBytes(); e != nil {
		return false
	}
	return true
}

// GetUserList 获取用户列表
func (this *Mongodb) GetUserList(lastId string, _sTime, _eTime int64, uid, nickName string, _sortType, size, filterType int) (uls []map[string]interface{}, err error) {
	filter := bson.M{}
	if uid != "" {
		filter["uid"] = uid
	}
	if nickName != "" {
		filter["nickname"] = bson.M{
			"$regex": nickName,
		}
	}
	createTimeFilter := bson.M{}
	if _sTime > 0 {
		createTimeFilter["$gte"] = _sTime
	}
	if _eTime > 0 {
		createTimeFilter["$lte"] = _eTime
	}
	if _sTime > 0 || _eTime > 0 {
		filter["create_time"] = createTimeFilter
	}
	now := time.Now().UnixMilli()
	if filterType == 4 {
		// 禁言中
		filter["banned_chat_end_time"] = bson.M{
			"$gt": now,
		}
	}
	if filterType == 5 {
		// 禁言中
		filter["ban_account_end_time"] = bson.M{
			"$gt": now,
		}
	}
	// 有lastId的话查询很方便  直接根据这个id来做优化式的类翻页查询
	// 但是没有的话,恰好_sortType 又是2(倒叙),就要去整个db的最后一条来作为起点,首次数据返回的效率上肯定低很多。=,=
	// 首次查询时,需要返回本文档
	returnThis := false
	if lastId == "" && _sortType == 2 {
		slimit := int64(1)
		c, e := this.getCollection().Find(context.TODO(), bson.M{}, &options.FindOptions{
			Sort: bson.M{
				"_id": -1,
			},
			Limit: &slimit,
		})
		if e != nil {
			return nil, e
		}
		returnThis = true
		data := struct {
			ID primitive.ObjectID `bson:"_id"`
		}{}
		// 实测
		// col.Find 返回的是一个文档组，即便是只有一条文档，也是会返回文档组，一般用cur.All解析,如果想用Decode拿出第一条,则需要移动游标。
		// col.FindOne 返回的是一个文档。可以用cur.Decode直接解析。
		c.Next(context.TODO())
		e = c.Decode(&data)
		if e != nil {
			return nil, e
		}
		c.Close(context.TODO())
		lastId = data.ID.Hex()
	}
	if lastId != "" {
		objectID, err := primitive.ObjectIDFromHex(lastId)
		if err != nil {
			return nil, err
		}
		if _sortType == 1 {
			filter["_id"] = bson.M{
				"$gt": objectID,
			}
		}
		if _sortType == 2 {
			if returnThis {
				filter["_id"] = bson.M{
					"$lte": objectID,
				}
			} else {
				filter["_id"] = bson.M{
					"$lt": objectID,
				}
			}

		}
	}
	limit := int64(size)
	cursor, err := this.getCollection().Find(context.TODO(), filter, &options.FindOptions{
		Projection: bson.M{
			"_id":                     1,
			"uid":                     1,
			"create_time":             1,
			"last_login_time":         1,
			"last_offline_time":       1,
			"sid":                     1,
			"play_sid":                1,
			"login_type":              1,
			"gold":                    1,
			"ingot":                   1,
			"nickname":                1,
			"guest_id":                1,
			"device_os":               1,
			"max_land_count":          1,
			"banned_chat_end_time":    1,
			"ban_account_end_time":    1,
			"rank_score":              1,
			"ban_record_list":         1,
			"team_uid":                1,
			"login_day_count":         1,
			"acc_total_newbie_counts": 1,
			"acc_total_game_counts":   1,
			"sum_online_time":         1,
			"pass_newbie_index":       1,
			"ip":                      1,
			"anti_cheat_ban_end_time": 1,
		},
		Sort: bson.M{
			"_id": -1,
		},
		Limit: &limit,
	})
	if err != nil {
		return nil, err
	}
	for cursor.Next(context.TODO()) {
		var elem map[string]interface{}
		if err = cursor.Decode(&elem); err == nil {
			elem["acc_newbie"] = 0
			if accNewbie, ok := elem["acc_total_newbie_counts"]; ok {
				accNewbieArr := ut.IntArray(accNewbie)
				if len(accNewbieArr) >= 1 {
					elem["acc_newbie"] = accNewbieArr[1]
				}
			}
			if accGame, ok := elem["acc_total_game_counts"]; ok {
				accGameArr := ut.IntArray(accGame)
				if len(accGameArr) >= 1 {
					elem["acc_game"] = accGameArr[1]
				}
			}
			elem["country"] = geoip.GetGeoInfo(ut.String(elem["ip"]))
			uls = append(uls, elem)
		}
	}
	// err = cursor.All(context.TODO(), &uls)
	cursor.Close(context.TODO())
	return uls, nil
}

func (this *Mongodb) GetUserListByRank() (uls []map[string]interface{}, err error) {
	cursor, err := this.getCollection().Find(context.TODO(), bson.M{"rank_score": bson.M{"$gt": 0}}, &options.FindOptions{
		Projection: bson.M{
			"_id":                   1,
			"uid":                   1,
			"create_time":           1,
			"nickname":              1,
			"head_icon":             1,
			"rank_score":            1,
			"acc_total_rank_counts": 1,
			"rank_season":           1,
		},
		Sort: bson.M{
			"rank_score": -1,
		},
	})
	if err != nil {
		return nil, err
	}
	err = cursor.All(context.TODO(), &uls)
	cursor.Close(context.TODO())
	return
}

// DeleteUser 删除用户
func (this *Mongodb) DeleteUser(id string) (err error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}
	filter := bson.M{
		"_id": objectID,
	}
	_, err = this.getCollection().DeleteOne(context.TODO(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (this *Mongodb) ChangeGuestId(uid, guestId string) (err error) {
	filter := &bson.M{
		"uid": uid,
	}
	_, err = this.getCollection().UpdateOne(context.TODO(), filter, &bson.M{
		"$set": bson.M{
			"guest_id": guestId,
		},
	})
	return err
}

// 玩家货币日志数据库
var goldRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_USER_GOLD_RECORD}

type GoldRecordTableData struct {
	Uid  string        `json:"uid" bson:"uid"`
	List []*GoldRecord `json:"list" bson:"list"`
}

type GoldRecord struct {
	Val    int `json:"val" bson:"val"`       //改变数量
	After  int `json:"after" bson:"after"`   //改变后数量
	Time   int `json:"time" bson:"time"`     //时间
	Reason int `json:"reason" bson:"reason"` //原因
}

// 添加金币变动日志
func AddGoldRecord(uid string, val, after, reason int32) {
	AddItemRecord(uid, ctype.GOLD, 0, val, after, reason, "")
}

// 获取玩家金币变动日志
func (this *Mongodb) GetGoldRecordList(_sTime, _eTime int64, uid string, _sortType, size int) (data []GoldRecordTableData, err error) {
	sortType := 1
	if _sortType == 2 {
		sortType = -1
	}
	pipeline := []bson.M{
		{"$match": bson.M{
			"uid": uid,
		}},
		{"$unwind": "$list"},
		{"$sort": bson.M{
			"list.time": sortType,
		}},
		{"$group": bson.M{
			"_id":  "$_id",
			"uid":  bson.M{"$first": "$uid"},
			"list": bson.M{"$push": "$list"},
		}},
		{"$project": bson.M{
			"list": bson.M{
				"$filter": bson.M{
					"input": "$list",
					"as":    "record",
					"cond": bson.M{
						"$and": bson.A{
							bson.M{"$gte": bson.A{"$$record.time", _sTime}},
							bson.M{"$lte": bson.A{"$$record.time", _eTime}},
						},
					},
					// "limit": size,
				},
			},
		}},
	}
	if cursor, err := this.getCollection().Aggregate(context.TODO(), pipeline); err == nil {
		err = cursor.All(context.TODO(), &data)
	}
	return
}

// 玩家元宝日志数据库
var ingotRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_USER_INGOT_RECORD}

// 玩家元宝日志
type IngotRecord struct {
	Uid    string `json:"uid" bson:"uid"`       //玩家uid
	Val    int    `json:"val" bson:"val"`       //改变数量
	After  int    `json:"after" bson:"after"`   //改变后数量
	Time   int    `json:"time" bson:"time"`     //时间
	Reason int    `json:"reason" bson:"reason"` //原因
}

// 添加元宝变动日志
func AddIngotRecord(uid string, val, after, reason int32) {
	AddItemRecord(uid, ctype.INGOT, 0, val, after, reason, "")
}

// 获取玩家元宝变动日志
func (this *Mongodb) GetIngotRecordList(_sTime, _eTime int64, uid string, _sortType, size, skip int) (data []IngotRecord, err error) {
	sortType := 1
	if _sortType == 2 {
		sortType = -1
	}
	// 查询条件
	filter := bson.M{
		"uid": uid,
		"$and": bson.A{
			bson.M{"time": bson.M{"$gte": _sTime}},
			bson.M{"time": bson.M{"$lte": _eTime}},
		},
	}
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"time": sortType})
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(size))
	// 查询
	cur, err := this.getCollection().Find(context.TODO(), filter, findOptions)
	if err != nil {
		log.Error("GetIngotRecordList find err: %v", err)
	}
	defer cur.Close(context.TODO())
	for cur.Next(context.TODO()) {
		var record IngotRecord
		err := cur.Decode(&record)
		if err != nil {
			log.Error("GetIngotRecordList decode err: %v", err)
		}
		data = append(data, record)
	}
	return
}

// 战斗记录表
var battleRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_RECORD_BATTLE}

// 根据sid获取集合
func (this *Mongodb) getCollectionBySid(sid string) *mongo.Collection {
	return mgo.GetCollection(this.table + "_" + sid)
}

// 报名信息
type ApplyInfo struct {
	Sid  int `bson:"sid"`  //区服id
	Time int `bson:"time"` //报名时间
}

// 玩家订阅信息
type UserSubInfo struct {
	OrderUid     string  `bson:"order_uid"`     //订单号
	ProductId    string  `bson:"product_id"`    //商品id
	CurrencyType string  `bson:"currency_type"` //支付币种
	EndTime      int64   `bson:"end_time"`      //结束时间
	PayAmount    float32 `bson:"pay_amount"`    //支付金额
	Auto         bool    `bson:"auto"`          //是否自动续订
}

// 黑名单
type BlacklistInfo struct {
	UID      string `bson:"uid"`
	Nickname string `bson:"nickname"`
	HeadIcon string `bson:"head_icon"`
	Time     int64  `bson:"time"`
}

// 兑换码表
var giftDb = &Mongodb{slg.DB_COLLECTION_NAME_GIFT}
var giftClaimDb = &Mongodb{slg.DB_COLLECTION_NAME_GIFT_CLAIM}

// 兑换码礼包
type CodeGift struct {
	Code       string `bson:"code"`        //兑换码
	Receiver   string `bson:"receiver"`    //接受人 0.所有人 多个用|隔开
	NoReceiver string `bson:"no_receiver"` //不会接收的人 多个用|隔开
	UID        string `bson:"uid"`

	Items   []*g.TypeObj    `bson:"items"`    //奖励物品
	IsClaim []*OldCodeClaim `bson:"is_claim"` //已领玩家列表 弃用 兼容完后可以删掉

	CreateTime     int64 `bson:"create_time"`   //创建时间
	AutoDelTime    int64 `bson:"auto_del_time"` //自动删除时间 0.表示永不删除
	Endtime        int64 `bson:"endtime"`       //结束时间
	SID            int32 `bson:"sid"`
	IsForeverClaim bool  `bson:"is_forever_claim"` //是否可无限领取
}

// 兑换码已领玩家
type CodeClaim struct {
	UID     string `bson:"uid"`      //玩家uid + "_" + code
	CodeUid string `bson:"code_uid"` //兑换码uid
	Time    int64  `bson:"time"`     //领取时间
}

// 兑换码已领玩家 弃用 兼容完后可以删掉
type OldCodeClaim struct {
	UID string `bson:"uid"` //玩家uid
	Sid int32  `bson:"sid"` //领取区服id
}

// 创建兑换码
func (this *Mongodb) CreateGift(sid int32, code string, receiver string, items []*g.TypeObj, isForeverClaim bool, endTime int64) string {
	if code == "" {
		code = ut.UID6()
		for this.HasGiftCode(code) {
			code = ut.UID6()
		}
	} else if this.HasGiftCode(code) {
		return ""
	}
	data := CodeGift{
		UID:            ut.ID(),
		SID:            sid,
		Code:           code,
		Receiver:       receiver,
		Items:          items,
		CreateTime:     time.Now().UnixMilli(),
		IsForeverClaim: isForeverClaim,
		Endtime:        endTime,
	}
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		log.Error(e.Error())
		return ""
	}
	return code
}

// 兑换码是否存在
func (this *Mongodb) HasGiftCode(code string) bool {
	var data CodeGift
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"code": code}).Decode(&data); e != nil {
		return false
	}
	if data.SID != -1 {
		if IsRoomClose(data.SID) {
			this.getCollection().DeleteOne(context.TODO(), bson.M{"uid": data.UID})
			return false
		}
	}
	return true
}

// 查询兑换码
func (this *Mongodb) FindCodeGift(code string) (data CodeGift, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"code": code}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 查询兑换码列表
func (this *Mongodb) GetCdkList(sid int) []CodeGift {
	filter := bson.M{"sid": sid}
	var gifts []CodeGift
	cur, err := this.getCollection().Find(context.TODO(), filter)
	if err != nil {
		return gifts
	}
	cur.All(context.TODO(), &gifts)
	return gifts
}

// 查询并更新领取
func (this *Mongodb) UpdateGiftClaim(code, userId, codeUid string) (exist bool, e string) {
	uid := userId + "_" + code
	data := &CodeClaim{UID: uid, Time: time.Now().UnixMilli(), CodeUid: codeUid}
	var oldDocument bson.M
	// 配置返回操作前的文档
	opts := options.FindOneAndUpdate().
		SetUpsert(true).                  // 如果文档不存在则插入
		SetReturnDocument(options.Before) // 返回操作前的文档
	err := this.getCollection().FindOneAndUpdate(context.TODO(), bson.M{"uid": uid},
		bson.M{"$set": data}, opts).Decode(&oldDocument)
	if err != nil && err != mongo.ErrNoDocuments {
		// 其他数据库错误
		return false, err.Error()
	}
	// 是否领取过
	exists := oldDocument != nil
	return exists, ""
}

// 查询兑换码领取人数
func (this *Mongodb) FindGiftClaimNum(codeUid string) (num int, err string) {
	if rst, e := this.getCollection().CountDocuments(context.TODO(), bson.M{"code_uid": codeUid}); e != nil {
		err = e.Error()
	} else {
		num = ut.Int(rst)
	}
	return
}

// 查询指定玩家领取兑换码记录
func (this *Mongodb) FindUserClaimGift(code, userId string) (data CodeClaim, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": userId + "_" + code}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 删除单个兑换码
func (this *Mongodb) DeleteCodeByUID(uid string) (err string) {
	if _, e := this.getCollection().DeleteOne(context.TODO(), bson.M{"uid": uid}); e != nil {
		err = e.Error()
	}
	return
}

// 删除指定兑换码全部领取记录
func (this *Mongodb) DeleteGiftClaims(codeUid string) (err string) {
	if _, e := this.getCollection().DeleteMany(context.TODO(), bson.M{"code_uid": codeUid}); e != nil {
		err = e.Error()
	}
	return
}

// 好友聊天表
var friendChatDb = &Mongodb{slg.DB_COLLECTION_NAME_FRIEND_CHAT}

// 添加聊天
func (this *Mongodb) InsertChat(channel string, data *FriendChatInfo) error {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"channel": channel}, bson.M{
		"$push": bson.M{
			"list": bson.M{
				"$each":     []FriendChatInfo{*data},
				"$position": 0,
			},
		},
	}, options.Update().SetUpsert(true)); e != nil {
		log.Error("InsertChat data: %v, err: %v", data, e)
		return e
	}
	return nil
}

// 更新聊天列表
func (this *Mongodb) UpdateChats(channel string, data []*FriendChatInfo) error {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"channel": channel}, bson.M{
		"$set": bson.M{
			"list": data,
		},
	}, options.Update().SetUpsert(true)); e != nil {
		log.Error("InsertChats data: %v, err: %v", data, e)
		return e
	}
	return nil
}

// 查询聊天
func (this *Mongodb) FindChat(channel string) (data FriendChatDb, err error) {
	err = this.getCollection().FindOne(context.TODO(), bson.M{"channel": channel}).Decode(&data)
	return
}

// 禁言/封禁记录
type BanRecordDb struct {
	Uid     string `bson:"uid"`      // 玩家uid
	Name    string `bson:"name"`     // 玩家名字
	BanTime int64  `bson:"ban_time"` // 禁言/封禁到期时间
	Time    int64  `bson:"time"`     // 操作时间
	Type    int32  `bson:"type"`     // 类型 0禁言 1封禁
}

// 禁言/封禁记录表
var banRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_RECORED_BAN}

// 添加禁言/封禁记录
func (this *Mongodb) InsertBanRecord(record *BanRecordDb) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), record); e != nil {
		err = e.Error()
	}
	return
}

// 查询禁言/封禁记录
func (this *Mongodb) FindBanRecords(uid string) (arr []*BanRecordDb, err error) {
	filter := bson.M{}
	if uid != "" {
		filter = bson.M{"uid": uid}
	}
	cur, err := this.getCollection().Find(context.TODO(), filter, options.Find().SetSort(bson.M{"time": -1}))
	if err != nil {
		return []*BanRecordDb{}, err
	} else if err = cur.Err(); err != nil {
		return []*BanRecordDb{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*BanRecordDb{}
	for cur.Next(context.TODO()) {
		var elem BanRecordDb
		if err = cur.Decode(&elem); err == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// 组队表
var teamDb = &Mongodb{slg.DB_COLLECTION_NAME_TEAM}

// 查询队伍
func (this *Mongodb) FindTeamByUid(uid string) (data TeamInfo, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 更新队伍数据
func (this *Mongodb) UpdateTeam(team *TeamInfo) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), &bson.M{"uid": team.Uid}, bson.M{"$set": team}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("UpdateTeam team: %v, err: %v", team, err)
	}
	return
}

// 更新队伍数据
func (this *Mongodb) UpdateTeams(datas []*TeamInfo) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("UpdateTeams catch error: %v", err)
		}
	}()
	models := []mongo.WriteModel{}
	for _, m := range datas {
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"uid": m.Uid}).SetUpdate(bson.M{"$set": m}).SetUpsert(true))
	}
	if len(models) == 0 {
		return
	} else if _, e := this.getCollection().BulkWrite(context.TODO(), models, options.BulkWrite().SetOrdered(false)); e != nil {
		log.Error("UpdateTeams error.", e.Error())
	}
}

// 删除队伍
func (this *Mongodb) DelTeamByUid(uid string) (data TeamInfo, err error) {
	_, err = this.getCollection().DeleteOne(context.TODO(), &bson.M{"uid": uid})
	return
}

// 创意工坊表
var workshopDb = &Mongodb{slg.DB_COLLECTION_NAME_WORKSHOP}

// 添加创意工坊数据
func (this *Mongodb) InsertWorkshopItem(item *WorkshopItem) (err error) {
	_, err = this.getCollection().InsertOne(context.TODO(), item)
	if err != nil {
		log.Error("InsertWorkshopItem item: %v, err: %v", item, err)
	}
	return
}

// 更新创意工坊物品状态
func (this *Mongodb) UpdateWorkshopItemState(uid string, state int) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), &bson.M{"uid": uid}, bson.M{"$set": bson.M{"state": state}})
	if err != nil {
		log.Error("UpdateWorkshopItemState uid: %v, err: %v", uid, err)
	}
	return
}

// 查询创意工坊物品
func (this *Mongodb) FindWorkshopItems(uid, userId string, skip, size, itemType, state, decs int, sort string) (arr []WorkshopItem, err error) {
	filter := bson.M{}
	if uid != "" {
		filter["uid"] = uid
	}
	if userId != "" {
		filter["user_id"] = userId
	}
	if itemType >= 0 {
		filter["type"] = itemType
	}
	if state >= 0 {
		filter["state"] = state
	}
	findOptions := options.Find()
	if sort != "" {
		findOptions.SetSort(bson.M{sort: decs})
	}
	findOptions.SetSkip(int64(skip))
	findOptions.SetLimit(int64(size))
	// 查询
	cur, err := this.getCollection().Find(context.TODO(), filter, findOptions)
	if err != nil {
		log.Error("FindWorkshopItems err: %v", err)
	}
	defer cur.Close(context.TODO())
	err = cur.All(context.TODO(), &arr)
	return
}

// 皮肤物品
type SkinItem struct {
	UID   string `bson:"uid"`   // Uid
	State int64  `bson:"state"` // 0是他人赠送 大于0是首次抽出的时间戳 -1为封禁
	Id    int32  `bson:"id"`    // 配置id
	Count int32  `bson:"count"` // 数量 弃用
}

// 好友礼物记录
type FriendGiftRecord struct {
	UID      string `bson:"uid"`
	ItemUid  string `bson:"item_uid"` //物品uid
	Sender   string `bson:"sender"`   //赠送者
	Receiver string `bson:"receiver"` //接收者

	Time        int64 `bson:"time"`         //赠送时间
	ReceiveTime int64 `bson:"receive_time"` //领取时间

	Id       int32 `bson:"id"`        //礼物id
	GiftType int32 `bson:"gift_type"` //礼物类型
	BoxId    int32 `bson:"box_id"`    //礼盒id
}

// 好友礼物记录表
var friendGiftRecordDb = &Mongodb{slg.DB_COLLECTION_NAME_FRIEND_GIFT}

// 添加好友礼物记录
func (this *Mongodb) InsertFriendGiftRecord(record *FriendGiftRecord) (err error) {
	_, err = this.getCollection().InsertOne(context.TODO(), record)
	if err != nil {
		log.Error("InsertFriendGiftRecord record: %v, err: %v", record, err)
	}
	return
}

// 更新好友礼物领取记录
func (this *Mongodb) UpdateFriendGiftRecord(uid string) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), &bson.M{"uid": uid}, bson.M{"$set": bson.M{"receive_time": time.Now().UnixMilli()}})
	if err != nil {
		log.Error("UpdateFriendGiftRecord uid: %v, err: %v", uid, err)
	}
	return
}

// 皮肤物品溯源信息
type SkinItemTrack struct {
	UID      string `bson:"uid"`
	Owner    string `bson:"owner"`     // 初始拥有者
	CurOwner string `bson:"cur_owner"` // 当前拥有者
	Id       int32  `bson:"id"`        // 配置id
}

// 皮肤物品溯源表
var skinItemTrackDb = &Mongodb{slg.DB_COLLECTION_NAME_SKIN_ITEM_TRACK}

// 添加皮肤溯源记录
func (this *Mongodb) InsertSkinItemTrack(record *SkinItemTrack) (err error) {
	_, err = this.getCollection().InsertOne(context.TODO(), record)
	if err != nil {
		log.Error("InsertSkinItemTrack record: %v, err: %v", record, err)
	}
	return
}

// 添加皮肤溯源记录列表
func (this *Mongodb) InsertSkinItemTrackList(recordList []interface{}) (err error) {
	_, err = this.getCollection().InsertMany(context.TODO(), recordList)
	if err != nil {
		log.Error("InsertSkinItemTrackList recordList: %v, err: %v", recordList, err)
	}
	return
}

// 更新皮肤溯源记录
func (this *Mongodb) UpdateSkinItemTrack(uid, curOwner string) (err error) {
	_, err = this.getCollection().UpdateOne(context.TODO(), &bson.M{"uid": uid}, bson.M{"$set": bson.M{"cur_owner": curOwner}})
	if err != nil {
		log.Error("UpdateSkinItemTrack uid: %v, err: %v", uid, err)
	}
	return
}

// 获取皮肤溯源记录
func (this *Mongodb) FindSkinItemTrack(uid string) (data SkinItemTrack, err error) {
	err = this.getCollection().FindOne(context.TODO(), &bson.M{"uid": uid}).Decode(&data)
	if err != nil {
		log.Error("FindSkinItemTrack uid: %v, err: %v", uid, err)
	}
	return
}

// 皮肤物品合成记录表
var skinItemComposeDb = &Mongodb{slg.DB_COLLECTION_NAME_SKIN_ITEM_COMPOSE}

// 皮肤物品合成记录信息
type SkinItemComposeRecord struct {
	UID         string      `bson:"uid"`
	UserId      string      `bson:"userId"`
	ComposeList []*SkinItem `bson:"composeList"`
	Time        int64       `bson:"time"`
	Id          int32       `bson:"id"`
}

// 添加皮肤合成记录
func (this *Mongodb) InsertSkinComposeRecord(record *SkinItemComposeRecord) (err error) {
	_, err = this.getCollection().InsertOne(context.TODO(), record)
	if err != nil {
		log.Error("InsertSkinComposeRecord record: %v, err: %v", record, err)
	}
	return
}

// 获取皮肤合成记录
func (this *Mongodb) FindSkinComposeRecord(uid string) (data SkinItemComposeRecord, err error) {
	err = this.getCollection().FindOne(context.TODO(), &bson.M{"uid": uid}).Decode(&data)
	if err != nil {
		log.Error("FindSkinComposeRecord uid: %v, err: %v", uid, err)
	}
	return
}

// 删除皮肤合成记录
func (this *Mongodb) DelSkinComposeRecord(uid string) (data SkinItemComposeRecord, err error) {
	_, err = this.getCollection().DeleteOne(context.TODO(), &bson.M{"uid": uid})
	if err != nil {
		log.Error("DelSkinComposeRecord uid: %v, err: %v", uid, err)
	}
	return
}

// 获取指定初始拥有者的皮肤溯源信息
func (this *Mongodb) FindSkinItemTrackByOwner(owner string) (arr []SkinItemTrack, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{"owner": owner})
	if err != nil {
		log.Error("FindSkinItemTrackByOwner find err: %v", err)
		return
	}
	defer cur.Close(context.TODO())
	err = cur.All(context.TODO(), &arr)
	return
}

// 房间报名信息表
var applyDb = &Mongodb{slg.DB_COLLECTION_NAME_APPLY_INFO}

// 报名信息
type RoomApplyInfo struct {
	MatchTime int64 `bson:"match_time"` //下次匹配时间
	RoomType  int32 `bson:"room_type"`  //区服类型
	Close     bool  `bson:"close"`      //是否关闭报名
}

// 获取所有房间报名信息
func (this *Mongodb) FindAllApplyInfo() (arr []*RoomApplyInfo, err error) {
	cur, err := this.getCollection().Find(context.TODO(), bson.M{})
	if err != nil {
		return []*RoomApplyInfo{}, err
	} else if err = cur.Err(); err != nil {
		return []*RoomApplyInfo{}, err
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	arr = []*RoomApplyInfo{}
	for cur.Next(context.TODO()) {
		var elem RoomApplyInfo
		if err = cur.Decode(&elem); err == nil {
			arr = append(arr, &elem)
		}
	}
	return
}

// DC兑换码表
var dcCodeDb = &Mongodb{slg.DB_COLLECTION_NAME_DC_CODE}

type DcCodeInfo struct {
	Code    string `bson:"code"`     //兑换码
	Uid     string `bson:"uid"`      //玩家uid
	IsClaim bool   `bson:"is_claim"` //是否兑换
}

// 获取DC兑换码
func (this *Mongodb) GetDcCode(uid string) string {
	data := DcCodeInfo{}
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		// 未获取到 生成兑换码
		// code :=
	}
	return data.Code
}
