package r

import (
	"context"
	slg "slgsrv/server/common"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"

	"go.mongodb.org/mongo-driver/bson"
)

type RoomTableData struct {
	WinCond   []int32    `bson:"win_cond"` //胜利条件 [type,value] 1.领地争夺
	Staminas  []int32    `bson:"staminas"` //体力限制 [初始体力,最大体力]
	MainCitys [][]int32  `bson:"main_citys"`
	ApplyUids [][]string `bson:"apply_uids"` //报名玩家uid列表

	MapSize       *ut.Vec2                     `bson:"map_size"`       //地图大小
	GameOverInfo  *slg.GameOverInfo            `bson:"game_over_info"` //游戏结束信息
	DeletePlayers map[string]*DeletePlayerInfo `bson:"delete_players"` //被删除的玩家信息

	InitTime        int64 `bson:"init_time"`         //初始化时间
	ApplyFinishTime int64 `bson:"apply_finish_time"` //报名完成时间
	OpenRoomTime    int64 `bson:"open_room_time"`    //服务器开启时间
	CreateTime      int64 `bson:"create_time"`       //服务器创建时间

	Id    int32 `bson:"id"`     //服务器id
	MapId int32 `bson:"map_id"` //地图id

	Type    uint8 `bson:"type"`     //服务器类型
	SubType uint8 `bson:"sub_type"` //子类型
	State   int8  `bson:"state"`    //服务器状态
	Save    bool  `bson:"save"`     //是否保留区服数据
}

// 获取所有房间数据
func getAllRooms() (list []RoomTableData, err string) {
	cur, e := mgo.GetCollection("room").Find(context.TODO(), bson.D{})
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	if e != nil {
		return []RoomTableData{}, e.Error()
	} else if e = cur.Err(); e != nil {
		return []RoomTableData{}, e.Error()
	} else if e = cur.All(context.TODO(), &list); e != nil {
		err = e.Error()
	}
	return
}

// 获取所有房间数据
func getRoomById(id int) (data RoomTableData, err string) {
	if e := mgo.GetCollection("room").FindOne(context.TODO(), bson.M{"id": id}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 玩家报名数据结构
type ApplyPlayer struct {
	Uid string `bson:"uid"` //uid
	// Name     string `bson:"name"`     //昵称
	// Headicon string `bson:"headicon"` //头像
	Language string `bson:"language"` //使用语言
}
