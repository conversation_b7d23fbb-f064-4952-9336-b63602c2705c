package game

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	r "slgsrv/server/game/room"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_LEAVE, this.leave)
	this.GetServer().RegisterGO(slg.RPC_LEAVE_BY_CHANGE_SER, this.leaveByChangeSer)
	this.GetServer().RegisterGO(slg.RPC_SELECT_ROOM, this.selectRoom)
	this.GetServer().RegisterGO(slg.RPC_GET_ROOM_INFO, this.getRoomInfo)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_COST_TYPE_OBJS, this.changeCostByTypeObjs)
	this.GetServer().RegisterGO(slg.RPC_PUT_PLAYER_NOTIFY, this.putPlayerNotify)
	this.GetServer().RegisterGO(slg.RPC_PUT_NOTIFY_QUEUE, this.putNotifyQueue)
	this.GetServer().RegisterGO(slg.RPC_CHECK_GAME_TASK_CONDITION, this.checkGameTaskCondition)
	this.GetServer().RegisterGO(slg.RPC_TRIGGER_TASK_BY_LOBBY, this.triggerTaskByLobby)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_FCM_TOKEN, this.changeFCMToken)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_LANGUAGE, this.changeLanguage)
	this.GetServer().RegisterGO(slg.RPC_KICK, this.Kick)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_OFFLINE_OPT, this.changeOfflineOpt)
	this.GetServer().RegisterGO(slg.RPC_CLOSE_ROOM, this.closeRoom)
	this.GetServer().RegisterGO(slg.RPC_STOP_SERVER, this.stopServer)
	this.GetServer().RegisterGO(slg.RPC_ADD_PLAYER_ITEMS, this.addPlayerItems)
	this.GetServer().RegisterGO(slg.RPC_CLEAR_PWANS_SKIN, this.rpcClearPawnsSkin)
	this.GetServer().RegisterGO(slg.RPC_PLAYER_GIVEUP_GAME, this.rpcGiveupGame)
	this.GetServer().RegisterGO(slg.RPC_GET_PLAYER_CUR_GAME_INFO, this.rpcGetPlayerCurGameInfo)

	this.GetServer().RegisterGO(slg.RPC_CHANGE_NICKNAME, this.changeNickname)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_HEADICON, this.changeHeadIcon)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_PERSONAL_DESC, this.changePersonalDesc)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_USER_TITLE, this.changeTitle)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_HERO_ATTR, this.changeHeroAttr)

	this.GetServer().RegisterGO(slg.RPC_SET_TA_PARAMS, this.setTaParams)
	this.GetServer().RegisterGO(slg.RPC_SET_CHEAT_CHECK_OPEN, this.setCheatCheckOpen)
	this.GetServer().RegisterGO(slg.RPC_SET_OFFLINE_MSG_PARAMS, this.setOfflineMsgParams)
	// this.GetServer().RegisterGO(slg.RPC_CHECKUP_DATE_NEXT_TO_DAYTIME, this.rpcUpdateNextToDayTime)
}

// 有玩家离开
func (this *Game) leave(session gate.Session) (result interface{}, err string) {
	e, room, plr, _ := checkError(session)
	if e != "" || !plr.EqualSession(session) {
		return
	}
	// 直接离开
	room.PlayerLeave(plr, 0)
	return
}

// 玩家切换服务器离开
func (this *Game) leaveByChangeSer(sid int32, uid string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if room.IsClose() {
		return nil, ecode.ROOM_CLOSE.String()
	}
	plr, ok := room.GetPlayer(uid).(*player.Model)
	if !ok || plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	room.PlayerLeave(plr, 3)
	return
}

// 选择房间
func (this *Game) selectRoom(sid int32, uid string) (map[string]interface{}, string) {
	room := r.GetRoomById(sid)
	if room == nil {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if room.IsClose() {
		return nil, ecode.ROOM_CLOSE.String()
	} else if room.GetWorld().GetPlayerIsGiveupGame(uid) {
		return nil, ecode.YET_GIVE_GAME.String() //已放弃本次对局
	} else if room.GetWorld().HasPlayer(uid) {
		return nil, ""
	} else if room.IsFull() {
		return nil, ecode.ROOM_FULL.String()
	} else if room.IsGameOver() {
		return nil, ecode.ROOM_OVER.String()
	}
	return nil, ""
}

// 获取服务器
func (this *Game) getRoomInfo(sid int32) (map[string]interface{}, string) {
	room := r.GetRoomById(sid)
	if room == nil {
		return nil, "false"
	}
	return room.Strip(), ""
}

// 获取服务器
func (this *Game) changeCostByTypeObjs(sid int32, uid string, items []*g.TypeObj, change int32) ([]byte, string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	plr, ok := room.GetPlayer(uid).(*player.Model)
	if !ok || plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	plr.ChangeCostByTypeObjs(items, change)
	return pb.ProtoMarshal(plr.ToItemByTypeObjsPb(items))
}

// 获取服务器
func (this *Game) putPlayerNotify(sid int32, uid string, tp int, data *pb.OnUpdatePlayerInfoNotify) (map[string]interface{}, string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	plr, ok := room.GetPlayer(uid).(*player.Model)
	if !ok || plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	plr.PutNotifyQueue(tp, data)
	return nil, ""
}

// 添加通知消息
func (this *Game) putNotifyQueue(sid int32, uid string, tp int32, data *pb.OnUpdateWorldInfoNotify) (map[string]interface{}, string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	wld.PutNotifyQueue(tp, data)
	return nil, ""
}

// 检测游戏内任务条件
func (this *Game) checkGameTaskCondition(sid int32, uid string, cond *g.TaskCondInfo) (ret bool, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return false, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld()
	plr, ok := room.GetOnlinePlayerOrDB(uid).(*player.Model)
	if !ok || plr == nil {
		return false, ecode.PLAYER_NOT_EXIST.String()
	} else if cond.Type == tctype.BUILD_LV { //建筑等级
		ret = array.Some(wld.GetAreaBuildsByID(plr.MainCityIndex, cond.ID), func(m g.Build) bool { return m.GetLV() >= cond.Count })
	} else if cond.Type == tctype.LIVER_EMPEROR { //肝帝
		ret = plr.SumOnlineTime > int64(cond.ID*ut.TIME_HOUR) && int32(len(wld.GetPlayerOwnCells(plr.Uid))) >= cond.Count
	} else if cond.Type == tctype.THOUSAND_MU { //一万亩
		ret = int32(len(wld.GetPlayerOwnCells(plr.Uid))) >= cond.Count
	} else if cond.Type == tctype.GIVE_RES_COUNT { //赠送的资源数量
		ret = plr.AccTotalGiveResCount >= int64(cond.Count)
	} else if cond.Type == tctype.EQUIP_ALL_ATTR_MAX { //装备所有属性都是最高属性
		ret = wld.CheckPlayerHasMaxAttrEquip(plr.Uid)
	} else if cond.Type == tctype.EQUIP_RECAST_COUNT { //装备重铸次数
		ret = wld.GetPlayerTotalEquipRecastCount(plr.Uid) >= cond.Count
	}
	room.CheckRemoveOfflinePlayer(plr)
	return
}

// 登录服触发任务
func (this *Game) triggerTaskByLobby(sid int32, uid string, condType, count, param int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	plr, ok := room.GetPlayer(uid).(*player.Model)
	if !ok || plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	plr.TriggerTaskByRpc(condType, count, param)
	return
}

// 登录服通知修改FCM令牌
func (this *Game) changeFCMToken(sid int32, uid string, token string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	room.GetWorld().SetPlayerFCMToken(uid, token)
	return
}

// 登录服通知修改语言
func (this *Game) changeLanguage(sid int32, uid string, lang string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	room.GetWorld().SetPlayerLanguage(uid, lang)
	return
}

// 强制踢下线
func (this *Game) Kick(sid int32, uid string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	plr := room.GetPlayer(uid)
	if plr != nil {
		plr.Kick(1)
	}
	return
}

// 登录服通知修改离线通知设置
func (this *Game) changeOfflineOpt(sid int32, uid string, opt []int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	room.GetWorld().SetPlayerOfflineOpt(uid, opt)
	return
}

// 关闭已结束服务器
func (this *Game) closeRoom(sid int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	room.CloseGameOverRoom()
	return
}

// 设置数数参数
func (this *Game) setTaParams(taOpen, taDebug string) (ret map[string]interface{}, err error) {
	slg.TA_OPEN = ut.If(taOpen == "1", true, false)
	slg.TA_DEBUG = ut.If(taDebug == "1", true, false)
	return
}

// 设置数数参数
func (this *Game) setCheatCheckOpen(open bool) (ret map[string]interface{}, err error) {
	constant.ANTI_CHEAT_OPEN = open
	return
}

// 设置离线通知参数
func (this *Game) setOfflineMsgParams(open bool) (ret map[string]interface{}, err error) {
	slg.OfflineMsgNotify = open
	return
}

// 停机保存
func (this *Game) stopServer(sid int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil {
		err = ecode.ROOM_NOT_EXIST.String()
		return
	}
	room.Stop(1)
	return
}

// 添加物品
func (this *Game) addPlayerItems(sid int32, uid string, items []*g.TypeObj) (result []byte, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if plr, ok := room.GetOnlinePlayerOrDB(uid).(*player.Model); ok && plr != nil {
		plr.ChangeCostByTypeObjs(items, 1)
		if !plr.IsOnline() {
			room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
		}
		output := plr.ToItemByTypeObjsPbOut(items, nil)
		return pb.ProtoMarshal(output)
	}
	return nil, ecode.PLAYER_NOT_EXIST.String()
}

// 卸下所有指定士兵id的指定皮肤
func (this *Game) rpcClearPawnsSkin(sid int32, uid string, skinId int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	pawnId := skinId / 1000
	wld.ClearConfigPawnSkin(uid, pawnId, skinId)
	wld.ClearPawnsSkin(uid, pawnId, skinId)
	return
}

// 放弃本次对局
func (this *Game) rpcGiveupGame(sid int32, uid string) (ret map[string]interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	plr := room.GetPlayer(uid).(*player.Model)
	if plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	if plr.AllianceUid != "" {
		if alli := wld.GetAlliance(plr.AllianceUid); alli != nil && alli.Creater == plr.Uid {
			// 盟主不能放弃游戏
			return nil, ecode.ALLI_CREATOR_CANT_GIVE_UP.String()
		}
	}
	settleInfo, settleAlliInfo, err := wld.DeletePlayerByGiveup(plr.Uid, plr.GetLid())
	if err != "" {
		return nil, err
	}
	room.PlayerLeave(plr, 3)
	ret = map[string]interface{}{
		"settleInfo":     settleInfo,
		"settleAlliInfo": settleAlliInfo,
	}
	return
}

// 获取当前对局信息
func (this *Game) rpcGetPlayerCurGameInfo(sid int32, uid string) (ret map[string]interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	plr := wld.GetTempPlayer(uid)
	if plr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	_, rank := wld.GetPlayerRankList(uid)

	ret = map[string]interface{}{
		"score":   plr.LandScoreTop + plr.AlliScoreTop,
		"rank":    rank,
		"runTime": room.GetRunTime(),
	}
	return
}

// 登录服通知修改昵称
func (this *Game) changeNickname(sid int32, uid string, nickname string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if plr, ok := room.GetPlayer(uid).(*player.Model); ok && plr != nil {
		plr.Nickname = nickname
	}
	// 修改
	room.GetWorld().(*world.Model).UpdatePlayerNickname(uid, nickname)
	return
}

// 改变头像
func (this *Game) changeHeadIcon(sid int32, uid string, headIcon string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if plr, ok := room.GetPlayer(uid).(*player.Model); ok && plr != nil {
		plr.HeadIcon = headIcon
	}
	room.GetWorld().(*world.Model).UpdatePlayerHeadIcon(uid, headIcon)
	return
}

// 改变个人简介
func (this *Game) changePersonalDesc(sid int32, uid string, personalDesc string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if plr, ok := room.GetPlayer(uid).(*player.Model); ok && plr != nil {
		plr.PersonalDesc = personalDesc
	}
	room.GetWorld().(*world.Model).UpdatePlayerPersonalDesc(uid, personalDesc)
	return
}

// 改变称号
func (this *Game) changeTitle(sid int32, uid string, id int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	room.GetWorld().(*world.Model).SetPlayerTitle(uid, id)
	return
}

// 改变英雄属性
func (this *Game) changeHeroAttr(sid int32, uid string, id int32, attrs [][]int32) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	}
	wld := room.GetWorld().(*world.Model)
	err = wld.UpdatePlayerHeroAttr(uid, id, attrs)
	return
}

// 过天刷新通知
func (this *Game) rpcUpdateNextToDayTime(sid int32, uid string) (result interface{}, err string) {
	room := r.GetRoomById(sid)
	if room == nil || room.IsClose() {
		return nil, ecode.ROOM_NOT_EXIST.String()
	} else if plr, ok := room.GetPlayer(uid).(*player.Model); ok && plr != nil {
		plr.CheckUpdateNextToDayTime(time.Now().UnixMilli(), false)
	}
	return
}
