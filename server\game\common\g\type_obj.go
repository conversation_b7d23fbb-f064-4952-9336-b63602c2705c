package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 通用类型对象
type TypeObj struct {
	Type  int32 `json:"type" bson:"type"`
	Id    int32 `json:"id" bson:"id"`
	Count int32 `json:"count" bson:"count"`
}

func NewTypeObj(t, id, count int32) *TypeObj {
	return &TypeObj{
		Type:  t,
		Id:    id,
		Count: count,
	}
}

func NewTypeObjNotId(t, count int32) *TypeObj {
	return &TypeObj{
		Type:  t,
		Id:    0,
		Count: count,
	}
}

func NewTypeObjByJson(data map[string]interface{}) *TypeObj {
	return NewTypeObj(ut.Int32(data["type"]), ut.Int32(data["id"]), ut.Int32(data["count"]))
}

func (this *TypeObj) String() string {
	return "(" + ut.Itoa(this.Type) + "," + ut.Itoa(this.Id) + "," + ut.Itoa(this.Count) + ")"
}

func (this *TypeObj) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"type":  this.Type,
		"id":    this.Id,
		"count": this.Count,
	}
}

func (this *TypeObj) ToPb() *pb.TypeObj {
	return &pb.TypeObj{
		Type:  int32(this.Type),
		Id:    int32(this.Id),
		Count: int32(this.Count),
	}
}

func (this *TypeObj) Clone() *TypeObj {
	return &TypeObj{
		Type:  this.Type,
		Id:    this.Id,
		Count: this.Count,
	}
}

func (this *TypeObj) GetIds() []int32 {
	if this.Type != ctype.BUILD_LV || this.Id < 10000 {
		return []int32{this.Id}
	}
	a := this.Id / 10000
	b := this.Id % 10000
	return []int32{a, b}
}

func ToTypeObjsPb(tos []*TypeObj) []*pb.TypeObj {
	return array.Map(tos, func(m *TypeObj, _ int) *pb.TypeObj { return m.ToPb() })
}

// 合并数量
func MergeTypeObjsCount(slice []*TypeObj, elems ...*TypeObj) []*TypeObj {
	for _, m := range elems {
		it := array.Find(slice, func(t *TypeObj) bool { return t.Type == m.Type && t.Id == m.Id })
		if it != nil {
			it.Count += m.Count
		} else {
			slice = append(slice, m.Clone())
		}
	}
	return slice
}
