package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDCeri() {
	this.GetServer().RegisterGO("HD_CeriStartStudy", this.ceriStartStudy)   //开始研究
	this.GetServer().RegisterGO("HD_CeriResetSelect", this.ceriResetSelect) //重置选择
}

// 开始研究
func (this *Game) ceriStartStudy(session gate.Session, msg *pb.GAME_HD_CERISTARTSTUDY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	lv, id := msg.GetLv(), msg.GetId()
	slot := plr.GetCeriSlotByLv(lv)
	if slot == nil || !slot.IsCanStudy() {
		return nil, ecode.CERI_SLOT_NOT_EXIST.String()
	} else if !array.Some(slot.SelectIds, func(m int32) bool { return m == id }) {
		return nil, ecode.CERI_SLOT_NOT_EXIST.String()
	} else if !plr.CheckPerCeriSlotIsStudy(lv) {
		return nil, ecode.NEED_STUDY_PER_SLOT.String() //需要研究上一个槽位
	}
	json := config.GetJsonData("ceri", id)
	if json == nil {
		return nil, ecode.CERI_SLOT_NOT_EXIST.String()
	}
	// 开始研究
	slot.StartStudy(id)
	// 添加到离线消息检测
	wld.AddCheckPlayerOfflineMsg(plr.Uid, constant.OFFLINE_MSG_TYPE_CERI, slot.StartTime+int64(slot.NeedTime), ut.String(id))
	s2c := &pb.GAME_HD_CERISTARTSTUDY_S2C{
		Slot: slot.ToPb(),
	}
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_study", map[string]interface{}{
		"study": map[string]interface{}{
			"study_lv":   slot.Lv,
			"study_type": slot.Type,
			"study_id":   slot.GetValue(),
		},
	})
	// 检测是否有下一个槽位 给下一个槽位随机选择
	if next := plr.GetCeriSlotByLv(lv + 1); next != nil && next.ID == 0 && len(next.SelectIds) == 0 {
		next.SelectIds = plr.CeriRandomSelect(next.Type, next.Lv)
		s2c.Next = next.ToPb()
	}
	return pb.ProtoMarshal(s2c)
}

// 重置选择
func (this *Game) ceriResetSelect(session gate.Session, msg *pb.GAME_HD_CERIRESETSELECT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	slot := plr.GetCeriSlotByLv(msg.GetLv())
	if slot == nil || !slot.IsCanStudy() {
		return nil, ecode.CERI_SLOT_NOT_EXIST.String()
	} else if !plr.CheckPerCeriSlotIsStudy(slot.Lv) {
		return nil, ecode.NEED_STUDY_PER_SLOT.String() //需要研究上一个槽位
	}
	goldCost := 0
	s2c := &pb.GAME_HD_CERIRESETSELECT_S2C{}
	if slot.ResetCount > 0 {
		gold := this.ChangePlayerGold(plr, -constant.RESET_CERI_SLOT_GOLD, constant.GOLD_CHANGE_RESET_CERI_SLOT)
		if gold == -1 {
			return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
		}
		s2c.Gold = gold
		s2c.UseGold = true
		goldCost = constant.RESET_CERI_SLOT_GOLD
	}
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_randomStudy", map[string]interface{}{
		"study": map[string]interface{}{
			"study_lv":   slot.Lv,
			"study_type": slot.Type,
		},
		"gold_cost":        goldCost,
		"study_resetCount": slot.ResetCount,
	})
	slot.ResetCount += 1
	slot.SelectIds = plr.CeriRandomSelect(slot.Type, slot.Lv)
	s2c.SelectIds = array.Clone(slot.SelectIds)
	s2c.ResetCount = slot.ResetCount
	return pb.ProtoMarshal(s2c)
}
