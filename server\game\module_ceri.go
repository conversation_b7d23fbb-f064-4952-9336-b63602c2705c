package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDCeri() {
	this.GetServer().RegisterGO("HD_StudySelect", this.studySelect)         // 选择研究
	this.GetServer().RegisterGO("HD_CeriResetSelect", this.ceriResetSelect) // 重置研究槽位选项
}

// 选择研究
func (this *Game) studySelect(session gate.Session, msg *pb.GAME_HD_STUDYSELECT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	id, lv, tp := msg.GetId(), msg.GetLv(), msg.GetTp()
	if id <= 0 {
		return nil, ecode.UNKNOWN.String()
	}
	var slotMap *g.CeriSlotMap
	switch tp {
	case constant.CERI_CONF_TYPE_POLICY:
		// 政策
		tplr := wld.GetTempPlayer(plr.Uid)
		if tplr == nil {
			return nil, ecode.UNKNOWN.String()
		}
		slotMap = tplr.PolicySlots
	case constant.CERI_CONF_TYPE_EQUIP:
		// 装备
		slotMap = plr.EquipCeriSlots
	case constant.CERI_CONF_TYPE_PAWN:
		// 士兵
		slotMap = plr.PawnCeriSlots
	}
	if slotMap == nil {
		return nil, ecode.UNKNOWN.String()
	}
	slotInfo := slotMap.GetSlotByLv(lv)
	// 是否是专武 专武只有检测是否有该士兵 不用判断是否在待选项中
	isExclusive := tp == constant.CERI_CONF_TYPE_EQUIP && constant.EXCLUSIVE_EQUIP_SLOT_CONF_MAP[lv] > 0
	if slotInfo == nil || (slotInfo.SelectIds == nil && !isExclusive) { // 槽位不存在
		return nil, ecode.UNKNOWN.String()
	}
	if isExclusive {
		json := config.GetJsonData("equipBase", id)
		if json == nil {
			return nil, ecode.UNKNOWN.String()
		}
		exclusive_pawn := ut.Int32(json["exclusive_pawn"])
		if !plr.IsUnlockPawn(exclusive_pawn) {
			return nil, ecode.UNKNOWN.String()
		}
	} else if !array.Has(slotInfo.SelectIds, id) { // 选择id不在待选项中
		return nil, ecode.UNKNOWN.String()
	}

	// 设置研究id
	slotInfo.ID = id
	slotInfo.SelectIds = nil
	if tp == constant.CERI_CONF_TYPE_POLICY { // 政策
		confMap := constant.POLICY_SLOT_CONF_MAP
		// 刷新政策
		plr.UpdatePolicyEffect(map[int32]bool{slotInfo.GetPolicyEffect(): true})
		if taskIdList := constant.POLICY_TASK_MAP[id]; len(taskIdList) > 0 {
			// 接受政策相关任务
			plr.AddOtherTaskAndComplete(taskIdList[0])
		}
		// 激活政策获得物品
		wld.ActivePolicyAddItems(id, plr.Uid)
		// 通知
		wld.PutNotifyQueue(constant.NQ_PLAYER_POLICY, &pb.OnUpdateWorldInfoNotify{
			Data_53: &pb.UpdatePolicys{
				Uid:     plr.Uid,
				Policys: slotMap.ToPolicys(),
			},
		})
		// 随机下一个槽位
		nextIndex := confMap[lv] + 1
		var nextLv int32 = 0
		for blv, index := range confMap {
			if index == nextIndex {
				nextLv = blv
				break
			}
		}
		this.CeriRandomSelect(slotMap, nextLv, plr)
	} else if tp == constant.CERI_CONF_TYPE_EQUIP && !isExclusive { // 普通装备
		index := array.FindIndex(constant.EQUIP_SLOT_CONF_NORMAL_LIST, func(m int32) bool { return lv == m })
		nextIndex := index + 1
		if nextIndex < len(constant.EQUIP_SLOT_CONF_NORMAL_LIST) {
			// 普通装备随机下一个槽位
			nextLv := constant.EQUIP_SLOT_CONF_NORMAL_LIST[nextIndex]
			this.CeriRandomSelect(slotMap, nextLv, plr)
		}
	}

	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_studySelect", map[string]interface{}{
		"type": tp,
		"id":   id,
		"lv":   lv,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_STUDYSELECT_S2C{
		Slots: slotMap.ToCerisPb(),
	})
}

// 重置研究槽位选项
func (this *Game) ceriResetSelect(session gate.Session, msg *pb.GAME_HD_CERIRESETSELECT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	lv, tp := msg.GetLv(), msg.GetTp()
	var slotMap *g.CeriSlotMap
	switch tp {
	case constant.CERI_CONF_TYPE_POLICY:
		// 政策
		tplr := wld.GetTempPlayer(plr.Uid)
		if tplr == nil {
			return nil, ecode.UNKNOWN.String()
		}
		slotMap = tplr.PolicySlots

	case constant.CERI_CONF_TYPE_EQUIP:
		// 装备
		slotMap = plr.EquipCeriSlots
	default:
		return nil, ecode.CERI_SLOT_NOT_EXIST.String()
	}
	slotInfo := slotMap.GetSlotByLv(lv)
	if slotInfo == nil {
		return nil, ecode.CERI_SLOT_NOT_EXIST.String() // 槽位不存在
	}
	if !slotMap.CheckLastSlotIsSelect(lv) {
		return nil, ecode.NEED_STUDY_PER_SLOT.String() // 需要研究上一个槽位
	}
	goldCost := 0
	s2c := &pb.GAME_HD_CERIRESETSELECT_S2C{}
	if slotInfo.ResetCount > 0 {
		gold := this.ChangePlayerGold(plr, -constant.RESET_CERI_SLOT_GOLD, constant.GOLD_CHANGE_RESET_CERI_SLOT)
		if gold == -1 {
			return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
		}
		s2c.Gold = gold
		s2c.UseGold = true
		goldCost = constant.RESET_CERI_SLOT_GOLD
	}

	this.CeriRandomSelect(slotMap, lv, plr)
	slotInfo.ResetCount += 1
	s2c.SelectIds = array.Clone(slotInfo.SelectIds)
	s2c.ResetCount = slotInfo.ResetCount

	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_randomStudy", map[string]interface{}{
		"study": map[string]interface{}{
			"study_lv":   lv,
			"study_type": tp,
		},
		"gold_cost":        goldCost,
		"study_resetCount": slotInfo.ResetCount,
	})
	return pb.ProtoMarshal(s2c)
}

// 研究随机
func (this *Game) CeriRandomSelect(slotMap *g.CeriSlotMap, lv int32, plr *player.Model) {
	if slotMap == nil || lv == 0 {
		return
	}
	// 给下一个槽位随机选择
	var opts map[string]interface{} = nil
	if slotMap.Type == constant.CERI_CONF_TYPE_PAWN { // 士兵
		opts = map[string]interface{}{"idMap": func() map[int32]bool { return plr.GetExtraUnlockPawnIdMap() }}
	} else if slotMap.Type == constant.CERI_CONF_TYPE_EQUIP { // 装备
		opts = map[string]interface{}{
			"idMap": func() map[int32]bool { return plr.GetExtraUnlockEquipIdMap() },
			"pawnMap": func() map[int32]bool {
				// 是专属则获取已解锁士兵map
				pawnMap := plr.GetExtraUnlockPawnIdMap()
				// 从已经研究里面选
				plr.PawnCeriSlots.GetIdBoolMap(pawnMap)
				return pawnMap
			},
		}
	}
	slotMap.CeriRandomSelect(lv, opts)
}
