package player

import (
	"encoding/json"
	"math/rand"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"
	"strings"
	"sync/atomic"
	"time"

	"github.com/sasha-s/go-deadlock"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

// 一个玩家
type Model struct {
	Uid          string
	Nickname     string
	HeadIcon     string
	PersonalDesc string
	AllianceUid  string //所在联盟

	room       g.Room //所在服务器
	session    gate.Session
	LoginMutex *deadlock.Mutex

	OccupyLandCountMap      map[int32][]int32           //历史攻占野地数量 key=地块等级 val=数量
	OccupyLandDifficultyMap *ut.MapLock[int32, int32]   //历史攻占野地难度数量
	Cereal                  *Output                     //粮食
	Timber                  *Output                     //木头
	Stone                   *Output                     //石头
	AddOutputEndTime        *ut.MapLock[int32, int64]   //增加产出结束时间
	HidePChatChannels       *ut.MapLock[string, string] //隐藏的私聊频道
	CaptureInfo             map[string]interface{}      //被沦陷的信息

	BTQueues          *BTQueueList                               //当前的建造队列
	Merchants         *MerchantList                              //商人列表
	Tasks             *TaskList                                  //新手任务列表
	TodayTasks        *TaskList                                  //每日任务
	OtherTasks        *TaskList                                  //其他任务
	CurrForgeEquip    *ForgeEquipInfo                            //当前打造装备信息
	CurSmeltEquipData *SmeltEquipData                            //当前融炼装备信息
	BeginDeserterInfo *BeginDeserterData                         //开始逃兵信息
	CompensateMap     *ut.MapLock[int32, float64]                //补偿资源map
	notifyQueue       chan *pb.OnUpdatePlayerInfoNotify          //通知队列
	effects           map[int32]float64                          //效果列表
	tempAreas         *ut.MapLock[int32, map[string]interface{}] //临时的区域信息
	AntiCheatQuest    *AntiCheatQuestion                         //防作弊问题
	EquipCeriSlots    *g.CeriSlotMap                             //装备研究槽位信息
	PawnCeriSlots     *g.CeriSlotMap                             //士兵研究槽位信息

	UnlockPawnIds  []int32         //当前额外解锁的兵种列表
	UnlockEquipIds []int32         //当前额外解锁的装备列表
	MapMarks       []*MapMarkInfo  //个人标记信息
	CeriSlots      []*CeriSlotInfo //研究所槽位列表 已弃用

	OccupyLandCountMapMutex *deadlock.RWMutex
	outputMutex             *deadlock.RWMutex
	CaptureMutex            *deadlock.RWMutex //被沦陷锁
	effectMutex             *deadlock.RWMutex
	ceriUnlockMutex         *deadlock.RWMutex //解锁研究内容锁

	CreateTime            int64 //创建时间
	LastLoginTime         int64 //最后登录时间
	OfflineTime           int64 //离线时间
	NextToDayTime         int64 //明日开始时间
	lastUpdateDBTime      int64 //最后一次更新数据库时间
	LastOutputTime        int64 //最后一次产出时间
	LastCityOutputTime    int64 //最后一次城市产出时间
	LastSendChatTime      int64 //最后一次发送聊天的时间
	LastTaTrackTime       int64 //最后上报的时间
	nextRefreshTime       int64 //下一次6点刷新时间点
	LastUpdateStaminaTime int64 //最后刷新体力的时间
	AntiCheatLastTime     int64 //防作弊检测阈上一次累计分数的时间
	AccTotalGiveResCount  int64 //累计赠送资源数
	LastOnlineTime        int64 //最后一次的在线时长x
	TopOnlineTime         int64 //最高在线时长x
	SumOnlineTime         int64 //累计在线时间
	CurSumOnlineTime      int64 //累计在线时间(包含在线时的累计)

	cerealConsume float64 //粮耗

	LoginCount     int32 //登陆次数
	LoginDayCount  int32 //登陆天数
	CLoginDayCount int32 //连续登录天数
	Version        int32 //版本

	ExpBook          int32 //经验书
	AccTotalExpBook  int32 //经验书 累计
	Iron             int32 //铁
	AccTotalIron     int32 //铁 累计
	UpScroll         int32 //卷轴
	AccTotalUpScroll int32 //卷轴 累计
	Fixator          int32 //固定器
	AccTotalFixator  int32 //固定器 累计
	outputInterval   int32 //产出间隔时间
	Stamina          int32 //奖励点
	CellTondenCount  int32 //当天已屯田次数

	MainCityIndex         int32 //主城位置
	ReCreateMainCityCount int32 //重新创建主城次数
	SmelCount             int32 //当前融炼次数
	TodayReplacementCount int32 //每日置换次数
	UpRecruitPawnCount    int32 //加速招募士兵数量
	FreeRecruitPawnCount  int32 //已免费招募士兵数量
	FreeLevingPawnCount   int32 //已免费训练士兵数量
	FreeCurePawnCount     int32 //已免费治疗士兵数量
	FreeForgeCount        int32 //已免费打造/重铸数量
	TodayOccupyCellCount  int32 //每日占领地块数量
	ExitAllianceCount     int32 //退出联盟次数
	CompensateCount       int32 //补偿资源次数
	notifyInterval        int32 //通知间隔
	AntiCheatS            int32 //防作弊检测累计分数
	AntiCheatL            int32 //防作弊检测阈值

	State uint8 //状态

	isRunning      bool
	hasNewTreasure bool //是否有新宝箱
	IsSpectate     bool //是否观战
	IsSettled      bool //是否结算
}

func newModel(room g.Room, uid, nickname, headIcon, personalDesc string) *Model {
	return &Model{
		room:                    room,
		Uid:                     uid,
		Nickname:                strings.Replace(nickname, "\n", "", -1),
		HeadIcon:                headIcon,
		PersonalDesc:            personalDesc,
		lastUpdateDBTime:        time.Now().UnixMilli(),
		State:                   constant.PS_NONE,
		effects:                 map[int32]float64{},
		effectMutex:             new(deadlock.RWMutex),
		outputMutex:             new(deadlock.RWMutex),
		OccupyLandCountMapMutex: new(deadlock.RWMutex),
		LoginMutex:              new(deadlock.Mutex),
		CaptureMutex:            new(deadlock.RWMutex),
		ceriUnlockMutex:         new(deadlock.RWMutex),
		notifyQueue:             make(chan *pb.OnUpdatePlayerInfoNotify, 1000),
		tempAreas:               ut.NewMapLock[int32, map[string]interface{}](),
	}
}

func InitModel(room g.Room, uid string, nickname, headIcon, personalDesc string, antiCheatData []int32) *Model {
	now := time.Now().UnixMilli()
	plr := newModel(room, uid, nickname, headIcon, personalDesc)
	plr.CreateTime = now
	plr.Cereal = NewOutput(constant.INIT_RES_COUNT, 0)
	plr.Timber = NewOutput(constant.INIT_RES_COUNT, 0)
	plr.Stone = NewOutput(constant.INIT_RES_COUNT, 0)
	plr.LastLoginTime = now
	plr.LastOutputTime = now
	plr.LastCityOutputTime = now
	plr.Stamina = room.GetStaminas()[0]
	plr.LastUpdateStaminaTime = now
	plr.OccupyLandCountMap = map[int32][]int32{}
	plr.OccupyLandDifficultyMap = ut.NewMapLock[int32, int32]()
	plr.UnlockPawnIds = []int32{}
	plr.UnlockEquipIds = []int32{}
	plr.BTQueues = &BTQueueList{List: []*BTInfo{}}
	plr.Merchants = &MerchantList{List: []*Merchant{}}
	plr.Tasks = &TaskList{List: []*g.TaskInfo{}, Finishs: []int32{}}
	plr.TodayTasks = &TaskList{List: []*g.TaskInfo{}, Finishs: []int32{}}
	plr.OtherTasks = &TaskList{List: []*g.TaskInfo{}, Finishs: []int32{}}
	plr.AddOutputEndTime = ut.NewMapLock[int32, int64]()
	plr.CeriSlots = []*CeriSlotInfo{}
	plr.HidePChatChannels = ut.NewMapLock[string, string]()
	plr.CompensateMap = ut.NewMapLock[int32, float64]()
	plr.TodayReplacementCount = 0
	plr.UpRecruitPawnCount = constant.UP_RECRUIT_PAWN_MAX_COUNT
	plr.SmelCount = 0
	plr.ExitAllianceCount = 0
	plr.TodayOccupyCellCount = 0
	plr.AccTotalGiveResCount = 0
	plr.ReCreateMainCityCount = 0
	plr.OfflineTime = 0
	plr.Version = VERSION
	plr.AntiCheatS = 0
	plr.AntiCheatL = getInitAntiCheatL(antiCheatData)
	plr.EquipCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_EQUIP, Map: map[int32]*g.CeriSlotInfo{}}
	plr.PawnCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_PAWN, Map: map[int32]*g.CeriSlotInfo{}}
	return plr
}

func InitSpectatorModel(room g.Room, uid string, nickname, headIcon, personalDesc string) *Model {
	plr := InitModel(room, uid, nickname, headIcon, personalDesc, nil)
	plr.IsSpectate = true
	return plr
}

func NewModelByDB(room g.Room, data TableData) *Model {
	plr := newModel(room, data.Uid, data.Nickname, data.HeadIcon, data.PersonalDesc)
	plr.Version = data.Version
	plr.CreateTime = data.CreateTime
	plr.LastLoginTime = data.LastLoginTime
	plr.LoginCount = data.LoginCount
	plr.LoginDayCount = data.LoginDayCount
	plr.CLoginDayCount = data.CLoginDayCount
	plr.LastOnlineTime = data.LastOnlineTime
	plr.TopOnlineTime = data.TopOnlineTime
	plr.SumOnlineTime = data.SumOnlineTime
	plr.CurSumOnlineTime = data.SumOnlineTime
	plr.OfflineTime = data.OfflineTime
	plr.NextToDayTime = data.NextToDayTime
	plr.LastOutputTime = data.LastOutputTime
	plr.LastCityOutputTime = data.LastCityOutputTime
	plr.Stamina = data.Stamina
	plr.CellTondenCount = data.CellTondenCount
	plr.LastUpdateStaminaTime = data.LastUpdateStaminaTime
	plr.OccupyLandCountMap = data.OccupyLandCountMap
	plr.OccupyLandDifficultyMap = ut.FromMapLock(data.OccupyLandDifficultyMap)
	plr.CaptureInfo = data.CaptureInfo
	plr.ExpBook = data.ExpBook
	plr.AccTotalExpBook = ut.If(data.AccTotalExpBook == 0, data.ExpBook, data.AccTotalExpBook)
	plr.Iron = data.Iron
	plr.AccTotalIron = ut.If(data.AccTotalIron == 0, data.Iron, data.AccTotalIron)
	plr.UpScroll = data.UpScroll
	plr.AccTotalUpScroll = data.AccTotalUpScroll
	plr.Fixator = data.Fixator
	plr.AccTotalFixator = data.AccTotalFixator
	plr.Cereal = NewOutput(data.Cereal, data.AccTotalCereal)
	plr.Timber = NewOutput(data.Timber, data.AccTotalTimber)
	plr.Stone = NewOutput(data.Stone, data.AccTotalStone)
	plr.MainCityIndex = data.MainCityIndex
	plr.ReCreateMainCityCount = data.ReCreateMainCityCount
	plr.UnlockPawnIds = data.UnlockPawnIds
	plr.UnlockEquipIds = data.UnlockEquipIds
	plr.AllianceUid = data.AllianceUid
	plr.BTQueues = &BTQueueList{List: data.BTQueues}
	plr.Merchants = &MerchantList{List: data.Merchants}
	plr.Tasks = &TaskList{List: InitTaskByDB(data.Tasks, "guideTask", 0), Finishs: data.TaskFinishs}
	plr.TodayTasks = &TaskList{List: InitTaskByDB(data.TodayTasks, "todayTask", room.GetRunDay()), Finishs: data.TodayTaskFinishs}
	plr.OtherTasks = &TaskList{List: InitTaskByDB(data.OtherTasks, "otherTask", 0), Finishs: data.OtherTaskFinishs}
	plr.CurrForgeEquip = data.CurrForgeEquip
	plr.CurSmeltEquipData = data.CurSmeltEquipData
	plr.AddOutputEndTime = ut.NewMapLock[int32, int64]().FromMap(data.AddOutputEndTime)
	plr.CeriSlots = ut.If(data.CeriSlots == nil, []*CeriSlotInfo{}, InitCeriSlots(data.CeriSlots))
	plr.HidePChatChannels = ut.NewMapLock[string, string]().FromMap(data.HidePChatChannels)
	plr.TodayReplacementCount = data.TodayReplacementCount
	plr.UpRecruitPawnCount = data.UpRecruitPawnCount
	plr.FreeRecruitPawnCount = data.FreeRecruitPawnCount
	plr.FreeLevingPawnCount = data.FreeLevingPawnCount
	plr.FreeCurePawnCount = data.FreeCurePawnCount
	plr.FreeForgeCount = data.FreeForgeCount
	plr.SmelCount = data.SmelCount
	plr.BeginDeserterInfo = data.BeginDeserterInfo
	plr.ExitAllianceCount = data.ExitAllianceCount
	plr.LastTaTrackTime = data.LastTaTrackTime
	plr.TodayOccupyCellCount = data.TodayOccupyCellCount
	plr.AccTotalGiveResCount = data.AccTotalGiveResCount
	plr.MapMarks = data.MapMarks
	plr.CompensateMap = ut.NewMapLock[int32, float64]().FromMap(data.CompensateMap)
	plr.CompensateCount = data.CompensateCount
	plr.IsSettled = data.IsSettled
	if data.EquipCeriSlots == nil {
		plr.EquipCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_EQUIP, Map: map[int32]*g.CeriSlotInfo{}}
	} else {
		plr.EquipCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_EQUIP, Map: data.EquipCeriSlots}
	}
	if data.PawnCeriSlots == nil {
		plr.PawnCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_PAWN, Map: map[int32]*g.CeriSlotInfo{}}
	} else {
		plr.PawnCeriSlots = &g.CeriSlotMap{Type: constant.CERI_CONF_TYPE_PAWN, Map: data.PawnCeriSlots}
	}
	if plr.OccupyLandCountMap == nil {
		plr.OccupyLandCountMap = map[int32][]int32{}
	}
	if plr.LastCityOutputTime == 0 {
		plr.LastCityOutputTime = time.Now().UnixMilli()
	}
	plr.AntiCheatS = data.AntiCheatS
	plr.AntiCheatL = data.AntiCheatL
	plr.AntiCheatLastTime = data.AntiCheatLastTime
	if plr.AntiCheatL == 0 {
		plr.AntiCheatL = constant.ANTI_CHEAT_L_INIT
	}
	return plr
}

// 返回数据库数据
func (this *Model) ToDB(nickname, headIcon, personalDesc string, title int32) TableData {
	wld := this.room.GetWorld()
	landScore, alliScore, landScoreTop, alliScoreTop := wld.GetPlayerLandScoreAndAlliScore(this.Uid)
	return TableData{
		Version:                 this.Version,
		Uid:                     this.Uid,
		Nickname:                nickname,
		HeadIcon:                headIcon,
		PersonalDesc:            personalDesc,
		Title:                   title,
		CreateTime:              this.CreateTime,
		LastLoginTime:           this.LastLoginTime,
		LoginCount:              this.LoginCount,
		LoginDayCount:           this.LoginDayCount,
		CLoginDayCount:          this.CLoginDayCount,
		LastOnlineTime:          this.LastOnlineTime,
		TopOnlineTime:           this.TopOnlineTime,
		SumOnlineTime:           this.SumOnlineTime,
		OfflineTime:             this.OfflineTime,
		NextToDayTime:           this.NextToDayTime,
		LastOutputTime:          this.LastOutputTime,
		LastCityOutputTime:      this.LastCityOutputTime,
		Stamina:                 this.Stamina,
		LastUpdateStaminaTime:   this.LastUpdateStaminaTime,
		LandScore:               landScore,
		LandScoreTop:            landScoreTop,
		AlliScore:               alliScore,
		AlliScoreTop:            alliScoreTop,
		OccupyLandCountMap:      this.ToOccupyLandCountMap(),
		OccupyLandDifficultyMap: this.OccupyLandDifficultyMap.Clone(),
		CaptureInfo:             this.CaptureInfo,
		ExpBook:                 this.ExpBook,
		AccTotalExpBook:         this.AccTotalExpBook,
		Iron:                    this.Iron,
		AccTotalIron:            this.AccTotalIron,
		UpScroll:                this.UpScroll,
		AccTotalUpScroll:        this.AccTotalUpScroll,
		Fixator:                 this.Fixator,
		AccTotalFixator:         this.AccTotalFixator,
		Cereal:                  this.Cereal.Value,
		AccTotalCereal:          this.Cereal.AccTotal,
		Timber:                  this.Timber.Value,
		AccTotalTimber:          this.Timber.AccTotal,
		Stone:                   this.Stone.Value,
		AccTotalStone:           this.Stone.AccTotal,
		MainCityIndex:           this.MainCityIndex,
		ReCreateMainCityCount:   this.ReCreateMainCityCount,
		AllianceUid:             this.AllianceUid,
		UnlockPawnIds:           this.UnlockPawnIds,
		UnlockEquipIds:          this.UnlockEquipIds,
		BTQueues:                this.BTQueues.List,
		Merchants:               this.Merchants.List,
		Tasks:                   this.Tasks.List,
		TaskFinishs:             this.Tasks.Finishs,
		TodayTasks:              this.TodayTasks.List,
		TodayTaskFinishs:        this.TodayTasks.Finishs,
		OtherTasks:              this.OtherTasks.List,
		OtherTaskFinishs:        this.OtherTasks.Finishs,
		Equips:                  this.ToEquips(),
		CurrForgeEquip:          this.CurrForgeEquip,
		CurSmeltEquipData:       this.CurSmeltEquipData,
		ConfigPawnMap:           this.ToConfigPawnMap(),
		FortAutoSupports:        wld.GetFortAutoSupports(this.Uid),
		AddOutputEndTime:        this.AddOutputEndTime.Clone(),
		HeroSlots:               this.ToHeroSlotsDB(),
		HidePChatChannels:       this.HidePChatChannels.Clone(),
		TodayReplacementCount:   this.TodayReplacementCount,
		UpRecruitPawnCount:      this.UpRecruitPawnCount,
		FreeRecruitPawnCount:    this.FreeRecruitPawnCount,
		FreeLevingPawnCount:     this.FreeLevingPawnCount,
		FreeCurePawnCount:       this.FreeCurePawnCount,
		FreeForgeCount:          this.FreeForgeCount,
		PolicySlots:             this.ToPolicySlotMap(),
		EquipCeriSlots:          this.EquipCeriSlots.ToCerisDB(),
		PawnCeriSlots:           this.PawnCeriSlots.ToCerisDB(),
		SmelCount:               this.SmelCount,
		BeginDeserterInfo:       this.BeginDeserterInfo,
		ExitAllianceCount:       this.ExitAllianceCount,
		LastTaTrackTime:         this.LastTaTrackTime,
		MaxLandCount:            wld.GetPlayerMaxLandCount(this.Uid),
		TodayOccupyCellCount:    this.TodayOccupyCellCount,
		AccTotalGiveResCount:    this.AccTotalGiveResCount,
		BattleRecordInfo:        wld.ToPlayerBattleRecordInfo(this.Uid),
		AvoidWarReduce:          wld.GetPlayerAvoidWarReduce(this.Uid),
		RodeleroCadetLv:         wld.GetPlayerRodeleroCadetLv(this.Uid),
		IsGiveupGame:            wld.GetPlayerIsGiveupGame(this.Uid),
		PawnDrillMap:            wld.ToPlayerPawnDrillMap(this.Uid),
		PawnMaxLvMap:            wld.ToPlayerPawnMaxLvMap(this.Uid),
		Language:                wld.GetPlayerLanguage(this.Uid),
		FCMToken:                wld.GetPlayerFCMToken(this.Uid),
		OfflineNotifyOtp:        wld.GetPlayerOfflineOpt(this.Uid),
		PolicyUseMap:            wld.ToPlayerPolicyUseMap(this.Uid),
		EquipUseMap:             wld.ToPlayerEquipUseMap(this.Uid),
		KillRecordMap:           wld.ToPlayerKillRecordMap(this.Uid),
		CityOutputMap:           wld.ToPlayerCityOutputDB(this.Uid),
		MapMarks:                this.MapMarks,
		CompensateMap:           this.CompensateMap.Clone(),
		CompensateCount:         this.CompensateCount,
		TreasureLostCount:       wld.GetPlayerTreasureLostCount(this.Uid),
		NoAvoidWar:              wld.GetPlayerNoAvoidWar(this.Uid),
		AntiCheatS:              this.AntiCheatS,
		AntiCheatL:              this.AntiCheatL,
		AntiCheatLastTime:       this.AntiCheatLastTime,
		CellTondenCount:         this.CellTondenCount,
		InjuryPawnList:          wld.InjuryPawnListClone(this.Uid),
		HospitalAbandomAcc:      wld.GetHospitalAbandomAcc(this.Uid),
		HospitalFullNoticeFlag:  wld.GetHospitalFullNoticeFlag(this.Uid),
		HospitalNoticeClose:     wld.GetHospitalNoticeClose(this.Uid),
		FarmType:                wld.GetPlayerFarmType(this.Uid),
		ResAcc:                  wld.GetPlayerResAcc(this.Uid),
		IsSettled:               wld.GetPlayerIsSettled(this.Uid),
	}
}

func (this *Model) ToPb() *pb.PlayerInfo {
	wld := this.room.GetWorld()
	var captureInfo *pb.CaptureInfo = nil
	if this.CaptureInfo != nil {
		captureInfo = &pb.CaptureInfo{Uid: pb.String(this.CaptureInfo["uid"]), Time: pb.Int64(this.CaptureInfo["time"])}
	}
	landScore, _, _, _ := wld.GetPlayerLandScoreAndAlliScore(this.Uid)
	if this.IsSpectator() {
		return &pb.PlayerInfo{
			Uid: this.Uid,
		}
	} else {
		killRecord := wld.ToPlayerKillRecordMap(this.Uid)
		KillRecordMap := map[int32]int32{}
		for k, v := range killRecord {
			KillRecordMap[int32(k)] = int32(v)
		}
		maxOccupyLandDifficulty := this.GetMaxOccupyLandDifficulty()
		return &pb.PlayerInfo{
			Uid:                     this.Uid,
			SumOnlineTime:           this.SumOnlineTime,
			MainCityIndex:           this.MainCityIndex,
			ExpBook:                 this.ExpBook,
			Iron:                    this.Iron,
			UpScroll:                this.UpScroll,
			Fixator:                 this.Fixator,
			GranaryCap:              this.GetGranaryCap(),
			WarehouseCap:            this.GetWarehouseCap(),
			Cereal:                  this.Cereal.ToPb(),
			Timber:                  this.Timber.ToPb(),
			Stone:                   this.Stone.ToPb(),
			CerealConsume:           this.cerealConsume,
			Stamina:                 this.Stamina,
			CaptureInfo:             captureInfo,
			Builds:                  wld.GetPlayerMainBuildsPb(this.MainCityIndex),
			AllianceUid:             this.AllianceUid,
			UnlockPawnIds:           this.UnlockPawnIds,
			UnlockEquipIds:          this.UnlockEquipIds,
			BtQueues:                this.ToBTQueuesPb(),
			PawnDrillQueues:         wld.ToDrillPawnQueuePb(this.MainCityIndex),
			PawnLevelingQueues:      wld.ToPawnLvingQueuePb(this.MainCityIndex),
			CuringQueues:            wld.ToPawnCuringQueuePb(this.MainCityIndex),
			ArmyDists:               wld.GetPlayerArmyDistArrayPb(this.Uid, false),
			Merchants:               this.Merchants.ToPb(),
			GuideTasks:              this.Tasks.ToPb(),
			TodayTasks:              this.TodayTasks.ToPb(),
			OtherTasks:              this.OtherTasks.ToPb(),
			Equips:                  this.ToEquipsPb(),
			CurrForgeEquip:          this.ToForgeEquipDataPb(),
			CurrSmeltEquip:          this.ToSmeltEquipDataPb(),
			ConfigPawnMap:           this.ToConfigPawnMapPb(),
			FortAutoSupports:        this.ToFortAutoSupportsPb(),
			AddOutputSurplusTime:    this.ToAddOutputSurplusTimePb(),
			HeroSlots:               this.ToHeroSlotsPb(),
			HidePChatChannels:       this.HidePChatChannels.Clone(),
			TodayReplacementCount:   this.TodayReplacementCount,
			UpRecruitPawnCount:      this.UpRecruitPawnCount,
			FreeRecruitPawnCount:    this.FreeRecruitPawnCount,
			FreeLevingPawnCount:     this.FreeLevingPawnCount,
			FreeCurePawnCount:       this.FreeCurePawnCount,
			FreeForgeCount:          this.FreeForgeCount,
			PolicySlots:             this.ToPolicysPb(),
			EquipSlots:              this.EquipCeriSlots.ToCerisPb(),
			PawnSlots:               this.PawnCeriSlots.ToCerisPb(),
			SmelCount:               this.SmelCount,
			ExitAllianceCount:       this.ExitAllianceCount,
			HasNewTreasure:          this.hasNewTreasure,
			TodayOccupyCellCount:    this.TodayOccupyCellCount,
			AccTotalGiveResCount:    this.AccTotalGiveResCount,
			LandScore:               landScore,
			OccupyLandCountMap:      this.ToOccupyLandCountMapPb(),
			KillRecordMap:           KillRecordMap,
			MapMarks:                this.ToMapMarksPb(),
			ReCreateMainCityCount:   this.ReCreateMainCityCount,
			CellTondenCount:         this.CellTondenCount,
			InjuryPawns:             wld.InjuryPawnListClone(this.Uid),
			IsSettled:               wld.GetPlayerIsSettled(this.Uid),
			MaxOccupyLandDifficulty: maxOccupyLandDifficulty,
		}
	}
}

// 给web用的
func (this *Model) ToWebData() map[string]interface{} {
	wld := this.room.GetWorld()
	landScore, alliScore, _, _ := wld.GetPlayerLandScoreAndAlliScore(this.Uid)
	return map[string]interface{}{
		"uid":                  this.Uid,
		"nickname":             this.Nickname,
		"mainCityIndex":        this.MainCityIndex,
		"lastLoginTime":        this.LastLoginTime,
		"offlineTime":          this.OfflineTime,
		"cereal":               this.Cereal.Strip(),
		"timber":               this.Timber.Strip(),
		"stone":                this.Stone.Strip(),
		"expBook":              this.ExpBook,
		"iron":                 this.Iron,
		"upScroll":             this.UpScroll,
		"fixator":              this.Fixator,
		"stamina":              this.Stamina,
		"landCount":            wld.GetPlayerLandCount(this.Uid, false),
		"sumOnlineTime":        this.SumOnlineTime,
		"loginDayCount":        this.LoginDayCount,
		"createTime":           this.CreateTime,
		"todayOccupyCellCount": this.TodayOccupyCellCount,
		"allianceUid":          this.AllianceUid,
		"allianceName":         wld.GetAllianceNameByUID(this.AllianceUid),
		"landScore":            landScore,
		"alliScore":            alliScore,
		"heroSlots":            array.Map(wld.GetPlayerHeroSlots(this.Uid), func(m *g.HeroSlotInfo, _ int) int32 { return m.GetHeroID() }),
	}
}

// 获取产出信息Pb
func (this *Model) ToOutputInfoPb() *pb.UpdateOutPut {
	return &pb.UpdateOutPut{
		GranaryCap:   int32(this.GetGranaryCap()),
		WarehouseCap: int32(this.GetWarehouseCap()),
		Cereal:       this.Cereal.ToPb(),
		Timber:       this.Timber.ToPb(),
		Stone:        this.Stone.ToPb(),
		Flag: pb.AddFlags(int64(pb.OutPutFlagEnum_GranaryCap), int64(pb.OutPutFlagEnum_WarehouseCap), int64(pb.OutPutFlagEnum_Cereal),
			int64(pb.OutPutFlagEnum_Timber), int64(pb.OutPutFlagEnum_Stone)),
	}
}

func (this *Model) Init() *Model {
	now := time.Now().UnixMilli()
	if this.CaptureInfo != nil && ut.String(this.CaptureInfo["uid"]) != "" {
		this.State = constant.PS_CAPTURE
	} else if this.IsSpectator() {
		this.State = constant.PS_ONLINE
		return this
	} else {
		wld := this.room.GetWorld()
		// 刷新是否过天
		this.CheckUpdateNextToDayTime(now, true)
		// 刷新体力
		this.CheckUpdateStamina(now, true)
		// 兼容
		this.Compati(this.Version)
		// 兼容一下任务
		this.CheckUpdateCanGuideTask()
		this.CheckUpdateCanTodayTask()
		// 兼容下英雄化身
		this.CheckUpdateHeroSlots()
		// 兼容商人
		this.CheckMerchants()
		// 兼容所有槽位信息
		this.CheckAllSlotInfo()
		// 优先刷新建筑建造
		this.InitUpdateBTQueue(now)
		// 初始建筑效果
		this.UpdateBuildEffect()
		// 初始产量
		this.UpdateOpSec(false)
		// 刷新产出
		this.CheckUpdateOutput(now, true)
		// 刷新城市产出
		this.CheckUpdateCityOutput(now, true)
		// 刷新装备打造
		this.CheckUpdateForgeEquip(now)
		// 刷新装备融炼
		this.CheckUpdateSmeltEquip(now)
		//
		this.hasNewTreasure = wld.CheckPlayerHasTreasure(this.Uid)
		//
		this.State = constant.PS_ONLINE
		// 移除资源离线检测
		wld.RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, ut.String(ctype.CEREAL))
		wld.RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, ut.String(ctype.TIMBER))
		wld.RemoveCheckPlayerOfflineMsg(this.Uid, constant.OFFLINE_MSG_TYPE_RES_FULL, ut.String(ctype.STONE))
	}
	if dt := time.Now().UnixMilli() - now; dt > 1000 {
		log.Info("plr init dt: %v, uid: %v", dt, this.Uid)
	}
	this.lastUpdateDBTime = now
	this.runTick()
	return this
}

// 重置
func (this *Model) Reset() {
	now := time.Now().UnixMilli()
	this.Cereal = NewOutput(constant.INIT_RES_COUNT, 0)
	this.Timber = NewOutput(constant.INIT_RES_COUNT, 0)
	this.Stone = NewOutput(constant.INIT_RES_COUNT, 0)
	this.ExpBook = 0
	this.Iron = 0
	this.UpScroll = 0
	this.Fixator = 0
	this.LastOutputTime = now
	this.LastCityOutputTime = now
	this.Stamina = this.room.GetStaminas()[0]
	this.LastUpdateStaminaTime = now
	this.OccupyLandCountMap = map[int32][]int32{}
	this.CaptureInfo = nil
	this.SmelCount = 0
	this.BeginDeserterInfo = nil
	this.CeriSlots = []*CeriSlotInfo{}
	this.Tasks.Finishs = []int32{}
	this.TodayTasks.Finishs = []int32{}
	this.TodayTasks.List = []*g.TaskInfo{}
	this.OtherTasks.Finishs = []int32{}
	this.OtherTasks.List = []*g.TaskInfo{}
	this.TodayOccupyCellCount = 0
	this.TodayReplacementCount = 0
	this.UpRecruitPawnCount = constant.UP_RECRUIT_PAWN_MAX_COUNT
	this.BTQueues.Lock()
	this.BTQueues.List = []*BTInfo{}
	this.BTQueues.Unlock()
	this.Init()
}

func (this *Model) cleanSession() {
	if this.session != nil {
		this.session.UnBind()
		this.session.Set("sid", "")
		this.session.Push()
		this.session.Close()
		this.session = nil
	}
}

// isClose=true 表示主动踢掉
func (this *Model) Close(close int) {
	this.State = constant.PS_OFFLINE
	if this.session != nil && close != 3 { //3表示切换服务器 所以不用断开连接
		if close != 0 {
			body, _ := pb.ProtoMarshal(&pb.GAME_ONGAMECLOSE_NOTIFY{IsClose: close == 2})
			this.session.SendNR("game/OnGameClose", body)
		}
		this.cleanSession()
	}
}

// 主动踢下线 0.挤号 1.封停
func (this *Model) Kick(tpe int) {
	if this.session != nil {
		// body, _ := pb.ProtoMarshal(&pb.GAME_ONKICK_NOTIFY{Type: int32(tpe)})
		// this.session.SendNR("game/OnKick", body)
		this.cleanSession()
	}
}

func (this *Model) GetUID() string {
	return this.Uid
}

func (this *Model) GetSID() int32 {
	return this.room.GetSID()
}

// 获取粮食产量
func (this *Model) GetCerealOp() int {
	return int(this.Cereal.opHour)
}

func (this *Model) IsOnline() bool {
	return (this.State == constant.PS_ONLINE || this.State == constant.PS_CAPTURE) && this.session != nil
}

func (this *Model) SetSession(session gate.Session) {
	this.session = session
}

func (this *Model) EqualSession(session gate.Session) bool {
	return this.session != nil && this.session.GetSessionID() == session.GetSessionID()
}

// 推送消息
func (this *Model) SendMsg(topic string, msg map[string]interface{}) {
	if this.IsOnline() {
		body, _ := json.Marshal(msg)
		if err := this.session.SendNR(topic, body); err != "" {
			log.Error("SendMsg ", err)
		}
	}
}

// 直接發
func (this *Model) SessionSendNR(topic string, body []byte) {
	if this.IsOnline() {
		this.session.SendNR(topic, body)
	}
}

// 获取大厅服id
func (this *Model) GetLid() string {
	if this.session == nil {
		return rds.GetUserLid(this.Uid)
	}
	return this.session.Get("lid")
}

// 20帧
func (this *Model) runTick() {
	if this.isRunning {
		return
	}
	this.isRunning = true
	go func() {
		fps := 1.0 / 20.0 * 1000.0
		tiker := time.NewTicker(time.Millisecond * time.Duration(fps))
		defer tiker.Stop()
		last := time.Now().UnixNano()
		for this.isRunning {
			now := <-tiker.C
			dt := int32((now.UnixNano() - last) / 1e6)
			last = now.UnixNano()
			if this.IsSpectator() {
				this.UpdateSpectator(dt, time.Now().UnixMilli())
			} else {
				this.Update(dt, time.Now().UnixMilli())
			}
		}
	}()
}

func (this *Model) StopTick() {
	this.State = constant.PS_OFFLINE
	this.isRunning = false
}

func (this *Model) InitNextToDayTime() {
	this.NextToDayTime = this.GetNextToDayTime6H()
}

// 获取明天的6点时间
func (this *Model) GetNextToDayTime6H() int64 {
	now := time.Now()
	nextRefresh := time.Date(now.Year(), now.Month(), now.Day(), 6, 0, 0, 0, time.Local)
	if now.After(nextRefresh) {
		nextRefresh = nextRefresh.Add(24 * time.Hour)
	}
	return nextRefresh.UnixMilli()
}

func (this *Model) Update(dt int32, now int64) {
	if !this.isRunning {
		return
	}
	if this.State == constant.PS_ONLINE {
		this.CheckUpdateOutput(now, false)        //刷新产出
		this.CheckUpdateCityOutput(now, false)    //刷新城市产出
		this.CheckUpdateBTQueue(now)              //刷新建造
		this.CheckUpdateForgeEquip(now)           //打造装备
		this.CheckUpdateSmeltEquip(now)           //融炼装备
		this.CheckUpdateStamina(now, false)       //刷新体力 必须要在 CheckUpdateNextToDayTime 前面调用
		this.CheckUpdateNextToDayTime(now, false) //刷新是否过天
	}
	if this.State == constant.PS_ONLINE || this.State == constant.PS_CAPTURE {
		this.checkNotify(dt) //定时通知
	}
	// 肝帝成就检测
	if this.CurSumOnlineTime < constant.LIVER_EMPEROR_ONLINE_TIME {
		sumOnlineTime := this.GetCurSumOnlineTime(now)
		if sumOnlineTime >= constant.LIVER_EMPEROR_ONLINE_TIME {
			// 触发任务
			this.TriggerTask(tctype.LIVER_EMPEROR, int32(len(this.room.GetWorld().GetPlayerOwnCells(this.Uid))), int32(sumOnlineTime/ut.TIME_HOUR))
		}
	}
	// 检测更新数据库
	if this.CheckCanUpdateDB(now) {
		this.room.UpdatePlayerDB(this)
	}
}

func (this *Model) UpdateSpectator(dt int32, now int64) {
	if !this.isRunning {
		return
	}
	if this.State == constant.PS_ONLINE || this.State == constant.PS_CAPTURE {
		this.checkNotify(dt) //定时通知
	}
}

// 检测是否过天
func (this *Model) CheckUpdateNextToDayTime(now int64, init bool) {
	if this.NextToDayTime > now {
		return
	}
	this.InitNextToDayTime()
	this.CleanCellTonden()
	this.TodayOccupyCellCount = 0
	this.TodayReplacementCount = 0
	this.TodayTasks.Lock()
	this.TodayTasks.Finishs = []int32{}
	this.room.GetWorld().SetHospitalFullNoticeFlag(this.Uid, int32(constant.INJURY_PAWN_MAX_COUNT)) // 重置医馆通知标记
	if !init {
		this.CheckUpdateCanTodayTask()
		this.TodayTasks.Unlock()
		this.PutNotifyQueue(constant.NQ_UPDATE_TODAY_INFO, &pb.OnUpdatePlayerInfoNotify{
			Data_52: &pb.UpdateTodayInfo{
				TodayOccupyCellCount:  int32(this.TodayOccupyCellCount),
				TodayReplacementCount: int32(this.TodayReplacementCount),
				TodayTasks:            this.TodayTasks.ToPb(),
				CellTondenCount:       this.CellTondenCount,
			},
		})
	} else {
		this.TodayTasks.Unlock()
	}
}

// 检测更新数据库
func (this *Model) CheckCanUpdateDB(now int64) bool {
	if now-this.lastUpdateDBTime < constant.PLAYER_UPDATE_DB_INTERVAL || !this.IsOnline() {
		return false
	} else if !this.room.HasPlayer(this.Uid) {
		log.Error("CheckCanUpdateDB 玩家已经不存在了!")
		this.StopTick()
		this.cleanSession()
		return false
	}
	this.lastUpdateDBTime = now
	return true
}

// 添加到通知队列
func (this *Model) PutNotifyQueue(nType int, data *pb.OnUpdatePlayerInfoNotify) {
	if data == nil || !this.IsOnline() || this.session == nil {
		return
	}
	data.Type = int32(nType)
	if _, ok := constant.NOTIFY_TYPE_BY_LOBBY[nType]; ok {
		// 由大厅推送
		byteData, _ := pb.ProtoMarshal(data)
		this.room.InvokeLobbyRpcNR(this.GetLid(), slg.RPC_UPDATE_USER_INFO, byteData)
	} else {
		this.notifyQueue <- data
	}
}

// 通知
func (this *Model) checkNotify(dt int32) {
	if len(this.notifyQueue) == 0 {
		return
	} else if this.notifyInterval < 100 { //100毫秒通知一次
		this.notifyInterval += dt
		return
	}
	list := []*pb.OnUpdatePlayerInfoNotify{}
	for len(this.notifyQueue) > 0 {
		msg := <-this.notifyQueue
		list = append(list, msg)
	}
	this.notifyInterval = 0
	if body, err := pb.ProtoMarshal(&pb.GAME_ONUPDATEPLAYERINFO_NOTIFY{List: list}); err == "" {
		this.SessionSendNR("game/OnUpdatePlayerInfo", body)
	} else {
		log.Error("CheckNotify error", err)
	}
}

// 沦陷
func (this *Model) Capture(uid string) {
	this.State = constant.PS_CAPTURE
	this.CaptureInfo = map[string]interface{}{
		"uid":  uid,
		"time": ut.Now(),
	}
	this.Cereal.Value = 0
	this.Timber.Value = 0
	this.Stone.Value = 0
	this.ExpBook = 0
	this.Iron = 0
	this.UpScroll = 0
	this.Fixator = 0
	this.Stamina = 0
	this.CleanCellTonden()
	this.BTQueues.Lock()
	this.BTQueues.List = []*BTInfo{}
	this.BTQueues.Unlock()
	this.Merchants.Lock()
	this.Merchants.List = []*Merchant{}
	this.Merchants.Unlock()
	this.CurrForgeEquip = nil
	this.CurSmeltEquipData = nil
	this.TodayOccupyCellCount = 0
	this.OccupyLandCountMap = map[int32][]int32{}
}

// 获取玩家军队数量
func (this *Model) GetAmyCount() int32 {
	wld := this.room.GetWorld()
	armyDist := wld.GetPlayerArmyDistArrayPb(this.Uid, false)
	var count int32
	for _, v := range armyDist {
		count += int32(len(v.Armys))
	}
	return count
}

// 获取已有的指定id士兵最高等级 id为0表示不限制id
func (this *Model) GetIdPawnMaxLv(id int32) int32 {
	wld := this.room.GetWorld()
	armyDist := wld.GetPlayerArmyDistArrayPb(this.Uid, true)
	var mLv int32
	for _, v := range armyDist {
		for _, a := range v.Armys {
			for _, p := range a.Pawns {
				if (id == 0 || p.Id == id) && p.Lv > mLv {
					mLv = p.Lv
				}
			}
		}
	}
	return mLv
}

// 设置隐藏私聊
func (this *Model) SetHidePChatChannel(channel string, uid string) {
	this.HidePChatChannels.Set(channel, uid)
}

// 获取隐藏私聊的uid
func (this *Model) GetHidePChatChannelChatUID(channel string) string {
	return this.HidePChatChannels.Get(channel)
}

// 设置联盟Uid
func (this *Model) SetAlliUid(uid string) { this.AllianceUid = uid }

// 获取沦陷信息
func (this *Model) GetCaptureInfo() map[string]interface{} {
	this.CaptureMutex.RLock()
	defer this.CaptureMutex.RUnlock()
	return this.CaptureInfo
}

// 是否沦陷
func (this *Model) IsCapture() bool {
	return this.State == constant.PS_CAPTURE || this.GetCaptureInfo() != nil
}

// 是否观战者
func (this *Model) IsSpectator() bool {
	// 已放弃、已结算且没有联盟的玩家也是观战者
	return this.IsSpectate || this.room.GetWorld().GetPlayerIsGiveupGame(this.Uid) || (this.IsSettled && this.AllianceUid == "")
}

// 获取当前累计在线时间
func (this *Model) GetCurSumOnlineTime(now int64) int64 {
	addOnlineTime := ut.MaxInt64(now-this.LastLoginTime, 0)
	this.CurSumOnlineTime = this.SumOnlineTime + addOnlineTime
	return this.CurSumOnlineTime
}

// 防作弊问题
type AntiCheatQuestion struct {
	AntiCheatQuestOptions []int  //防作弊检测题目选项
	AntiCheatQuestItem    string //防作弊检测题目特征
	Time                  int64  //生成时间
	WrongCount            int    //回答错误次数
	NotPassCount          int    //未通过次数
}

// 获取防作弊问题倒计时
func (this *AntiCheatQuestion) GetSurplusTime() int {
	// 有效时间为创建时间+回答限时-打错次数*每次答错扣除时间
	endTime := this.Time + constant.ANTI_CHEAT_QUEST_TIME_LIMIT - int64(this.WrongCount*constant.ANTI_CHEAT_WRONG_ANSWER_REDUCE_TIME)
	return ut.Max(int(endTime-ut.Now()), 0)
}

// 防作弊检测 返回false则需要答题
func (this *Model) AntiCheatScoreCheck() bool {
	if !constant.ANTI_CHEAT_OPEN {
		// 未开启
		return true
	}
	now := time.Now().UnixMilli()
	// 每累计指定的单位时间没有升级建筑或攻占野地或招募士兵或训练士兵则减少积分
	if restNum := int32(now-this.AntiCheatLastTime) / constant.ANTI_CHEAT_REST_REDUCE_TIME; restNum > 0 {
		this.AntiCheatS = ut.MaxInt32(0, this.AntiCheatS-restNum*constant.ANTI_CHEAT_REST_REDUCE_SCORE)
	}
	this.AntiCheatLastTime = now
	return this.AntiCheatS < this.AntiCheatL
}

// 添加防作弊检测积分
func (this *Model) AddAntiCheatScore(score int32) {
	if !constant.ANTI_CHEAT_OPEN {
		// 未开启
		return
	}
	this.AntiCheatS += score
}

// 生成防作弊检测题目
func (this *Model) GenAntiCheatQuest() []int {
	questArr := []int{}
	// 随机特征
	index := rand.Intn(len(constant.ANTI_CHEAT_QUEST_ITEMS))
	itemName := constant.ANTI_CHEAT_QUEST_ITEMS[index]
	answerArr := []int{}   // 符合特征的图片id列表
	noAnswerArr := []int{} // 不符合特征的图片id列表

	// 遍历配置表 将图片的id放入对应的列表中
	cfg := config.GetJson("antiCheat")
	if cfg.Datas == nil {
		log.Error("GenAntiCheatQuest cfg nil")
		return nil
	}
	for _, v := range cfg.Datas {
		id := ut.Int(v["id"])
		if ut.Int(v[itemName]) == 1 {
			answerArr = append(answerArr, id)
		} else {
			noAnswerArr = append(noAnswerArr, id)
		}
	}

	otherOpCount := constant.ANTI_CHEAT_QUEST_OPTION_COUNT - 1
	if len(answerArr) == 0 || len(noAnswerArr) < otherOpCount {
		// 答案或其他待选项配置不足
		log.Error("GenAntiCheatQuest cfg item err, itemName: %v", itemName)
		return nil
	}

	// 先随机正确答案
	answerIndex := rand.Intn(len(answerArr))
	questArr = append(questArr, answerArr[answerIndex])
	// 再随机其他选项
	for i := 0; i < constant.ANTI_CHEAT_QUEST_OPTION_COUNT-1; i++ {
		otherIndex := rand.Intn(len(noAnswerArr))
		questArr = append(questArr, noAnswerArr[otherIndex])
		// 去重
		noAnswerArr = append(noAnswerArr[:otherIndex], noAnswerArr[otherIndex+1:]...)
	}
	quest := this.AntiCheatQuest
	if quest == nil {
		// 题目存到玩家身上
		quest = &AntiCheatQuestion{}
		this.AntiCheatQuest = quest
	}
	quest.AntiCheatQuestItem = itemName
	quest.AntiCheatQuestOptions = questArr
	quest.Time = ut.Now()
	quest.WrongCount = 0
	rst := []int{}
	for _, v := range questArr {
		rst = append(rst, v)
	}
	// 打乱发给玩家的选项顺序
	ut.ShuffleSlice(rst)
	return rst
}

// 是否达到屯田次数上限
func (this *Model) IsCellTondenLimit() bool {
	return atomic.LoadInt32(&this.CellTondenCount) >= constant.CELL_TONDEN_DAILY_COUNT
}

// 添加屯田次数
func (this *Model) AddCellTonden(count int32) {
	atomic.AddInt32(&this.CellTondenCount, count)
}

// 清空当天屯田次数
func (this *Model) CleanCellTonden() {
	atomic.StoreInt32(&this.CellTondenCount, 0)
}

// 是否出局
func (this *Model) IsFailed() bool {
	return this.room.IsConquer() && this.AllianceUid == ""
}

// 获取攻占过地块的最大难度
func (this *Model) GetMaxOccupyLandDifficulty() int32 {
	var maxOccupyLandDifficulty int32 = 1
	this.OccupyLandDifficultyMap.ForEach(func(v int32, k int32) bool {
		difficulty := k / 1000
		if difficulty > maxOccupyLandDifficulty {
			maxOccupyLandDifficulty = difficulty
		}
		return true
	})
	return maxOccupyLandDifficulty
}

// 获取初始防作弊检测阈值
func getInitAntiCheatL(antiCheatData []int32) int32 {
	if antiCheatData == nil || len(antiCheatData) < 2 {
		return constant.ANTI_CHEAT_L_INIT
	}
	fail, pass := antiCheatData[0], antiCheatData[1]
	return constant.ANTI_CHEAT_L_INIT + ut.MaxInt32(ut.MinInt32(fail-pass, 5), -4)*200
}
