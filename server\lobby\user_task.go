package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// ----------------------------------------------------------------------常规任务-----------------------------------------------------------------------------
type GeneralTaskList struct {
	deadlock.RWMutex
	List         []*g.TaskInfo
	Finishs      []int32 // 完成列表
	ToDayFinishs []int32 // 每日任务完成列表
}

func (this *GeneralTaskList) Strip() []int32 {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) int32 { return m.ID })
}

func (this *GeneralTaskList) ToPb() []*pb.TaskInfo {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) *pb.TaskInfo {
		return &pb.TaskInfo{
			Id:       m.ID,
			Progress: m.Progress,
		}
	})
}

func InitGeneralTaskByDB(tasks []*g.TaskInfo) []*g.TaskInfo {
	for i := len(tasks) - 1; i >= 0; i-- {
		if !tasks[i].InitJson("generalTask", 0) {
			tasks = append(tasks[:i], tasks[i+1:]...)
		}
	}
	return tasks
}

// 刷新可以做的任务
func (this *User) CheckUpdateGeneralTask() {
	// 删除没有的
	finishMap := map[int32]bool{}
	for i := len(this.GeneralTasks.Finishs) - 1; i >= 0; i-- {
		id := this.GeneralTasks.Finishs[i]
		if json := config.GetJsonData("generalTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.GeneralTasks.Finishs = append(this.GeneralTasks.Finishs[:i], this.GeneralTasks.Finishs[i+1:]...)
		}
	}
	for i := len(this.GeneralTasks.ToDayFinishs) - 1; i >= 0; i-- {
		id := this.GeneralTasks.ToDayFinishs[i]
		if json := config.GetJsonData("generalTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.GeneralTasks.ToDayFinishs = append(this.GeneralTasks.ToDayFinishs[:i], this.GeneralTasks.ToDayFinishs[i+1:]...)
		}
	}
	// 删除已经完成的任务且重复的
	ids := map[int32]bool{}
	for i := len(this.GeneralTasks.List) - 1; i >= 0; i-- {
		m := this.GeneralTasks.List[i]
		prevId := m.GetPrevID()
		if finishMap[m.ID] || ids[m.ID] || (prevId != 0 && !finishMap[prevId]) || (this.IsPastDue(m.GetJson()) && !this.checkTaskCondition(nil, m)) {
			this.GeneralTasks.List = append(this.GeneralTasks.List[:i], this.GeneralTasks.List[i+1:]...)
		} else {
			ids[m.ID] = true
		}
	}
	// 新增可以做的任务
	datas := config.GetJson("generalTask").Datas
	for _, m := range datas {
		id, prev_id := ut.Int32(m["id"]), ut.Int32(m["prev_id"])
		if finishMap[id] || (prev_id != 0 && !finishMap[prev_id]) || this.IsPastDue(m) {
			continue
		} else if !ids[id] {
			this.GeneralTasks.List = append(this.GeneralTasks.List, g.NewTaskInfo(m))
		}
	}
}

// 是否过期
func (this *User) IsPastDue(json map[string]interface{}) bool {
	if ut.Int(json["type"]) == constant.TASK_TYPE_LIMIT { // 限时任务
		startTime, endTime := ut.String(json["start_time"]), ut.String(json["end_time"])
		if startTime != "" && endTime != "" && !helper.CheckActivityAutoDate(startTime, endTime) {
			return true
		}
	}
	return false
}

// 是否完成了这个任务
func (this *User) IsFinishGeneralTask(id int32) bool {
	this.GeneralTasks.RLock()
	defer this.GeneralTasks.RUnlock()
	return array.Has(this.GeneralTasks.Finishs, id) || array.Has(this.GeneralTasks.ToDayFinishs, id)
}

// 获取常规任务
func (this *User) GetGeneralTask(id int32) *g.TaskInfo {
	this.GeneralTasks.RLock()
	defer this.GeneralTasks.RUnlock()
	if task := array.Find(this.GeneralTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 完成一个任务
func (this *User) FinishGeneralTask(task *g.TaskInfo) {
	this.GeneralTasks.Lock()
	defer this.GeneralTasks.Unlock()
	// 先删除任务列表里面的
	this.GeneralTasks.List = array.RemoveItem(this.GeneralTasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	if task.GetType() == constant.TASK_TYPE_TODAY {
		this.GeneralTasks.ToDayFinishs = append(this.GeneralTasks.ToDayFinishs, task.ID)
	} else {
		this.GeneralTasks.Finishs = append(this.GeneralTasks.Finishs, task.ID)
	}
	// 添加下一个任务
	this.CheckUpdateGeneralTask()
	this.FlagUpdateDB()
}

// ----------------------------------------------------------------------成就任务-----------------------------------------------------------------------------
type AchieveTaskList struct {
	deadlock.RWMutex
	List    []*g.TaskInfo
	Finishs []int32 // 完成列表
}

func (this *AchieveTaskList) Strip() []int32 {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) int32 { return m.ID })
}

func (this *AchieveTaskList) ToPb() []*pb.TaskInfo {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) *pb.TaskInfo {
		return &pb.TaskInfo{
			Id:       m.ID,
			Progress: m.Progress,
		}
	})
}

func InitAchieveTaskByDB(tasks []*g.TaskInfo) []*g.TaskInfo {
	for i := len(tasks) - 1; i >= 0; i-- {
		if !tasks[i].InitJson("achieveTask", 0) {
			tasks = append(tasks[:i], tasks[i+1:]...)
		}
	}
	return tasks
}

// 刷新可以做的任务
func (this *User) CheckUpdateAchieveTask() {
	// 删除没有的
	finishMap := map[int32]bool{}
	for i := len(this.AchieveTasks.Finishs) - 1; i >= 0; i-- {
		id := this.AchieveTasks.Finishs[i]
		if json := config.GetJsonData("achieveTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.AchieveTasks.Finishs = append(this.AchieveTasks.Finishs[:i], this.AchieveTasks.Finishs[i+1:]...)
		}
	}
	// 删除已经完成的任务且重复的
	ids := map[int32]bool{}
	for i := len(this.AchieveTasks.List) - 1; i >= 0; i-- {
		m := this.AchieveTasks.List[i]
		prevId := m.GetPrevID()
		if finishMap[m.ID] || ids[m.ID] || (prevId != 0 && !finishMap[prevId]) {
			this.AchieveTasks.List = append(this.AchieveTasks.List[:i], this.AchieveTasks.List[i+1:]...)
		} else {
			ids[m.ID] = true
		}
	}
	// 新增可以做的任务
	datas := config.GetJson("achieveTask").Datas
	for _, m := range datas {
		id, prev_id := ut.Int32(m["id"]), ut.Int32(m["prev_id"])
		if finishMap[id] || (prev_id != 0 && !finishMap[prev_id]) {
			continue
		} else if !ids[id] {
			newTask := g.NewTaskInfo(m)
			this.NewTaskProgress(newTask)
			this.AchieveTasks.List = append(this.AchieveTasks.List, newTask)
		}
	}
}

// 是否完成了这个任务
func (this *User) IsFinishAchieveTask(id int32) bool {
	this.AchieveTasks.RLock()
	defer this.AchieveTasks.RUnlock()
	return array.Has(this.AchieveTasks.Finishs, id)
}

// 获取成就任务
func (this *User) GetAchieveTask(id int32) *g.TaskInfo {
	this.AchieveTasks.RLock()
	defer this.AchieveTasks.RUnlock()
	if task := array.Find(this.AchieveTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 完成一个任务
func (this *User) FinishAchieveTask(task *g.TaskInfo) {
	this.AchieveTasks.Lock()
	defer this.AchieveTasks.Unlock()
	// 先删除任务列表里面的
	this.AchieveTasks.List = array.RemoveItem(this.AchieveTasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	this.AchieveTasks.Finishs = append(this.AchieveTasks.Finishs, task.ID)
	// 添加下一个任务
	this.CheckUpdateAchieveTask()
	this.FlagUpdateDB()
}

// 解锁称号 (后台)
func (this *User) AddTitleByGm(id int32) {
	if !this.HasTitle(id) {
		// 找到称号对应的任务
		json := array.Find(config.GetJson("achieveTask").Datas, func(m map[string]interface{}) bool { return ut.Int32(m["title_id"]) == id })
		if json != nil {
			taskId := ut.Int32(json["id"])
			taskCond := ut.StringToInt32s(ut.String(json["cond"]), ",")
			prevId := ut.Int32(json["prev_id"])
			taskCondType := taskCond[0]
			taskParam := taskCond[2]
			// 找到已接收的任务中同类型的前置任务
			var curTask *g.TaskInfo
			this.AchieveTasks.RLock()
			for _, v := range this.AchieveTasks.List {
				if v.GetCondInfo().Type == taskCondType {
					curTask = v
					break
				}
			}
			this.AchieveTasks.RUnlock()
			if curTask == nil {
				this.AchieveTasks.Lock()
				// 前置任务不存在 直接完成该任务
				this.AchieveTasks.Finishs = append(this.AchieveTasks.Finishs, taskId)
				// 添加下一个任务
				this.CheckUpdateAchieveTask()
				this.AchieveTasks.Unlock()
			} else if curTask.ID == taskId {
				// 相同id 直接完成该任务
				this.FinishAchieveTask(curTask)
			} else if curTask.ID < taskId {
				// 前置任务id小于该称号任务 需完成之前所有前置任务
				this.AchieveTasks.Lock()
				// 完成之前所有的前置任务
				for prevId != 0 && prevId != curTask.ID {
					this.AchieveTasks.Finishs = append(this.AchieveTasks.Finishs, prevId)
					taskConf := config.GetJson("achieveTask").GetById(prevId)
					if taskConf != nil {
						prevId = ut.Int32(taskConf["prev_id"])
					} else {
						break
					}
				}
				// 最后完成该任务并接收新任务
				this.AchieveTasks.Finishs = append(this.AchieveTasks.Finishs, taskId)
				this.CheckUpdateAchieveTask()
				this.AchieveTasks.Unlock()
			}
			// 非任务进度数据记录的任务更新对应的玩家数据
			if taskCondType == tctype.WIN_COUNT {
				// 胜利次数
				if this.AccTotalGameCounts[0] < taskParam {
					this.AccTotalGameCounts[0] = taskParam
				}
			}
		}
	}
	// 添加称号
	this.UnlockTitle(id)
}

// ----------------------------------------------------------------------Common-----------------------------------------------------------------------------
// 检测任务条件
func (this *User) checkTaskCondition(module *Lobby, task *g.TaskInfo) bool {
	cond := task.GetCondInfo()
	if task == nil {
		return false
	} else if cond.Type == tctype.LOGIN_DAY_COUNT { // 登录天数
		return this.LoginDayCount >= cond.Count
	} else if cond.Type == tctype.INVITE_FRIEND { // 邀请好友
		return this.GetInviteFriendNotUseCount() >= cond.Count
	} else if cond.Type == tctype.TODAY_TURNTABLE_COUNT { // 每日转盘次数
		return this.WheelCurrCount >= cond.Count
	} else if cond.Type == tctype.SUM_TURNTABLE_COUNT { // 转盘总次数
		return this.WheelSumCount >= cond.Count
	} else if cond.Type == tctype.WIN_COUNT { // 获得游戏胜利次数
		return this.AccTotalGameCounts[0] >= cond.Count
	} else if cond.Type == tctype.WHEEL_MUL { // 在大转盘转到x倍
		return this.MaxWheelMul >= cond.ID
	} else if cond.Type == tctype.PLAY_GAME_COUNT { // 完成对局次数
		return this.GetTotalGameCount() >= cond.Count
	} else if cond.Type == tctype.BUILD_LV { // 建筑等级
		playSid := this.GetPlaySid()
		if playSid == 0 {
			return false
		}
		ok, err := ut.RpcBool(module.InvokeGameRpc(playSid, slg.RPC_CHECK_GAME_TASK_CONDITION, ut.Bytes(this.UID), ut.Bytes(cond)))
		if err != "" {
			log.Error("checkTaskCondition error. " + err)
		}
		return ok
	}
	return task.Progress >= cond.Count
}

// 触发任务
func (this *User) TriggerTask(module *Lobby, condType int32, count int32, param int32) {
	notifyGeneralTasks, notifyAchieveTasks := this.TriggerLoginTask(condType, count, param)
	if len(notifyGeneralTasks)+len(notifyAchieveTasks) > 0 {
		notifyTasks := &pb.TaskUpdateNotify{
			GeneralTasks: notifyGeneralTasks,
			AchieveTasks: notifyAchieveTasks,
		}
		this.PutNotifyQueue(constant.NQ_UPDATE_TASKS, &pb.OnUpdatePlayerInfoNotify{Data_55: notifyTasks})
	}

	// 游戏内任务 指定类型调用RPC触发游戏内任务
	if module != nil {
		module.InvokeGameRpcNR(this.GetPlaySid(), slg.RPC_TRIGGER_TASK_BY_LOBBY, this.UID, condType, count, param)
	}
}

// 触发登陆服任务
func (this *User) TriggerLoginTask(condType int32, count int32, param int32) (notifyGeneralTasks []*pb.TaskInfo, notifyAchieveTasks []*pb.TaskInfo) {
	// 常规任务
	this.GeneralTasks.RLock()
	defer this.GeneralTasks.RUnlock()
	for _, t := range this.GeneralTasks.List {
		if t.GetCondInfo().Type == condType && t.GetCondInfo().ID == param {
			if t.AddProgress(count) {
				notifyGeneralTasks = append(notifyGeneralTasks, t.ToPb())
			}
		}
	}
	// 成就任务
	if serverType := this.GetPlaySid() / slg.ROOM_TYPE_FLAG; serverType == slg.CUSTOM_SERVER_TYPE {
		// 自定义区服不触发成就
	} else {
		this.AchieveTasks.RLock()
		defer this.AchieveTasks.RUnlock()
		for _, t := range this.AchieveTasks.List {
			cond := t.GetCondInfo()
			if cond.Type == condType {
				if condType == tctype.LIVER_EMPEROR && param >= cond.ID { // 肝帝单独处理
					if t.AddProgress(count) && t.Progress >= cond.Count {
						notifyAchieveTasks = append(notifyAchieveTasks, t.ToPb())
					}
				} else if cond.ID == param {
					if t.AddProgress(count) && t.Progress >= cond.Count {
						notifyAchieveTasks = append(notifyAchieveTasks, t.ToPb())
					}
				}
			}
		}
	}
	return
}

// 新任务进度继承 一些新任务的进度会直接继承玩家数据
func (this *User) NewTaskProgress(task *g.TaskInfo) {
	if task.GetCondInfo().Type == tctype.THOUSAND_MU {
		task.Progress = this.MaxLandCount
	}
}
