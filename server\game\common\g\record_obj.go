package g

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"go.mongodb.org/mongo-driver/bson"
)

// 士兵阵亡记录
type PawmDeadRecord struct {
	KillerOwner string `json:"killer_owner" bson:"killer_owner"` //击杀者所属玩家uid
	Id          int32  `json:"id" bson:"id"`                     //阵亡士兵配置id
	Lv          int32  `json:"lv" bson:"lv"`                     //阵亡士兵等级
	KillerId    int32  `json:"killer_id" bson:"killer_id"`       //击杀者配置id
}

// 战斗积分记录
type BattleScoreRecordData struct {
	Uid     string `bson:"uid"`      //战斗记录uid
	Owner   string `bson:"owner"`    //玩家uid
	AlliUid string `bson:"alli_uid"` //战斗开始时所属联盟uid
	Date    string `bson:"date"`     //日期 以开始时间为准

	ValidInfo     map[int32]int32           `bson:"valid_info"`     //有效战斗数据
	InvalidInfo   map[int32]int32           `bson:"invalid_info"`   //无效战斗数据
	DeadInfo      []*PawmDeadRecord         `bson:"dead_info"`      //阵亡数据
	PawnStatistic map[int32]map[int32]int32 `bson:"pawn_statistic"` //士兵数据统计
	ArmyUidList   []string                  `bson:"army_uid_list"`  //参与战斗的军队uid列表

	Score     float64 `bson:"score"`      //联盟积分
	BeginTime int64   `bson:"begin_time"` //开始时间
	EndTime   int64   `bson:"end_time"`   //结束时间

	Index       int32 `bson:"index"`        //位置
	BattleCount int32 `bson:"battle_count"` //战斗次数
	Version     int32 `bson:"version"`      //战斗记录版本号
	Iswin       bool  `bson:"iswin"`        //是否胜利
}

func (this *BattleScoreRecordData) ToPb() *pb.BattleScoreRecord {
	return &pb.BattleScoreRecord{
		Uid:         this.Uid,
		BeginTime:   this.BeginTime,
		EndTime:     this.EndTime,
		Date:        this.Date,
		ValidInfo:   this.ValidInfo,
		InvalidInfo: this.InvalidInfo,
		DeadInfo: array.Map(this.DeadInfo, func(m *PawmDeadRecord, _ int) *pb.PawnDeadInfo {
			return &pb.PawnDeadInfo{
				Id:          m.Id,
				Lv:          m.Lv,
				KillerId:    m.KillerId,
				KillerOwner: m.KillerOwner,
			}
		}),
		ArmyUidList: this.ArmyUidList,
		Index:       this.Index,
		IsWin:       this.Iswin,
		IsCanPlay:   this.Version == 15, // BATTLE_RECORD_VERSION
	}
}

// 战斗帧数数据记录
type BattleRecordData struct {
	Hp        []int32        `bson:"hp"`
	Builds    []*BuildStrip  `bson:"builds"`
	BuildInfo []int32        `bson:"buildInfo"`
	Armys     []*ArmyStrip2  `bson:"armys"`
	Army      *ArmyStrip2    `bson:"army"`
	Fighters  []*FigherStrip `bson:"fighters"`
	Pawn      *PawnStrip2    `bson:"pawn"`

	Uid     string `bson:"uid"`
	Owner   string `bson:"owner"`
	ArmyUid string `bson:"armyUid"`

	RandSeed          int   `bson:"randSeed"`
	Type              int32 `bson:"type"`
	CurrentFrameIndex int32 `bson:"currentFrameIndex"`
	Camp              int32 `bson:"camp"`
	Fps               int32 `bson:"fps"`
	CityId            int32 `bson:"cityId"`
}

// 使用序列化深拷贝
func (this *BattleRecordData) Copy() *BattleRecordData {
	ret := &BattleRecordData{}
	if recordBytes, err := bson.Marshal(this); err == nil {
		bson.Unmarshal(recordBytes, ret)
	}
	return ret
}

// 战斗者数据
type FigherStrip struct {
	Uid          string `bson:"uid"`
	AttackTarget string `bson:"attackTarget"`
	Owner        string `bson:"owner"`

	Point *ut.Vec2   `bson:"point"`
	Hp    []int32    `bson:"hp"`
	Buffs []*BuffObj `bson:"buffs"`

	Id   int32 `bson:"id"`
	Lv   int32 `bson:"lv"`
	Camp int32 `bson:"camp"`

	AttackIndex int32 `bson:"attackIndex"`
	WaitRound   int32 `bson:"waitRound"`
	RoundCount  int32 `bson:"roundCount"`
	AttackCount int32 `bson:"attackCount"`
	EnterDir    int32 `bson:"enterDir"`

	TowerId int32 `bson:"towerId"`
	TowerLv int32 `bson:"towerLv"`

	IsFalg      bool `bson:"isFalg"`
	IsNoncombat bool `bson:"isNoncombat"`
	IsPet       bool `bson:"isPet"`
}

func (this *FigherStrip) ToBuffsPb() []*pb.BuffInfo {
	return array.Map(this.Buffs, func(m *BuffObj, _ int) *pb.BuffInfo { return m.ToPb() })
}

// 军队用于战斗记录的数据
type ArmyStrip2 struct {
	Uid      string        `bson:"uid"`
	Name     string        `bson:"name"`
	Owner    string        `bson:"owner"`
	Pawns    []*PawnStrip2 `bson:"pawns"`
	Index    int32         `bson:"index"`
	State    int32         `bson:"state"`
	EnterDir int32         `bson:"enterDir"`
}

// 士兵用于战斗记录的数据
type PawnStrip2 struct {
	Uid             string          `bson:"uid"`
	Point           *ut.Vec2        `bson:"point"`
	Equip           *EquipStrip     `bson:"equip"`
	Portrayal       *PortrayalStrip `bson:"portrayal"`
	Index           int32           `bson:"index"`
	Id              int32           `bson:"id"`
	Lv              int32           `bson:"lv"`
	SkinId          int32           `bson:"skinId"`
	CurHp           int32           `bson:"curHp"`
	CurAnger        int32           `bson:"curAnger"`
	AttackSpeed     int32           `bson:"AttackSpeed"`
	RodeleroCadetLv int32           `bson:"rodeleroCadetLv"`
	PetId           int32           `bson:"petId"`
}

// 建筑用于战斗记录的数据
type BuildStrip struct {
	Uid   string   `bson:"uid"`
	Point *ut.Vec2 `bson:"point"`
	Index int32    `bson:"index"`
	Id    int32    `bson:"id"`
	Lv    int32    `bson:"lv"`
}

// 战斗前军队数据
type BattlePreArmyData struct {
	Uid       string        `bson:"uid"`
	Name      string        `bson:"name"`
	Owner     string        `bson:"owner"`
	Pawns     []*PawnStrip3 `bson:"pawns"`
	BeginTime int64         `bson:"beginTime"`
	EndTime   int64         `bson:"endTime"`
	PawnCount int32         `bson:"pawnCount"`
}

type PawnStrip3 struct {
	Uid         string `bson:"uid"`
	PortrayalId int32  `bson:"portrayalId"`
	Id          int32  `bson:"id"`
	Lv          int32  `bson:"lv"`
	EquipId     int32  `bson:"equipId"`
	MaxHp       int32  `bson:"maxHp"`
	CurHp       int32  `bson:"curHp"`
}
