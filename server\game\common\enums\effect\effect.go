package effect

// 通用效果对象的类型
const (
	NONE                  = iota
	BT_QUEUE              //建造队列 1
	BUILD_CD              //减少修建时间 2
	GRANARY_CAP           //粮仓容量 3
	WAREHOUSE_CAP         //仓库容量 4
	XL_CD                 //招募士兵时间 5
	ALLIANCE_PERS         //联盟人数 6
	MERCHANT_COUNT        //商人数量 7
	MERCHANT_TRANSIT_CAP  //商人运送量 8
	WALL_HP               //城墙血量 9
	FORGE_CD              //打造装备速度 10
	ARMY_COUNT            //军队数量 11
	RES_OUTPUT            //资源产量 12
	MARCH_CD              //减少行军时间 13
	UPLVING_CD            //训练士兵时间 14
	GW_CAP                //增加粮仓和仓库容量 15
	XL_2LV                //有一定几率训练出2级士兵 16
	TRANSIT_CD            //减少商人运送时间 17
	MAIN_MARCH_MUL        //主城要塞行军时间加速 18
	FORT_BUILD_CD         //要塞修建时间减少 19
	TREASURE_AWARD        //宝箱奖励增加 20
	FREE_RECAST           //有一定几率免费重铸装备 21
	CEREAL_OUTPUT         //粮食产量 22
	TIMBER_OUTPUT         //木头产量 23
	STONE_OUTPUT          //石头产量 24
	TOWER_LV              //哨塔等级 25
	FARM_OUTPUT           //农场产量 26
	QUARRY_OUTPUT         //采石场产量 27
	MILL_OUTPUT           //伐木场产量 28
	BACK_COURT            //速速回防 29
	MARKET_SERVICE_CHARGE //置换手续费 30
	CITY_COUNT_LIMIT      //地面建筑上限 31
	RES_OUTPUT_PERCENT    //资源产量 百分比 32
	RECRUIT_COST          //招募士兵费用 33
	UPLVING_COST          //训练士兵费用 34
	TOWER_HP              //哨塔，要塞，城墙的耐久提高 35
	OTHER_RES_ODDS        //地块的其他资源掉落概率提高 36
	CURE_CD               //治疗士兵加速 37
)
