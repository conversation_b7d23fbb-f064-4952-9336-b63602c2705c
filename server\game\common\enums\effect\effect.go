package effect

// 通用效果对象的类型
const (
	NONE                  = iota
	BT_QUEUE              //建造队列 1
	BUILD_CD              //减少修建时间 2
	GRANARY_CAP           //粮仓容量 3
	WAREHOUSE_CAP         //仓库容量 4
	XL_CD                 //招募士兵时间 5
	ALLIANCE_PERS         //联盟人数 6
	MERCHANT_COUNT        //商人数量 7
	DRILL_QUEUE           //招募队列 8
	WALL_HP               //城墙血量 9
	FORGE_CD              //打造装备速度 10
	ARMY_COUNT            //军队数量 11
	RES_OUTPUT            //资源产量 12
	MARCH_CD              //减少行军时间 13
	UPLVING_CD            //训练士兵时间 14
	GW_CAP                //增加粮仓和仓库容量 15
	XL_2LV                //有一定几率训练出2级士兵 16
	TRANSIT_CD            //每个商人的运输量增加1000 减少商人运送时间 17
	MAIN_MARCH_MUL        //主城要塞行军时间加速 18
	CITY_BUILD_CD         //地面建筑修建时间减少 19
	TREASURE_AWARD        //宝箱奖励增加 20
	FREE_RECAST           //有一定几率免费重铸装备 21
	RARE_RES_OUTPUT       //书铁每天增加 22
	MORE_RARE_RES         //书铁超过100 每天增加 23
	LV_UP_QUEUE           //训练队列 24
	TOWER_LV              //哨塔等级 25
	FARM_OUTPUT           //农场产量 26
	QUARRY_OUTPUT         //采石场产量 27
	MILL_OUTPUT           //伐木场产量 28
	CURE_QUEUE            //治疗队列 29
	MARKET_SERVICE_CHARGE //置换手续费 30
	CITY_COUNT_LIMIT      //地面建筑上限 31
	_                     // 32 ---------------------------（弃用）
	RECRUIT_COST          //招募士兵费用 33
	UPLVING_COST          //训练士兵费用 34
	TOWER_HP              //哨塔，要塞，城墙的耐久提高 35
	OTHER_RES_ODDS        //地块的其他资源掉落概率提高 36
	CURE_CD               //治疗士兵加速 37
	DEFEND_HALO           //我方领地加最大生命 38
	ATTACK_HALO           //敌方领地加攻击 39
	ADD_MAX_HP            //所有士兵加生命上限 40
	ADD_ATTACK            //所有士兵加攻击力 41
	LV_1_POWER            //所有1级士兵加生命上限和攻击力 42
	ALL_OCCUPY_REWARD     //10个玩家沦陷后获得卷轴或钉子 43
	OCCUPY_ROBBERY        //攻占后掠夺资源 44
	CELL_TONDEN_CD        //屯田时间减少 45
	CELL_TONDEN_TREASURE  //屯田宝箱增加 46
	CURE_FREE_COUNT       //免费治疗次数 47
	FREE_RECAST_COUNT     //免费打造/重铸次数 48
	FREE_DRILL_COUNT      //免费招募次数 49
	FREE_LEVING_COUNT     //免费训练次数 50
	SEASON_ADD_SCROLLS    //每季节获得卷轴 51
	SEASON_ADD_FIXATOR    //每季节获得钉子 52
	ADD_DMG_TO_MONSTER    //增加对野怪的伤害 53
	ADD_DMG_TO_BUILD      //增加对建筑的伤害 54
	CIRCLE_OF_LIFE        //每回合恢复生命 55
)
