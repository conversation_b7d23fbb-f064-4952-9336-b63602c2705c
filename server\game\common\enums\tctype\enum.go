package tctype

// 任务条件类型
const (
	NONE                  = 0
	BUILD_LV              = 4    //建筑等级 id,lv
	CELL_COUNT            = 12   //拥有领地数 count
	LAND_LV_COUNT         = 1001 //拥有x级地块数量 lv,count
	LOGIN_DAY_COUNT       = 1002 //登录天数 count
	INVITE_FRIEND         = 1003 //邀请好友 count
	TODAY_TURNTABLE_COUNT = 1004 //转盘次数 count
	LAND_TYPE_COUNT       = 1005 //拥有x类型地块数量 type,count
	WIN_COUNT             = 1006 //获得游戏胜利次数 count
	LIVER_EMPEROR         = 1007 //肝帝 time,data <RECORD PROGRESS> 成就
	THOUSAND_MU           = 1008 //百亩/千亩 <RECORD PROGRESS> 成就
	TODAY_CELL_COUNT      = 1009 //每日打地数量 count
	GIVE_RES_COUNT        = 1010 //赠送资源数量 count
	EQUIP_ALL_ATTR_MAX    = 1011 //装备所有属性都是最高属性 <RECORD PROGRESS> 成就
	EQUIP_RECAST_COUNT    = 1012 //装备重铸次数 count <RECORD PROGRESS> 成就
	ONE_BLOOD_SURVIVE     = 1013 //任意士兵剩余一滴血活到战斗结束 <RECORD PROGRESS> 成就
	C_DODGE_FIVE_TIMES    = 1014 //任意士兵在回合内连续闪避5次 <RECORD PROGRESS> 成就
	SAME_LEVEL_SECKILL    = 1015 //任意士兵秒杀同等级士兵 <RECORD PROGRESS> 成就
	WHEEL_MUL             = 1016 //在大转盘转到x倍 count
	RECRUIT_PAWN_COUNT    = 1017 //招募任意士兵 count <RECORD PROGRESS>
	ARMY_COUNT            = 1018 //拥有多少军队 count
	RECRUIT_PAWN_APPOINT  = 1019 //招募指定id士兵 id,count <RECORD PROGRESS>
	UPLV_PAWN_APPOINT     = 1020 //升级指定士兵到指定等级 id,lv <RECORD PROGRESS> <BY DATA>
	FORGE_EQUIP_APPOINT   = 1021 //打造指定装备 id
	STUDY_TYPE_APPOINT    = 1022 //研究指定类型 type
	// _         = 1023 //在内政选择任意一个政策 弃用
	ARMY_PAWN_COUNT     = 1024 //军队最多的士兵数量 count <RECORD PROGRESS> <BY DATA>
	BT_MAP_RES_BUILD    = 1025 //任意修建一个农场或伐木场或采石场
	BT_MAP_BUILD        = 1026 //修建指定地图建筑 id,count
	UPLV_PAWN           = 1027 //升级任意士兵到指定等级 lv <RECORD PROGRESS> <BY DATA>
	RECHARGE_COUNT      = 1028 //充值次数 count <RECORD PROGRESS>
	NOT_GET_CRIT        = 1029 //未被99%概率暴击 <RECORD PROGRESS> 成就
	RECRUIT_PAWN_TYPE   = 1030 //招募指定类型士兵 type,count <RECORD PROGRESS>
	RECRUIT_RANGED_PAWN = 1031 //招募远程士兵 count <RECORD PROGRESS>
	OCCUPY_ANCIENT      = 1032 //攻占遗迹 id <RECORD PROGRESS>
	PLAY_GAME_COUNT     = 1033 //完成对局次数 count
	WORSHIP_HERO        = 1034 //供奉一个英雄 count
	FORGE_EXC_EQUIP     = 1035 //打造x个专属装备 count
	KILL_MONSTER_COUNT  = 1036 //击杀野怪数量 count <RECORD PROGRESS>
	ALL_CAPTURE_COUNT   = 1037 //全部沦陷数量 count
	SUM_TURNTABLE_COUNT = 1038 //转盘转动总次数 count
	FORGE_EQUIP         = 1039 //打造x个装备 count
	LAND_10K_COUNT      = 1040 //一万亩 data count <RECORD PROGRESS>
)

// 进度为数值的任务map
var TASK_PROGRESS_DATA_TYPE_MAP = map[int32]bool{
	LIVER_EMPEROR:      true, //肝帝
	THOUSAND_MU:        true, //一万亩
	EQUIP_RECAST_COUNT: true, //装备重铸次数
}

// 游戏服触发并同步登陆服的任务
var GAME_TRGGER_TO_LOGIN_TYPE_MAP = map[int32]bool{
	EQUIP_RECAST_COUNT: true, //装备重铸次数
	EQUIP_ALL_ATTR_MAX: true, //装备所有属性都是最高属性
	ONE_BLOOD_SURVIVE:  true, //任意士兵剩余一滴血活到战斗结束
	C_DODGE_FIVE_TIMES: true, //任意士兵在回合内连续闪避5次
	SAME_LEVEL_SECKILL: true, //任意士兵秒杀同等级士兵
	NOT_GET_CRIT:       true, //未被99%概率暴击
	LIVER_EMPEROR:      true, //肝帝
}

// 世界事件类型
const (
	WORLD_EVENT_TYPE_NONE           = iota
	WORLD_EVENT_TYPE_10K_LAND_FIRST //第一个万亩
)
