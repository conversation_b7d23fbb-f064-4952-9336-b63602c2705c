package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strconv"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) InitHDTeam() {
	this.GetServer().RegisterGO("HD_GetTeamInfo", this.getTeamInfo)                       //获取队伍信息
	this.GetServer().RegisterGO("HD_GetRoomStateInfos", this.getRoomStateInfos)           //获取指定房间类型的相关信息
	this.GetServer().RegisterGO("HD_InviteTeammate", this.inviteTeammate)                 //组队邀请
	this.GetServer().RegisterGO("HD_InviteTeammateResponse", this.inviteTeammateResponse) //组队邀请回复
	this.GetServer().RegisterGO("HD_DelTeammate", this.delTeammate)                       //删除队员
	this.GetServer().RegisterGO("HD_ExitTeam", this.exitTeam)                             //退出队伍
	this.GetServer().RegisterGO("HD_ChangeTeamMode", this.changeTeamMode)                 //切换模式选择
	this.GetServer().RegisterGO("HD_SelectExpectPos", this.selectExpectPos)               //选择期望的位置
	this.GetServer().RegisterGO("HD_DidsbandTeam", this.disbandTeam)                      //解散队伍
	this.GetServer().RegisterGO("HD_TeamChangeJob", this.teamChangeJob)                   //变更队员职位
}

// 获取队伍信息
func (this *Lobby) getTeamInfo(session gate.Session, msg *pb.LOBBY_HD_GETTEAMINFO_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 只要进入过大厅就算引导完成
	user.CarryNoviceData = false
	s2c := &pb.LOBBY_HD_GETTEAMINFO_S2C{}
	s2c.TeamInfo = this.GetTeamInfoPb(user)
	s2c.TeamInviteList = user.TeamInvitesToPb()
	s2c.UserNumMax = int32(slg.TEAM_USER_NUM_MAX)
	return pb.ProtoMarshal(s2c)
}

// 获取房间类型的相关信息
func (this *Lobby) getRoomStateInfos(session gate.Session, msg *pb.LOBBY_HD_GETROOMSTATEINFOS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	now := int64(ut.Now())
	// 兼容下
	room := GetRoomById(user.PlaySID)
	if room == nil || room.IsClose {
		room = nil
		user.SetPlaySid(0)
	}
	// 是否新手
	canPlayNewbie := user.CheckCanPlayGameNewbie()
	playSid := user.PlaySID
	playServerType := playSid / slg.ROOM_TYPE_FLAG
	// 可以玩的区服类型
	canPlayRoomTypes := []int32{slg.RANK_SERVER_TYPE, slg.NORMAL_SERVER_TYPE}
	// 如果可以玩新手区 并且当前没有玩其他区 兼容 已经在新手区游戏中
	if (canPlayNewbie && playSid == 0) || (playSid != 0 && playServerType == slg.ROOKIE_SERVER_TYPE) {
		canPlayRoomTypes = []int32{slg.ROOKIE_SERVER_TYPE, slg.RANK_SERVER_TYPE, slg.NORMAL_SERVER_TYPE}
	}
	// 获取排位和自由的下次开启时间
	nextMatchTime := this.GetRoomNextMatchTime()
	states := []*pb.RoomStateInfo{}
	allotPlaySid := 0
	// 如果在玩
	if playSid != 0 {
		delete(nextMatchTime, playServerType)
		info := &pb.RoomStateInfo{RoomType: int32(playServerType), State: slg.MODE_INFO_STATE_IN_GAME}
		if room == nil || room.State != slg.SERVER_STATUS_OPEN {
			info.State = slg.MODE_INFO_STATE_CLOSE
		} else {
			info.Time = int64(ut.Max(0, int(now-room.CreateTime)))
		}
		states = append(states, info)
	} else if canPlayNewbie { //新手区
		info := &pb.RoomStateInfo{RoomType: slg.ROOKIE_SERVER_TYPE, State: slg.MODE_INFO_STATE_IN_GAME}
		if room == nil {
			room = GetCanPlayRookieRoom(user) //获取一个可以玩的新手区
		}
		if room == nil || room.State != slg.SERVER_STATUS_OPEN {
			info.State = slg.MODE_INFO_STATE_CLOSE
		} else {
			info.Time = int64(ut.Max(0, int(now-room.CreateTime)))
			allotPlaySid = int(room.GetId())
		}
		states = append(states, info)
	}
	if !user.IsNovicePlayer() {
		// 非新手获取队伍信息
		if teamInfo := this.GetTeamInfoPb(user); teamInfo != nil && teamInfo.MatchOpenTime > 0 { // 团队是否开始匹配
			delete(nextMatchTime, teamInfo.RoomType)
			// 仍在匹配中 删除该模式下其他状态的数据
			states = array.Delete(states, func(m *pb.RoomStateInfo) bool { return m.RoomType == teamInfo.RoomType })
			states = append(states, &pb.RoomStateInfo{
				RoomType: teamInfo.RoomType,
				State:    slg.MODE_INFO_STATE_IN_MATCH,
				Time:     int64(ut.Max(0, int(teamInfo.MatchOpenTime-now))),
			})
		}
	}
	// 添加剩余 匹配时间
	for k, v := range nextMatchTime {
		state := slg.MODE_INFO_STATE_NOT_APPLY
		if applyCloseMap.Get(k) {
			// 未开放
			state = slg.MODE_INFO_STATE_NOT_OPEN
		}
		states = append(states, &pb.RoomStateInfo{
			RoomType: int32(k),
			State:    int32(state),
			Time:     int64(v),
		})
	}
	return pb.ProtoMarshal(&pb.LOBBY_HD_GETROOMSTATEINFOS_S2C{
		PlaySid:          int32(playSid),
		AllotPlaySid:     int32(allotPlaySid),
		States:           states,
		CanPlayRoomTypes: canPlayRoomTypes,
	})
}

// 邀请加入队伍
func (this *Lobby) inviteTeammate(session gate.Session, msg *pb.LOBBY_HD_INVITETEAMMATE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.PlaySID != 0 {
		return nil, ecode.UNKNOWN.String()
	} else if user.IsNovicePlayer() {
		return nil, ecode.NOVICE_PLAYER.String()
	}
	uid := msg.GetUid()
	var friendUser *User
	// uid为数字则通过uid查询
	if _, err := strconv.Atoi(uid); err == nil {
		friendUser = GetUserByDBNotAdd(uid)
	}
	if friendUser == nil && uid != user.Nickname { //找不到用昵称找
		friendUser = GetUserByNicknmaeOrDB(uid)
	}
	if friendUser == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if friendUser.UID == user.UID {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	teamUid := user.TeamUid
	if teamUid == "" {
		// 邀请人没有队伍 会在邀请的rpc中创建队伍
		teamUid = user.UID
	}
	_, err = this.InvokeTeamFunc(teamUid, slg.RPC_TEAM_INVITE,
		user.UID, friendUser.UID, friendUser.Nickname, friendUser.HeadIcon,
		user.TeamUid, user.Nickname, user.HeadIcon, msg.GetRoomType(),
	)
	return
}

// 组队邀请回复
func (this *Lobby) inviteTeammateResponse(session gate.Session, msg *pb.LOBBY_HD_INVITETEAMMATERESPONSE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if err = user.UserJoinTeamCheck(); err != "" {
		user.TeamInviteList = []*TeamInviteInfo{}
		return //玩家自己不能加入
	}
	user.TeamInviteListLock.Lock()
	defer user.TeamInviteListLock.Unlock()
	teamUid, isAgree := msg.GetUid(), msg.GetIsAgree()
	index := array.FindIndex(user.TeamInviteList, func(m *TeamInviteInfo) bool { return m.Uid == teamUid })
	if index == -1 {
		return nil, ecode.TEAM_NOT_EXIST.String()
	} else if !isAgree {
		this.InvokeTeamFuncNR(teamUid, slg.RPC_TEAM_INVITE_RESPONSE, user.UID, isAgree)
		this.SendTeamSysMsg(teamUid, slg.TEAM_MSG_TYPE_REFUSE, user.Nickname)
	} else if rst, e := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_TEAM_INVITE_RESPONSE, user.UID, isAgree)); e == "" {
		s2c := &pb.LOBBY_HD_INVITETEAMMATERESPONSE_S2C{}
		s2c.TeamInfo = &pb.TeamInfo{}
		pb.ProtoUnMarshal(rst, s2c.TeamInfo)
		// 拒绝其他队伍
		for _, v := range user.TeamInviteList {
			if v.TeamUid != teamUid {
				this.InvokeTeamFuncNR(v.TeamUid, slg.RPC_TEAM_INVITE_RESPONSE, user.UID, false)
			}
		}
		// 设置该玩家的队伍信息
		user.SetTeamUid(teamUid)
		user.TeamInviteList = []*TeamInviteInfo{}
		// user.AddSubChannels(rds.RDS_USER_SUB_CHANNEL_TEAM + user.TeamUid)
		this.SendTeamSysMsg(teamUid, slg.TEAM_MSG_TYPE_JOIN, user.Nickname)
		return pb.ProtoMarshal(s2c)
	} else {
		err = e
	}
	user.TeamInviteList = append(user.TeamInviteList[:index], user.TeamInviteList[index+1:]...)
	return
}

// 删除队员
func (this *Lobby) delTeammate(session gate.Session, msg *pb.LOBBY_HD_DELTEAMMATE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	log.Info("delTeammate team uid: %v, del uid: %v", user.TeamUid, msg.GetUid())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.TeamUid != "" && user.TeamUid != user.UID {
		return nil, ecode.NOT_OPERATING_AUTH.String() //只有队长可以踢出队员
	}
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_TEAM_DEL_TEAMMATE, msg.GetUid())
	return
}

// 退出队伍
func (this *Lobby) exitTeam(session gate.Session, msg *pb.LOBBY_HD_EXITTEAM_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	log.Info("exitTeam team uid: %v, exit uid: %v", user.TeamUid, user.UID)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if _, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_EXIST_TEAM, user.UID); err == "" {
		user.TeamInviteListLock.Lock()
		user.TeamInviteList = []*TeamInviteInfo{}
		user.TeamInviteListLock.Unlock()
	}
	return
}

// 改变队伍模式
func (this *Lobby) changeTeamMode(session gate.Session, msg *pb.LOBBY_HD_CHANGETEAMMODE_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.TeamUid != user.UID {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_CHANGE_TEAM_MODE, msg.GetRoomType())
	return
}

// 选择期望的位置
func (this *Lobby) selectExpectPos(session gate.Session, msg *pb.LOBBY_HD_SELECTEXPECTPOS_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.PlaySID > 0 {
		return nil, ecode.IN_GAME.String()
	}
	user.ExpectPosition = msg.GetPos()
	return
}

// 解散队伍
func (this *Lobby) disbandTeam(session gate.Session, _ *pb.LOBBY_HD_DIDSBANDTEAM_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	log.Info("disbandTeam team uid: %v", user.TeamUid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.TeamUid != user.UID {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_DISBAND_TEAM, user.UID)
	return
}

// 变更队员职位
func (this *Lobby) teamChangeJob(session gate.Session, msg *pb.LOBBY_HD_TEAMCHANGEJOB_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.TeamUid != user.UID {
		return nil, ecode.NOT_OPERATING_AUTH.String()
	}
	uid, job := msg.GetUid(), ut.Int(msg.GetJob())
	_, err = this.InvokeTeamFunc(user.TeamUid, slg.RPC_TEAM_CHANGE_JOB, uid, job)
	return
}

// 获取房间类型下一次开始匹配时间
func (this *Lobby) GetRoomNextMatchTime() map[int32]int64 {
	rst, err := ut.RpcInterfaceMap(this.InvokeMatchRpc(slg.RPC_GET_ROOM_NEXT_MATCH_TIME))
	if err != "" {
		log.Error("GetRoomNextMatchTime err: %v", err)
	}
	if rst == nil {
		return map[int32]int64{}
	}
	ret := map[int32]int64{}
	for k, v := range rst {
		ret[ut.Int32(k)] = ut.Int64(v)
	}
	return ret
}
