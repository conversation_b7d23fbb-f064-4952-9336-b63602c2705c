package g

import (
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
)

// 一个政策信息
type PolicyInfo struct {
	json   map[string]interface{}
	effect *EffectObj
	ID     int32 `json:"id" bson:"id"`
}

func NewPolicyInfo(id int32) *PolicyInfo {
	if id <= 0 {
		return nil
	}
	json := config.GetJsonData("policy", id)
	if json == nil {
		return nil
	}
	info := &PolicyInfo{ID: id}
	info.json = json
	info.effect = StringToEffectObjs(json["effect"])[0]
	return info
}

func InitPolicyByDB(policys map[int32]*PolicyInfo) map[int32]*PolicyInfo {
	ret := map[int32]*PolicyInfo{}
	if policys != nil {
		for k, m := range policys {
			if json := config.GetJsonData("policy", m.ID); json != nil {
				m.json = json
				m.effect = StringToEffectObjs(json["effect"])[0]
				ret[k] = m
			}
		}
	}
	return ret
}

func (this *PolicyInfo) GetEffect() *EffectObj {
	return this.effect
}

func (this *PolicyInfo) GetUseCond() interface{} {
	return this.json["use_cond"]
}

// 是否需要解锁
func (this *PolicyInfo) IsNeedUnlock() bool {
	return ut.Int(this.json["need_unlock"]) == 1
}
