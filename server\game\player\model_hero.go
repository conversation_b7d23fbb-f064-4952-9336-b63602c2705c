package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	"slgsrv/utils/array"
)

// 兼容下
func (this *Model) CheckUpdateHeroSlots() {
	wld := this.room.GetWorld()
	heroSlots := wld.GetPlayerHeroSlots(this.Uid)
	for _, m := range heroSlots {
		if m.AvatarArmyUID == "" || m.Hero == nil {
		} else if hero := wld.GetPlayerArmyHero(this.Uid, m.AvatarArmyUID); hero == nil || hero.ID != m.Hero.ID {
			m.AvatarArmyUID = ""
		}
	}
}

func (this *Model) ToHeroSlotsDB() []map[string]interface{} {
	return array.Map(this.room.GetWorld().GetPlayerHeroSlots(this.Uid), func(m *g.HeroSlotInfo, _ int) map[string]interface{} { return m.<PERSON><PERSON><PERSON>() })
}

func (this *Model) ToHeroSlotsPb() []*pb.HeroSlotInfo {
	return array.Map(this.room.GetWorld().GetPlayerHeroSlots(this.Uid), func(m *g.HeroSlotInfo, _ int) *pb.HeroSlotInfo { return m.ToPb() })
}
