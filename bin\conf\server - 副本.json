{"Settings": {"ServerType": "development", "MongodbURL": "mongodb://127.0.0.1:27017/slgsrv?w=majority", "MongodbDB": "slgsrv", "GameMongodbURL": "mongodb://127.0.0.1:27017/slgsrv?w=majority", "GameMongodbDB": "slgsrv", "RecordMongodbURL": "mongodb://127.0.0.1:27017/slgsrv?w=majority", "RecordMongodbDB": "slg_record", "Redis": "redis://:1234@127.0.0.1:6379", "TranslateRedis": "redis://:1234@127.0.0.1:6379"}, "Module": {"gate": [{"Id": "gate001", "ProcessID": "development", "Settings": {"WSAddr": ":4653", "TCPAddr": ":4563"}}], "http": [{"Id": "http001", "ProcessID": "development", "Settings": {"Port": "8181"}}], "login": [{"Id": "login001", "ProcessID": "development"}], "match": [{"Id": "match001", "ProcessID": "development"}], "chat": [{"Id": "chat001", "ProcessID": "development"}], "mail": [{"Id": "mail001", "ProcessID": "development"}], "lobby": [{"Id": "lobby1", "ProcessID": "development"}], "game": [{"Id": "game2000148", "ProcessID": "development", "Settings": {"Sid": 2000148}}]}, "Mqtt": {"WirteLoopChanNum": 10, "ReadPackLoop": 1, "ReadTimeout": 600, "WriteTimeout": 300}, "Rpc": {"MaxCoroutine": 10000, "RpcExpired": 1, "LogSuccess": false}, "Log": {"file": {"daily": true, "level": 7}}}