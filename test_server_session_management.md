# 服务器会话管理接口测试文档

## 🎯 **问题背景**

当 game 节点宕机并重启后，虽然 Consul 中注册了新的服务信息，但 match 节点的 `app.serverList` 中仍然缓存着旧的 session 连接，导致无法连接到重启后的 game 节点。

## 🛠️ **解决方案**

添加了两个 HTTP 接口来管理服务器会话：

### 1. **获取所有节点信息**
- **接口**: `GET /http/getAllServerNodes`
- **功能**: 获取当前所有服务器节点的详细信息
- **返回数据**:
  ```json
  {
    "code": 0,
    "data": {
      "timestamp": 1642345678901,
      "nodes": {
        "game": [
          {
            "id": "game1",
            "type": "game",
            "session": "0xc000123456"
          }
        ],
        "lobby": [...],
        "match": [...]
      },
      "app_info": {
        "process_id": "match@match1",
        "settings": {...}
      }
    }
  }
  ```

### 2. **强制删除服务器会话**
- **接口**: `POST /http/forceRemoveServerSession`
- **参数**: `serverID` (要删除的服务器ID)
- **功能**: 强制从 `app.serverList` 中删除指定的服务器会话
- **返回数据**:
  ```json
  {
    "code": 0,
    "data": {
      "server_id": "game1",
      "found": true,
      "session": "0xc000123456",
      "removed_successfully": true,
      "success": true
    },
    "desc": "服务器会话删除成功"
  }
  ```

## 🔧 **使用流程**

### 场景：game1 节点重启后连接失败

1. **查看当前节点状态**:
   ```bash
   curl -X GET "http://match-server:port/http/getAllServerNodes"
   ```

2. **删除旧的会话**:
   ```bash
   curl -X POST "http://match-server:port/http/forceRemoveServerSession" \
        -d "serverID=game1"
   ```

3. **验证删除结果**:
   - 再次调用 `getAllServerNodes` 确认节点已删除
   - 下次调用 `GetServersByType("game")` 时会重新从 Consul 获取

## ⚠️ **注意事项**

### 1. **危险操作警告**
- `forceRemoveServerSession` 使用反射访问私有字段
- 依赖于 vmqant 框架的内部实现
- 可能在框架升级后失效

### 2. **安全考虑**
- 仅在确认节点确实已重启时使用
- 建议在维护窗口期间操作
- 操作前备份重要数据

### 3. **监控建议**
- 监控接口调用日志
- 关注删除操作的成功率
- 观察删除后的连接恢复情况

## 🔍 **技术实现细节**

### 反射删除机制
```go
// 通过反射访问 app.serverList
appValue := reflect.ValueOf(app)
serverListField := appValue.FieldByName("serverList")

// 假设是 sync.Map 类型
if serverListField.Type().String() == "sync.Map" {
    deleteMethod := serverListField.MethodByName("Delete")
    deleteMethod.Call([]reflect.Value{reflect.ValueOf(serverID)})
}
```

### 错误处理
- Panic 恢复机制
- 详细的错误日志
- 操作结果验证

## 📊 **预期效果**

| 操作前 | 操作后 |
|--------|--------|
| ❌ match 连接到旧的 game session | ✅ match 重新从 Consul 获取新的 game 节点 |
| ❌ RPC 调用失败 | ✅ RPC 调用成功 |
| ❌ 服务不可用 | ✅ 服务正常运行 |

## 🎯 **最佳实践**

1. **定期检查**: 定期调用 `getAllServerNodes` 检查节点状态
2. **自动化脚本**: 编写监控脚本自动检测和处理僵尸连接
3. **告警机制**: 当检测到连接异常时自动告警
4. **日志分析**: 分析删除操作的模式，优化重启策略

## 🔄 **后续优化**

1. **框架层面**: 建议 vmqant 框架提供官方的会话管理 API
2. **健康检查**: 实现更智能的连接健康检查机制
3. **自动恢复**: 开发自动检测和恢复僵尸连接的机制
