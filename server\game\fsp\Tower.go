package fsp

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/behavior"
	"slgsrv/server/game/common/astar"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"

	"github.com/sasha-s/go-deadlock"
)

type Tower struct {
	Fighter
	area Area //当前场景
}

func (this *Tower) Init(uid string, id, lv int32, point *ut.Vec2, area Area, camp int32, ctrl *FSPBattleController) *Tower {
	this.area = area
	this.entity = this.area.GetWorld().NewTempPawn(area.GetIndex(), uid, point, area.GetOwner(), "", id, lv)
	this.camp = camp
	this.ctrl = ctrl
	this.blackboardMutex = new(deadlock.RWMutex)
	this.blackboard = map[int32]map[string]interface{}{}
	this.behavior = behavior.NewBehaviorTree().Load(this.entity.GetBehaviorId(), this)
	this.searchRange = new(astar.SearchRange).Init(ctrl.CheckIsBattleArea)
	this.astar = new(astar.AStarRange).Init(ctrl.CheckHasPassToState)
	return this
}

func (this *Tower) CheckBattleBeginTrigger() {
}

func (this *Tower) MD5() string {
	return ut.Itoa(this.attackIndex) + "_" + this.GetPoint().ID() + "_" + ut.Itoa(this.entity.GetID()) + "_" + ut.Itoa(this.camp) + "_" + ut.Itoa(this.GetAttack())
}

func (this *Tower) Strip() *g.FigherStrip {
	attackTarget := ""
	if this.attackTarget != nil {
		attackTarget = this.attackTarget.GetUID()
	}
	return &g.FigherStrip{
		Uid:          this.GetUID(),
		AttackIndex:  this.attackIndex,
		AttackTarget: attackTarget,
		RoundCount:   this.roundCount,
		AttackCount:  this.attackCount,
		TowerId:      this.entity.GetID(),
		TowerLv:      ut.MaxInt32(1, this.entity.GetLV()),
		Point:        this.entity.GetPoint(),
	}
}

func (this *Tower) ToPb() *pb.FighterInfo {
	attackTarget := ""
	if this.attackTarget != nil {
		attackTarget = this.attackTarget.GetUID()
	}
	return &pb.FighterInfo{
		Uid:          this.GetUID(),
		AttackIndex:  int32(this.attackIndex),
		AttackTarget: attackTarget,
		RoundCount:   int32(this.roundCount),
		AttackCount:  int32(this.attackCount),
		TowerId:      int32(this.entity.GetID()),
		TowerLv:      ut.MaxInt32(1, this.entity.GetLV()),
		Point:        pb.NewVec2(this.entity.GetPoint()),
	}
}

func (this *Tower) ToDB() *g.FigherStrip {
	return this.Strip()
}

func (this *Tower) GetTempAttackIndex() int32 { return 0 } //最后出手

func (this *Tower) IsPawn() bool      { return false }
func (this *Tower) IsBuild() bool     { return false }
func (this *Tower) IsTower() bool     { return true }
func (this *Tower) IsNoncombat() bool { return false }
func (this *Tower) IsFlag() bool      { return false }
func (this *Tower) IsHero() bool      { return false }

func (this *Tower) GetOwner() string { return this.area.GetOwner() }
func (this *Tower) GetMaxHp() int32  { return this.area.GetMaxHP() }

func (this *Tower) InitSkillAttackAnimTime() {

}

func (this *Tower) AmendMaxHp(ignoreBuffType int32) int32 {
	return this.GetMaxHp()
}

func (this *Tower) IsDie() bool {
	return this.area.GetCurHP() <= 0
}

// 刷新等级
func (this *Tower) UpdateLv(lv int32) {
	this.entity.UpdateLv(lv)
}

// 刷新id
func (this *Tower) UpdateAttrByBattle(id int32, lv int32) {
	this.entity.UpdateAttrByBattle(id, lv)
}

// 受击前处理
func (this *Tower) HitPrepDamageHandle(damage, trueDamage int32) (int32, int32) {
	return damage, trueDamage
}

// 受击
func (this *Tower) OnHit(damage int32, baseDamage int32) (int32, int32, int32) {
	return 0, 0, 0
}

// 回血
func (this *Tower) OnHeal(val int32, suckbloodShield bool) int32 {
	return 0
}

// 增加怒气
func (this *Tower) AddAnger(val int32) {

}

// 设置怒气
func (this *Tower) setAnger(val int32) {

}

// 是否可以释放技能
func (this *Tower) IsCanUseSkill() bool {
	return false
}

// 添加buff
func (this *Tower) AddBuff(tp int32, provider string, lv int32) *g.BuffObj {
	return nil
}

func (this *Tower) CheckTriggerBuff(tp int32) *g.BuffObj {
	return nil
}

func (this *Tower) CheckTriggerBuffOr(types ...int32) *g.BuffObj {
	return nil
}

func (this *Tower) IsHasBuff(tp int32) bool {
	return false
}

// 删除一个buff
func (this *Tower) RemoveBuff(tp int32) bool {
	return false
}

// 删除多个buff
func (this *Tower) RemoveMultiBuff(types ...int32) {

}

// 删除指定某人施加的buff
func (this *Tower) RemoveBuffByProvider(tp int32, provider string) bool {
	return false
}

// 删除所有护盾buff
func (this *Tower) RemoveShieldBuffs() {
}
