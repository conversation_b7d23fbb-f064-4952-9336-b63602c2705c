package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
	"time"

	"github.com/huyangv/vmqant/log"
)

const (
	COMPOSE_SKIN_ITEM_TYPE = 4 // 合成皮肤类型
)

func (this *User) ToSkinImtemsPb() []*pb.SkinItem {
	skinItems := []*pb.SkinItem{}
	now := time.Now().UnixMilli()
	for _, v := range this.SkinItemList {
		skin := &pb.SkinItem{
			Id:    v.Id,
			Uid:   v.UID,
			State: v.State,
		}
		if v.State > 0 {
			// 大于0为购买时的时间戳 返回给客户端赠送需持有的剩余时间
			var holdTime int64
			if IsRareSkin(v.Id) {
				holdTime = slg.SEND_RARE_SKIN_GIFT_TIME_LIMIT
			} else {
				holdTime = slg.SEND_NORMAL_SKIN_GIFT_TIME_LIMIT
			}
			skin.State = int64(ut.MaxInt64(0, v.State+holdTime-now))
		} else {
			skin.State = int64(v.State)
		}
		skinItems = append(skinItems, skin)
	}
	return skinItems
}

// 添加皮肤物品
func (this *User) AddSkinItem(uid string, id int32, isGift, isBan bool) *SkinItem {
	if config.GetJsonData("pawnSkin", id) == nil {
		return nil
	}
	if uid == "" {
		uid = GenUid()
	}
	this.SkinItemListLock.Lock()
	skinItem := &SkinItem{
		UID: uid,
		Id:  id,
	}
	if isBan {
		// 被封禁
		skinItem.State = -1
	} else if !isGift {
		// 自己购买的 状态为当前时间戳
		skinItem.State = time.Now().UnixMilli()
	}
	this.SkinItemList = append(this.SkinItemList, skinItem)
	this.SkinItemListLock.Unlock()
	return skinItem
}

// 赠送扣除皮肤物品
func (this *User) ReduceSkinItemByGift(id int32) *SkinItem {
	now := time.Now().UnixMilli()
	this.SkinItemListLock.Lock()
	defer this.SkinItemListLock.Unlock()
	normalSkinCnt := 0 // 对应id普通皮肤的数量
	for i := len(this.SkinItemList) - 1; i >= 0; i-- {
		item := this.SkinItemList[i]
		if item.State < 0 { // 被封禁
			continue
		}
		var holdTime int64
		isRare := IsRareSkin(item.Id)
		if isRare {
			holdTime = slg.SEND_RARE_SKIN_GIFT_TIME_LIMIT
		} else {
			holdTime = slg.SEND_NORMAL_SKIN_GIFT_TIME_LIMIT
			normalSkinCnt++
		}
		// 他人赠送或自己购买的满足持有时间可以赠送
		canSend := (item.State == 0) || (item.State > 0 && now-item.State >= holdTime)
		if !isRare && normalSkinCnt < 2 {
			// 赠送非隐藏款时 必须拥有2个以上才能赠送
			canSend = false
		}
		if item.Id == id && canSend {
			this.SkinItemList = append(this.SkinItemList[:i], this.SkinItemList[i+1:]...)
			return item
		}
	}
	return nil
}

// 合成皮肤物品
func (this *User) ComposeSkinItem(id int32) (err string) {
	skinCfg := config.GetJsonData("pawnSkin", id)
	if skinCfg == nil {
		return ecode.SKIN_NOT_EXIST.String()
	} else if ut.Int32(skinCfg["cond"]) != COMPOSE_SKIN_ITEM_TYPE { // 不是合成皮肤
		return ecode.SKIN_NOT_EXIST.String()
	}
	if this.HasPawnSkin(id) {
		return ecode.PAWN_SKIN_EXIST.String() // 皮肤已存在
	}
	composeStr := ut.String(skinCfg["value"])
	composeCfg := strings.Split(composeStr, ",")
	if len(composeCfg) < 2 { // 配置错误
		log.Error("ComposeSkinItem cfg err id: %v, composeStr: %v", id, composeStr)
		return ecode.SKIN_NOT_EXIST.String()
	}
	// 合成消耗的皮肤id和数量
	costSkinId := ut.Int32(composeCfg[0])
	costCnt := ut.Int(composeCfg[1])

	// 先皮肤物品的锁再皮肤id列表的锁
	this.SkinItemListLock.Lock()
	defer this.SkinItemListLock.Unlock()
	this.PawnSkinLock.Lock()
	defer this.PawnSkinLock.Unlock()
	if array.Has(this.UnlockPawnSkinIds, id) { // 皮肤已存在
		return ecode.PAWN_SKIN_EXIST.String()
	}

	now := time.Now().UnixMilli()
	composeResSkins := []*SkinItem{}
	composeIndexs := []int{}
	totalCount := 0 // 拥有该皮肤总数
	for i, v := range this.SkinItemList {
		if IsRareSkin(v.Id) { // 稀有皮肤不能合成
			continue
		}
		if v.Id == costSkinId {
			if v.State >= 0 && len(composeResSkins) < costCnt {
				composeResSkins = append(composeResSkins, v)
				composeIndexs = append(composeIndexs, i)
			}
			totalCount++
		}
	}
	if len(composeResSkins) < costCnt {
		// 材料皮肤数量不足
		return ecode.RES_NOT_ENOUGH.String()
	}
	// 删除合成材料皮肤
	for i := len(composeIndexs) - 1; i >= 0; i-- {
		index := composeIndexs[i]
		this.SkinItemList = append(this.SkinItemList[:index], this.SkinItemList[index+1:]...)
	}
	// 添加合成的皮肤
	this.UnlockPawnSkinIds = append(this.UnlockPawnSkinIds, id)
	// 添加合成记录
	record := &SkinItemComposeRecord{
		UID:         this.UID + "_" + ut.String(id),
		Id:          id,
		UserId:      this.UID,
		Time:        now,
		ComposeList: composeResSkins,
	}
	go skinItemComposeDb.InsertSkinComposeRecord(record)

	if totalCount == len(composeResSkins) && this.GetPlaySid() != 0 {
		playSid := this.GetPlaySid()
		// 兑换耗尽材料皮肤且正在对局中 则在游戏服卸下使用中的该皮肤
		lobbyModule.InvokeGameRpcNR(playSid, slg.RPC_CLEAR_PWANS_SKIN, this.UID, costSkinId)
	}
	return
}

// 封禁用户皮肤溯源处理
func (this *User) BanUserSkinItemTrack(isBan bool) {
	// 找出所有初始拥有者是该玩家的皮肤溯源信息
	skinItemList, err := skinItemTrackDb.FindSkinItemTrackByOwner(this.UID)
	if err != nil {
		return
	}
	if skinItemList != nil {
		log.Info("BanUserSkinItemTrack uid: %v, count: %v", this.UID, len(skinItemList))
		skinItemUidMap := map[string]map[string]bool{} //待处理的map k=>玩家uid v=>皮肤uid的map
		for _, v := range skinItemList {
			if skinItemUidMap[v.CurOwner] == nil {
				skinItemUidMap[v.CurOwner] = map[string]bool{}
			}
			skinItemUidMap[v.CurOwner][v.UID] = true
		}
		for userId, uidMap := range skinItemUidMap {
			// rpc通知皮肤当前拥有者
			log.Info("BanUserSkinItemTrack otherUid: %v, count: %v", userId, len(uidMap))
			lobbyModule.InvokeUserFuncNR(userId, slg.RPC_SKIN_ITEM_TRACK, this.UID, uidMap, isBan, this.Nickname)
		}
	}
}

// 处理被封禁用户赠送的皮肤
func (this *User) HandleBanSkinItem(banUserId string, uidMap map[string]bool, isBan bool, nickname string) {
	log.Info("HandleBanSkinItem banUserId: %v, uid: %v, isBan: %v, count: %v", banUserId, this.UID, isBan, len(uidMap))
	canUseSkinMap := map[int32]bool{} //可使用的皮肤id map
	banSkinIdMap := map[int32]int32{} //被封禁的皮肤id map
	this.SkinItemListLock.Lock()
	defer this.SkinItemListLock.Unlock()

	banRecord := &SkinItemBanRecord{
		UserId:       this.UID,
		BanUserId:    banUserId,
		Time:         ut.Now(),
		BanList:      []string{},
		SeparateList: []int32{},
	}
	for _, v := range this.SkinItemList {
		if uidMap[v.UID] {
			if isBan {
				// 封禁皮肤
				v.State = -1
				banSkinIdMap[v.Id]++
				banRecord.BanList = append(banRecord.BanList, v.UID)
			} else {
				// 解封
				v.State = 0
			}
		} else if isBan {
			canUseSkinMap[v.Id] = true
		}
	}

	if isBan {
		if len(banSkinIdMap) < len(uidMap) {
			// 已封禁的皮肤数量小于待封禁的皮肤数量 检测已合成的皮肤
			composeResList := []*SkinItem{}
			delList := []int{} // 待删除下标
			this.PawnSkinLock.RLock()
			for i := len(this.UnlockPawnSkinIds) - 1; i >= 0; i-- {
				id := this.UnlockPawnSkinIds[i]
				if !IsComposedSkin(id) {
					continue
				}
				// 获取合成记录
				recordUid := this.UID + "_" + ut.String(id)
				record, err := skinItemComposeDb.FindSkinComposeRecord(recordUid)
				if err != nil {
					log.Error("HandleBanSkinItem FindSkinComposeRecord err: %v, userId: %v, id: %v", err, this.UID, id)
					continue
				}
				needSeparate := false
				for _, v := range record.ComposeList {
					if uidMap[v.UID] {
						// 材料皮肤被封 需要拆分合成的皮肤
						v.State = -1
						banSkinIdMap[v.Id]++
						needSeparate = true
					}
				}
				if !needSeparate {
					canUseSkinMap[id] = true
					continue
				}
				banSkinIdMap[id]++
				// 删除被合成的皮肤的下标
				delList = append(delList, i)
				// 添加材料皮肤
				composeResList = append(composeResList, record.ComposeList...)
				banRecord.SeparateList = append(banRecord.SeparateList, id)
				// 删除合成记录
				go skinItemComposeDb.DelSkinComposeRecord(recordUid)
			}
			this.PawnSkinLock.RUnlock()
			this.PawnSkinLock.Lock()
			for _, delIndex := range delList {
				this.UnlockPawnSkinIds = append(this.UnlockPawnSkinIds[:delIndex], this.UnlockPawnSkinIds[delIndex+1:]...)
			}
			this.PawnSkinLock.Unlock()

			// 添加材料皮肤到背包
			this.SkinItemList = append(this.SkinItemList, composeResList...)
		}
		if len(banSkinIdMap) == 0 {
			return
		}
		if len(banRecord.BanList) > 0 {
			// 添加封禁记录
			go skinItemBanDb.InsertSkinItemBanRecord(banRecord)
		}

		// 皮肤封禁通知邮件 格式: 被封玩家昵称|@skinIds_封禁皮肤idx数量;封禁皮肤idx数量
		banSkinStr := ""
		// 皮肤物品被封且无同id可用皮肤 则在游戏中卸下
		for id, count := range banSkinIdMap {
			if IsComposedSkin(id) {
			} else {
				if len(banSkinStr) > 0 {
					banSkinStr += ";"
				}
				banSkinStr += ut.String(id) + "x" + ut.String(count)
			}
			if canUseSkinMap[id] {
				continue
			}
			// 正在对局中 则在游戏服卸下使用中的该皮肤
			if playSid := this.GetPlaySid(); playSid != 0 {
				lobbyModule.InvokeGameRpcNR(playSid, slg.RPC_CLEAR_PWANS_SKIN, this.UID, id)
			}
		}
		mailStr := nickname + "|@skinIds_" + banSkinStr
		lobbyModule.sendMailItemOne(0, slg.MAIL_BAN_SKIN_ID, "", mailStr, "-1", this.UID, nil)
	}
	this.FlagUpdateDB()
}

// 分解所有合成的皮肤
func (this *User) SeparateSkin() {
	composeResList := []*SkinItem{}
	this.PawnSkinLock.Lock()
	for i := len(this.UnlockPawnSkinIds) - 1; i >= 0; i-- {
		id := this.UnlockPawnSkinIds[i]
		if !IsComposedSkin(id) {
			continue
		}
		// 获取合成记录
		recordUid := this.UID + "_" + ut.String(id)
		record, err := skinItemComposeDb.FindSkinComposeRecord(recordUid)
		if err != nil {
			log.Error("HandleBanSkinItem FindSkinComposeRecord err: %v, userId: %v, id: %v", err, this.UID, id)
			continue
		}
		// 删除被合成的皮肤
		this.UnlockPawnSkinIds = append(this.UnlockPawnSkinIds[:i], this.UnlockPawnSkinIds[i+1:]...)
		// 添加材料皮肤
		composeResList = append(composeResList, record.ComposeList...)
		// 正在对局中 则在游戏服卸下使用中的该皮肤
		if this.GetPlaySid() != 0 {
			lobbyModule.InvokeGameRpcNR(this.GetPlaySid(), slg.RPC_CLEAR_PWANS_SKIN, this.UID, id)
		}
		// 删除合成记录
		go skinItemComposeDb.DelSkinComposeRecord(recordUid)
	}
	this.PawnSkinLock.Unlock()
	// 添加材料皮肤到背包
	this.SkinItemListLock.Lock()
	this.SkinItemList = append(this.SkinItemList, composeResList...)
	this.SkinItemListLock.Unlock()
	this.FlagUpdateDB()
}

// 是否合成的皮肤
func IsComposedSkin(id int32) bool {
	cfg := config.GetJsonData("pawnSkin", id)
	if cfg == nil {
		return false
	}
	return ut.Int32(cfg["cond"]) >= COMPOSE_SKIN_ITEM_TYPE
}
