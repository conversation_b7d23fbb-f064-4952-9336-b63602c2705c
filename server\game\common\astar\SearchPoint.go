package astar

import (
	ut "slgsrv/utils"
)

// 范围搜索
type SearchPoint struct {
	opened       []*ut.Vec2                        //开放列表
	closed       map[string]bool                   //关闭列表
	checkHasPass func(x, y int32, camp int32) bool //检测方法
	hasPointMap  map[string]bool
}

func (this *SearchPoint) Init(checkHasPass func(x, y int32, camp int32) bool) *SearchPoint {
	this.checkHasPass = checkHasPass
	return this
}

// 检查节点
func (this *SearchPoint) findNode(tx, ty int32) (_ *ut.Vec2, ok bool) {
	id := ut.Itoa(tx) + "_" + ut.Itoa(ty)
	if this.closed[id] { //找过了
		return
	} else if this.hasPointMap[id] || !this.checkHasPass(tx, ty, 0) { //有障碍
		this.closed[id] = true
		this.opened = append(this.opened, ut.NewVec2(tx, ty))
		return
	}
	return ut.NewVec2(tx, ty), true
}

func (this *SearchPoint) Search(start *ut.Vec2, points []*ut.Vec2, rang int32) (_ *ut.Vec2, ok bool) {
	this.opened = []*ut.Vec2{start}
	this.closed = map[string]bool{start.ID(): true}
	this.hasPointMap = map[string]bool{}
	for _, p := range points {
		this.hasPointMap[p.ID()] = true
	}
	// 开始搜寻
	for len(this.opened) > 0 {
		p := this.opened[0]
		this.opened = this.opened[1:]
		if rang > 0 && GetPointToPointDis(p, start) > rang {
			continue
		}
		if point, ok := this.findNode(p.X, p.Y-1); ok { //上
			return point, true
		} else if point, ok := this.findNode(p.X+1, p.Y); ok { //右
			return point, true
		} else if point, ok := this.findNode(p.X, p.Y+1); ok { //下
			return point, true
		} else if point, ok := this.findNode(p.X-1, p.Y); ok { //左
			return point, true
		}
	}
	return
}

// 获取两点的距离
func GetPointToPointDis(a *ut.Vec2, b *ut.Vec2) int32 {
	return ut.AbsInt32(a.X-b.X) + ut.AbsInt32(a.Y-b.Y)
}
