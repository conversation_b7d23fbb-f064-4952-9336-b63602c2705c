package lobby

import ut "slgsrv/utils"

const (
	WORKSHOP_ITEM_STATE_AUDIT      = iota //审核中
	WORKSHOP_ITEM_STATE_AUDIT_OK          //审核通过
	WORKSHOP_ITEM_STATE_AUDIT_FAIL        //审核未通过
)

// 创意工坊物品
type WorkshopItem struct {
	Uid        string `bson:"uid"`
	Type       int    `bson:"type"`        // 类型
	UserId     string `bson:"user_id"`     // 玩家uid
	CreateTime int64  `bson:"create_time"` // 创建时间
	State      int    `bson:"state"`       // 状态
	Price      int    `bson:"price"`       // 价格
	SellCount  int    `bson:"sell_count"`  // 销量
	Base64Str  string `bson:"base64_str"`  // 图片base64编码
}

// 添加创意工坊物品
func AddWorkshopItem(userId string, itemType, price int, base64Str string) {
	item := &WorkshopItem{
		Uid:        ut.ID(),
		Type:       itemType,
		UserId:     userId,
		CreateTime: ut.Now(),
		State:      WORKSHOP_ITEM_STATE_AUDIT,
		Price:      price,
		SellCount:  0,
		Base64Str:  base64Str,
	}
	workshopDb.InsertWorkshopItem(item)
}
