package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

type AllianceMap struct {
	deadlock.RWMutex
	Map map[string]*Alliance
}

type AllianceBaseData struct {
	Name string
	Icon int32
}

// 初始化联盟里面玩家的名字信息
func (this *Model) InitAlliancePlayerInfo(alli *Alliance) *Alliance {
	var sumEmbassyLv int32
	// 成员
	for _, m := range alli.Members.List {
		if plr := this.GetTempPlayer(m.Uid); plr != nil {
			m.nickname = plr.Nickname
			m.headIcon = plr.HeadIcon
			m.offlineTime = ut.MaxInt64(1, plr.OfflineTime)
			m.embassyLv = this.GetAreaBuildLvById(plr.MainCityIndex, constant.ALLI_BUILD_ID)
			sumEmbassyLv += m.embassyLv
		}
	}
	alli.SumEmbassyLv = sumEmbassyLv
	alli.CheckUpdateSelectPolicys()
	// 申请
	for _, m := range alli.Applys.List {
		if plr := this.GetTempPlayer(m.Uid); plr != nil {
			m.offlineTime = ut.MaxInt64(1, plr.OfflineTime)
			plr.AddApplyAlli(alli.Uid)
		}
	}
	// 刷新成员上限
	this.updateAlliancePersLimit(alli)
	return alli
}

// 刷新成员上限
func (this *Model) updateAlliancePersLimit(alli *Alliance) {
	plr := this.GetTempPlayer(alli.Creater)
	if plr == nil {
	} else if area := this.GetArea(plr.MainCityIndex); area != nil {
		if build := area.GetAllianceBuild(); build != nil && build.Lv > 0 && len(build.Effects) > 0 {
			alli.MemberPersLimit = int32(build.Effects[0].Value)
			return
		}
	}
	alli.MemberPersLimit = constant.ALLI_INIT_PERS
}

// 玩家升级大使馆
func (this *Model) UpdatePlayerAlliMemberEmbassyLv(alliUid, uid string, lv int32) {
	if alli := this.GetAlliance(alliUid); alli != nil {
		this.UpdateAllianceMemberEmbassyLv(alli, uid, lv)
	}
}
func (this *Model) UpdateAllianceMemberEmbassyLv(alli *Alliance, uid string, lv int32) {
	// 刷新总等级
	alli.Members.RLock()
	var sumEmbassyLv int32
	for _, m := range alli.Members.List {
		if m.Uid == uid {
			m.embassyLv = lv
		}
		sumEmbassyLv += m.embassyLv
	}
	alli.Members.RUnlock()
	alli.SumEmbassyLv = sumEmbassyLv
	alli.CheckUpdateSelectPolicys()
	// 如果是盟主 刷新成员上限
	if alli.Creater == uid {
		this.updateAlliancePersLimit(alli)
	}
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_EMBASSY_LVUP, this.GetPlayerNickname(uid), ut.String(lv))
	// 通知成员 大使馆等级
	msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBEREMBASSYLV_NOTIFY{
		Uid:           uid,
		EmbassyLv:     lv,
		SumEmbassyLv:  alli.SumEmbassyLv,
		PersLimit:     alli.MemberPersLimit,
		SelectPolicys: alli.ToSelectPolicysPb(),
	})
	this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMemberEmbassyLv", msgBytes, alli.GetMemberUids())
	// 通知
	this.PutNotifyQueue(constant.NQ_ALLI_BASE_INFO, &pb.OnUpdateWorldInfoNotify{Data_62: []*pb.AlliBaseInfo{this.ToAlliBaseInfo(alli)}})
}

// 刷新总大使馆等级
func (this *Model) UpdateAllianceSumEmbassyLv(alli *Alliance, isNotify bool) {
	alli.Members.RLock()
	var sumEmbassyLv int32
	for _, m := range alli.Members.List {
		sumEmbassyLv += m.embassyLv
	}
	alli.Members.RUnlock()
	alli.SumEmbassyLv = sumEmbassyLv
	alli.CheckUpdateSelectPolicys()
	// 通知
	if isNotify {
		msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBEREMBASSYLV_NOTIFY{
			SumEmbassyLv:  alli.SumEmbassyLv,
			PersLimit:     alli.MemberPersLimit,
			SelectPolicys: alli.ToSelectPolicysPb(),
		})
		this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMemberEmbassyLv", msgBytes, alli.GetMemberUids())
	}
}

func (this *Model) ToAlliancesDB() map[string]*Alliance {
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	obj := map[string]*Alliance{}
	for k, v := range this.Alliances.Map {
		obj[k] = v
	}
	return obj
}

// 获取联盟列表
func (this *Model) ToAlliances(uid string) []*pb.AllianceShortInfo {
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	// targetIndex := this.GetPlayerMainIndex(uid)
	arr := []*pb.AllianceShortInfo{}
	for _, m := range this.Alliances.Map {
		data := m.ToShortData()
		// data.Dis = int32(this.GetToMapCellDis(this.GetPlayerMainIndex(m.Creater), targetIndex))
		data.IsApply = m.IsInApplyList(uid)
		arr = append(arr, data)
	}
	return arr
}

// 所有联盟基础信息
func (this *Model) ToAllAlliBaseInfoPb() []*pb.AlliBaseInfo {
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	arr := []*pb.AlliBaseInfo{}
	for _, m := range this.Alliances.Map {
		arr = append(arr, this.ToAlliBaseInfo(m))
	}
	return arr
}

func (this *Model) ToAlliBaseInfo(alli *Alliance) *pb.AlliBaseInfo {
	var createrIndex int32 = -1
	if this.GetSeason().GetType() == 3 { //冬季
		createrIndex = this.GetPlayerMainIndex(alli.Creater)
	}
	return alli.ToBaseInfoPb(createrIndex)
}

// 获取联盟
func (this *Model) GetAlliance(uid string) *Alliance {
	if uid == "" {
		return nil
	}
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	return this.Alliances.Map[uid]
}

func (this *Model) GetAllianceNameByUID(uid string) string {
	if alli := this.GetAlliance(uid); alli != nil {
		return alli.Name
	}
	return ""
}

// 检测联盟名字
func (this *Model) CheckHasAllianceName(name string) bool {
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	for _, m := range this.Alliances.Map {
		if m.Name == name {
			return true
		}
	}
	return false
}

// 创建联盟
func (this *Model) CreateAlliance(name string, icon, buildLv int32, plr *player.Model) *Alliance {
	alli := NewAlliance().Init(name, icon, buildLv, plr.Uid)
	this.AddAlliMember(alli, plr.Uid, 0) //默认把创建者 添加进去
	alli.AddAlliLog(constant.ALLI_LOG_TYPE_CREATE, plr.Nickname)
	this.Alliances.Lock()
	this.Alliances.Map[alli.Uid] = alli
	this.Alliances.Unlock()
	return alli
}

// 添加联盟成员
func (this *Model) AddAlliMember(alli *Alliance, uid string, job int32) {
	member := alli.AddMember(uid)
	member.Job = job
	member.JoinTime = time.Now().UnixMilli()
	if alli.NoAvoidWar {
		// 该联盟无免战 则新成员也无免战
		this.SetPlayerNoAvoidWar(uid, true)
	}
	// 临时信息
	if plr := this.GetTempPlayer(uid); plr != nil {
		member.nickname = plr.Nickname
		member.headIcon = plr.HeadIcon
		member.offlineTime = this.GetPlayerOnlineState(plr)
		member.embassyLv = this.GetAreaBuildLvById(plr.MainCityIndex, constant.ALLI_BUILD_ID)
		plr.AllianceUid = alli.Uid
		plr.AlliScore = 0
		plr.IsNeedUpdateDB = true
	} else {
		member.embassyLv = 1
	}
	// 刷新大使馆等级
	this.UpdateAllianceSumEmbassyLv(alli, uid != alli.Creater)
	// 通知世界
	this.PutNotifyQueue(constant.NQ_PLAYER_JOIN_ALLI, &pb.OnUpdateWorldInfoNotify{
		Data_32: &pb.JoinAndExitAlliInfo{
			Uid:          uid,
			AllianceName: alli.Name,
			BaseInfo:     this.ToAlliBaseInfo(alli),
		},
	})
	// 映射到player
	if plr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model); plr != nil {
		plr.AllianceUid = alli.Uid //映射
		if !plr.IsOnline() {
			this.room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
		} else {
			plr.UpdatePolicyEffect(alli.Policys.GetPolicyEffectMapBool())
		}
	}
	if this.CheckPlayerHasAncient(uid) {
		// 该玩家拥有遗迹 检测是否结束和取消免战
		this.AncientAvoidWarCheck()
		this.AncientGameoverCheck()
	}
	if alli.NoAvoidWar {
		// 联盟取消免战也取消该玩家免战
		this.SetAlliNoAvoidWar(uid, true)
	}
	list := this.GetMarchsClone()
	// 向盟友通知该玩家行军
	for _, march := range list {
		if march.Owner == uid {
			this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
		}
	}
}

// 删除成员
func (this *Model) RemoveAlliMember(alli *Alliance, uid string, cause string) {
	log.Info("RemoveAlliMember uid: %v, auid: %v", uid, alli.Uid)
	alli.RemoveMember(uid)
	// 刷新大使馆等级
	this.UpdateAllianceSumEmbassyLv(alli, true)
	var build g.Build = nil
	// 临时信息
	if plr := this.GetTempPlayer(uid); plr != nil {
		plr.AllianceUid = ""
		plr.AlliScore = 0 //清空联盟积分
		plr.IsNeedUpdateDB = true
		// 重置大使馆等级到1级
		build = this.SetBuildLv(plr.MainCityIndex, constant.ALLI_BUILD_ID, 1)
		// 通知世界
		this.PutNotifyQueue(constant.NQ_PLAYER_EXIT_ALLI, &pb.OnUpdateWorldInfoNotify{Data_43: &pb.JoinAndExitAlliInfo{
			Uid:      plr.Uid,
			BaseInfo: this.ToAlliBaseInfo(alli),
		}})
	}
	// 映射到player
	if plr, _ := this.room.GetOnlinePlayerOrDB(uid).(*player.Model); plr != nil {
		plr.AllianceUid = "" //映射
		if !plr.IsOnline() {
			this.room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
		} else {
			plr.UpdatePolicyEffect(alli.Policys.GetPolicyEffectMapBool())
			if build != nil {
				plr.PutNotifyQueue(constant.NQ_BUILD_UP, &pb.OnUpdatePlayerInfoNotify{Data_5: build.ToShortDataPb()}) //通知玩家
			}
		}
	}
	// 检测所有成员的军队
	this.CheckAlliMembersArmys(alli, uid)
	// 删除该玩家取消免战标记
	if alli.NoAvoidWar {
		this.SetPlayerNoAvoidWar(uid, false)
	}
	// 通知其他成员
	msgBytes, _ := pb.ProtoMarshal(&pb.GAME_ONUPDATEALLIMEMBERS_NOTIFY{Members: alli.ToMembersPb(this)})
	this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliMembers", msgBytes, alli.GetMemberUids())

	// 退出所有联盟副频
	updateChannelList := []*AlliChannel{}
	delChannelList := []*AlliChannel{}
	alli.ChatChannels.RWMutex.Lock()
	for i := len(alli.ChatChannels.List) - 1; i >= 0; i-- {
		channel := alli.ChatChannels.List[i]
		if !channel.MemberFilter {
			continue
		}
		if mIndex := array.FindIndex(channel.MemberUids, func(m string) bool { return m == uid }); mIndex >= 0 {
			// 存在则从该频道移除
			channel.MemberUids = append(channel.MemberUids[:mIndex], channel.MemberUids[mIndex+1:]...)
			if len(channel.MemberUids) == 0 {
				// 成员数为0 删除该频道
				delChannelList = append(delChannelList, channel)
				alli.ChatChannels.List = append(alli.ChatChannels.List[:i], alli.ChatChannels.List[i+1:]...)
			} else {
				updateChannelList = append(updateChannelList, channel)
			}
		}
	}
	alli.ChatChannels.RWMutex.Unlock()
	// 通知联盟频道信息
	for _, v := range updateChannelList {
		this.room.PutNotifyAllPlayersQueue("game/OnAllianceCreateChatChannel", pb.Bytes(&pb.GAME_ONALLIANCECREATECHATCHANNEL_NOTIFY{Info: v.ToPb()}), alli.GetMemberUids())
	}
	for _, v := range delChannelList {
		this.room.PutNotifyAllPlayersQueue("game/OnAllianceDelChatChannel", pb.Bytes(&pb.GAME_ONALLIANCEDELCHATCHANNEL_NOTIFY{Uid: v.Uid, Name: v.Name}), alli.GetMemberUids())
	}

	log.Info("RemoveAlliMember finish uid: %v, auid: %v", uid, alli.Uid)
	// 上报
	this.TaTrack(uid, 0, "ta_alliance", map[string]interface{}{
		"alliance_uid":            alli.Uid,
		"alliance_type":           "leave",
		"alliance_member":         alli.GetMemberCount() + 1,
		"alliance_maxmember":      alli.MemberPersLimit,
		"alliance_sum_embassy_lv": alli.SumEmbassyLv,
		"leave_type":              cause,
	})
}

func (this *Model) CheckAlliMembersArmys(alli *Alliance, uid string) {
	// 检测行军
	this.CheckAllMarchTargetCanAddArmy(false, -1)
	if alli == nil {
		return
	}
	uidMap1 := map[string]bool{uid: true}
	uidMap2 := map[string]bool{}
	// 检测盟里剩余的玩家
	for _, m := range alli.Members.List {
		uidMap2[m.Uid] = true
		this.CheckInOtherPlayerArmy(m.Uid, uidMap1)
	}
	// 退出联盟的时候 在其他联盟成员的军队或在自己地方的其他人的军队 都自动返回
	this.CheckInOtherPlayerArmy(uid, uidMap2)
}

// 添加申请者
func (this *Model) AddAlliApply(alli *Alliance, plr *player.Model, desc string) {
	apply := alli.AddApply(plr.Uid)
	apply.Desc = desc
	if tplr := this.GetTempPlayer(plr.Uid); tplr != nil {
		apply.offlineTime = this.GetPlayerOnlineState(tplr)
		tplr.AddApplyAlli(alli.Uid)
	}
}

// 删除申请者
func (this *Model) RemoveAlliApply(alli *Alliance, uid string) {
	alli.RemoveApply(uid)
	if tplr := this.GetTempPlayer(uid); tplr != nil {
		tplr.RemoveApplyAlli(alli.Uid)
	}
}

// 清理玩家的申请列表
func (this *Model) CleanPlayerAlliApply(uid string, notify bool) {
	plr := this.GetTempPlayer(uid)
	if plr == nil {
		return
	}
	for _, m := range plr.ApplyAlliUids {
		if alli := this.GetAlliance(m); alli != nil {
			alli.RemoveApply(uid)
			// 通知
			if notify {
				this.room.PutNotifyAllPlayersQueue("game/OnUpdateAlliApplys", pb.Bytes(&pb.GAME_ONUPDATEALLIAPPLYS_NOTIFY{Applys: alli.ToApplysPb()}), alli.GetMemberUidsByApplyMgr())
			}
		}
	}
	plr.ApplyAlliUids = []string{}
}

// 检测两个玩家是否一个联盟
func (this *Model) CheckIsOneAlliance(a, b string) bool {
	if a == b {
		return true
	}
	ap, bp := this.GetTempPlayer(a), this.GetTempPlayer(b)
	if ap == nil || bp == nil {
		return false
	} else if ap.AllianceUid == "" && bp.AllianceUid == "" {
		return false
	}
	return ap.AllianceUid == bp.AllianceUid
}
func (this *Model) CheckIsOneAllianceByIndex(a, b int32) bool {
	if a == b {
		return true
	}
	aCell, bCell := this.GetCell(a), this.GetCell(b)
	if aCell == nil || bCell == nil {
		return false
	}
	return this.CheckIsOneAlliance(aCell.Owner, bCell.Owner)
}

// 获取玩家所属联盟uid
func (this *Model) GetPlayerAlliUid(uid string) string {
	ply := this.GetTempPlayer(uid)
	if ply == nil {
		return ""
	}
	return ply.AllianceUid
}

// 是否联盟创建者
func (this *Model) IsAlliCreater(uid string) bool {
	plr := this.GetTempPlayer(uid)
	if plr == nil || plr.AllianceUid == "" {
		return false
	}
	alli := this.GetAlliance(plr.AllianceUid)
	return alli != nil && alli.Creater == uid
}

// 删除联盟
func (this *Model) RemoveAlliance(alli *Alliance, mailId int) {
	// 删除玩家的联盟
	indexMap := map[int32]bool{}
	for _, m := range alli.Members.List {
		var build g.Build = nil
		if tplr := this.GetTempPlayer(m.Uid); tplr != nil {
			tplr.AllianceUid = ""
			tplr.AlliScore = 0 //清空联盟分
			tplr.IsNeedUpdateDB = true
			// 重置大使馆等级到1级
			build = this.SetBuildLv(tplr.MainCityIndex, constant.ALLI_BUILD_ID, 1)
		}
		if plr, _ := this.room.GetOnlinePlayerOrDB(m.Uid).(*player.Model); plr != nil {
			plr.AllianceUid = ""
			// 取消大使馆修建
			if build != nil && plr.CancelBTByID(constant.ALLI_BUILD_ID) {
				this.room.SendMailItemOne(slg.MAIL_ALLI_BUILD_CANCEL_ID, "", alli.Name, "-1", plr.Uid, build.GetUpCosts()) //发送补偿费用
				if plr.IsOnline() {
					plr.PutNotifyQueue(constant.NQ_BT_QUEUE, &pb.OnUpdatePlayerInfoNotify{Data_6: plr.ToBTQueuesPb()}) //通知玩家
				}
			}
			if plr.IsOnline() {
				plr.UpdatePolicyEffect(alli.Policys.GetPolicyEffectMapBool())
				if build != nil {
					plr.PutNotifyQueue(constant.NQ_BUILD_UP, &pb.OnUpdatePlayerInfoNotify{Data_5: build.ToShortDataPb()}) //通知玩家
				}
			} else if m.Uid != alli.Creater {
				this.room.UpdatePlayerDB(plr) //如果没在线 就保存到数据库
			}
			if alli.NoAvoidWar {
				this.SetPlayerNoAvoidWar(m.Uid, false)
			}
		}
		dist := this.GetPlayerArmyDist(m.Uid)
		for index := range dist {
			indexMap[index] = true
		}
		// 发送邮件
		this.room.SendMailOne(mailId, "", alli.Name, "-1", m.Uid)
	}
	// 遣返所有在其他玩家的军队
	for index := range indexMap {
		if area := this.GetArea(index); area != nil {
			this.CheckAreaArmyNotBelongHere(area)
		}
	}
	// 删除联盟
	this.Alliances.Lock()
	delete(this.Alliances.Map, alli.Uid)
	this.Alliances.Unlock()
	// 删除数据库的
	this.db.DeleteAlliance(alli.Uid)
	// 通知
	this.PutNotifyQueue(constant.NQ_DISSOLVE_ALLIANCE, &pb.OnUpdateWorldInfoNotify{Data_30: alli.Uid})
	// 上报
	this.TaTrack(alli.Creater, 0, "ta_alliance", map[string]interface{}{
		"alliance_uid":            alli.Uid,
		"alliance_type":           "dissolve",
		"alliance_member":         alli.GetMemberCount(),
		"alliance_maxmember":      alli.MemberPersLimit,
		"alliance_sum_embassy_lv": alli.SumEmbassyLv,
		"dissolve_type":           mailId,
	})
}

// 遣返在其他玩家领地的军队
func (this *Model) CheckInOtherPlayerArmy(uid string, target map[string]bool) {
	dist := this.GetPlayerArmyDist(uid)
	for index := range dist {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		area.FspMutex.RLock()
		isBattle := area.IsBattle()
		canOccupy := true
		if isBattle {
			// 地块在战斗中 判断是否能攻占
			canOccupy = this.CheckCanOccupyCell(index, uid)
		}
		oneAlli := this.CheckIsOneAlliance(area.Owner, uid)
		inTarget := target[area.Owner]
		for i := int32(len(area.Armys.List)) - 1; i >= 0; i-- {
			if army := area.Armys.List[i]; army.Owner == uid && !this.CheckArmyMarchingByUID(army.Uid) && !oneAlli && (inTarget || !canOccupy) {
				// 该地块属于目标玩家 或者在战斗中且不能攻占 则遣返
				if isBattle {
					area.RepatriateArmyByBattle(army) //如果战斗中还要删除战斗里面的
				}
				this.RepatriateArmyToEmptyArea(area, army, i, "CheckInOtherPlayerArmy")
			}
		}
		area.FspMutex.RUnlock()
	}
}

// 获取联盟名map
func (this *Model) GetAlliNameMap() map[string]*AllianceBaseData {
	this.Alliances.RLock()
	defer this.Alliances.RUnlock()
	ret := map[string]*AllianceBaseData{}
	for uid, alli := range this.Alliances.Map {
		ret[uid] = &AllianceBaseData{Name: alli.Name, Icon: alli.Icon}
	}
	return ret
}

// 获取联盟用于排行的领地数
func (this *Model) GetAlliRankLandCount(alli *Alliance) (int32, int32) {
	var count, sum int32
	alli.Members.RLock()
	for _, member := range alli.Members.List {
		landCount := this.GetPlayerLandCount(member.Uid, false)
		sum += landCount
		if member.JoinTime < ut.NowZeroTime() { //检测是否可以算入领地 加入的时间小于当前00点
			count += landCount
		}
	}
	alli.Members.RUnlock()
	return count, sum
}

// 每天结算玩家联盟分
func (this *Model) UpdatePlayerAlliScore(t time.Time) {
	log.Info("UpdatePlayerAlliScore start")
	// 获取日期
	date := t.Format("2006-01-02")
	list := []*TempPlayer{}
	this.allTempPlayers.RLock()
	for _, ply := range this.allTempPlayers.Map {
		list = append(list, ply)
	}
	this.allTempPlayers.RUnlock()
	for _, m := range list {
		this.CalPlayerAlliScore(m.Uid, date, m)
	}
	log.Info("UpdatePlayerAlliScore end")
}

// 计算玩家指定日期联盟分
func (this *Model) CalPlayerAlliScore(uid, date string, ply *TempPlayer) {
	if ply.IsGiveupGame || ply.IsSpectate {
		return //已经放弃的或观战的 跳过
	} else if ply.AllianceUid == "" {
		return //没有加入联盟则不结算
	}
	playerRecord := this.room.GetRecord().GetPlayerBattleScoreRecordsByDate(uid, date)
	if playerRecord != nil {
		// 已结算过
		log.Error("CalPlayerAlliScore repeat uid: %v, date: %v", uid, date)
		return
	}
	record, rst := this.room.GetRecord().GetBattleScoreRecordsByDate(uid, date, ply.AllianceUid)
	if !rst {
		// 未查询到记录
		return
	}
	// addScore := float32(0)
	// // 计算无效战斗数据
	// for k, v := range record.InvalidInfo {
	// 	// 计算无效战斗的额外比例
	// 	if paramEx, ok := bdtype.ALLI_SCORE_INVALID_BATTLE_PARAM[int(k)]; ok {
	// 		if param, ok1 := bdtype.ALLI_SCORE_PARAM[int(k)]; ok1 {
	// 			// 再根据积分参数比例计算分数
	// 			addScore += float32(v) * paramEx * param
	// 		}
	// 	}
	// }
	// // 计算有效战斗数据
	// for k, v := range record.ValidInfo {
	// 	if param, ok := bdtype.ALLI_SCORE_PARAM[int(k)]; ok {
	// 		// 根据积分参数比例计算分数
	// 		addScore += float32(v) * param
	// 	}
	// }
	// // 计算士兵阵亡数据
	// for _, v := range record.DeadInfo {
	// 	costArr := GetPawnResCost(int(v.Id), int(v.Lv))
	// 	res := float32(0)
	// 	for _, obj := range costArr {
	// 		if obj.Type == ctype.EXP_BOOK {
	// 			res += float32(obj.Count) * bdtype.ALLI_SCORE_PAWN_DEAD_COST_BOOK
	// 		} else {
	// 			res += float32(obj.Count) * bdtype.ALLI_SCORE_PAWN_DEAD_COST_RES
	// 		}
	// 	}
	// 	score += res * bdtype.ALLI_SCORE_PAWN_DEAD_PARAM
	// }
	score := ut.Float64(ut.Int(ut.Float64Round(record.Score, 0)))
	// 添加到玩家每日统计
	record.Score = score
	this.room.GetRecord().AddPlayerBattleScoreRecord(record)
	// 更新玩家联盟分
	ply.AlliScore += ut.Int32(score)
	if ply.AlliScore > ply.AlliScoreTop {
		ply.AlliScoreTop = ply.AlliScore
	}
	ply.IsNeedUpdateDB = true
	log.Info("CalPlayerAlliScore uid: %v, socre: %v, newScore: %v", uid, score, ply.AlliScore)
	return
}

// 通知联盟基础信息
func (this *Model) NotifyAllAlliBaseInfo() {
	this.PutNotifyQueue(constant.NQ_ALLI_BASE_INFO, &pb.OnUpdateWorldInfoNotify{Data_62: this.ToAllAlliBaseInfoPb()})
}
