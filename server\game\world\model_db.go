package world

import (
	"time"

	"github.com/huyangv/vmqant/log"
)

// 获取单个地块信息
func (this *Model) GetCellInfoToDB(index int32) map[string]interface{} {
	t := time.Now().UnixMilli()
	defer func() {
		if dt := time.Now().UnixMilli() - t; dt > 500 {
			log.Info("GetCellInfoToDB index: %v, time: %v", index, dt)
		}
	}()
	this.Cells.RLock()
	cell := this.Cells.Map[index]
	this.Cells.RUnlock()
	if cell == nil {
		return map[string]interface{}{}
	} else {
		return cell.ToDB()
	}
}

// 获取战场信息
func (this *Model) GetAreaInfoToDB(index int32) map[string]interface{} {
	t := time.Now().UnixMilli()
	defer func() {
		if dt := time.Now().UnixMilli() - t; dt > 500 {
			log.Info("GetAreaInfoToDB index: %v, time: %v", index, dt)
		}
	}()
	area := this.GetArea(index)
	if area == nil {
		return map[string]interface{}{}
	} else {
		return area.ToDB()
	}
}

func (this *Model) WrapOneData(index int32) WorldTableData {
	return WorldTableData{
		Index:    index,
		LandId:   this.Lands[index],
		CellInfo: this.GetCellInfoToDB(index),
		AreaInfo: this.GetAreaInfoToDB(index),
	}
}

// 标记区域 更新数据库
func (this *Model) TagUpdateDBByIndex(index int32) {
	if this.updateIndexs != nil {
		this.updateIndexs <- index
	}
}

// 战斗结算中标记
func (this *Model) TagBattleSettling() {
	this.battleSettleWg.Add(1)
}

// 战斗结算完成标记
func (this *Model) TagBattleSettleFinish() {
	this.battleSettleWg.Done()
}

// 刷新数据库
func (this *Model) UpdateWorldDB(close bool) {
	sum := len(this.updateIndexs)
	if sum == 0 || this.lastUpdateCellTime > 0 {
		return
	}
	sid := this.room.GetSID()
	this.lastUpdateCellTime = time.Now().UnixMilli()
	datas := []WorldTableData{}
	indexMap := map[int32]bool{}
	for len(this.updateIndexs) > 0 {
		index := <-this.updateIndexs
		if indexMap[index] {
			continue
		}
		indexMap[index] = true
		if len(indexMap) >= 200 {
			log.Warning("UpdateWorldDB len over 200...")
			break
		}
	}
	wrapStart := time.Now().UnixMilli()
	for index := range indexMap {
		datas = append(datas, this.WrapOneData(index))
	}
	if dt := time.Now().UnixMilli() - wrapStart; dt >= 500 {
		log.Info("===== UpdateWorldDB [%v] wrapTime: %vms", sid, dt)
	}
	count, surplus := len(datas), len(this.updateIndexs)
	if close || count > 5 {
		log.Info("===== UpdateWorldDB [%v] sum: %v, count: %v, surplus: %v", sid, sum, count, surplus)
	}
	this.db.UpdateWorldMap(datas)
	dt := time.Now().UnixMilli() - this.lastUpdateCellTime
	this.lastUpdateCellTime = 0
	if close || dt >= 1000 {
		log.Info("===== UpdateWorldDB [%v] sum: %v, count: %v, surplus: %v, sumTime: %vms", sid, sum, count, surplus, dt)
	}
	if close && surplus > 0 {
		this.UpdateWorldDB(close)
	}
}

func (this *Model) UpdateGameDB() {
	dt, sid := time.Now().UnixMilli(), this.room.GetSID()
	log.Info("===== UpdateGameDB [%v] begin.", sid)
	// 保存城市皮肤信息
	this.db.UpdateCitySkin(this.CitySkinMap.Clone())
	// 保存免战信息
	this.db.UpdateAvoidWar(this.AvoidWarAreaData.GetAvoidWarClone())
	this.db.UpdateAvoidWar2(this.AvoidWarAreas2.Clone())
	// 保存修建城市信息
	this.db.UpdateBTCity(this.GetBTCityQueuesClone())
	// 保存招募士兵
	this.db.UpdateDrillPawn(this.ToDrillPawnQueueDB())
	// 保存治疗士兵
	this.db.UpdatePawnCuring(this.ToPawnCuringQueueDB())
	// 保存训练士兵
	this.db.UpdatePawnLving(this.ToPawnLvingQueueDB())
	// 保存行军
	this.db.UpdateMarch(this.GetMarchsClone())
	// 保存运送
	this.db.UpdateTransit(this.GetTransitsClone())
	// 保存屯田信息
	this.db.UpdateCellTonden(this.CellTondenData.GetCellTondenClone())
	// 保存古城捐献信息
	var ancientArr []*AncientInfoTableData
	this.AncientCityMap.ForEach(func(info *AncientInfo, k int32) bool {
		if ancientArr == nil {
			ancientArr = []*AncientInfoTableData{}
		}
		ancientArr = append(ancientArr, info.ToDb())
		return true
	})
	if ancientArr != nil {
		this.db.UpdateAncientInfo(ancientArr)
	}
	log.Info("===== UpdateGameDB [%v] end. %vms", sid, time.Now().UnixMilli()-dt)
}

// 保存联盟
func (this *Model) UpdateAllianceDB() {
	dt, sid := time.Now().UnixMilli(), this.room.GetSID()
	log.Info("===== UpdateAllianceDB [%v] begin.", sid)
	this.db.UpdateAlliance(this.ToAlliancesDB())
	log.Info("===== UpdateAllianceDB [%v] end. %vms", sid, time.Now().UnixMilli()-dt)
}

// 保存聊天
func (this *Model) UpdateChatDB() {
	dt, sid := time.Now().UnixMilli(), this.room.GetSID()
	log.Info("===== UpdateChatDB [%v] begin.", sid)
	this.db.UpdateChat(this.Chats)
	log.Info("===== UpdateChatDB [%v] end. %vms", sid, time.Now().UnixMilli()-dt)
}
