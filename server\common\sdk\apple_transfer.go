package sdk

import (
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	ut "slgsrv/utils"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
)

const (
	// apple旧账号配置
	APPLE_OLD_APPID                  = "twgame.global.acers"
	APPLE_OLD_PRIVATE_KEY_PATH       = "bin/conf/AuthKey_A946JS4A4B.p8"
	APPLE_OLD_STORE_PRIVATE_KEY_PATH = "bin/conf/SubscriptionKey_7B7PK2P98S.p8"
	APPLE_OLD_TEAM_ID                = "ZM6HGAP5T2"
	APPLE_OLD_SHARED_SECRET          = "26e6cdeca31945398ef84fdea746ae8c"
	APPLE_OLD_KID                    = "A946JS4A4B"

	// apple新账号配置
	APPLE_NEW_TEAM_ID          = "YT86BQG28K"
	APPLE_NEW_APPID            = "twgame.global.acers"
	APPLE_NEW_PRIVATE_KEY_PATH = "bin/conf/AuthKey_ML7W92SNU5.p8"
	APPLE_NEW_KID              = "ML7W92SNU5"

	APPLE_GET_TOKEN_URL        = "https://appleid.apple.com/auth/token"
	APPLE_GET_TRANSFER_SUB_URL = "https://appleid.apple.com/auth/usermigrationinfo" // 获取转移id以及根据转移id获取新的openId是一个URL
)

var (
	client   *http.Client
	Token    *AppleTokenRst
	NewToken *AppleTokenRst
)

type TransferInfo struct {
	Uid         string `bson:"uid"`
	OpenId      string `bson:"open_id"`      // 旧的openId
	TransferSub string `bson:"transfer_sub"` // 转移标识符
	Finish      bool   `bson:"finish"`       // 是否完成转移
	Error       string `bson:"error"`        // 报错
}

func InitAppleTransfer() {
	if client == nil {
		client = &http.Client{Timeout: 10 * time.Second}
	}
}

type AppleTokenRst struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
}

// 获取旧团队账号token
func GetOldToken() string {
	if Token == nil {
		Token = getToken(APPLE_OLD_APPID, APPLE_OLD_TEAM_ID, APPLE_OLD_KID, APPLE_OLD_PRIVATE_KEY_PATH)
	} else if Token.ExpiresIn > ut.Now() {
		// 已过期
		Token = getToken(APPLE_OLD_APPID, APPLE_OLD_TEAM_ID, APPLE_OLD_KID, APPLE_OLD_PRIVATE_KEY_PATH)
	}
	if Token == nil {
		return ""
	}
	return Token.AccessToken
}

// 获取新团队账号token
func GetNewToken() string {
	if NewToken == nil {
		NewToken = getToken(APPLE_NEW_APPID, APPLE_NEW_TEAM_ID, APPLE_NEW_KID, APPLE_NEW_PRIVATE_KEY_PATH)
	} else if NewToken.ExpiresIn > ut.Now() {
		// 已过期
		NewToken = getToken(APPLE_NEW_APPID, APPLE_NEW_TEAM_ID, APPLE_NEW_KID, APPLE_NEW_PRIVATE_KEY_PATH)
	}
	if NewToken == nil {
		return ""
	}
	return NewToken.AccessToken
}

// 获取团队账号token
func getToken(appId, teamId, kid, keyPath string) *AppleTokenRst {
	// 生成请求的jwt
	sign := getClientSecret(appId, teamId, kid, keyPath)
	if sign == "" {
		return nil
	}
	now := ut.Now()
	formData := url.Values{}
	formData.Add("grant_type", "client_credentials")
	formData.Add("scope", "user.migration")
	formData.Add("client_id", appId)
	formData.Add("client_secret", sign)
	formStr := formData.Encode()
	req, err := http.NewRequest("POST", APPLE_GET_TOKEN_URL, strings.NewReader(formStr))
	if err != nil {
		log.Error("GetOldToken http req err: %v", err)
		return nil
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("getOldToken err: %v", err)
		return nil
	}
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		log.Error("getToken http resp status err, status: %v, errStr: %v", resp.Status, string(body))
		return nil
	}
	ret := &AppleTokenRst{}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("getToken ret err:%v", err)
		return nil
	}
	ret.ExpiresIn = now + ret.ExpiresIn*1000
	return ret
}

type AppleGetTransferSubRst struct {
	TransferSub string `json:"transfer_sub"`
}

// apple获取转移id
func GetTransferSub(openId string) (sub, e string) {
	// 生成请求的jwt
	sign := getClientSecret(APPLE_OLD_APPID, APPLE_OLD_TEAM_ID, APPLE_OLD_KID, APPLE_OLD_PRIVATE_KEY_PATH)
	if sign == "" {
		e = "GetTransferSub sign err"
		log.Error(e)
		return "", e
	}
	accessToken := GetOldToken()
	if accessToken == "" {
		e = "GetTransferSub sign err"
		log.Error(e)
		return "", e
	}
	formData := url.Values{}
	formData.Add("sub", openId)
	formData.Add("target", APPLE_NEW_TEAM_ID)
	formData.Add("client_id", APPLE_OLD_APPID)
	formData.Add("client_secret", sign)
	formStr := formData.Encode()
	req, err := http.NewRequest("POST", APPLE_GET_TRANSFER_SUB_URL, strings.NewReader(formStr))
	if err != nil {
		log.Error("GetTransferSub http req err: %v", err)
		return "", err.Error()
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Bearer "+accessToken)
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("GetTransferSub err: %v", err)
		return "", err.Error()
	}
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		log.Error("GetTransferSub http resp status err, status: %v, errStr: %v", resp.Status, string(body))
		return "", string(body)
	}
	ret := &AppleGetTransferSubRst{}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("GetTransferSub ret err:%v", err)
		return "", err.Error()
	}
	return ret.TransferSub, ""
}

type AppleGetNewOpenIdRst struct {
	Sub   string `json:"sub"`
	Email string `json:"email"`
}

// 根据转移id获取新的openId
func GetAppleNewOpenId(transferSub string) (newOpenId, e string) {
	// 生成请求的jwt
	sign := getClientSecret(APPLE_NEW_APPID, APPLE_NEW_TEAM_ID, APPLE_NEW_KID, APPLE_NEW_PRIVATE_KEY_PATH)
	if sign == "" {
		e = "GetAppleNewOpenId sign err"
		log.Error(e)
		return "", e
	}
	accessToken := GetNewToken()
	if accessToken == "" {
		e = "GetAppleNewOpenId GetNewToken err"
		log.Error(e)
		return "", e
	}
	formData := url.Values{}
	formData.Add("transfer_sub", transferSub)
	formData.Add("client_id", APPLE_NEW_APPID)
	formData.Add("client_secret", sign)
	formStr := formData.Encode()
	req, err := http.NewRequest("POST", APPLE_GET_TRANSFER_SUB_URL, strings.NewReader(formStr))
	if err != nil {
		log.Error("GetAppleNewOpenId http req err: %v", err)
		return "", err.Error()
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Bearer "+accessToken)
	resp, err := client.Do(req)
	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()
	if err != nil {
		log.Error("GetAppleNewOpenId err: %v", err)
		return "", err.Error()
	}
	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		log.Error("GetAppleNewOpenId http resp status err, status: %v, errStr: %v", resp.Status, string(body))
		return "", string(body)
	}
	ret := &AppleGetNewOpenIdRst{}
	err = json.Unmarshal(body, &ret)
	if err != nil {
		log.Error("GetAppleNewOpenId ret err:%v", err)
		return "", err.Error()
	}
	return ret.Sub, ""
}

func getClientSecret(appId, teamId, kid, keyPath string) string {
	now := ut.Now() / 1000
	// 生成请求的jwt
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"iss": teamId,
		"iat": now,
		"exp": now + 60*60*24*30,
		"aud": "https://appleid.apple.com",
		"sub": appId,
	})
	jwtToken.Header["alg"] = "ES256"
	jwtToken.Header["kid"] = kid
	err := getAppleLoginPrivateKey(keyPath)
	if err != nil {
		log.Error("getApplePrivateKey err: %v", err)
		return ""
	}
	sign, err := jwtToken.SignedString(appleLoginPrivateKey)
	if err != nil {
		log.Error("AppleLoginCheck sign err: %v", err)
		return ""
	}
	return sign
}
