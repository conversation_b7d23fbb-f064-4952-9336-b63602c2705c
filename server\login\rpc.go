package login

import (
	slg "slgsrv/server/common"
)

func (this *Login) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_LOBBY_QUEUE, this.lobbyQueue)
}

// 大厅服排队
func (this *Login) lobbyQueue(sessionID, serverID string) (result interface{}, err string) {
	// unit := &LobbyQueueUnit{
	// 	SessionID: sessionID,
	// 	ServerID:  serverID,
	// }
	// rds.RdsListRPush(rds.RDS_LOBBY_QUEUE, unit)
	// lobbyQueue.Lock()
	// lobbyQueue.tailNum++
	// userCurNum := lobbyQueue.tailNum
	// lobbyQueue.Unlock()
	// result = map[string]interface{}{
	// 	"lobbyQueueNum": userCurNum,
	// }
	return
}
