package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/eeffect"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// 装备信息
type EquipInfo struct {
	json map[string]interface{}

	Attrs          [][]int32         `json:"attrs" bson:"attrs"`
	LastAttrs      [][]int32         `json:"lastAttrs" bson:"last_attrs"` //上一次重铸属性
	effects        []*EquipEffectObj //效果
	skillIntensify []int32           //技能强化 0.技能id 1.技能等级

	ID              int32 `json:"id" bson:"id"`
	RecastCount     int32 `json:"recastCount" bson:"recast_count"` //重铸次数
	attack          int32
	hp              int32
	exclusivePawnId int32 //专属士兵

	NextForgeFree bool `json:"nextForgeFree" bson:"next_forge_free"` //下次是否免费重铸
}

func NewEquip(id int32) *EquipInfo {
	equip := &EquipInfo{
		Attrs:       [][]int32{},
		LastAttrs:   [][]int32{},
		RecastCount: 0,
	}
	equip.SetID(id)
	return equip
}

func NewEquipByJson(data map[string]interface{}) *EquipInfo {
	equip := NewEquip(ut.Int32(data["id"]))
	if attrs, ok := data["attrs"].(primitive.A); ok {
		for _, m := range attrs {
			if arr, ok := m.(primitive.A); ok {
				attr := []int32{}
				for _, val := range arr {
					attr = append(attr, ut.Int32(val))
				}
				equip.Attrs = append(equip.Attrs, attr)
			}
		}
	}
	equip.UpdateAttr()
	return equip
}

func NewEquipByPb(data *pb.EquipInfo) *EquipInfo {
	equip := NewEquip(ut.Int32(data.Id))
	if data.Attrs != nil {
		for _, m := range data.Attrs {
			attr := []int32{}
			for _, val := range m.Attr {
				attr = append(attr, ut.Int32(val))
			}
			equip.Attrs = append(equip.Attrs, attr)
		}
	}
	equip.UpdateAttr()
	return equip
}

type EquipStrip struct {
	Id    int32     `bson:"id"`
	Attrs [][]int32 `bson:"attrs"`
}

func (this *EquipInfo) ToStrip() *EquipStrip {
	return &EquipStrip{
		Id:    this.ID,
		Attrs: CloneAttrs(this.Attrs),
	}
}

func (this *EquipInfo) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"id":    this.ID,
		"attrs": CloneAttrs(this.Attrs),
	}
}

func (this *EquipInfo) ToPb() *pb.EquipInfo {
	return &pb.EquipInfo{
		Id:            int32(this.ID),
		RecastCount:   int32(this.RecastCount),
		NextForgeFree: this.NextForgeFree,
		Attrs:         CloneEffectAttrsToPb(this.Attrs),
		LastAttrs:     CloneEffectAttrsToPb(this.LastAttrs),
	}
}

func (this *EquipInfo) ToBasePb() *pb.EquipInfo {
	return &pb.EquipInfo{
		Id:    int32(this.ID),
		Attrs: CloneEffectAttrsToPb(this.Attrs),
	}
}

func (this *EquipInfo) GetJson() map[string]interface{}    { return this.json }
func (this *EquipInfo) GetAttack() int32                   { return this.attack }
func (this *EquipInfo) GetHP() int32                       { return this.hp }
func (this *EquipInfo) GetEquipEffects() []*EquipEffectObj { return this.effects }
func (this *EquipInfo) GetSkillIntensify() []int32         { return this.skillIntensify }
func (this *EquipInfo) GetExclusivePawnId() int32          { return this.exclusivePawnId }
func (this *EquipInfo) CloneAttrs() [][]int32              { return CloneAttrs(this.Attrs) }
func (this *EquipInfo) IsSmelt() bool                      { return this.ID > 10000 } //是否融炼装备

func (this *EquipInfo) GetEquipEffectByType(tp int32) *EquipEffectObj {
	if effect := array.Find(this.effects, func(m *EquipEffectObj) bool { return m.Type == tp }); effect != nil {
		return effect
	}
	return nil
}

// 设置id
func (this *EquipInfo) SetID(id int32) {
	this.ID = id
	if id > 10000 { //表示融炼装备
		id /= 10000
	}
	this.json = config.GetJsonData("equipBase", id)
	if skill_intensify := this.json["skill_intensify"]; skill_intensify != nil {
		this.skillIntensify = ut.StringToInt32s(ut.String(skill_intensify), ",")
	} else {
		this.skillIntensify = nil
	}
	this.exclusivePawnId = ut.Int32(this.json["exclusive_pawn"])
}

// 设置属性
func (this *EquipInfo) SetAttr(attrs [][]int32) {
	this.Attrs = CloneAttrs(attrs)
	this.UpdateAttr()
}

// 刷新属性
func (this *EquipInfo) UpdateAttr() {
	this.attack = 0
	this.hp = 0
	this.effects = []*EquipEffectObj{}
	for _, arr := range this.Attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if fieldType == 0 || fieldType == 1 { //属性
			if tp == 1 {
				this.hp += value
			} else if tp == 2 {
				this.attack += value
			}
		} else if fieldType == 2 { //效果
			if len(arr) == 4 {
				this.effects = append(this.effects, NewEquipEffectObj(tp, float64(value), arr[3]))
			} else {
				this.effects = append(this.effects, NewEquipEffectObj(tp, float64(value), 0))
			}
		}
	}
	if this.effects == nil || len(this.effects) == 0 {
		return
	}
	// 效果排序
	sort.Slice(this.effects, func(i, j int) bool {
		as, bs := 0, 0
		if aj := config.GetJsonData("equipEffect", this.effects[i].Type); aj != nil {
			as = ut.Int(aj["sort"])
		}
		if bj := config.GetJsonData("equipEffect", this.effects[j].Type); bj != nil {
			bs = ut.Int(bj["sort"])
		}
		return as < bs
	})
}

// 随机属性
// attrs = [{a,b,c}]
// a = 0.主属性 1.副属性 2.效果 3.效果概率
// b = (a == 0 || a == 1)
//
//			1.血量 2.攻击
//		   (a == 2)
//	     效果类型
func (this *EquipInfo) RandomAttr(lockEffect int32) {
	// 先记录上一次的属性
	this.LastAttrs = CloneAttrs(this.Attrs)
	this.Attrs = [][]int32{}
	//
	var lockAttr []int32 = nil
	if lockEffect > 0 && this.exclusivePawnId > 0 {
		lockAttr = array.Find(this.LastAttrs, func(m []int32) bool { return len(m) >= 2 && m[0] == 2 && m[1] == lockEffect })
	}
	// 主属性 0
	if hp := ut.String(this.json["hp"]); hp != "" { // 1
		arr := ut.StringToInts(hp, ",")
		this.Attrs = append(this.Attrs, []int32{0, 1, int32(ut.Random(arr[0], arr[1]))})
	}
	if attack := ut.String(this.json["attack"]); attack != "" { // 2
		arr := ut.StringToInts(attack, ",")
		this.Attrs = append(this.Attrs, []int32{0, 2, int32(ut.Random(arr[0], arr[1]))})
	}
	// 副属性 1 只有40%的概率获得副属性
	if attach := ut.String(this.json["attach"]); attach != "" && ut.Chance(40) {
		arr := ut.StringToInt32s(attach, ",")
		this.Attrs = append(this.Attrs, []int32{1, arr[0], ut.RandomInt32(arr[1], arr[2])})
	}
	// 效果
	effectIds := ut.StringToInt32s(ut.String(this.json["effect"]), "|")
	effectIndexMap := map[int32]int32{}
	for i, id := range effectIds {
		effectIndexMap[id] = int32(i) + 1
	}
	cnts := ut.StringToInts(ut.String(this.json["effect_count"]), ",")
	cnt := ut.Random(cnts[0], cnts[1])
	if lockAttr != nil && effectIndexMap[lockAttr[1]] > 0 {
		cnt = ut.Max(cnt-1, 1)                           //如果有锁定效果 这里就要少一个
		effectIds = array.Remove(effectIds, lockAttr[1]) //删除
	} else {
		lockAttr = nil
	}
	ids := []int32{}
	for i := 0; i < cnt && i < len(effectIds); i++ {
		index := ut.Random(0, len(effectIds)-1)
		ids = append(ids, effectIds[index])
		effectIds = append(effectIds[:index], effectIds[index+1:]...)
	}
	effects := [][]int32{}
	for _, id := range ids {
		if json := config.GetJsonData("equipEffect", id); json != nil {
			v := []int32{2, id}
			if arr := ut.StringToInt32s(ut.String(json["value"]), ","); len(arr) == 2 {
				v = append(v, ut.RandomInt32(arr[0], arr[1]))
			} else {
				v = append(v, 0) //这里就算没有 也要push一个0因为 后面可能有概率
			}
			if arr := ut.StringToInt32s(ut.String(json["odds"]), ","); len(arr) == 2 { //效果概率
				v = append(v, ut.RandomInt32(arr[0], arr[1]))
			}
			effects = append(effects, v)
		}
	}
	if lockAttr != nil {
		effects = append(effects, lockAttr)
	}
	sort.Slice(effects, func(i, j int) bool { return effectIndexMap[effects[i][1]] < effectIndexMap[effects[j][1]] }) //这里要排个序 必须根据配置顺序来
	// 如果是时光旗 只要对应的主属性
	if this.ID == 6025 && len(effects) > 0 && len(effects[0]) > 0 {
		if effects[0][1] == eeffect.TODAY_ADD_HP { //加攻生命 删除攻击
			this.Attrs = array.RemoveItem(this.Attrs, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 2 })
		} else if effects[0][1] == eeffect.TODAY_ADD_ATTACK { //加攻击力 删除生命
			this.Attrs = array.RemoveItem(this.Attrs, func(arr []int32) bool { return arr[0] == 0 && arr[1] == 1 })
		}
	}
	this.Attrs = append(this.Attrs, effects...)
	this.UpdateAttr()
}

// 还原属性
func (this *EquipInfo) RestoreAttr() {
	if len(this.LastAttrs) == 0 {
		return
	}
	this.Attrs = CloneAttrs(this.LastAttrs)
	this.LastAttrs = [][]int32{}
	this.UpdateAttr()
}

// 检测是否专属士兵的装备
func (this *EquipInfo) CheckExclusivePawn(id int32) bool {
	return this.exclusivePawnId == 0 || this.exclusivePawnId == id
}

// 是否专属装备
func (this EquipInfo) IsExclusive() bool {
	return this.exclusivePawnId != 0
}

// 是否满属性
func (this *EquipInfo) IsMaxAttr() bool {
	if len(this.Attrs) < 2 || this.IsSmelt() || this.ID == 6025 { //时光旗排除
		return false
	}
	attach := ut.String(this.json["attach"])
	hasAttach := false
	for _, arr := range this.Attrs {
		fieldType, tp, value := arr[0], arr[1], arr[2]
		if fieldType == 0 {
			if tp == 1 {
				if hp := ut.String(this.json["hp"]); hp != "" && ut.StringToInt32s(hp, ",")[1] != value { // 1
					return false
				}
			} else if tp == 2 {
				if attack := ut.String(this.json["attack"]); attack != "" && ut.StringToInt32s(attack, ",")[1] != value { // 1
					return false
				}
			}
		} else if fieldType == 1 {
			if attach != "" && ut.StringToInt32s(attach, ",")[2] != value {
				return false
			}
			hasAttach = true
		} else if fieldType == 2 {
			if json := config.GetJsonData("equipEffect", tp); json != nil {
				if arr1 := ut.StringToInt32s(ut.String(json["value"]), ","); len(arr1) == 2 {
					if arr1[1] != value {
						return false
					}
				}
				if arr1 := ut.StringToInt32s(ut.String(json["odds"]), ","); len(arr1) == 2 { //效果概率
					if len(arr) != 4 || arr[3] != arr1[1] {
						return false
					}
				}
			}
		}
	}
	if attach != "" && !hasAttach {
		return false
	}
	return true
}

// 是否有一样的属性效果
func (this *EquipInfo) HasAttrEffect(attrs [][]int32) bool {
	for _, arr := range this.Attrs {
		if arr[0] != 2 {
			continue
		} else if array.Some(attrs, func(m []int32) bool { return m[0] == 2 && m[1] == arr[1] }) {
			return true
		}
	}
	return false
}
