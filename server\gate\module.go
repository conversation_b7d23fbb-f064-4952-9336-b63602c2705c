package gate

import (
	"context"
	"fmt"
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	rds "slgsrv/utils/redis"
	"strconv"
	"strings"
	"time"

	"github.com/sasha-s/go-deadlock"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	basegate "github.com/huyangv/vmqant/gate/base"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	argsutil "github.com/huyangv/vmqant/rpc/util"
	"github.com/huyangv/vmqant/selector"
	"github.com/pkg/errors"
)

var Module = func() module.Module {
	return new(Gate)
}

type Gate struct {
	basegate.Gate //继承
}

func (this *Gate) GetType() string {
	return "gate"
}

func (this *Gate) Version() string {
	return "1.0.0"
}

func (this *Gate) OnInit(app module.App, settings *conf.ModuleSettings) {
	//注意这里一定要用 gate.Gate 而不是 module.BaseModule
	this.Gate.OnInit(this, app, settings,
		gate.Heartbeat(time.Second*30),
		//gate.BufSize(2048*20), //网络读写缓存大小
		gate.SetSessionLearner(this), //设置监听是否链接成功和断开链接
		gate.SetStorageHandler(this), //设置持久化处理器
		gate.SetRouteHandler(this),
	)
	this.InitRpc()
}

// 当连接建立 并且MQTT协议握手成功
func (this *Gate) Connect(session gate.Session) {
	log.Info(session.GetIP() + " -> 建立链接(" + session.GetNetwork() + ")" + "sessionId: " + session.GetSessionID())
}

// 当连接关闭	或者客户端主动发送MQTT DisConnect命令 ,这个函数中Session无法再继续后续的设置操作，只能读取部分配置内容了
func (this *Gate) DisConnect(session gate.Session) {
	uid := session.GetUserID()
	log.Info(session.GetIP() + " -> 断开链接, isGuest:" + strconv.FormatBool(uid == ""))
	if uid == "" {
		return //如果是游客的话 直接返回
	}
	// 通知游戏服
	sid := session.Get("sid")
	if sid == "" || sid == "0" {
	} else if _, err := this.InvokeWithCleanup("game@game"+sid, slg.RPC_LEAVE, session); err != "" {
		log.Error("game OnLeave Error: %v", err)
	}
	// 通知大厅服
	lid := session.Get("lid")
	if lid == "" || lid == "0" {
	} else if _, err := this.InvokeWithCleanup("lobby@lobby"+lid, slg.RPC_LEAVE, uid, session.GetSessionID()); err != "" {
		log.Error("login OnLeave Error: %v", err)
	}
}

func (gate *Gate) Storage(session gate.Session) (err error) {
	//log.Info("需要处理对Session的持久化")
	return nil
}

func (gate *Gate) Delete(session gate.Session) (err error) {
	//log.Info("需要删除Session持久化数据")
	return nil
}

func (gate *Gate) Query(Userid string) ([]byte, error) {
	//log.Info("查询Session持久化数据")
	return nil, fmt.Errorf("no redis")
}

func (this *Gate) Heartbeat(session gate.Session) {
	//log.Info("用户在线的心跳包", session.GetIP())
}

func (this *Gate) OnRoute(session gate.Session, topic string, msg []byte) (bool, interface{}, error) {
	topics := strings.Split(topic, "/")
	var msgid string
	if len(topics) < 2 {
		errorstr := "Topic must be [moduleType@moduleID]/[handler]|[moduleType@moduleID]/[handler]/[msgid]"
		log.Error(errorstr)
		return true, nil, errors.Errorf(errorstr)
	} else if len(topics) == 3 {
		msgid = topics[2]
	}
	needreturn := msgid != ""
	startsWith := strings.HasPrefix(topics[1], "HD_")
	if !startsWith {
		return needreturn, nil, errors.Errorf(fmt.Sprintf("Method(%s) must begin with 'HD_'", topics[1]))
	}
	// log.Info("OnRoute session=" + session.GetUserID())
	// log.Info("OnRoute moduleType=" + moduleType + ", func=" + topics[1])
	moduleType := topics[0]
	var serverSession module.ServerSession
	var err error
	if moduleType == slg.MACH_SERVER_TYPE_GAME {
		sid := session.Get("sid")
		serverSession, err = this.GetRouteServer(moduleType,
			selector.WithStrategy(func(services []*registry.Service) selector.Next {
				// log.Info("services[0] $v", services[0].Nodes[0])
				var mtx deadlock.Mutex
				return func() (*registry.Node, error) {
					mtx.Lock()
					defer mtx.Unlock()
					for _, service := range services {
						for _, node := range service.Nodes {
							if node.Metadata["sid"] == sid {
								return node, nil
							}
						}
					}
					return nil, fmt.Errorf("no node")
				}
			}))
	} else if moduleType == slg.MACH_SERVER_TYPE_LOBBY {
		lid := session.Get("lid")
		if topics[1] == "HD_TryLogin" {
			c2s := &pb.LOBBY_HD_TRYLOGIN_C2S{}
			err := pb.ProtoUnMarshal(msg, c2s)
			if err != nil {
				log.Error("lobby assign err: %v", err)
				return true, nil, errors.Errorf(ecode.DB_ERROR.String())
			}
			str := ut.AESDecrypt(c2s.GetAccountToken())
			uid := str[0:8]
			lid = rds.MallocUserLid(uid, true)
			if lid == "" {
				// 大厅服已到负载上限 进入排队队列
				// queueRst, err := ut.RpcInterfaceMap(this.Invoke(slg.MACH_SERVER_TYPE_LOGIN, slg.RPC_LOBBY_QUEUE, ut.Bytes(session.GetSessionID()), ut.Bytes(this.GetServerID())))
				// if err != "" {
				// 	return true, nil, errors.Errorf(ecode.TOKEN_INVALID.String())
				// }
				// setLobbyQueueNum(session, ut.Int(queueRst["lobbyQueueNum"]))
				log.Info("lobby malloc lid nil, uid: %v", uid)
				return true, nil, errors.Errorf(ecode.LOBBY_QUEUE_UP.String())
			}
			session.Set("lid", lid)
		} else if lid == "" {
			errorstr := "session not bind"
			log.Error(errorstr)
			return true, nil, errors.Errorf(errorstr)
		}

		serverSession, err = this.GetRouteServer(moduleType,
			selector.WithStrategy(func(services []*registry.Service) selector.Next {
				var mtx deadlock.Mutex
				return func() (*registry.Node, error) {
					mtx.Lock()
					defer mtx.Unlock()
					for _, service := range services {
						for _, node := range service.Nodes {
							if node.Metadata["lid"] == lid {
								return node, nil
							}
						}
					}
					return nil, fmt.Errorf("no node")
				}
			}))
	} else {
		serverSession, err = this.GetRouteServer(moduleType)
	}
	if err != nil || serverSession == nil {
		return needreturn, nil, errors.Errorf(fmt.Sprintf("Service(type:%s) not found", topics[0]))
	}
	// // 发送请求统计到工具服
	// uid := session.GetUserID()
	// this.InvokeNR("http", "OnReqLog", ut.Bytes(uid), ut.Bytes(topics[1]), ut.Bytes(ut.Now()))
	var ArgsType = make([]string, 2)
	var args = make([][]byte, 2)
	ArgsType[0] = gate.RPCParamSessionType
	args[0], err = session.Serializable()
	ArgsType[1] = argsutil.BYTES
	args[1] = msg
	sessionClone := session.Clone()
	sessionClone.SetTopic(topic)

	ArgsType[0] = gate.RPCParamSessionType
	b, err := session.Serializable()
	if err != nil {
		return needreturn, nil, err
	}
	args[0] = b
	ctx, cancel := context.WithTimeout(context.TODO(), this.App.Options().RPCExpired)
	defer cancel()
	if needreturn {
		result, e := serverSession.CallArgs(ctx, topics[1], ArgsType, args)
		return true, result, errors.New(e)
	}
	e := serverSession.CallNRArgs(topics[1], ArgsType, args)
	if e != nil {
		log.Warning("Gate rpc", e)
	}
	return false, nil, e
}

// 获取当前用户在大厅服排队序号
func getLobbyQueueNum(session gate.Session) int {
	return ut.Int(session.Get("lobbyQueueNum"))
}

// 设置当前用户在大厅服排队序号
func setLobbyQueueNum(session gate.Session, num int) {
	session.Set("lobbyQueueNum", ut.String(num))
}
