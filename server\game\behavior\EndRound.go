package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/hero"
	ut "slgsrv/utils"
)

// 结束回合
type EndRound struct {
	BaseAction

	initDelay     int32
	isChangeState bool
}

func (this *EndRound) OnInit(conf map[string]interface{}) {
	this.initDelay = ut.Int32(conf["parameters"])
}

func (this *EndRound) OnOpen() {
	this.isChangeState = false
	this.SetBlackboardData("delay", this.initDelay+ut.Int32(this.GetTreeBlackboardData("addRoundEndDelayTime")))
}

func (this *EndRound) OnTick(dt int32) Status {
	if ut.Int(this.GetTreeBlackboardData("batterCount")) > 0 {
		return SUCCESS //继续回合
	}
	currTime, delay := ut.Int32(this.GetBlackboardData("currTime")), ut.Int32(this.GetBlackboardData("delay"))
	this.SetBlackboardData("currTime", currTime+dt)
	actionTime := ut.Int32(this.GetTreeBlackboardData("addRoundEndActionHitTime"))
	// 动作时间
	if actionTime > 0 {
		ctrl := this.GetCtrl()
		heroSkill, attackTarget := this.target.GetPortrayalSkill(), this.target.GetAttackTarget()
		// 张辽
		if heroSkill == nil {
		} else if heroSkill.Id == hero.ZHANG_LIAO && attackTarget != nil {
			if currTime < actionTime {
				return RUNNING
			} else if !ut.Bool(this.GetBlackboardData("isAction")) {
				this.SetBlackboardData("isAction", true)
				// 对范围敌人造成伤害
				arr := attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), heroSkill.Target, heroSkill.GetParamsInt(), "")
				for _, m := range arr {
					ctrl.OnHitBaseTrueDamage(this.target, m, map[string]interface{}{"attackAmend": heroSkill.GetValue() * 0.01})
				}
				// 添加减伤buff
				this.target.AddBuff(bufftype.ASSAULT, this.target.GetUID(), 1)
			}
		}
	}
	// 结束时间
	if currTime >= delay || !this.isActioning(delay > this.initDelay) {
		// 如果还没有攻击过 清理当前的目标 下次重新选
		if !ut.Bool(this.GetTreeBlackboardData("isAttack")) {
			this.target.ChangeAttackTarget(nil)
			// 夏侯渊 清理奔袭buff
			if this.target.CheckPortrayalSkill(hero.XIA_HOUYUAN) != nil {
				this.target.RemoveBuff(bufftype.LONG_RANGE_RAID)
			}
		}
		// 标记结束回合
		this.SetTreeBlackboardData("isRoundEnd", true)
		return SUCCESS
	} else if !this.isChangeState {
		this.isChangeState = true
		this.target.ChangeState(constant.PAWN_STATE_STAND)
	}
	return RUNNING
}

// 是否行动过
func (this *EndRound) isActioning(hasAddRoundEndDelayTime bool) bool {
	return hasAddRoundEndDelayTime || ut.Bool(this.GetTreeBlackboardData("isDeductHpAction")) || ut.Bool(this.GetTreeBlackboardData("isBloodAction")) || ut.Bool(this.GetTreeBlackboardData("isMove")) || ut.Bool(this.GetTreeBlackboardData("isAttack"))
}
