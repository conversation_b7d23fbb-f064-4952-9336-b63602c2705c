[{"id": 10101, "type": 1, "use_type": 0, "target": 4, "value": 1.4, "params": 0.2, "anim_time": "", "name": "pawnSkillText.name_10104", "desc": "pawnSkillText.desc_101", "desc_params": "ui.pawn_type_4|40%|20%"}, {"id": 10201, "type": 2, "use_type": 0, "target": 4, "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_10104", "desc": "pawnSkillText.desc_102", "desc_params": "ui.pawn_type_4|40%"}, {"id": 10102, "type": 1, "use_type": 0, "target": 3, "value": 1.4, "params": 0.2, "anim_time": "", "name": "pawnSkillText.name_10103", "desc": "pawnSkillText.desc_101", "desc_params": "ui.pawn_type_3|40%|20%"}, {"id": 10202, "type": 2, "use_type": 0, "target": 3, "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_10103", "desc": "pawnSkillText.desc_102", "desc_params": "ui.pawn_type_3|40%"}, {"id": 10103, "type": 1, "use_type": 0, "target": 1, "value": 1.4, "params": 0.2, "anim_time": "", "name": "pawnSkillText.name_10101", "desc": "pawnSkillText.desc_101", "desc_params": "ui.pawn_type_1|40%|20%"}, {"id": 10203, "type": 2, "use_type": 0, "target": 1, "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_10101", "desc": "pawnSkillText.desc_102", "desc_params": "ui.pawn_type_1|40%"}, {"id": 10104, "type": 1, "use_type": 0, "target": 2, "value": 1.4, "params": 0.2, "anim_time": "", "name": "pawnSkillText.name_10102", "desc": "pawnSkillText.desc_101", "desc_params": "ui.pawn_type_2|40%|20%"}, {"id": 10204, "type": 2, "use_type": 0, "target": 2, "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_10102", "desc": "pawnSkillText.desc_102", "desc_params": "ui.pawn_type_2|40%"}, {"id": 10105, "type": 1, "use_type": 0, "target": 100, "value": 3, "params": "", "anim_time": "", "name": "pawnSkillText.name_101100", "desc": "pawnSkillText.desc_104", "desc_params": "ui.pawn_type_100|2"}, {"id": 10106, "type": 1, "use_type": 0, "target": 2, "value": 1.2, "params": 0.1, "anim_time": "", "name": "pawnSkillText.name_10102", "desc": "pawnSkillText.desc_101", "desc_params": "ui.pawn_type_2|20%|10%"}, {"id": 10206, "type": 2, "use_type": 0, "target": 2, "value": 0.8, "params": "", "anim_time": "", "name": "pawnSkillText.name_10102", "desc": "pawnSkillText.desc_102", "desc_params": "ui.pawn_type_2|20%"}, {"id": 10301, "type": 103, "use_type": 0, "target": "", "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_103", "desc": "pawnSkillText.desc_103", "desc_params": ""}, {"id": 10401, "type": 104, "use_type": 0, "target": 6, "value": 1.3, "params": "", "anim_time": "", "name": "pawnSkillText.name_104", "desc": "pawnSkillText.desc_104", "desc_params": "ui.pawn_type_6|30%"}, {"id": 10501, "type": 105, "use_type": 0, "target": "", "value": 0.2, "params": "", "anim_time": "", "name": "pawnSkillText.name_105", "desc": "pawnSkillText.desc_105", "desc_params": ""}, {"id": 10601, "type": 106, "use_type": 0, "target": "", "value": 0.4, "params": "", "anim_time": "", "name": "pawnSkillText.name_106", "desc": "pawnSkillText.desc_106", "desc_params": ""}, {"id": 10701, "type": 107, "use_type": 0, "target": "", "value": 30, "params": "", "anim_time": "", "name": "pawnSkillText.name_107", "desc": "pawnSkillText.desc_107", "desc_params": ""}, {"id": 10801, "type": 108, "use_type": 0, "target": "", "value": 20, "params": 0.05, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "5%"}, {"id": 10802, "type": 108, "use_type": 0, "target": "", "value": 25, "params": 0.1, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "10%"}, {"id": 10803, "type": 108, "use_type": 0, "target": "", "value": 31, "params": 0.15, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "15%"}, {"id": 10804, "type": 108, "use_type": 0, "target": "", "value": 38, "params": 0.2, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "20%"}, {"id": 10805, "type": 108, "use_type": 0, "target": "", "value": 47, "params": 0.25, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "25%"}, {"id": 10806, "type": 108, "use_type": 0, "target": "", "value": 58, "params": 0.3, "anim_time": "", "name": "pawnSkillText.name_108", "desc": "pawnSkillText.desc_108", "desc_params": "30%"}, {"id": 10901, "type": 109, "use_type": 0, "target": 99, "value": 1.5, "params": "", "anim_time": "", "name": "pawnSkillText.name_109", "desc": "pawnSkillText.desc_109", "desc_params": ""}, {"id": 11001, "type": 110, "use_type": 0, "target": "", "value": "", "params": "", "anim_time": "", "name": "pawnSkillText.name_110", "desc": "pawnSkillText.desc_110", "desc_params": ""}, {"id": 11101, "type": 111, "use_type": 0, "target": "", "value": "", "params": "", "anim_time": "", "name": "pawnSkillText.name_111", "desc": "pawnSkillText.desc_111", "desc_params": ""}, {"id": 11201, "type": 112, "use_type": 0, "target": "", "value": 0.15, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "15%"}, {"id": 11202, "type": 112, "use_type": 0, "target": "", "value": 0.16, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "16%"}, {"id": 11203, "type": 112, "use_type": 0, "target": "", "value": 0.17, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "17%"}, {"id": 11204, "type": 112, "use_type": 0, "target": "", "value": 0.18, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "18%"}, {"id": 11205, "type": 112, "use_type": 0, "target": "", "value": 0.19, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "19%"}, {"id": 11206, "type": 112, "use_type": 0, "target": "", "value": 0.2, "params": "", "anim_time": "650,0.42,5009", "name": "pawnSkillText.name_112", "desc": "pawnSkillText.desc_112", "desc_params": "20%"}, {"id": 11301, "type": 113, "use_type": 0, "target": "", "value": 2, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "2%"}, {"id": 11302, "type": 113, "use_type": 0, "target": "", "value": 2, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "2%"}, {"id": 11303, "type": 113, "use_type": 0, "target": "", "value": 3, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "3%"}, {"id": 11304, "type": 113, "use_type": 0, "target": "", "value": 3, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "3%"}, {"id": 11305, "type": 113, "use_type": 0, "target": "", "value": 4, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "4%"}, {"id": 11306, "type": 113, "use_type": 0, "target": "", "value": 5, "params": "", "anim_time": "", "name": "pawnSkillText.name_113", "desc": "pawnSkillText.desc_113", "desc_params": "5%"}, {"id": 11401, "type": 114, "use_type": 0, "target": "", "value": 10, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "10"}, {"id": 11402, "type": 114, "use_type": 0, "target": "", "value": 20, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "20"}, {"id": 11403, "type": 114, "use_type": 0, "target": "", "value": 30, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "30"}, {"id": 11404, "type": 114, "use_type": 0, "target": "", "value": 40, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "40"}, {"id": 11405, "type": 114, "use_type": 0, "target": "", "value": 50, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "50"}, {"id": 11406, "type": 114, "use_type": 0, "target": "", "value": 100, "params": "", "anim_time": "", "name": "pawnSkillText.name_114", "desc": "pawnSkillText.desc_114", "desc_params": "100"}, {"id": 11501, "type": 115, "use_type": 0, "target": "", "value": 0.01, "params": "", "anim_time": "", "name": "pawnSkillText.name_115", "desc": "pawnSkillText.desc_115", "desc_params": "1%"}, {"id": 11502, "type": 115, "use_type": 0, "target": "", "value": 0.03, "params": "", "anim_time": "", "name": "pawnSkillText.name_115", "desc": "pawnSkillText.desc_115", "desc_params": "3%"}, {"id": 11503, "type": 115, "use_type": 0, "target": "", "value": 0.05, "params": "", "anim_time": "", "name": "pawnSkillText.name_115", "desc": "pawnSkillText.desc_115", "desc_params": "5%"}, {"id": 11601, "type": 116, "use_type": 0, "target": "", "value": 0.5, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "50%"}, {"id": 11602, "type": 116, "use_type": 0, "target": "", "value": 0.6, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "60%"}, {"id": 11603, "type": 116, "use_type": 0, "target": "", "value": 0.7, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "70%"}, {"id": 11604, "type": 116, "use_type": 0, "target": "", "value": 0.8, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "80%"}, {"id": 11605, "type": 116, "use_type": 0, "target": "", "value": 0.9, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "90%"}, {"id": 11606, "type": 116, "use_type": 0, "target": "", "value": 1, "params": "", "anim_time": "", "name": "pawnSkillText.name_116", "desc": "pawnSkillText.desc_116", "desc_params": "100%"}, {"id": 11701, "type": 117, "use_type": 0, "target": "", "value": 0.45, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "45%"}, {"id": 11702, "type": 117, "use_type": 0, "target": "", "value": 0.5, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "50%"}, {"id": 11703, "type": 117, "use_type": 0, "target": "", "value": 0.55, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "55%"}, {"id": 11704, "type": 117, "use_type": 0, "target": "", "value": 0.6, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "60%"}, {"id": 11705, "type": 117, "use_type": 0, "target": "", "value": 0.65, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "65%"}, {"id": 11706, "type": 117, "use_type": 0, "target": "", "value": 0.7, "params": 50, "anim_time": "1.04,0.65,0", "name": "pawnSkillText.name_117", "desc": "pawnSkillText.desc_117", "desc_params": "70%"}, {"id": 11801, "type": 118, "use_type": 0, "target": "", "value": "", "params": "", "anim_time": "", "name": "pawnSkillText.name_118", "desc": "pawnSkillText.desc_118", "desc_params": ""}, {"id": 11901, "type": 119, "use_type": 0, "target": "", "value": 1, "params": 25, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "100%|25%"}, {"id": 11902, "type": 119, "use_type": 0, "target": "", "value": 1.05, "params": 30, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "105%|30%"}, {"id": 11903, "type": 119, "use_type": 0, "target": "", "value": 1.1, "params": 35, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "110%|35%"}, {"id": 11904, "type": 119, "use_type": 0, "target": "", "value": 1.15, "params": 40, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "115%|40%"}, {"id": 11905, "type": 119, "use_type": 0, "target": "", "value": 1.2, "params": 45, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "120%|45%"}, {"id": 11906, "type": 119, "use_type": 0, "target": "", "value": 1.25, "params": 50, "anim_time": "0.84,0.56,0", "name": "pawnSkillText.name_119", "desc": "pawnSkillText.desc_119", "desc_params": "125%|50%"}, {"id": 12001, "type": 120, "use_type": 0, "target": "", "value": 0.7, "params": "", "anim_time": "", "name": "pawnSkillText.name_120", "desc": "pawnSkillText.desc_120", "desc_params": "70%"}, {"id": 12101, "type": 121, "use_type": 0, "target": 2, "value": 5, "params": "", "anim_time": "", "name": "pawnSkillText.name_121", "desc": "pawnSkillText.desc_121", "desc_params": ""}, {"id": 12201, "type": 122, "use_type": 0, "target": "", "value": "", "params": "", "anim_time": "", "name": "pawnSkillText.name_122", "desc": "pawnSkillText.desc_122", "desc_params": ""}, {"id": 20101, "type": 201, "use_type": 1, "target": 2, "value": 1, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "100%"}, {"id": 20102, "type": 201, "use_type": 1, "target": 2, "value": 1.1, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "110%"}, {"id": 20103, "type": 201, "use_type": 1, "target": 2, "value": 1.2, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "120%"}, {"id": 20104, "type": 201, "use_type": 1, "target": 2, "value": 1.3, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "130%"}, {"id": 20105, "type": 201, "use_type": 1, "target": 2, "value": 1.4, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "140%"}, {"id": 20106, "type": 201, "use_type": 1, "target": 2, "value": 1.5, "params": 30, "anim_time": "0.8,0.5,0", "name": "pawnSkillText.name_201", "desc": "pawnSkillText.desc_201", "desc_params": "150%"}, {"id": 20201, "type": 202, "use_type": 1, "target": 5, "value": 0.7, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "70%"}, {"id": 20202, "type": 202, "use_type": 1, "target": 5, "value": 0.8, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "80%"}, {"id": 20203, "type": 202, "use_type": 1, "target": 5, "value": 0.9, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "90%"}, {"id": 20204, "type": 202, "use_type": 1, "target": 5, "value": 1, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "100%"}, {"id": 20205, "type": 202, "use_type": 1, "target": 5, "value": 1.1, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "110%"}, {"id": 20206, "type": 202, "use_type": 1, "target": 5, "value": 1.2, "params": 40, "anim_time": "600,0.6,5002", "name": "pawnSkillText.name_202", "desc": "pawnSkillText.desc_202", "desc_params": "120%"}, {"id": 20301, "type": 203, "use_type": 1, "target": "", "value": 1, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "100%|30%"}, {"id": 20302, "type": 203, "use_type": 1, "target": "", "value": 1.05, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "105%|40%"}, {"id": 20303, "type": 203, "use_type": 1, "target": "", "value": 1.1, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "110%|50%"}, {"id": 20304, "type": 203, "use_type": 1, "target": "", "value": 1.15, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "115%|60%"}, {"id": 20305, "type": 203, "use_type": 1, "target": "", "value": 1.2, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "120%|70%"}, {"id": 20306, "type": 203, "use_type": 1, "target": "", "value": 1.25, "params": "", "anim_time": "0.9,0.7,0", "name": "pawnSkillText.name_203", "desc": "pawnSkillText.desc_203", "desc_params": "125%|80%"}, {"id": 20401, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "30"}, {"id": 20402, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "40"}, {"id": 20403, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "50"}, {"id": 20404, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "60"}, {"id": 20405, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "70"}, {"id": 20406, "type": 204, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.35,0.72,0", "name": "pawnSkillText.name_204", "desc": "pawnSkillText.desc_204", "desc_params": "80"}, {"id": 20501, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "30%"}, {"id": 20502, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "35%"}, {"id": 20503, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "40%"}, {"id": 20504, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "45%"}, {"id": 20505, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "50%"}, {"id": 20506, "type": 205, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.1,0,0", "name": "pawnSkillText.name_205", "desc": "pawnSkillText.desc_205", "desc_params": "55%"}, {"id": 20601, "type": 206, "use_type": 1, "target": "", "value": 0.5, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "50%"}, {"id": 20602, "type": 206, "use_type": 1, "target": "", "value": 0.6, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "60%"}, {"id": 20603, "type": 206, "use_type": 1, "target": "", "value": 0.7, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "70%"}, {"id": 20604, "type": 206, "use_type": 1, "target": "", "value": 0.8, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "80%"}, {"id": 20605, "type": 206, "use_type": 1, "target": "", "value": 0.9, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "90%"}, {"id": 20606, "type": 206, "use_type": 1, "target": "", "value": 1, "params": 2, "anim_time": "550,0.8,5005", "name": "pawnSkillText.name_206", "desc": "pawnSkillText.desc_206", "desc_params": "100%"}, {"id": 20701, "type": 207, "use_type": 1, "target": "", "value": 0.6, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "60%"}, {"id": 20702, "type": 207, "use_type": 1, "target": "", "value": 0.68, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "68%"}, {"id": 20703, "type": 207, "use_type": 1, "target": "", "value": 0.76, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "76%"}, {"id": 20704, "type": 207, "use_type": 1, "target": "", "value": 0.84, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "84%"}, {"id": 20705, "type": 207, "use_type": 1, "target": "", "value": 0.92, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "92%"}, {"id": 20706, "type": 207, "use_type": 1, "target": "", "value": 1, "params": 3, "anim_time": "550,0.45,5006", "name": "pawnSkillText.name_207", "desc": "pawnSkillText.desc_207", "desc_params": "100%"}, {"id": 20801, "type": 208, "use_type": 1, "target": 5, "value": 1, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "100%"}, {"id": 20802, "type": 208, "use_type": 1, "target": 5, "value": 1.05, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "105%"}, {"id": 20803, "type": 208, "use_type": 1, "target": 5, "value": 1.1, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "110%"}, {"id": 20804, "type": 208, "use_type": 1, "target": 5, "value": 1.15, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "115%"}, {"id": 20805, "type": 208, "use_type": 1, "target": 5, "value": 1.2, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "120%"}, {"id": 20806, "type": 208, "use_type": 1, "target": 5, "value": 1.25, "params": "0.3,0.4", "anim_time": "1,0.9,0", "name": "pawnSkillText.name_208", "desc": "pawnSkillText.desc_208", "desc_params": "125%"}, {"id": 20901, "type": 209, "use_type": 1, "target": "", "value": 0.45, "params": 5, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "45%|5"}, {"id": 20902, "type": 209, "use_type": 1, "target": "", "value": 0.5, "params": 6, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "50%|6"}, {"id": 20903, "type": 209, "use_type": 1, "target": "", "value": 0.55, "params": 7, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "55%|7"}, {"id": 20904, "type": 209, "use_type": 1, "target": "", "value": 0.6, "params": 8, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "60%|8"}, {"id": 20905, "type": 209, "use_type": 1, "target": "", "value": 0.65, "params": 9, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "65%|9"}, {"id": 20906, "type": 209, "use_type": 1, "target": "", "value": 0.7, "params": 10, "anim_time": "1.08,0.54,0", "name": "pawnSkillText.name_209", "desc": "pawnSkillText.desc_209", "desc_params": "70%|10"}, {"id": 21001, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "1%|55%"}, {"id": 21002, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "1.5%|60%"}, {"id": 21003, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "2%|65%"}, {"id": 21004, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "2.5%|70%"}, {"id": 21005, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "3%|75%"}, {"id": 21006, "type": 210, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "550,0.81,5007", "name": "pawnSkillText.name_210", "desc": "pawnSkillText.desc_210", "desc_params": "3.5%|80%"}, {"id": 21101, "type": 211, "use_type": 1, "target": "", "value": 0.15, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "15%"}, {"id": 21102, "type": 211, "use_type": 1, "target": "", "value": 0.2, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "20%"}, {"id": 21103, "type": 211, "use_type": 1, "target": "", "value": 0.25, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "25%"}, {"id": 21104, "type": 211, "use_type": 1, "target": "", "value": 0.3, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "30%"}, {"id": 21105, "type": 211, "use_type": 1, "target": "", "value": 0.35, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "35%"}, {"id": 21106, "type": 211, "use_type": 1, "target": "", "value": 0.4, "params": "", "anim_time": "1.1,0.8,0", "name": "pawnSkillText.name_211", "desc": "pawnSkillText.desc_211", "desc_params": "40%"}, {"id": 21201, "type": 212, "use_type": 1, "target": 5, "value": 1, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "100%"}, {"id": 21202, "type": 212, "use_type": 1, "target": 5, "value": 1.05, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "105%"}, {"id": 21203, "type": 212, "use_type": 1, "target": 5, "value": 1.1, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "110%"}, {"id": 21204, "type": 212, "use_type": 1, "target": 5, "value": 1.15, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "115%"}, {"id": 21205, "type": 212, "use_type": 1, "target": 5, "value": 1.2, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "120%"}, {"id": 21206, "type": 212, "use_type": 1, "target": 5, "value": 1.25, "params": "0.48,0.48", "anim_time": "1.44,1.2,0", "name": "pawnSkillText.name_212", "desc": "pawnSkillText.desc_212", "desc_params": "125%"}, {"id": 21301, "type": 213, "use_type": 1, "target": 2, "value": 0.75, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "75%"}, {"id": 21302, "type": 213, "use_type": 1, "target": 2, "value": 0.8, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "80%"}, {"id": 21303, "type": 213, "use_type": 1, "target": 2, "value": 0.85, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "85%"}, {"id": 21304, "type": 213, "use_type": 1, "target": 2, "value": 0.9, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "90%"}, {"id": 21305, "type": 213, "use_type": 1, "target": 2, "value": 0.95, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "95%"}, {"id": 21306, "type": 213, "use_type": 1, "target": 2, "value": 1, "params": "0.4,0.7", "anim_time": "1.4,0,0", "name": "pawnSkillText.name_213", "desc": "pawnSkillText.desc_213", "desc_params": "100%"}, {"id": 21401, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "2"}, {"id": 21402, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "3"}, {"id": 21403, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "4"}, {"id": 21404, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "5"}, {"id": 21405, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "7"}, {"id": 21406, "type": 214, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.2,0.8,0", "name": "pawnSkillText.name_214", "desc": "pawnSkillText.desc_214", "desc_params": "9"}, {"id": 21501, "type": 215, "use_type": 1, "target": "", "value": 0.45, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "45%"}, {"id": 21502, "type": 215, "use_type": 1, "target": "", "value": 0.5, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "50%"}, {"id": 21503, "type": 215, "use_type": 1, "target": "", "value": 0.55, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "55%"}, {"id": 21504, "type": 215, "use_type": 1, "target": "", "value": 0.6, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "60%"}, {"id": 21505, "type": 215, "use_type": 1, "target": "", "value": 0.65, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "65%"}, {"id": 21506, "type": 215, "use_type": 1, "target": "", "value": 0.7, "params": "", "anim_time": "1.6,1,0", "name": "pawnSkillText.name_215", "desc": "pawnSkillText.desc_215", "desc_params": "70%"}, {"id": 21601, "type": 216, "use_type": 1, "target": 2, "value": 0.45, "params": 0.45, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "45%|45%"}, {"id": 21602, "type": 216, "use_type": 1, "target": 2, "value": 0.5, "params": 0.5, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "50%|50%"}, {"id": 21603, "type": 216, "use_type": 1, "target": 2, "value": 0.55, "params": 0.55, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "55%|55%"}, {"id": 21604, "type": 216, "use_type": 1, "target": 2, "value": 0.6, "params": 0.6, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "60%|60%"}, {"id": 21605, "type": 216, "use_type": 1, "target": 2, "value": 0.65, "params": 0.65, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "65%|65%"}, {"id": 21606, "type": 216, "use_type": 1, "target": 2, "value": 0.7, "params": 0.7, "anim_time": "1.6,1,0", "name": "pawnSkillText.name_216", "desc": "pawnSkillText.desc_216", "desc_params": "70%|70%"}, {"id": 21701, "type": 217, "use_type": 1, "target": "", "value": 0.3, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "30%"}, {"id": 21702, "type": 217, "use_type": 1, "target": "", "value": 0.4, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "40%"}, {"id": 21703, "type": 217, "use_type": 1, "target": "", "value": 0.5, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "50%"}, {"id": 21704, "type": 217, "use_type": 1, "target": "", "value": 0.6, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "60%"}, {"id": 21705, "type": 217, "use_type": 1, "target": "", "value": 0.7, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "70%"}, {"id": 21706, "type": 217, "use_type": 1, "target": "", "value": 0.8, "params": "", "anim_time": "550,0.52,5001", "name": "pawnSkillText.name_217", "desc": "pawnSkillText.desc_217", "desc_params": "80%"}, {"id": 21801, "type": 218, "use_type": 1, "target": "", "value": 1, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "100%"}, {"id": 21802, "type": 218, "use_type": 1, "target": "", "value": 1.05, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "105%"}, {"id": 21803, "type": 218, "use_type": 1, "target": "", "value": 1.1, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "110%"}, {"id": 21804, "type": 218, "use_type": 1, "target": "", "value": 1.15, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "115%"}, {"id": 21805, "type": 218, "use_type": 1, "target": "", "value": 1.2, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "120%"}, {"id": 21806, "type": 218, "use_type": 1, "target": "", "value": 1.25, "params": 0.8, "anim_time": "1.26,0.84,0", "name": "pawnSkillText.name_218", "desc": "pawnSkillText.desc_218", "desc_params": "125%"}, {"id": 21901, "type": 219, "use_type": 1, "target": 2, "value": 25, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "25%"}, {"id": 21902, "type": 219, "use_type": 1, "target": 2, "value": 30, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "30%"}, {"id": 21903, "type": 219, "use_type": 1, "target": 2, "value": 35, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "35%"}, {"id": 21904, "type": 219, "use_type": 1, "target": 2, "value": 40, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "40%"}, {"id": 21905, "type": 219, "use_type": 1, "target": 2, "value": 45, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "45%"}, {"id": 21906, "type": 219, "use_type": 1, "target": 2, "value": 50, "params": "0.4,0.1", "anim_time": "0.64,0.32,0", "name": "pawnSkillText.name_219", "desc": "pawnSkillText.desc_219", "desc_params": "50%"}, {"id": 22001, "type": 220, "use_type": 1, "target": 7, "value": 5, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "5"}, {"id": 22002, "type": 220, "use_type": 1, "target": 7, "value": 8, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "8"}, {"id": 22003, "type": 220, "use_type": 1, "target": 7, "value": 11, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "11"}, {"id": 22004, "type": 220, "use_type": 1, "target": 7, "value": 14, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "14"}, {"id": 22005, "type": 220, "use_type": 1, "target": 7, "value": 17, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "17"}, {"id": 22006, "type": 220, "use_type": 1, "target": 7, "value": 20, "params": "", "anim_time": "550,0.7,5020", "name": "pawnSkillText.name_220", "desc": "pawnSkillText.desc_220", "desc_params": "20"}, {"id": 22101, "type": 221, "use_type": 1, "target": 4, "value": 0.75, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "75%"}, {"id": 22102, "type": 221, "use_type": 1, "target": 4, "value": 0.8, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "80%"}, {"id": 22103, "type": 221, "use_type": 1, "target": 4, "value": 0.85, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "85%"}, {"id": 22104, "type": 221, "use_type": 1, "target": 4, "value": 0.9, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "90%"}, {"id": 22105, "type": 221, "use_type": 1, "target": 4, "value": 0.95, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "95%"}, {"id": 22106, "type": 221, "use_type": 1, "target": 4, "value": 1, "params": 0.36, "anim_time": "450,0,5038", "name": "pawnSkillText.name_221", "desc": "pawnSkillText.desc_221", "desc_params": "100%"}, {"id": 30101, "type": 301, "use_type": 1, "target": 1, "value": "", "params": "", "anim_time": "1.32,1.08,0", "name": "pawnSkillText.name_301", "desc": "pawnSkillText.desc_301", "desc_params": ""}, {"id": 30201, "type": 302, "use_type": 1, "target": 5, "value": 2.5, "params": "", "anim_time": "500,0.84,5003", "name": "pawnSkillText.name_302", "desc": "pawnSkillText.desc_302", "desc_params": ""}, {"id": 30301, "type": 303, "use_type": 1, "target": "", "value": 0.1, "params": "", "anim_time": "500,0.7,5005", "name": "pawnSkillText.name_303", "desc": "pawnSkillText.desc_303", "desc_params": ""}, {"id": 30401, "type": 304, "use_type": 1, "target": "", "value": 0.01, "params": "", "anim_time": "0.84,0.6,0", "name": "pawnSkillText.name_304", "desc": "pawnSkillText.desc_304", "desc_params": ""}, {"id": 30501, "type": 305, "use_type": 1, "target": "", "value": 1.2, "params": 0.5, "anim_time": "0.72,0.6,0", "name": "pawnSkillText.name_305", "desc": "pawnSkillText.desc_305", "desc_params": ""}, {"id": 30601, "type": 306, "use_type": 1, "target": 2, "value": 1, "params": "0.3,0.6", "anim_time": "1,0,0", "name": "pawnSkillText.name_306", "desc": "pawnSkillText.desc_306", "desc_params": ""}, {"id": 30701, "type": 307, "use_type": 1, "target": "", "value": 0.75, "params": "", "anim_time": "0.84,0.6,0", "name": "pawnSkillText.name_307", "desc": "pawnSkillText.desc_307", "desc_params": ""}, {"id": 30801, "type": 308, "use_type": 1, "target": "", "value": "", "params": "", "anim_time": "1.12,0.56,0", "name": "pawnSkillText.name_308", "desc": "pawnSkillText.desc_308", "desc_params": ""}, {"id": 30901, "type": 309, "use_type": 1, "target": 2, "value": 0.5, "params": "", "anim_time": "1.28,0.96,0", "name": "pawnSkillText.name_309", "desc": "pawnSkillText.desc_309", "desc_params": ""}, {"id": 31001, "type": 310, "use_type": 1, "target": 3, "value": 0.2, "params": 240, "anim_time": "0.24,0.08,0", "name": "pawnSkillText.name_310", "desc": "pawnSkillText.desc_310", "desc_params": ""}]