package lobby

// type RoomMongodb struct {
// 	table string
// }

// var (
// 	mgo_database  string
// 	mgo_client    *mongo.Client
// 	db_room       = &RoomMongodb{slg.DB_COLLECTION_NAME_ROOM}
// 	db_game_machs = &GameMachMongodb{slg.DB_COLLECTION_NAME_GAME_MACHS}
// )

// func InitRoomDB(url, dbname string) {
// 	mgo_database = dbname
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	opt := options.Client().ApplyURI(url)
// 	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
// 	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
// 	opt.SetMaxPoolSize(20)                  //使用最大的连接数
// 	var err error
// 	if mgo_client, err = mongo.Connect(ctx, opt); err == nil {
// 		log.Info("room_mongodb init success! " + url + "[" + mgo_database + "]")
// 	} else if err == mongo.ErrNoDocuments {
// 		log.Error("room_mongodb init error! ErrNoDocuments")
// 	} else {
// 		log.Error(err.Error())
// 	}
// }

// func (this *RoomMongodb) getCollection() *mongo.Collection {
// 	return mgo_client.Database(mgo_database).Collection(this.table)
// }

// // 获取所有房间数据
// func (this *RoomMongodb) FindAll() (list []r.RoomTableData, err string) {
// 	cur, e := this.getCollection().Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cur.Close(context.TODO())
// 	}()
// 	if e != nil {
// 		return []r.RoomTableData{}, e.Error()
// 	} else if e = cur.Err(); e != nil {
// 		return []r.RoomTableData{}, e.Error()
// 	} else if e = cur.All(context.TODO(), &list); e != nil {
// 		err = e.Error()
// 	}
// 	return
// }

// // 获取指定房间数据
// func (this *RoomMongodb) Find(sid int) (roomData r.RoomTableData, err string) {
// 	filter := &bson.M{
// 		"id": sid,
// 	}
// 	e := this.getCollection().FindOne(context.TODO(), filter).Decode(&roomData)
// 	if e != nil {
// 		err = e.Error()
// 	}
// 	return
// }

// type GameMachMongodb struct {
// 	table string
// }

// func (this *GameMachMongodb) getCollection() *mongo.Collection {
// 	return mgo_client.Database(mgo_database).Collection(this.table)
// }

// // 删除游戏服指定表
// func DelGameCol(col string) error {
// 	err := mgo_client.Database(mgo_database).Collection(col).Drop(context.TODO())
// 	if err != nil {
// 		log.Error("DelGameCol col: %v, err: %v", col, err)
// 	}
// 	return err
// }

// // 删除游戏服指定表指定sid的数据
// func DelGameColDataBySid(col string, sid int) error {
// 	_, err := mgo_client.Database(mgo_database).Collection(col).DeleteMany(context.TODO(), &bson.M{"sid": sid})
// 	if err != nil {
// 		log.Error("DelGameColDataBySid col: %v, sid: %v, err: %v", col, sid, err)
// 	}
// 	return err
// }
