package lobby

import (
	"context"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/enums/bdtype"
	"time"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type RoomMongodb struct {
	table string
}

var (
	RoomDbDatabase string
	RoomDbClient   *mongo.Client
	db_room        = &RoomMongodb{slg.DB_COLLECTION_NAME_ROOM}
)

func InitRoomDB(url, dbname string) {
	RoomDbDatabase = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  //使用最大的连接数
	var err error
	if RoomDbClient, err = mongo.Connect(ctx, opt); err == nil {
		log.Info("room_mongodb init success! " + url + "[" + RoomDbDatabase + "]")
	} else if err == mongo.ErrNoDocuments {
		log.Error("room_mongodb init error! ErrNoDocuments")
	} else {
		log.Error(err.Error())
	}
}

func (this *RoomMongodb) getCollection() *mongo.Collection {
	return RoomDbClient.Database(RoomDbDatabase).Collection(this.table)
}

func GetRoomDbDatabase() *mongo.Database {
	return RoomDbClient.Database(RoomDbDatabase)
}

// 根据玩家id获取士兵数据统计列表
func FindBattleScoreRecordsByOwner(userId, sid string) (pawnStats []map[int32]map[int32]int32, err error) {
	filter := bson.M{"owner": userId}
	opts := options.Find().SetProjection(bson.M{
		"pawn_statistic": 1,
		"_id":            0, // 排除_id字段
	})

	cur, e := GetRoomDbDatabase().Collection("record_battle_score_"+sid).Find(context.TODO(), filter, opts)
	defer cur.Close(context.TODO())
	if e != nil {
		return nil, e
	}

	var records []struct {
		PawnStatistic map[int32]map[int32]int32 `bson:"pawn_statistic"`
	}

	if e = cur.All(context.TODO(), &records); e != nil {
		log.Error("FindBattleScoreRecordsByOwner userId: %v err: %v", userId, e)
		return nil, e
	}

	for _, record := range records {
		pawnStats = append(pawnStats, record.PawnStatistic)
	}
	return pawnStats, nil
}

// 获取玩家士兵数据统计
func GetPlayerPawnStatistics(userId, sid string) map[int32]map[int32]int32 {
	// 查询该玩家的所有记录
	arr, err := FindBattleScoreRecordsByOwner(userId, sid)
	if err != nil {
		return nil
	}
	if len(arr) == 0 {
		return nil
	}

	rst := map[int32]map[int32]int32{}
	// 整合数据
	for _, v := range arr {
		for pawnId, m := range v {
			pawnStat := rst[pawnId]
			if pawnStat == nil {
				pawnStat = map[int32]int32{}
				rst[pawnId] = pawnStat
			}
			pb.MapMergeInt32(pawnStat, m)
			pawnStat[bdtype.FIGHT_COUNT]++
		}
	}
	return rst
}
