package ta

import (
	slg "slgsrv/server/common"
	ut "slgsrv/utils"

	"github.com/ThinkingDataAnalytics/go-sdk/thinkingdata"
	"github.com/huyangv/vmqant/log"
)

const USER_OFFLINE_TRACK_INTERVAL = ut.TIME_MINUTE * 30 //用户离线上报间隔

var (
	ta thinkingdata.TDAnalytics
)

func Init() {
	if !slg.TA_OPEN {
		return
	}
	appId, batchSize, serverUrl := "debug-appid", 20, "https://receiver-ta.twomiles.cn"
	if slg.TA_DEBUG {
		batchSize = 1
	} else if slg.IsChinaArea() {
		appId = "655d3dd4d2e4421fa8aef4fc0bf06ca9" //国内
	} else {
		appId = "64cf813ae2ef43608f067452f0dfad7e" //海外
		serverUrl = "https://receiver-ta-global.twomiles.cn"
	}
	// consumer, err := thinkingdata.NewBatchConsumer("https://global-receiver-ta.thinkingdata.cn", appId)
	// consumer, err := thinkingdata.NewBatchConsumer("https://receiver-ta.twomiles.cn", appId)
	consumer, err := thinkingdata.NewBatchConsumerWithBatchSize(serverUrl, appId, batchSize)
	if err != nil {
		log.Error("init ta error", err.Error())
	}
	ta = thinkingdata.New(consumer)
	log.Info("init ta done. appId: %v, serverUrl: %v, zoneOffset: %v", appId, serverUrl, slg.GetServerZoneOffset())
}

// func getAccountId(sid int, uid string, reCreateCount int) string {
// 	accountId := uid + "_" + ut.Itoa(sid)
// 	if reCreateCount > 0 {
// 		accountId = accountId + "_" + ut.Itoa(reCreateCount)
// 	}
// 	return accountId
// }

func Track(sid int32, uid string, did string, reCreateCount int32, eventName string, properties map[string]interface{}) {
	if !slg.TA_OPEN {
		return
	} else if slg.SERVER_AREA != slg.SERVER_AREA_HK {
		uid = slg.SERVER_AREA + "_" + uid
	}
	properties["uid"] = uid
	properties["sid"] = sid
	properties["sid_type"] = sid / slg.ROOM_TYPE_FLAG
	go _track(uid, did, eventName, properties)
}

// 上报系统事件
func TrackSystem(eventName string, properties map[string]interface{}) {
	if slg.TA_OPEN {
		go _track("system", "", eventName, properties)
	}
}

func _track(accountId string, did string, eventName string, properties map[string]interface{}) {
	properties["area_type"] = slg.SERVER_AREA
	properties["#zone_offset"] = slg.GetServerZoneOffset()
	if err := ta.Track(accountId, did, eventName, properties); err != nil {
		log.Error("ta track error, event: %v, accountId: %v, did: %v, error: %v", eventName, accountId, did, err)
	} else {
		// log.Info("taTrack accountId: %v, did: %v, eventName: %v, properties: %v", accountId, did, eventName, properties)
	}
}

func UserSet(sid int32, uid string, did string, reCreateCount int32, properties map[string]interface{}) {
	if slg.TA_OPEN {
		go _userSet(sid, uid, did, properties)
	}
}

func _userSet(sid int32, uid string, did string, properties map[string]interface{}) {
	if slg.SERVER_AREA != slg.SERVER_AREA_HK {
		uid = slg.SERVER_AREA + "_" + uid
	}
	properties["uid"] = uid
	properties["sid"] = sid
	properties["area_type"] = slg.SERVER_AREA
	if err := ta.UserSet(uid, did, properties); err != nil {
		log.Error("ta UserSet error ", err.Error())
	} else {
		// log.Info("taUserSet accountId: %v, sid: %v, uid: %v, did: %v, properties: %v", accountId, sid, uid, did, properties)
	}
}
