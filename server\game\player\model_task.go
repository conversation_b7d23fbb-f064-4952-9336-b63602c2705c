package player

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

type TaskList struct {
	deadlock.RWMutex
	List    []*g.TaskInfo
	Finishs []int32 //完成列表
}

func (this *TaskList) Strip() []int32 {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) int32 { return m.ID })
}

func (this *TaskList) ToPb() []*pb.TaskInfo {
	this.RLock()
	defer this.RUnlock()
	return array.Map(this.List, func(m *g.TaskInfo, _ int) *pb.TaskInfo {
		return m.ToPb()
	})
}

func InitTaskByDB(tasks []*g.TaskInfo, jsonName string, runDay int32) []*g.TaskInfo {
	for i := len(tasks) - 1; i >= 0; i-- {
		if !tasks[i].InitJson(jsonName, runDay) {
			tasks = append(tasks[:i], tasks[i+1:]...)
		}
	}
	return tasks
}

// 检测条件是否满足
func (this *Model) CheckTaskConditions(tasks []*g.TaskInfo) bool {
	return !array.Some(tasks, func(m *g.TaskInfo) bool { return !this.CheckTaskConditionOne(m) })
}
func (this *Model) CheckTaskConditionOne(task *g.TaskInfo) bool {
	cond := task.GetCondInfo()
	if cond == nil {
		return false
	}
	wld := this.room.GetWorld()
	if cond.Type == tctype.BUILD_LV { //建筑等级
		return array.Some(wld.GetAreaBuildsByID(this.MainCityIndex, cond.ID), func(m g.Build) bool { return m.GetLV() >= cond.Count })
	} else if cond.Type == tctype.CELL_COUNT { //拥有领地数
		return wld.GetPlayerLandCount(this.Uid, cond.ID == 1) >= cond.Count
	} else if cond.Type == tctype.LAND_LV_COUNT { //拥有x级地块数量
		return wld.GetPlayerLandCountByLv(this.Uid, cond.ID) >= cond.Count
	} else if cond.Type == tctype.LAND_TYPE_COUNT { //拥有x类型地块数量
		return wld.GetPlayerLandCountByType(this.Uid, cond.ID) >= cond.Count
	} else if cond.Type == tctype.TODAY_CELL_COUNT { //每日打地数量
		return this.TodayOccupyCellCount >= cond.Count
	} else if cond.Type == tctype.ARMY_COUNT { //拥有多少军队
		return this.GetAmyCount() >= cond.Count
	} else if cond.Type == tctype.FORGE_EQUIP_APPOINT { //打造指定装备
		return array.Some(wld.GetPlayerEquips(this.Uid), func(m *g.EquipInfo) bool { return m.ID == cond.ID })
	} else if cond.Type == tctype.STUDY_TYPE_APPOINT { //研究指定类型
		return array.Some(this.CeriSlots, func(m *CeriSlotInfo) bool { return m.Type == cond.ID && m.StartTime == 0 })
	} else if cond.Type == tctype.SELECT_POLICY { //在内政选择任意一个政策
		return len(this.ToPolicys()) > 0
	} else if cond.Type == tctype.BT_MAP_RES_BUILD { //任意修建一个农场或伐木场或采石场
		return wld.GetPlayerHasResBuild(this.Uid)
	} else if cond.Type == tctype.BT_MAP_BUILD { //修建指定地图建筑
		return wld.GetPlayerOwnedCityCountByID(this.Uid, cond.ID) >= cond.Count
	} else if cond.Type == tctype.WORSHIP_HERO { //供奉x个英雄
		return len(array.Filter(wld.GetPlayerHeroSlots(this.Uid), func(m *g.HeroSlotInfo, _ int) bool { return m.GetHeroID() > 0 })) >= int(cond.Count)
	} else if cond.Type == tctype.FORGE_EXC_EQUIP { //打造x个专属装备
		return len(array.Filter(wld.GetPlayerEquips(this.Uid), func(m *g.EquipInfo, _ int) bool { return m.IsExclusive() })) >= int(cond.Count)
	}
	return task.Progress >= cond.Count
}

// 触发任务
func (this *Model) TriggerTask(condType, count, param int32) bool {
	notifyGuideTasks, notifyTodayTasks := this.TriggerGameTask(condType, count, param)
	notifyTasks := &pb.TaskUpdateNotify{
		GuideTasks: notifyGuideTasks,
		TodayTasks: notifyTodayTasks,
	}
	// 登录服任务 指定类型调用RPC触发登录服任务
	if _, ok := tctype.GAME_TRGGER_TO_LOGIN_TYPE_MAP[condType]; ok {
		this.room.InvokeLobbyRpcNR(this.GetLid(), slg.RPC_TRIGGER_TASK_BY_GAME, this.Uid, condType, count, param)
	}
	// 通知客户端
	return this.notifyUpdateTasks(notifyTasks)
}

// Rpc触发任务 将Rpc参数中更新的任务及游戏服更新的任务一起通知给客户端
func (this *Model) TriggerTaskByRpc(condType, count, param int32) {
	notifyGuideTasks, notifyTodayTasks := this.TriggerGameTask(condType, count, param)
	if len(notifyGuideTasks)+len(notifyTodayTasks) > 0 {
		notifyTasks := &pb.TaskUpdateNotify{
			GuideTasks: notifyGuideTasks,
			TodayTasks: notifyTodayTasks,
		}
		// 通知客户端
		this.notifyUpdateTasks(notifyTasks)
	}
}

// 触发游戏服任务
func (this *Model) TriggerGameTask(condType, count, param int32) (notifyGuideTasks []*pb.TaskInfo, notifyTodayTasks []*pb.TaskInfo) {
	// 引导任务
	this.Tasks.RLock()
	defer this.Tasks.RUnlock()
	for _, t := range this.Tasks.List {
		if t.GetCondInfo().Type == condType && t.GetCondInfo().ID == param {
			if this.UpdateTaskProgress(t, count) {
				notifyGuideTasks = append(notifyGuideTasks, t.ToPb())
			}
		}
	}
	// 每日任务
	this.TodayTasks.RLock()
	defer this.TodayTasks.RUnlock()
	for _, t := range this.TodayTasks.List {
		if t.GetCondInfo().Type == condType && t.GetCondInfo().ID == param {
			if this.UpdateTaskProgress(t, count) {
				notifyTodayTasks = append(notifyTodayTasks, t.ToPb())
			}
		}
	}
	return
}

// 任务进度更新通知
func (this *Model) notifyUpdateTasks(notify *pb.TaskUpdateNotify) bool {
	if len(notify.GuideTasks)+len(notify.TodayTasks)+len(notify.OtherTasks) > 0 {
		this.PutNotifyQueue(constant.NQ_UPDATE_TASKS, &pb.OnUpdatePlayerInfoNotify{
			Data_55: notify,
		})
		return true
	}
	return false
}

// 更新任务进度
func (this *Model) UpdateTaskProgress(task *g.TaskInfo, progress int32) bool {
	cond := task.GetCondInfo()
	if IsProgressByData(cond.Type) {
		return UpdateTaskProgressByData(task, progress)
	} else {
		return task.AddProgress(progress)
	}
}

// 初始化数据替代进度的任务
func (this *Model) InitTaskProgress(task *g.TaskInfo) {
	cond := task.GetCondInfo()
	switch cond.Type {
	case tctype.UPLV_PAWN_APPOINT:
		task.Progress = this.GetIdPawnMaxLv(cond.ID)
	case tctype.ARMY_PAWN_COUNT:
		task.Progress = this.room.GetWorld().GetMaxPawnCountInArmy(this.Uid)
	case tctype.UPLV_PAWN:
		task.Progress = this.GetIdPawnMaxLv(0)
	}
}

// 是否为数据替代进度的任务
func IsProgressByData(TaskType int32) bool {
	if TaskType == tctype.UPLV_PAWN_APPOINT ||
		TaskType == tctype.ARMY_PAWN_COUNT ||
		TaskType == tctype.UPLV_PAWN {
		return true
	}
	return false
}

// 更新数据替代进度的任务
func UpdateTaskProgressByData(task *g.TaskInfo, data int32) bool {
	taskType := task.GetCondInfo().Type
	if !IsProgressByData(taskType) {
		return false
	}
	if taskType == tctype.UPLV_PAWN_APPOINT ||
		taskType == tctype.UPLV_PAWN {
		// 特殊处理 只能覆盖更高等级
		if data <= task.Progress {
			return false
		}
	}
	task.Progress = data
	return true
}
