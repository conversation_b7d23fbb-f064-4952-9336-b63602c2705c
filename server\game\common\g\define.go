package g

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/astar"
	ut "slgsrv/utils"
	"slgsrv/utils/random"
)

type Room interface {
	GetSID() int32
	GetType() uint8
	GetSubType() uint8
	GetCreateTime() int64
	SetCreateTime(val int64) // 设置开服时间
	GetWinCond() []int32     // 获取获胜条件
	SetWinCond(val []int32)  // 设置获胜条件
	GetMapSize() *ut.Vec2    // 获取地图大小
	GetMapId() int32         // 获取地图id
	GetFarmType() int32      // 获取默认开荒方式
	GetPlayer(uid string) Player
	HasPlayer(uid string) bool
	IsPlayerOnline(uid string) bool // 玩家是否在线
	GetOnlinePlayerOrDB(uid string) Player
	DeletePlayer(uid string)      // 从数据库删除
	AddMainCityIndex(index int32) // 重新添加主城位置到列表
	UpdatePlayerDB(plr Player)
	CheckRemoveOfflinePlayer(plr Player)
	GetStaminas() []int32 // 获取体力
	GetRunTime() int32
	GetRunDay() int32 // 运行天数
	GetWorld() World
	GetBazaar() Bazaar
	GetRecord() Record
	NotifyAll(topic string, msg *pb.GAME_ONNOTICE_NOTIFY, ignores ...string)
	NotifyAllByBytes(topic string, body []byte, ignores ...string)
	PutNotifyAllPlayersQueue(topic string, msg []byte, uids []string)
	PutPlayerNotifyQueue(nType int, uid string, msg *pb.OnUpdatePlayerInfoNotify)
	PutPlayerUpdateOpSec(uid string)
	PutNotifyQueueAllPlayer(t int, data *pb.OnUpdatePlayerInfoNotify, ignores ...string)
	PutNotifyPlayerHasTreasure(uid string)
	NotifyAllPlayerUpdateOpSec()
	GameOver(winType int32, winUid string, winName string, winCellCount int32, winners [][]string, ancientInfo []int32)
	IsGameOver() bool
	GetGameWiner() (winType int32, winUID string)
	RecordDeletePlayer(uid string, index, cellCount int32, offlineTime int64)
	Invoke(moduleType string, _func string, params ...interface{}) (result interface{}, err string)
	InvokeNR(moduleType string, _func string, params ...interface{})
	InvokeLobbyRpc(id string, _func string, params ...interface{}) (result interface{}, err string)
	InvokeLobbyRpcNR(id string, _func string, params ...interface{})
	SendMailOne(contentId int, title, content, sender, receiver string)
	SendMailItemOne(contentId int, title, content, sender, receiver string, items []*TypeObj)
	CheckRoomVersionLower(version string) bool
	IsConquer() bool
	GetPlayerNum() int32
}

type World interface {
	ToPb(isReconnect bool) *pb.WorldInfo
	GetSeason() Season
	GetServerRunDay() int32
	GetPlayerCellOutput(uid string) (cereal, timber, stone float64)
	GetPlayerCerealConsume(uid string) float64                              // 获取玩家粮耗
	GetPlayerOwnCells(uid string) []int32                                   // 获取玩家拥有的地块
	GetPlayerLandCount(uid string, notHasMainCityCount bool) int32          // 获取玩家拥有的地块数量
	GetPlayerTitle(uid string) int32                                        // 获取玩家的称号
	GetPlayerPersonalDesc(uid string) string                                // 获取玩家的个人简介
	GetPlayerLandScoreAndAlliScore(uid string) (int32, int32, int32, int32) // 获取玩家领地积分和联盟积分
	GetPlayerDataToDB(uid string) (string, string, string, int32)
	ToHttpTempPlayerStrip(uid string) map[string]interface{}
	UpdatePlayerOnlineState(uid string, time int64)
	UpdatePlayerOutput(uid string)                            // 刷新在线玩家的产出
	UpdatePlayerHeroDie(uid string, armyUid string, id int32) // 刷新玩家的英雄阵亡
	AreaBuildBTComplete(index int32, uid string, lv int32) Build
	SetBuildLv(index, id, lv int32) Build
	CompatiBuildLv0(index int32, uids []string) // 兼容等级0的建筑
	ToDrillPawnQueuePb(index int32) map[string]*pb.DrillPawnInfoList
	ToPawnLvingQueuePb(index int32) []*pb.PawnLvingInfo
	GetAreaAllBuildEffect(index int32) map[int32]*EffectObj
	GetAreaBuildEffect(index int32, uid string) map[int32]*EffectObj
	ToAreaBuildShortInfo(index int32, uid string) map[string]interface{}
	GetToMapCellDis(sindex int32, tindex int32) int32
	AreaBattleEnd(index int32, attacker string, fighters []string, atkReinforceCount, defReinforceCount int32,
		datas []*BattleRecordData, battleInfo map[string]map[int32]int32, battlePlayerUids []string, treasuresMap map[string][]*TreasureInfo,
		deadInfoMap map[string][]*PawmDeadRecord, alliUidMap map[string]string, lostTressureMap map[string]map[int32]int32, fullLostCount int32)
	NotifyPlayerArmyDistInfo(uid string)                                        // 通知玩家的军队分布情况
	GetPlayerMainBuildsPb(index int32) []*pb.AreaBuildInfo                      // 获取玩家主城的建筑列表pb
	GetPlayerBuildLvById(uid string, id int32) int32                            // 获取玩家某个建筑的等级
	GetPlayerBuildsSumLvById(uid string, id int32) int32                        // 获取玩家建筑总等级
	GetPlayerArmyDistArrayPb(uid string, withPawnsInfo bool) []*pb.AreaDistInfo // 获取玩家军队分布pb数组
	GetPlayerArmyHero(uid string, armyUid string) *PortrayalInfo                // 获取玩家军队中的英雄
	GetMaxPawnCountInArmy(uid string) int32                                     // 获取玩家军队中最多的士兵数
	UpdatePlayerAlliMemberEmbassyLv(alliUid string, uid string, lv int32)       // 刷新联盟成员大使馆等级
	ForgePlayerEquip(uid string, euid string, lockEffect int32) *EquipInfo      // 打造完成装备
	SmeltPlayerEquip(string, string, [][]int32) *EquipInfo                      // 融炼完成装备
	GetPlayerEquips(uid string) []*EquipInfo                                    // 获取玩家装备列表
	GetPlayerEquip(uid string, id int32) *EquipInfo                             // 获取某个装备
	GetPlayerEquipByUid(uid string, euid string) *EquipInfo                     // 获取某个装备
	GetPlayerHeroSlots(uid string) []*HeroSlotInfo                              // 获取玩家英雄槽位列表
	UpdatePlayerHeroAttr(uid string, id int32, attrs [][]int32) string          // 刷新玩家英雄属性
	UpdatePlayerPawnEquipInfo(uid string, euid string, attrs [][]int32)         // 刷新玩家装备属性
	ToPlayerConfigPawnMap(uid string) map[int32]*PawnConfigInfo                 // 获取玩家的士兵装备配置
	ToPlayerConfigPawnMapPb(uid string) map[int32]*pb.PawnConfigInfo            // 获取玩家的士兵装备配置
	GetFortAutoSupports(uid string) map[int32]bool                              // 获取玩家的要塞自动支援配置
	CheckPlayerHasTreasure(uid string) bool                                     // 检测是否有新的宝箱
	AddPlayerRodeleroCadet(uid string, val int32, maxLv int32)                  // 添加玩家剑盾兵见习勇士层数
	GetPlayerRodeleroCadetLv(uid string) int32
	RecordPlayerKillCount(uid string, id int32, count int32) // 记录玩家击杀数量
	GetPlayerIsGiveupGame(uid string) bool
	NotifyAreaCheckFrame(index int32, msg *pb.GAME_ONFSPCHECKFRAME_NOTIFY)
	TagUpdateDBByIndex(index int32)
	NotifyAreaUpdateInfo(index, ntype int32, data *pb.GAME_ONUPDATEAREAINFO_NOTIFY, ignores ...string)
	GetAreaBuildsByID(index, id int32) []Build
	CheckIsOneAlliance(a, b string) bool                                                     // 检测两个玩家是否同一个联盟
	GetPlayerAlliUid(uid string) string                                                      // 获取玩家所属联盟uid
	GetPlayerNickname(uid string) string                                                     // 获取玩家昵称
	GetPlayerLandCountByLv(uid string, lv int32) int32                                       // 获取玩家x级土地数量
	GetPlayerLandCountByType(uid string, tp int32) int32                                     // 获取玩家x级土地数量
	GetPlayerOwnedCityCountByID(uid string, id int32) int32                                  // 获取玩家的某个城市数量 不包含修建中
	GetPlayerHasResBuild(uid string) bool                                                    // 获取玩家是否拥有资源建筑
	GetPlayerMainIndex(uid string) int32                                                     // 获取玩家主城位置
	FindPlayerMainIndex(uid string) int32                                                    // 查找玩家的主城位置
	HasPlayer(uid string) bool                                                               // 是否有某个玩家
	GetAllianceNameByUID(uid string) string                                                  // 获取里联盟名字
	ChangePlayerStaminaAndAddScore(uid string, landLv, uiLv, val int32, isBattle bool) int32 // 扣除玩家奖励点
	GetLandLv(index int32) int32
	GetLandType(index int32) int32                // 获取地块类型
	RemovePawnLvingPawn(index int32, puid string) // 删除士兵练级
	RemovePawnByDeserter(uid string, count int)   // 删除士兵来之逃兵
	NewTempPawn(index int32, uid string, point *ut.Vec2, owner, armyUid string, id, lv int32) Pawn
	GetPlayerTowerLvByPawn(owner string, pawnId int32) int32   // 获取玩家的箭塔等级
	ChangeArmyDistIndex(owner string, uid string, index int32) // 切换军队位置
	CheckArmyMarchingByUID(uid string) bool                    // 检测军队是否在行军中
	ToPolicySlotMap(uid string) map[int32]*CeriSlotInfo        // 返回政策map信息用于db
	ToPolicys(uid string) map[int32]int32
	ToPolicysPb(uid string) map[int32]*pb.CeriSlotInfo
	GetPlayerPolicyEffectFloatByUid(uid string, tp int32) float64 // 获取玩家政策效果
	GetPlayerPolicyEffectIntByUid(uid string, tp int32) int32
	GetPlayerFightPolicyBuffs(uid string, defender string) [][]int32 // 获取玩家战斗政策buff
	GetPolicyLvById(uid string, policyId int32) int32
	GetPlayerAccTotalPawnCount(uid string) int32                                         // 获取玩家累计士兵数量
	GetPlayerTotalEquipRecastCount(uid string) int32                                     // 获取玩家一共重铸装备次数
	CheckPlayerHasMaxAttrEquip(uid string) bool                                          // 检测玩家是否有满属性装备
	GetPlayerMaxLandCount(uid string) int32                                              // 获取玩家最大拥有的地块数量
	GetPlayerMainBuildLv(uid string) int32                                               // 获取玩家主城建筑等级
	GetPlayerTreasureLostCount(uid string) int32                                         // 获取玩家宝箱损失补偿次数
	GetPlayerNoAvoidWar(uid string) bool                                                 // 获取玩家是否取消免战
	SetPlayerNoAvoidWar(uid string, noAvoidWar bool)                                     // 设置玩家是否取消免战
	AncientAvoidWarCheck()                                                               // 古城免战检测
	ToPlayerBattleRecordInfo(uid string) map[int32]int32                                 // 返回战斗记录信息
	SendPlayerHasTreasure(uid string)                                                    // 通知玩家有新宝箱
	RecordPlayerDistinctId(uid string, distinctId string)                                // 记录玩家的访客id
	GetPlayerDistinctId(uid string) string                                               // 获取玩家的访客id
	TriggerTask(uid string, condType, count, param int32)                                // 触发任务
	WorldPbRLock()                                                                       // 世界数据pb读锁
	WorldPbRUnLock()                                                                     // 世界数据pb释放读锁
	GetWorldPbCells() map[string]*pb.PlayerCellBytesInfo                                 // 获取世界pb数据的地块信息
	GetWorldPbCellsByUserId(uid string) *pb.PlayerCellBytesInfo                          // 获取世界pb数据中指定玩家的地块信息
	TagBattleSettling()                                                                  // 战斗结算中标记
	TagBattleSettleFinish()                                                              // 战斗结算结束标记
	GetPlayerAvoidWarReduce(uid string) int32                                            // 获取主城免战减少信息
	ToPlayerPawnDrillMap(uid string) map[int32]int32                                     // 返回玩家招募士兵信息
	ToPlayerPawnMaxLvMap(uid string) map[int32]int32                                     // 返回玩家满级士兵信息
	ToPlayerPolicyUseMap(uid string) map[int32]bool                                      // 返回本局使用过政策map
	ToPlayerEquipUseMap(uid string) map[int32]bool                                       // 返回本局使用过装备map
	ToPlayerKillRecordMap(uid string) map[int32]int32                                    // 返回本局击杀记录map
	ToPlayerCityOutputDB(uid string) map[int32][]*TypeObj                                // 返回玩家城市产出
	GetPlayerLanguage(uid string) string                                                 // 获取玩家使用语言
	SetPlayerLanguage(uid, lang string)                                                  // 设置玩家使用语言
	GetPlayerFCMToken(uid string) string                                                 // 获取玩家FCM令牌
	SetPlayerFCMToken(uid, token string)                                                 // 设置玩家FCM令牌
	OfflineNotify(uid string, msgType int32, params ...string)                           // 离线消息通知
	GetPlayerOfflineOpt(uid string) []int32                                              // 获取玩家离线通知设置
	SetPlayerOfflineOpt(uid string, opt []int32)                                         // 设置玩家离线通知设置
	RemoveAvoidWarArea(index int32)                                                      // 删除免战
	DelPlayerAvoidWar(uid string)                                                        // 删除玩家所有免战
	DirectOccupyCell(index int32, uid string, isAddScore bool)                           // 直接占领某块领地
	AddCheckPlayerOfflineMsg(uid string, msgType int32, endTime int64, params ...string) // 添加玩家离线消息检测
	RemoveCheckPlayerOfflineMsg(uid string, msgType int32, params ...string)             // 移除玩家离线消息检测
	ChangeCheckPlayerOfflineMsgTime(uid string, msgType, add int32, params ...string)    // 修改玩家离线消息检测时间
	UpdatePlayerCityOutput(uid string, count int32, init bool)                           // 刷新玩家的城市产出
	UpdatePlayerPawnHeroInfo(uid string, id int32, attrs [][]int32)                      // 刷新玩家士兵英雄属性
	UpdatePlayerHeroArmy(uid string, id int32, armyUid string)                           // 刷新玩家的英雄所属军队
	CleanGeneralAvoidWarArea()                                                           // 清理免战
	CreateAncientCities()                                                                // 创建古城
	NotifyAllAlliBaseInfo()                                                              // 通知盟主基础信息
	SetAncientLv(id, lv int32)                                                           // 直接设置古城等级
	GetAncientEffectFloatByPlayer(uid string, tp int32) float64                          // 获取遗迹效果根据玩家
	GetHasCreateAncient() bool                                                           // 获取是否已生成遗迹
	GetAncientHighestLv() int32                                                          // 获取遗迹最高等级
	UpdateSepctatorMainIndex(uid string, mainIndex int32)                                // 更新观战者主城
	GetPlayerTreasureAwardMul(uid string) float64                                        // 获取玩家的宝箱奖励倍数
	GetPlayerPawnTrackInfo(uid string) []map[string]interface{}                          // 获取玩家士兵上报信息
	AddBattlePassScore(uid string, score int32)                                          // 通知大厅服添加战令积分
	GetLandAttrConf(index int32, fighter string) map[string]interface{}                  // 获取指定地块配置
	InjuryPawnListClone(uid string) []*pb.InjuryPawnInfo                                 // 获取医馆受伤士兵列表
	AddInjuryPawn(uid, userId string, id, lv, skinId, cureCount int32)                   // 医馆添加受伤士兵
	GetPlayerTransitMerchantCount(uid string) int32                                      // 获取玩家运输中商人数量
	GetHospitalAbandomAcc(userId string) int32                                           // 获取医馆满员后累计放弃的士兵数量
	GetHospitalFullNoticeFlag(userId string) int32                                       // 获取医馆通知标记
	SetHospitalFullNoticeFlag(userId string, flag int32)                                 // 设置医馆通知标记
	GetHospitalNoticeClose(userId string) bool                                           // 获取医馆通知开关
	SetHospitalNoticeClose(userId string, isClose bool)                                  // 设置医馆通知开关
	GetPlayerFarmType(uid string) int32                                                  // 获取开荒方式
	GetPlayerResAcc(uid string) int64                                                    // 获取玩家总经济
	ChangePlayerResAcc(uid string, change int32)                                         // 玩家总经济变动
	CheckPolicySlotInfo(uid string, buildLv int32)                                       // 兼容检测政策槽位信息
	UpdatePolicySlotInfo(uid string, blv int32, init bool)                               // 刷新政策槽位信息
	GetPlayerIsSettled(uid string) bool
	GetCaptureNum() int32
	AddPolicySeasonItems()
	AllPlrActivePolicyAddItems(policyId int32)
	ToPawnCuringQueuePb(index int32) []*pb.PawnCuringInfo
	GetPawnCost(id, lv int32) []*TypeObj
	PutNotifyQueue(t int32, msg *pb.OnUpdateWorldInfoNotify)                                      // 添加到通知队列
	TaTrack(uid string, reCreateCount int32, eventName string, properties map[string]interface{}) // 数数上报
	TaUserSet(uid string, reCreateCount int32, properties map[string]interface{})
}

type Season interface {
	GetType() int
	GetEffectFloat(tp int) float64
	ChangeBaseResCost(list []*TypeObj, tp int) []*TypeObj
	ToPb() *pb.SeasonInfo
	Update(now int64)
	IsEnd() bool
	SetSeasonPolicy(season, id int32)
}

type Build interface {
	GetUID() string
	GetID() int32
	GetLV() int32
	GetBuildPawnId() int32
	GetUpCosts() []*TypeObj
	ToShortData() map[string]interface{}
	ToShortDataPb() *pb.AreaBuildInfo
	ToShortInfoPb() *pb.AreaBuildInfo
}

// 市场
type Bazaar interface {
	AddTradingRes(sell *TypeObj, buy *TypeObj, owner string, merchantCount int32, onlyAlli bool)
	RemoveTradingRes(uid string)
	Strip() map[string]interface{}
	GetTradingResCount() int
	CleanPlayerBazaarRes(uid string)
	ToTradingRessPb(plyUid string) []*pb.TradingRessInfo
	GetTradeCurPrice(sellType, buyType int32) float64
	TradePriceToPb() []*pb.TradePriceInfo
	ModifyTradePrice(sellType, buyType int32, price float64)
	GetTradingMerchantCount(uid string) int32 // 获取玩家市场中商人数量
}

// 记录
type Record interface {
	RemoveAllRecordByUID(uid string)
	GetArmyRecords(uid string, isBattle bool) []*pb.ArmyRecordInfo
	AddArmyMarchRecord(tp int32, owner string, armyUid string, armyName string, armyIndex, targetIndex int32)
	AddArmyBattleRecords(battleUid string, armyIdnex int32, datas []map[string]interface{})
	AddBattleRecord(uid string, index int32, beginTime, endTime int64, datas []*BattleRecordData, treasures []*TreasureInfo)
	GetBattleRecord(uid string) *pb.BattleRecordInfo
	GetPlayerBattleRecords(uid string) []*pb.BattleScoreRecord
	GetArmyBattleRecordsByUids(uids []string, battleUid string) []*pb.ArmyRecordInfo
	AddBazaarRecord(tp int8, owner, target string, res map[string]interface{}, names ...string)
	GetBazaarRecords(uid string) []*pb.BazaarRecordInfo
	AddBattleScoreRecords(recordUid string, startTime, endTime int64, alliUidMap map[string]string, deadInfoMap map[string][]*PawmDeadRecord,
		validUserInfoMap map[string]map[int32]int32, invalidUserInfoMap map[string]map[int32]int32, scoreMap map[string]float64,
		pawnStatisticMap map[string]map[int32]map[int32]int32, armyUidListMap map[string][]string, index int32, playerWinMap map[string]bool)
	GetBattleScoreRecordsByDate(uid, date, alli string) (*BattleScoreRecordData, bool)
	GetPlayerBattleScoreRecords(uid, date, alli string) (*BattleScoreRecordData, bool)
	AddPlayerBattleScoreRecord(data *BattleScoreRecordData)
	GetPlayerBattleScoreRecordsByDate(uid, date string) *BattleScoreRecordData
	GetAlliPlayersBattleScoreRecordsByDate(alliUid, date string) []*pb.BattleScoreRecordByDate
	BattleScoreRecordToPb(record BattleScoreRecordData) *pb.BattleScoreRecordByDate
}

// 玩家
type Player interface {
	GetUID() string
	IsOnline() bool
	SessionSendNR(topic string, body []byte)
	PutNotifyQueue(nType int, data *pb.OnUpdatePlayerInfoNotify)
	GetTempAreaInfo(index, land int32, monsterEquipMap map[int32]map[string]interface{}) map[string]interface{} // 获取临时的区域信息
	ChangeMerchantState(c, t, count int32)                                                                      // 改变商人状态
	ToMerchants() []map[string]interface{}
	ToMerchantsPb() []*pb.MerchantInfo
	ToItemByTypeObjsPb(tos []*TypeObj) *pb.UpdateOutPut
	UpdateOpSec(isNotify bool)                   // 刷新产量
	UpdatePolicyEffect(effectMap map[int32]bool) // 刷新政策效果
	ChangeCostByTypeObjOne(m *TypeObj, change int32) (val int32, add int32)
	NotifyHasTreasure(val bool) // 通知有新的宝箱
	Kick(tpe int)               // 强制踢下线
	SetAlliUid(uid string)
	GetOccupyLandCount(lv int32) int32
	CheckAndDeductCostByTypeObjOne(to *TypeObj) (b bool)
	AddAntiCheatScore(score int32) // 添加防作弊检测积分
}

// 士兵
type Pawn interface {
	GetEnterDir() int32
	GetID() int32
	GetUID() string
	GetLV() int32
	GetSkinID() int32
	GetViewID() int32
	IsMaxLv() bool
	IsFighting() bool
	GetPawnType() int32
	GetOwner() string
	GetArmyUid() string
	GetPoint() *ut.Vec2
	SetPoint(point *ut.Vec2)
	IsDie() bool
	GetCurHP() int32
	GetInitMaxHP() int32
	GetMaxHP() int32
	AmendMaxHp(ignoreBuffType int32) int32
	SetCurHP(val int32)
	SetMaxHP(val int32)
	InitAnger()
	GetCurAnger() int32
	GetMaxAnger() int32
	SetCurAnger(val int32)
	IsHasAnger() bool
	CleanAllBuffs()
	SetBuffsByDB(data interface{})
	BuffsLock()
	BuffsUnlock()
	BuffsRLock()
	BuffsRUnlock()
	GetBuffs() []*BuffObj
	BuffsClone() []*BuffObj
	AddBuff(buff *BuffObj)
	RemoveBuffByIndex(index int32)
	RemoveBuffByIndexNoLock(index int32)
	GetBaseJson() map[string]interface{}
	GetAttrJson() map[string]interface{}
	GetAttackRange() int32
	GetMoveRange() int32
	GetBehaviorId() int32
	GetAttack() int32
	GetInstabilityMaxAttack() int32
	AmendAttack(attack int32, ignoreBuffType int32) int32
	GetMoveVelocity() int32
	GetSkillByType(tp int32) PawnSkill
	GetActiveSkill() PawnSkill
	GetEquip() *EquipInfo
	GetEquipEffects() []*EquipEffectObj
	GetEquipEffectByType(tp int32) *EquipEffectObj
	GetAttackIndex() int32
	GetAttackSpeed() int32
	SetLv(lv int32)
	UpdateLv(lv int32)
	UpdateAttrByBattle(id int32, lv int32)
	GetRodeleroCadetLv() int32
	GetPetId() int32
	IsHero() bool
	IsBoss() bool
	GetPortrayal() *PortrayalInfo
	GetPortrayalID() int32
	GetPortrayalSkill() *PortrayalSkill
	CleanStrategyBuffs()
	AddStrategyBuff(strategy *StrategyObj)
	GetStrategyBuff(tp int32) *StrategyObj
	GetStrategyValue(tp int32) int32
	IsHasStrategys(tps ...int32) bool
	ToNotifyPb() *pb.AreaPawnInfo
	ToBuffsDB() []map[string]interface{}
	ToBuffsPb() []*pb.BuffInfo
}

// 士兵技能
type PawnSkill interface {
	InitAttackAnimTime(pawn Pawn)
	GetId() int32
	GetBaseId() int32
	GetLv() int32
	GetType() int32
	GetUseType() int32
	GetTarget() int32
	GetValue() float64
	GetParamsInt() int32
	GetParamsFloat64() float64
	GetParamsInts() []int32
	GetParamsFloats() []float64
	GetParamsString() string
	GetNeedAttackTime() int32
	GetNeedHitTime() int32
	GetBulletId() int32
	GetIntensifyType() int32
	IsExclusiveIntensify() bool
}

// 战斗控制
type BattleCtrl interface {
	GetRandom() *random.Random
	GetAreaSize() *ut.Vec2
	GetFighters() []IFighter
	GetFighter(uid string) IFighter
	GetFightersByPoint(x, y int32) []IFighter // 获取某个点的所有士兵
	GetFighterCountByPoint(point *ut.Vec2) int32
	GetFighterCountForCampByPoint(point *ut.Vec2, camp int32, reverse bool) int32 // 获取某个点位的士兵数量
	GetHeroFighters(skiilId, camp int32, ignoreUid string) []IFighter             // 获取某个英雄列表
	UpdateFighterPointMap()                                                       // 刷新点位
	RemoveDieFighters() string
	AddFighterBattleInfo(uid, owner string, tp, val int32) // 添加记录
	AddFighterBattleDamageInfo(attackerUID string, defender IFighter, damage int32)
	CheckHasFighter(x, y int32) bool
	CheckHasFighterById(x, y, id int32) bool
	CheckIsBattleArea(x, y int32) bool
	SearchDiaupPoint(point *ut.Vec2, points []*ut.Vec2) (*ut.Vec2, bool)
	SearchIdlePoint(point *ut.Vec2, rang int32) (*ut.Vec2, bool)
	CheckDashPoint(point *ut.Vec2, camp int32) bool
	GetAttackDamage(attacker IFighter, defender IFighter, data map[string]interface{}) (int32, int32, int32, bool)
	GetBaseAttackDamage(attacker IFighter, defender IFighter, data map[string]interface{}) int32
	OnHitBaseTrueDamage(attacker IFighter, defender IFighter, data map[string]interface{}) (int32, int32, int32)
	DoAttackAfter(attacker IFighter, defender IFighter, data map[string]interface{}) int32
	RemoveHasFighterPointByLast(paths []*ut.Vec2) []*ut.Vec2
	TriggerTaskAfterDamage(attacker IFighter, defender IFighter, damage int32, hpRatioBefore float64)
	GetLockMovePointFighter(point *ut.Vec2) *AttackMovePointLockInfo
	SetLockMovePointFighter(point *ut.Vec2, fighter IFighter, weight int64, movePoint *ut.Vec2) bool
	DelLockMovePointFighter(point *ut.Vec2) bool
	GetBowmanMovePaths(attacker IFighter) (*ut.Vec2, string)
	GetCurrentFrameIndex() int32
	AddPetPawn(uid string, id, lv int32, target *ut.Vec2, camp int32, owner string) IFighter
	AddNoncombat(uid string, id, lv int32, target *ut.Vec2, camp int32, owner string, params map[string]interface{})
	RemoveNoncombat(uid string)
	NormalizeVec2(point *ut.Vec2) *ut.Vec2
}

// 战斗者
type IFighter interface {
	MD5() string
	ToDB() *FigherStrip
	Strip() *FigherStrip
	ToPb() *pb.FighterInfo
	CheckBattleBeginTrigger()
	InitSkillAttackAnimTime()
	GetRoundCount() int32
	GetEnterDir() int32
	GetSearchDir(attackTarget IFighter) int32
	GetEntity() Pawn
	GetID() int32
	GetUID() string
	GetArmyUID() string
	GetLV() int32
	SetLV(val int32)
	UpdateLv(val int32)
	GetCamp() int32
	GetPoint() *ut.Vec2
	GetPoints() []*ut.Vec2
	SetPoint(point *ut.Vec2)
	GetLastPoint() *ut.Vec2
	GetSearchRange() *astar.SearchRange
	GetAstar() *astar.AStarRange
	GetCtrl() BattleCtrl
	GetAttackAnimTimes() [][]float64
	GetCanAttackTargets() []CanAttackTargetInfo
	GetCanAttackFighters() []IFighter
	GetCanAttackPawnByRange(fighters []IFighter, rang int32, cnt int32, ignoreUid string) []IFighter    // 只攻击士兵
	GetCanAttackFighterByRange(fighters []IFighter, rang int32, cnt int32, ignoreUid string) []IFighter // 包含军旗
	GetCanAttackRangeFighter(fighters []IFighter, rang int32, cnt int32, ignoreUid string, cb func(m IFighter) bool) []IFighter
	GetCanAttackRangeFighterByPoint(point *ut.Vec2, fighters []IFighter, rang int32, cnt int32, cb func(m IFighter) bool) []IFighter
	GetCanAttackRangeTargets(point *ut.Vec2, fighters []IFighter, rang int32, ignoreUid string, cb func(m IFighter) bool) ([]IFighter, []IFighter)
	GetTargetCanAttackPoints(target IFighter) []*ut.Vec2
	GetCanByReqelPoint(point *ut.Vec2, rang int32) *ut.Vec2
	SetBlackboard(id int32, key string, val interface{})
	GetBlackboardData(id int32, key string) interface{}
	SetBlackboardMap(val map[int32]map[string]interface{})
	ToBlackboardMap() map[int32]map[string]interface{}
	ToBlackboardMapPb() map[int32]*pb.BlackboardInfo
	CleanCanAttackTargets()
	CleanBlackboard(retainKeys ...string)
	GetAttrJson() map[string]interface{}
	GetBaseJson() map[string]interface{}
	GetPawnType() int32
	GetSkillByType(tp int32) PawnSkill
	GetActiveSkill() PawnSkill // 获取主动技能
	GetEquipEffects() []*EquipEffectObj
	GetEquipEffectByType(tp int32) *EquipEffectObj
	CheckPortrayalSkill(id int32) *PortrayalSkill
	GetPortrayalSkill() *PortrayalSkill
	GetPortrayalSkillID() int32
	GetAttack() int32                   // 获取攻击力
	GetActAttack() int32                // 获取实际攻击力
	GetIgnoreBuffAttack(tp int32) int32 // 获取忽略某个buff的攻击力
	GetInstabilityMaxAttack() int32
	GetInstabilityRandomAttack(index int32) int32
	AmendRestrainValue(val float64, tp int32) float64 // 修正克制数值
	AmendAttack(attack int32) int32
	GetAttackSpeed() int32
	GetAttackIndex() int32 // 获取攻击顺序
	SetAttackIndex(val int32)
	SetAttackCount(val int32)
	GetAttackCount() int32
	AddAttackCount(val int32)
	AddRoundCount(val int32)
	GetTempAttackIndex() int32 // 临时的出手顺序 因为有先后手的关系所以需要一个临时的用于计算
	IsDie() bool
	IsPawn() bool
	IsTower() bool
	IsBuild() bool
	IsNoncombat() bool
	IsFlag() bool
	IsHero() bool
	IsBoss() bool
	GetPetMaster() string
	HitPrepDamageHandle(damage, trueDamage int32) (int32, int32)
	OnHit(damage int32, baseDamage int32) (int32, int32, int32)
	OnHeal(val int32, suckbloodShield bool) int32
	GetHPRatio() float64
	IsFullHP() bool
	IsNotAddAngerByBuff() bool
	IsHasAnger() bool
	GetAngerRatio() float64
	GetCurAnger() int32  // 获取怒气
	AddAnger(v int32)    // 增加怒气
	DeductAnger(v int32) // 扣除怒气
	SetAnger(val int32)
	SetFullAnger(ratio float64, check bool) // 设置满怒气
	IsCanUseSkill() bool                    // 是否可以释放技能
	AddBuff(tp int32, provider string, lv int32) *BuffObj
	AddBuffValue(tp int32, provider string, value float64) *BuffObj
	IsHasNegativeBuff() bool
	IsHasBuff(tp int32) bool
	IsHasBuffs(types ...int32) bool
	GetBuff(tp int32) *BuffObj
	GetBuffValue(tp int32) float64
	GetBuffs() []*BuffObj
	GetBuffOrAdd(tp int32, provider string) *BuffObj
	CheckTriggerBuff(tp int32) *BuffObj
	CheckTriggerBuffOr(types ...int32) *BuffObj
	RemoveBuff(tp int32) bool
	RemoveMultiBuff(types ...int32)
	RemoveBuffByProvider(tp int32, provider string) bool
	RemoveShieldBuffs()
	RemoveAllDebuff()
	GetStrategyBuff(tp int32) *StrategyObj
	GetStrategyValue(tp int32) int32
	GetStrategyParams(tp int32) int32
	IsHasStrategys(tps ...int32) bool
	ChangeState(state int32)
	UpdateAttrByBattle(id, lv int32)
	UpdateStrategyEffect()
	GetAttackTarget() IFighter
	SetAttackTarget(val IFighter)
	ChangeAttackTarget(val IFighter) IFighter
	IsRoundEnd() bool
	EndAction()
	BeginAction()
	SetBattleOverDelay(val int32)
	UpdateBattleOver(val int32) bool
	IsBattleOver() bool
	UpdateBuff()
	BehaviorTick(dt int32)
	SetRoundEnd()
	AddRoundEndDelayTime(val int32)
	AddRoundEndActionTime(hitTime int32, sumTime int32)
	GetWaitRound() int32
	SetWaitRound(val int32)
	GetCDodgeCount() int32
	AddCDodgeCount()
	ResetCDodgeCount()
	GetFightPower(target IFighter) int32
	GetAttackRange() int32
	GetShieldVal() int32
	GetOwner() string
	GetCurHp() int32
	GetMaxHp() int32
	AmendMaxHp(ignoreBuffType int32) int32
	SetCamp(camp int32)
	SetTempRandomVal(val int32)
	GetTempRandomVal() int32
	GetSuckBloodValue() float64
	UpdateMaxHpRecord(maxHp int32)
	// 是否在攻击范围
	CheckInAttackRange(target IFighter, attackRange int32) bool
	CheckInMyAttackRange(target IFighter) bool
	// 获取最近距离
	GetMinDis(target IFighter) int32
	// GetAttackTargetWeight() int64
	// SetAttackTargetWeight(weight int64)
	// BeTargetedAddFighter(fighter IFighter) bool
	// BeTargetedRemoveFighter(fighter IFighter) bool
	// GetBeTargetedMinWeightFighter() IFighter
	// CheckTargetBeAttackedEnough() bool
}

// 可攻击的目标信息
type CanAttackTargetInfo struct {
	UID               string
	Point             *ut.Vec2
	Target            IFighter
	AttackTargetPoint *ut.Vec2   // 攻击所占的点位
	Paths             []*ut.Vec2 // 移动路径
	// Points            []*ut.Vec2 //没有任何士兵的位置

	Weight      int64 // 选择权重
	MoveWeight  int64 // 移动后对锁定点位的权重
	Dis         int32 // 距离
	AttackIndex int32 // 出手顺序
	IsRestrain  bool  // 是否克制
}

// 攻击移动点位锁定信息
type AttackMovePointLockInfo struct {
	Fighter   IFighter
	Weight    int64    // 移动后的权重
	MovePoint *ut.Vec2 // 移动后的点位
}
