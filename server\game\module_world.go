package game

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDWorld() {
	this.GetServer().RegisterGO("HD_GetAreaInfo", this.getAreaInfo)               //获取战场信息
	this.GetServer().RegisterGO("HD_GetCitySkins", this.getCitySkins)             //获取城市皮肤
	this.GetServer().RegisterGO("HD_GetMarchs", this.getMarchs)                   //获取行军信息
	this.GetServer().RegisterGO("HD_GetTransits", this.getTransits)               //获取运送信息
	this.GetServer().RegisterGO("HD_GetBattleDist", this.getBattleDist)           //获取当前地图战斗分布情况
	this.GetServer().RegisterGO("HD_GetAvoidWarDist", this.getAvoidWarDist)       //获取当前地图免战分布情况
	this.GetServer().RegisterGO("HD_GetTondenDist", this.getTondenDist)           //获取当前地图屯田分布情况
	this.GetServer().RegisterGO("HD_GetBTCityQueues", this.getBTCityQueues)       //获取当前修建城市列表
	this.GetServer().RegisterGO("HD_GetSelectArmys", this.getSelectArmys)         //获取选择军队列表
	this.GetServer().RegisterGO("HD_OccupyCell", this.occupyCell)                 //进攻
	this.GetServer().RegisterGO("HD_MoveCellArmy", this.moveCellArmy)             //移动军队
	this.GetServer().RegisterGO("HD_CellTonden", this.cellTonden)                 //屯田
	this.GetServer().RegisterGO("HD_CancelCellTonden", this.cancelCellTonden)     //取消屯田
	this.GetServer().RegisterGO("HD_CancelMarch", this.cancelMarch)               //取消行军
	this.GetServer().RegisterGO("HD_CreateCity", this.createCity)                 //修建城市
	this.GetServer().RegisterGO("HD_DismantleCity", this.dismantleCity)           //拆除城市
	this.GetServer().RegisterGO("HD_ChangeCitySkin", this.changeCitySkin)         //改变城市皮肤
	this.GetServer().RegisterGO("HD_SyncCellInfo", this.syncCellInfo)             //同步地块信息
	this.GetServer().RegisterGO("HD_SendCellEmoji", this.sendCellEmoji)           //发送地图表情
	this.GetServer().RegisterGO("HD_ClaimCityOutput", this.claimCityOutput)       //领取城市产出
	this.GetServer().RegisterGO("HD_ForceRevokeArmy", this.forceRevokeArmy)       //强制撤回军队
	this.GetServer().RegisterGO("HD_GetWorldRandomInfo", this.getWorldRandomInfo) //获取全局随机信息
	this.GetServer().RegisterGO("HD_GetWorldEvent", this.getWorldEvent)           //获取世界事件
}

// 获取战场信息
func (this *Game) getAreaInfo(session gate.Session, msg *pb.GAME_HD_GETAREAINFO_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := msg.GetIndex()
	if index < 0 || index >= int32(len(wld.Lands)) {
		return nil, ecode.UNKNOWN.String()
	}
	area := wld.GetArea(index)
	s2c := &pb.GAME_HD_GETAREAINFO_S2C{}
	if !wld.CheckLandCanOccupy(index) {
		return nil, ecode.UNKNOWN.String()
	} else if area == nil {
		s2c.Data = &pb.AreaInfo{Index: index}
		return pb.ProtoMarshal(s2c)
	}
	wld.RecordReqAreaPlayer(index, plr.Uid)
	if area.IsHasHp() { //玩家领地 或在战斗
		s2c.Data = area.ToPb()
	} else if area.IsAncient() { //遗迹
		s2c.Data = wld.GetAncientCityTempArea(area)
	} else {
		s2c.Data = wld.GetTempAreaByPlayerPb(plr.Uid, index) //这里获取玩家临时记录的
	}
	return pb.ProtoMarshal(s2c)
}

// 获取城市皮肤信息
func (this *Game) getCitySkins(session gate.Session, msg *pb.GAME_HD_GETCITYSKINS_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETCITYSKINS_S2C{
		CitySkins: wld.ToCitySkinsPb(),
	})
}

// 获取行军信息
func (this *Game) getMarchs(session gate.Session, msg *pb.GAME_HD_GETMARCHS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETMARCHS_S2C{
		List: wld.ToMarchsPb(plr.Uid),
	})
}

// 获取运送信息
func (this *Game) getTransits(session gate.Session, msg *pb.GAME_HD_GETTRANSITS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETTRANSITS_S2C{
		List: wld.ToTransitsPb(plr.MainCityIndex),
	})
}

// 获取地图上面的战斗分布情况
func (this *Game) getBattleDist(session gate.Session, msg *pb.GAME_HD_GETBATTLEDIST_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	wld.BattleDist.RLock()
	defer wld.BattleDist.RUnlock()
	return pb.ProtoMarshal(&pb.GAME_HD_GETBATTLEDIST_S2C{
		BattleDistMap: wld.ToBattleDistPb(),
	})
}

// 获取地图上面的免战分布情况
func (this *Game) getAvoidWarDist(session gate.Session, msg *pb.GAME_HD_GETAVOIDWARDIST_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETAVOIDWARDIST_S2C{
		AvoidWarAreas:  wld.ToAvoidWarAreasPb(),
		AvoidWarAreas2: wld.ToAvoidWarAreas2Pb(),
	})
}

// 获取地图屯田分布情况
func (this *Game) getTondenDist(session gate.Session, msg *pb.GAME_HD_GETTONDENDIST_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETTONDENDIST_S2C{
		Tondens: wld.ToCellTondenAreasPb(),
	})
}

// 获取当前修建城市列表
func (this *Game) getBTCityQueues(session gate.Session, msg *pb.GAME_HD_GETBTCITYQUEUES_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETBTCITYQUEUES_S2C{
		BtCityQueues: wld.ToBTCityQueuesPb(),
	})
}

// 获取选择的军队列表
func (this *Game) getSelectArmys(session gate.Session, msg *pb.GAME_HD_GETSELECTARMYS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := wld.AmendIndex(msg.GetIndex())
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	}
	tp := msg.GetType()
	var canGotoCount int32 //可前往这块地的数量
	if tp == 2 {           //攻占
		if !wld.CheckNotOccupyTimeCanOccupy(area, plr.Uid) {
			return nil, ecode.NOT_IN_OCCUPY_TIME.String() //固定时间段或有我方或盟友军队时才可攻击
		} else if wld.IsHasProtect(plr.Uid, index) {
			return nil, ecode.CELL_PROTECT.String() //地块保护中
		} else if wld.IsAvoidWarArea(area, plr.Uid) {
			return nil, ecode.AVOID_WAR_NOT_ATTACK.String() //对方免战中
		} else if !wld.CheckCanOccupyWildAncient(area, plr.Uid) {
			return nil, ecode.NOT_IN_OCCUPY_ANCIENT_TIME.String() //未在攻占无主遗迹时间段内
		}
	} else if tp == 3 { //屯田
		if area.Owner != plr.Uid {
			return nil, ecode.UNKNOWN.String() //只能在自己的地块屯田
		} else if area.IsBattle() {
			return nil, ecode.BATTLEING.String() //交战中无法屯田
		} else if wld.CheckCellBTCitying(index) {
			return nil, ecode.YET_BTING.String() //正在修建或拆除中
		} else if err, canGotoCount = wld.IsAreaFullArmyAndAddTimesByTonden(area, plr.Uid, ""); err != "" {
			return //军队是否满了 包含行军和增援
		}
		index = -1
	}
	// 屯田换一个判断方法 因为屯田里面有军队也可以选择
	if tp != 3 {
		if err, canGotoCount = wld.IsAreaFullArmyAndAddTimesByMove(area, plr.Uid, 1); err != "" {
			return //军队是否满了 包含行军和增援
		}
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETSELECTARMYS_S2C{
		List:         wld.GetPlayerArmysPb(plr.Uid, constant.GAT_NONE, index),
		CanGotoCount: canGotoCount,
	})
}

// 进攻
func (this *Game) occupyCell(session gate.Session, msg *pb.GAME_HD_OCCUPYCELL_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	indexs, uids, target := msg.GetIndexs(), msg.GetUids(), wld.AmendIndex(msg.GetTarget())
	armyCount := int32(len(uids))
	tarea := wld.GetArea(target)
	if tarea == nil || tarea.Owner == plr.Uid || int32(len(indexs)) != armyCount {
		return nil, ecode.UNKNOWN.String()
	} else if !wld.CheckCanOccupyCell(target, plr.Uid) {
		return nil, ecode.ONLY_ATTACK_ADJOIN_CELL.String() //没有在自己或联盟的范围内
	} else if !wld.CheckNotOccupyTimeCanOccupy(tarea, plr.Uid) {
		return nil, ecode.NOT_IN_OCCUPY_TIME.String() //固定时间段或有我方或盟友军队时才可攻击
	} else if wld.IsAvoidWarArea(tarea, plr.Uid) {
		return nil, ecode.AVOID_WAR_NOT_ATTACK.String() //对方免战中
	} else if wld.IsHasProtect(plr.Uid, target) {
		return nil, ecode.CELL_PROTECT.String() //地块保护中
	} else if !wld.CheckCanOccupyWildAncient(tarea, plr.Uid) {
		// 未在攻占无主遗迹时间段内
		return nil, ecode.NOT_IN_OCCUPY_ANCIENT_TIME.String()
	}
	count := wld.GetAreaFullArmyCount(tarea, plr.Uid)
	gotoCount, gotoCountAll, _ := wld.GetGoToAreaMarchCountAndAll(tarea, plr.Uid)
	if count >= 0 {
		return nil, ecode.AREA_FULL_ARMY.String()
	} else if gotoCount+armyCount > ut.AbsInt32(count) {
		return nil, ecode.MANY_GOTO_MARCH.String()
	} else if tarea.IsBattle() && gotoCountAll+armyCount > tarea.GetAttackerCanAddPawnTimes() {
		return nil, ecode.BATTLE_ARMY_ACC_LIMIT.String() //目标地块累计进攻军队上限
	} else if tarea.Owner == "" && !plr.AntiCheatScoreCheck() { //防作弊检测
		return nil, ecode.ANTI_CHEAT.String()
	}

	ArmyList := []*world.AreaArmy{}
	var marchMaxTime float64 // 行军最长耗时
	marchcd := wld.GetPlayerMarchCd(plr.Uid)
	// 军队检测
	for i, v := range indexs {
		index := wld.AmendIndex(ut.Int32(v))
		area := wld.GetArea(index)
		if area == nil {
			return nil, ecode.UNKNOWN.String()
		} else if !wld.CheckOccupyPlayerMinDis(area.GetIndex(), tarea) {
			return nil, ecode.OCCUPY_CELL_DIS_COND.String() //如果是玩家 需要在5格内
		} else if area.IsBattle() {
			return nil, ecode.BATTLEING.String()
		}
		uid := uids[i]
		army := area.GetArmyByUid(uid)
		if army == nil || army.Owner != plr.Uid {
			return nil, ecode.ARMY_NOT_EXIST.String()
		} else if army.IsCellTonden() { //屯田中
			return nil, ecode.CELL_TONDENING.String()
		} else if army.IsPawnDrilling() { //招募中
			return nil, ecode.UNKNOWN.String()
		} else if army.IsPawnCuring() { //治疗中
			return nil, ecode.UNKNOWN.String()
		} else if wld.CheckArmyMarchingByUID(army.Uid) {
			return nil, ecode.MARCHING.String()
		} else if wld.CheckArmyLving(army) {
			return nil, ecode.PAWN_LVING.String()
		} else if !wld.CheckOccupyPlayerMinDis(army.AIndex, tarea) {
			return nil, ecode.OCCUPY_CELL_DIS_COND.String() //如果是玩家 需要在5格内
		}

		ArmyList = append(ArmyList, army)
		if msg.GetIsSameSpeed() {
			// 计算行军时间
			index := wld.AmendIndex(ut.Int32(indexs[i]))
			speed := wld.GetPlayerMarchSeepUp(index, target, plr.MainCityIndex, false, tarea.IsCanUpSpeedMarchCity())
			costTime := wld.CalcArmyMarchTime(army, index, target, speed, marchcd)
			if marchMaxTime == 0 || costTime > marchMaxTime {
				marchMaxTime = costTime
			}
		}
	}

	// 处理行军
	for i, army := range ArmyList {
		index := wld.AmendIndex(ut.Int32(indexs[i]))
		if msg.GetIsSameSpeed() {
			// 同时抵达
			wld.AddMarchArmy(index, army, target, marchMaxTime, marchcd, false, false, false)
			continue
		}
		speed := wld.GetPlayerMarchSeepUp(index, target, plr.MainCityIndex, false, false)
		// 计算自动返回的位置
		army.AutoBackIndex = -1
		switch msg.GetAutoBackType() {
		case 1: //最近要塞或者主城
			army.AutoBackIndex = -2
		case 2: //主城
			army.AutoBackIndex = wld.GetPlayerMainIndex(army.Owner)
		case 3: //出发点
			army.AutoBackIndex = index
		}
		// 加入行军
		wld.AddMarchArmy(index, army, target, speed, marchcd, false, false, false)
	}
	return
}

// 移动
func (this *Game) moveCellArmy(session gate.Session, msg *pb.GAME_HD_MOVECELLARMY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	indexs, uids, target := msg.GetIndexs(), msg.GetUids(), wld.AmendIndex(msg.GetTarget())
	tarea := wld.GetArea(target)
	if tarea == nil {
		return nil, ecode.UNKNOWN.String()
	} else if !wld.CheckIsOneAlliance(tarea.Owner, plr.Uid) {
		return nil, ecode.ONLY_MOVE_SELF_CELL.String()
	} else if err, _ = wld.IsAreaFullArmyAndAddTimesByMove(tarea, plr.Uid, int32(len(uids))); err != "" {
		return //军队是否满了 包含行军和增援
	}

	ArmyList := []*world.AreaArmy{}
	var marchMaxTime float64 // 行军最长耗时
	marchcd := wld.GetPlayerMarchCd(plr.Uid)
	// 军队检测
	for i, v := range indexs {
		index := wld.AmendIndex(ut.Int32(v))
		area := wld.GetArea(index)
		if area == nil {
			return nil, ecode.UNKNOWN.String()
		} else if area.IsBattle() {
			return nil, ecode.BATTLEING.String()
		}
		uid := uids[i]
		army := area.GetArmyByUid(uid)
		if army == nil || army.Owner != plr.Uid {
			return nil, ecode.ARMY_NOT_EXIST.String()
		} else if army.IsCellTonden() { //屯田中
			return nil, ecode.CELL_TONDENING.String()
		} else if army.IsPawnDrilling() { //招募中
			return nil, ecode.UNKNOWN.String()
		} else if army.IsPawnCuring() { //治疗中
			return nil, ecode.UNKNOWN.String()
		} else if wld.CheckArmyMarchingByUID(army.Uid) {
			return nil, ecode.MARCHING.String()
		} else if wld.CheckArmyLving(army) {
			return nil, ecode.PAWN_LVING.String()
		} else if army.AIndex == target {
			return nil, ecode.UNKNOWN.String()
		}

		ArmyList = append(ArmyList, army)
		if msg.GetIsSameSpeed() {
			// 计算行军时间
			index := wld.AmendIndex(ut.Int32(indexs[i]))
			speed := wld.GetPlayerMarchSeepUp(index, target, plr.MainCityIndex, true, tarea.IsCanUpSpeedMarchCity())
			costTime := wld.CalcArmyMarchTime(army, index, target, speed, marchcd)
			if marchMaxTime == 0 || costTime > marchMaxTime {
				marchMaxTime = costTime
			}
		}
	}
	// 处理行军
	tareaIsCanUpSpeedMarchCity := tarea.IsCanUpSpeedMarchCity()
	for i, army := range ArmyList {
		index := wld.AmendIndex(ut.Int32(indexs[i]))
		if msg.GetIsSameSpeed() {
			// 同时抵达
			wld.AddMarchArmy(index, army, target, marchMaxTime, marchcd, false, false, false)
			continue
		}
		speed := wld.GetPlayerMarchSeepUp(index, target, plr.MainCityIndex, true, tareaIsCanUpSpeedMarchCity)
		army.AutoBackIndex = -1
		// 加入行军
		wld.AddMarchArmy(index, army, target, speed, marchcd, false, false, false)
	}
	return
}

// 屯田
func (this *Game) cellTonden(session gate.Session, msg *pb.GAME_HD_CELLTONDEN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if plr.IsCellTondenLimit() {
		return nil, ecode.CELL_TONDEN_LIMIT.String() // 当天次数上限
	}
	index, uid, target := wld.AmendIndex(msg.Index), msg.GetUid(), wld.AmendIndex(msg.GetTarget())
	tarea := wld.GetArea(target)
	if tarea == nil {
		return nil, ecode.UNKNOWN.String()
	} else if tarea.Owner != plr.Uid {
		return nil, ecode.ONLY_MOVE_SELF_CELL.String()
	} else if err, _ = wld.IsAreaFullArmyAndAddTimesByTonden(tarea, plr.Uid, uid); err != "" {
		return //军队是否满了 包含行军和增援
	} else if tarea.IsBattle() {
		return nil, ecode.BATTLEING.String() // 战斗中
	} else if !plr.AntiCheatScoreCheck() { //防作弊检测
		return nil, ecode.ANTI_CHEAT.String()
	}
	// 军队检测
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String()
	}
	army := area.GetArmyByUid(uid)
	if army == nil || army.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if army.IsPawnDrilling() { //是否在训练中
		return nil, ecode.UNKNOWN.String()
	} else if army.IsPawnCuring() { //是否在治疗中
		return nil, ecode.UNKNOWN.String()
	} else if wld.CheckArmyMarchingByUID(army.Uid) {
		return nil, ecode.MARCHING.String()
	} else if wld.CheckArmyLving(army) {
		return nil, ecode.PAWN_LVING.String()
	} else if army.IsCellTonden() { //正在屯田中
		return nil, ecode.CELL_TONDENING.String()
	}

	json := wld.GetLandAttrConf(index, plr.Uid)
	if json == nil {
		return nil, ecode.UNKNOWN.String()
	} else if plr.Stamina < ut.Int32(json["need_stamina"]) { //奖励点不足
		return nil, ecode.STAMINA_NOT_ENOUGH.String()
	}

	if index == target {
		// 军队就在屯田的位置 不用行军
		wld.AddCellTonden(index, plr.Uid, uid)
	} else {
		// 处理行军
		speed := wld.GetPlayerMarchSeepUp(index, target, plr.MainCityIndex, false, false)
		army.AutoBackIndex = -1
		marchcd := wld.GetPlayerMarchCd(plr.Uid)
		// 加入行军
		wld.AddMarchArmy(index, army, target, speed, marchcd, false, false, true)
	}

	return pb.ProtoMarshal(&pb.GAME_HD_CELLTONDEN_S2C{})
}

// 取消屯田
func (this *Game) cancelCellTonden(session gate.Session, msg *pb.GAME_HD_CANCELCELLTONDEN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}

	index := wld.AmendIndex(msg.Index)
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String()
	} else if area.Owner != plr.Uid {
		return nil, ecode.UNKNOWN.String()
	}
	wld.RemoveCellTonden(index)

	return pb.ProtoMarshal(&pb.GAME_HD_CANCELCELLTONDEN_S2C{})
}

// 取消行军
func (this *Game) cancelMarch(session gate.Session, msg *pb.GAME_HD_CANCELMARCH_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	uid := msg.GetUid()
	march := wld.GetMarchByUID(uid)
	if march == nil {
		return nil, ecode.MARCH_NOT_EXIST.String()
	} else if march.Owner != plr.Uid {
		return nil, ecode.MARCH_NOT_EXIST.String()
	} else if march.AutoRevoke { //如果是自动遣返的不得取消
		return nil, ecode.MARCH_NOT_EXIST.String()
	} else if march.ArmyIndex == march.TargetIndex { //已经取消了 不能再取消
		return nil, ecode.MARCH_NOT_EXIST.String()
	} else if cell := wld.GetCell(march.StartIndex); cell == nil || !wld.CheckIsOneAlliance(cell.Owner, plr.Uid) { //起点不是自己的领地或联盟的领地不可以撤回
		return nil, ecode.MARCH_NOT_EXIST.String()
	} else if march.ForceRevoke { //强制撤回不能取消
		return nil, ecode.MARCH_NOT_EXIST.String()
	}
	sArea := wld.GetArea(march.ArmyIndex)
	if sArea == nil {
		return nil, ecode.MARCH_NOT_EXIST.String()
	}
	if sArea.IsBattle() {
		_, gotoCountAll, isFriend := wld.GetGoToAreaMarchCountAndAll(sArea, plr.Uid)
		canAddPawnTimes := sArea.GetDefenderCanAddPawnTimes()
		if !isFriend {
			canAddPawnTimes = sArea.GetAttackerCanAddPawnTimes()
		}
		if gotoCountAll >= canAddPawnTimes {
			return nil, ecode.BATTLE_ARMY_ACC_LIMIT.String() //该地块累计防守军队上限
		}
	}
	army := sArea.GetArmyByUid(march.ArmyUid)
	if army == nil || army.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	}
	army.AutoBackIndex = -1
	// 取消
	wld.CancelMarchArmy(march, false)
	return
}

// 强制撤回军队
func (this *Game) forceRevokeArmy(session gate.Session, msg *pb.GAME_HD_FORCEREVOKEARMY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	uid := msg.GetUid()
	mainCityIndex := wld.GetPlayerMainIndex(plr.Uid)
	area := wld.GetArea(mainCityIndex)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	}
	// 该军队是否在行军中
	isMarching := wld.CheckArmyMarchingByUID(uid)
	if isMarching {
		// 在行军中则撤回该行军
		march := wld.GetMarchByArmyUID(uid)
		if march == nil || march.ForceRevoke {
			return nil, ecode.MARCH_NOT_EXIST.String()
		}
		if march.Owner == plr.Uid || !wld.CheckIsOneAlliance(plr.Uid, march.Owner) {
			// 只能撤回盟友的军队
			return nil, ecode.MARCH_NOT_EXIST.String()
		}
		wld.ForceRevokeMarchArmy(march, mainCityIndex)
	} else {
		if area.IsBattle() {
			// 战斗中不能撤离
			return nil, ecode.BATTLEING.String()
		}
		// 获取军队
		army := area.GetArmyByUid(uid)
		if army == nil {
			return nil, ecode.ARMY_NOT_EXIST.String()
		}
		if army.Owner == plr.Uid || !wld.CheckIsOneAlliance(plr.Uid, army.Owner) {
			// 只能撤回盟友的军队
			return nil, ecode.ARMY_NOT_EXIST.String()
		}
		// 强制撤回该军队
		wld.ForceRevokeArmyToEmptyArea(area, army)
	}
	return
}

// 修建城市
func (this *Game) createCity(session gate.Session, msg *pb.GAME_HD_CREATECITY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, id := msg.GetIndex(), msg.GetId()
	if id >= constant.CHANGAN_CITY_ID && id <= constant.LUOYANG_CITY_ID {
		// 不能修建古城
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	cell := wld.GetCell(index)
	if cell == nil || cell.Owner != plr.Uid || cell.CityId != 0 {
		return nil, ecode.UNKNOWN.String()
	} else if wld.CheckCellBTCitying(index) { //正在修建中
		return nil, ecode.YET_BTING.String()
	} else if wld.IsCellTondenArea(index) { //屯田中
		return nil, ecode.CELL_TONDENING.String()
	}
	json := config.GetJsonData("city", id)
	if json == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if landType, needLand := wld.GetLandType(index), ut.Int32(json["need_land"]); needLand > 0 && needLand != landType {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if !plr.CheckCTypesNeedArea(g.StringToTypeObjs(json["bt_cond"]), plr.MainCityIndex) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	}
	count := wld.GetPlayerCityCountByID(plr.Uid, id)
	btCount := ut.Int32(json["bt_count"]) + plr.GetPolicyEffectInt(effect.CITY_COUNT_LIMIT)
	if count >= btCount {
		return nil, ecode.EXCEED_BT_COUNT.String() //超出修建数量
	} else if !plr.CheckAndDeductCostByTypeObjs(g.StringToTypeObjs(json["bt_cost"])) { // 扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	cd := 1.0 - plr.GetPolicyEffectFloat(effect.CITY_BUILD_CD)*0.01
	// 如果是要塞再看是否有加速修建要塞的政策
	time := int32(ut.Float64(json["bt_time"]) * 1000.0 * cd / float64(slg.GetBuildSpeedUp()))
	// 加入队列
	wld.PutBTCityQueue(index, id, ut.MaxInt32(1000, time))
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CREATECITY_S2C{
		Output: plr.ToOutputInfoPb(),
	})
}

// 拆除城市
func (this *Game) dismantleCity(session gate.Session, msg *pb.GAME_HD_DISMANTLECITY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := msg.GetIndex()
	cell := wld.GetCell(index)
	if cell == nil || cell.Owner != plr.Uid || cell.CityId == 0 {
		return nil, ecode.UNKNOWN.String()
	} else if wld.CheckCellBTCitying(index) { //正在拆除中
		return nil, ecode.YET_DISMANTLEING.String()
	}
	if cell.IsAncient() {
		// 不能拆除古城
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	json := config.GetJsonData("city", cell.CityId)
	if json == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	// 清理这里的产出
	wld.DelPlayerCityOutputByIndex(plr.Uid, index, false)
	cd := 1.0 - plr.GetPolicyEffectFloat(effect.CITY_BUILD_CD)*0.01
	// 加入队列
	time := int32(ut.Float64(json["dt_time"]) * 1000.0 * cd / float64(slg.GetBuildSpeedUp()))
	wld.PutBTCityQueue(index, 0, ut.MaxInt32(1000, time))
	return
}

// 改变城市皮肤
func (this *Game) changeCitySkin(session gate.Session, msg *pb.GAME_HD_CHANGECITYSKIN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, skinId := msg.GetIndex(), msg.GetSkinId()
	cell := wld.GetCell(index)
	if cell == nil || cell.Owner != plr.Uid || cell.CityId == 0 || cell.CityId == skinId || cell.IsAncient() {
		return nil, ecode.UNKNOWN.String()
	} else if wld.CheckCellBTCitying(index) { //正在拆除中
		return nil, ecode.YET_DISMANTLEING.String()
	} else if _, err = this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHECK_USE_CITY_SKIN, plr.Uid, cell.CityId, skinId); err != "" {
		return //检测是否存在
	}
	wld.UpdateCitySkin(index, skinId)
	return
}

// 同步地块信息
func (this *Game) syncCellInfo(session gate.Session, msg *pb.GAME_HD_SYNCCELLINFO_C2S) (ret []byte, err string) {
	e, room, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index := msg.GetIndex()
	cell := wld.GetCell(index)
	if cell == nil {
		return nil, ecode.UNKNOWN.String()
	}
	cells := []*pb.CellInfo{wld.ToCellPb(cell)}
	indexs := cell.GetOwnIndexs(room.GetMapSize())
	for _, idx := range indexs {
		if idx == index {
			continue
		} else if c := wld.GetCell(index); c != nil {
			cells = append(cells, wld.ToCellPb(c))
		}
	}
	return pb.ProtoMarshal(&pb.GAME_HD_SYNCCELLINFO_S2C{
		Cells: cells,
	})
}

// 发送地块表情
func (this *Game) sendCellEmoji(session gate.Session, msg *pb.GAME_HD_SENDCELLEMOJI_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	emoji, index := msg.GetEmoji(), msg.GetIndex()
	if landType := wld.GetLandType(index); landType < 3 {
		return nil, ecode.UNKNOWN.String()
	}
	json := config.GetJsonData("chatEmoji", emoji)
	if json == nil {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	}
	emojiType := ut.Int(json["type"])
	if emojiType == 1 || ut.Int(json["use_map"]) == 0 {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String() //不能发送普通表情
	}
	// 检测是否可以发
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.UNKNOWN.String()
	} else if emojiType == 2 && area.Owner != plr.Uid {
		return nil, ecode.ONLY_MY_CELL_SEND_EMOJI.String() //只能在自己的领地发送动态表情
	} else if ut.Int(json["cond"]) == 0 {
	} else if _, e := this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHECK_HAS_CHAT_EMOJI, plr.Uid, emoji); e != "" {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String() //检测表情是否存在
	}
	info := &pb.CellEmojiInfo{
		Uid:   plr.Uid,
		Index: index,
		Emoji: emoji,
	}
	// 通知
	if emojiType != 3 { //所有人可见
		wld.PutNotifyQueue(constant.NQ_CELL_EMOJI, &pb.OnUpdateWorldInfoNotify{Data_61: info})
	} else if alli := wld.GetAlliance(plr.AllianceUid); alli != nil { //盟友可见
		room.PutNotifyAllPlayersQueuePb("game/OnCellEmoji", &pb.GAME_ONCELLEMOJI_NOTIFY{Info: info}, alli.GetMemberUids())
	}
	return pb.ProtoMarshal(&pb.GAME_HD_SENDCELLEMOJI_S2C{})
}

// 领取城市产出
func (this *Game) claimCityOutput(session gate.Session, msg *pb.GAME_HD_CLAIMCITYOUTPUT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	tplr := wld.GetTempPlayer(plr.Uid)
	if tplr == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	index := msg.GetIndex()
	items := tplr.CityOutputMap.Get(index)
	if items == nil {
		return nil, ecode.CANNOT_CLAIM.String() //没有可领取的奖励
	}
	// 这里直接领取了
	plr.ChangeCostByTypeObjs(items, 1)
	// 删掉这个位置的奖励
	wld.DelPlayerCityOutput(tplr, index, true)
	return pb.ProtoMarshal(&pb.GAME_HD_CLAIMCITYOUTPUT_S2C{
		Rewards: plr.ToItemByTypeObjsPb(items),
	})
}

// 获取全局随机信息
func (this *Game) getWorldRandomInfo(session gate.Session, msg *pb.GAME_HD_GETWORLDRANDOMINFO_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	s2c := &pb.GAME_HD_GETWORLDRANDOMINFO_S2C{
		ExclusiveMap: map[int32]*pb.Int32ArrayInfo{},
		PawnCostMap:  map[int32]int32{},
	}
	for id, arr := range wld.ExclusiveEffectMap {
		s2c.ExclusiveMap[id] = &pb.Int32ArrayInfo{Arr: arr}
	}
	for k, v := range wld.PawnCostMap {
		s2c.PawnCostMap[k] = v
	}
	return pb.ProtoMarshal(s2c)
}

// 获取世界事件
func (this *Game) getWorldEvent(session gate.Session, msg *pb.GAME_HD_GETWORLDEVENT_C2S) (ret []byte, err string) {
	e, _, _, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GETWORLDEVENT_S2C{EventMap: wld.WorldEventMap.Clone()})
}
