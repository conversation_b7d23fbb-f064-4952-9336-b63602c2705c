package g

import ut "slgsrv/utils"

// 通用效果对象
type EffectObj struct {
	Type  int32
	Value float64
	Param float64
}

func NewEffectObj(t int32, value float64, param float64) *EffectObj {
	return &EffectObj{
		Type:  t,
		Value: value,
		Param: param,
	}
}

func (this *EffectObj) String() string {
	return "(" + ut.Itoa(this.Type) + "," + ut.Itoa(this.Value) + ")"
}

func (this *EffectObj) Clone() *EffectObj {
	return NewEffectObj(this.Type, this.Value, this.Param)
}
