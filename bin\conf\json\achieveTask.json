[{"id": 10010001, "cond": "1006,0,1", "prev_id": 0, "type": 1, "title_id": 101001, "show_progress": 1, "desc": "taskText.2001", "params": 1}, {"id": 10010002, "cond": "1006,0,2", "prev_id": 10010001, "type": 1, "title_id": 101002, "show_progress": 1, "desc": "taskText.2001", "params": 2}, {"id": 10010003, "cond": "1006,0,3", "prev_id": 10010002, "type": 1, "title_id": 101003, "show_progress": 1, "desc": "taskText.2001", "params": 3}, {"id": 10010004, "cond": "1006,0,4", "prev_id": 10010003, "type": 1, "title_id": 101004, "show_progress": 1, "desc": "taskText.2001", "params": 4}, {"id": 10010005, "cond": "1006,0,5", "prev_id": 10010004, "type": 1, "title_id": 101005, "show_progress": 1, "desc": "taskText.2001", "params": 5}, {"id": 10010006, "cond": "1006,0,6", "prev_id": 10010005, "type": 1, "title_id": 101006, "show_progress": 1, "desc": "taskText.2001", "params": 6}, {"id": 10010007, "cond": "1006,0,7", "prev_id": 10010006, "type": 1, "title_id": 101007, "show_progress": 1, "desc": "taskText.2001", "params": 7}, {"id": 10010008, "cond": "1006,0,8", "prev_id": 10010007, "type": 1, "title_id": 101008, "show_progress": 1, "desc": "taskText.2001", "params": 8}, {"id": 10010009, "cond": "1006,0,9", "prev_id": 10010008, "type": 1, "title_id": 101009, "show_progress": 1, "desc": "taskText.2001", "params": 9}, {"id": 10010010, "cond": "1006,0,10", "prev_id": 10010009, "type": 1, "title_id": 101010, "show_progress": 1, "desc": "taskText.2001", "params": 10}, {"id": 10010011, "cond": "1006,0,11", "prev_id": 10010010, "type": 1, "title_id": 101011, "show_progress": 1, "desc": "taskText.2001", "params": 11}, {"id": 10010012, "cond": "1006,0,12", "prev_id": 10010011, "type": 1, "title_id": 101012, "show_progress": 1, "desc": "taskText.2001", "params": 12}, {"id": 10010013, "cond": "1006,0,13", "prev_id": 10010012, "type": 1, "title_id": 101013, "show_progress": 1, "desc": "taskText.2001", "params": 13}, {"id": 10010014, "cond": "1006,0,14", "prev_id": 10010013, "type": 1, "title_id": 101014, "show_progress": 1, "desc": "taskText.2001", "params": 14}, {"id": 10010015, "cond": "1006,0,15", "prev_id": 10010014, "type": 1, "title_id": 101015, "show_progress": 1, "desc": "taskText.2001", "params": 15}, {"id": 10010016, "cond": "1006,0,16", "prev_id": 10010015, "type": 1, "title_id": 101016, "show_progress": 1, "desc": "taskText.2001", "params": 16}, {"id": 10010017, "cond": "1006,0,17", "prev_id": 10010016, "type": 1, "title_id": 101017, "show_progress": 1, "desc": "taskText.2001", "params": 17}, {"id": 10010018, "cond": "1006,0,18", "prev_id": 10010017, "type": 1, "title_id": 101018, "show_progress": 1, "desc": "taskText.2001", "params": 18}, {"id": 10010019, "cond": "1006,0,19", "prev_id": 10010018, "type": 1, "title_id": 101019, "show_progress": 1, "desc": "taskText.2001", "params": 19}, {"id": 10010020, "cond": "1006,0,20", "prev_id": 10010019, "type": 1, "title_id": 101020, "show_progress": 1, "desc": "taskText.2001", "params": 20}, {"id": 10010021, "cond": "1006,0,21", "prev_id": 10010020, "type": 1, "title_id": 101021, "show_progress": 1, "desc": "taskText.2001", "params": 21}, {"id": 10010022, "cond": "1006,0,22", "prev_id": 10010021, "type": 1, "title_id": 101022, "show_progress": 1, "desc": "taskText.2001", "params": 22}, {"id": 10010023, "cond": "1006,0,23", "prev_id": 10010022, "type": 1, "title_id": 101023, "show_progress": 1, "desc": "taskText.2001", "params": 23}, {"id": 10010024, "cond": "1006,0,24", "prev_id": 10010023, "type": 1, "title_id": 101024, "show_progress": 1, "desc": "taskText.2001", "params": 24}, {"id": 10010025, "cond": "1006,0,25", "prev_id": 10010024, "type": 1, "title_id": 101025, "show_progress": 1, "desc": "taskText.2001", "params": 25}, {"id": 10010026, "cond": "1006,0,26", "prev_id": 10010025, "type": 1, "title_id": 101026, "show_progress": 1, "desc": "taskText.2001", "params": 26}, {"id": 10010027, "cond": "1006,0,27", "prev_id": 10010026, "type": 1, "title_id": 101027, "show_progress": 1, "desc": "taskText.2001", "params": 27}, {"id": 10010028, "cond": "1006,0,28", "prev_id": 10010027, "type": 1, "title_id": 101028, "show_progress": 1, "desc": "taskText.2001", "params": 28}, {"id": 10010029, "cond": "1006,0,29", "prev_id": 10010028, "type": 1, "title_id": 101029, "show_progress": 1, "desc": "taskText.2001", "params": 29}, {"id": 10010030, "cond": "1006,0,30", "prev_id": 10010029, "type": 1, "title_id": 101030, "show_progress": 1, "desc": "taskText.2001", "params": 30}, {"id": 10010031, "cond": "1006,0,31", "prev_id": 10010030, "type": 1, "title_id": 101031, "show_progress": 1, "desc": "taskText.2001", "params": 31}, {"id": 10010032, "cond": "1006,0,32", "prev_id": 10010031, "type": 1, "title_id": 101032, "show_progress": 1, "desc": "taskText.2001", "params": 32}, {"id": 10010033, "cond": "1006,0,33", "prev_id": 10010032, "type": 1, "title_id": 101033, "show_progress": 1, "desc": "taskText.2001", "params": 33}, {"id": 10010034, "cond": "1006,0,34", "prev_id": 10010033, "type": 1, "title_id": 101034, "show_progress": 1, "desc": "taskText.2001", "params": 34}, {"id": 10010035, "cond": "1006,0,35", "prev_id": 10010034, "type": 1, "title_id": 101035, "show_progress": 1, "desc": "taskText.2001", "params": 35}, {"id": 10010036, "cond": "1006,0,36", "prev_id": 10010035, "type": 1, "title_id": 101036, "show_progress": 1, "desc": "taskText.2001", "params": 36}, {"id": 10010037, "cond": "1006,0,37", "prev_id": 10010036, "type": 1, "title_id": 101037, "show_progress": 1, "desc": "taskText.2001", "params": 37}, {"id": 10010038, "cond": "1006,0,38", "prev_id": 10010037, "type": 1, "title_id": 101038, "show_progress": 1, "desc": "taskText.2001", "params": 38}, {"id": 10010039, "cond": "1006,0,39", "prev_id": 10010038, "type": 1, "title_id": 101039, "show_progress": 1, "desc": "taskText.2001", "params": 39}, {"id": 10010040, "cond": "1006,0,40", "prev_id": 10010039, "type": 1, "title_id": 101040, "show_progress": 1, "desc": "taskText.2001", "params": 40}, {"id": 10010041, "cond": "1006,0,41", "prev_id": 10010040, "type": 1, "title_id": 101041, "show_progress": 1, "desc": "taskText.2001", "params": 41}, {"id": 10010042, "cond": "1006,0,42", "prev_id": 10010041, "type": 1, "title_id": 101042, "show_progress": 1, "desc": "taskText.2001", "params": 42}, {"id": 10010043, "cond": "1006,0,43", "prev_id": 10010042, "type": 1, "title_id": 101043, "show_progress": 1, "desc": "taskText.2001", "params": 43}, {"id": 10010044, "cond": "1006,0,44", "prev_id": 10010043, "type": 1, "title_id": 101044, "show_progress": 1, "desc": "taskText.2001", "params": 44}, {"id": 10010045, "cond": "1006,0,45", "prev_id": 10010044, "type": 1, "title_id": 101045, "show_progress": 1, "desc": "taskText.2001", "params": 45}, {"id": 10010046, "cond": "1006,0,46", "prev_id": 10010045, "type": 1, "title_id": 101046, "show_progress": 1, "desc": "taskText.2001", "params": 46}, {"id": 10010047, "cond": "1006,0,47", "prev_id": 10010046, "type": 1, "title_id": 101047, "show_progress": 1, "desc": "taskText.2001", "params": 47}, {"id": 10010048, "cond": "1006,0,48", "prev_id": 10010047, "type": 1, "title_id": 101048, "show_progress": 1, "desc": "taskText.2001", "params": 48}, {"id": 10010049, "cond": "1006,0,49", "prev_id": 10010048, "type": 1, "title_id": 101049, "show_progress": 1, "desc": "taskText.2001", "params": 49}, {"id": 10010050, "cond": "1006,0,50", "prev_id": 10010049, "type": 1, "title_id": 101050, "show_progress": 1, "desc": "taskText.2001", "params": 50}, {"id": 10020001, "cond": "1007,120,2000", "prev_id": 0, "type": 2, "title_id": 102001, "show_progress": 0, "desc": "taskText.2002", "params": "120|2000"}, {"id": 10030001, "cond": "1008,0,100", "prev_id": 0, "type": 3, "title_id": 103001, "show_progress": 1, "desc": "taskText.2003", "params": "100"}, {"id": 10030002, "cond": "1008,0,1000", "prev_id": 10030001, "type": 3, "title_id": 103002, "show_progress": 1, "desc": "taskText.2003", "params": "1000"}, {"id": 10030003, "cond": "1040,0,10000", "prev_id": 0, "type": 3, "title_id": 103003, "show_progress": 1, "desc": "taskText.2015", "params": "10000"}, {"id": 10050001, "cond": "1011,0,1", "prev_id": 0, "type": 5, "title_id": 105001, "show_progress": 0, "desc": "taskText.2006", "params": ""}, {"id": 10060001, "cond": "1012,0,100", "prev_id": 0, "type": 6, "title_id": 106001, "show_progress": 0, "desc": "taskText.2007", "params": "100"}, {"id": 10070001, "cond": "1013,0,1", "prev_id": 0, "type": 7, "title_id": 107001, "show_progress": 0, "desc": "taskText.2008", "params": ""}, {"id": 10080001, "cond": "1014,0,1", "prev_id": 0, "type": 8, "title_id": 108001, "show_progress": 0, "desc": "taskText.2009", "params": ""}, {"id": 10090001, "cond": "1015,0,1", "prev_id": 0, "type": 9, "title_id": 109001, "show_progress": 0, "desc": "taskText.2010", "params": ""}, {"id": 10110001, "cond": "1029,0,1", "prev_id": 0, "type": 11, "title_id": 111001, "show_progress": 0, "desc": "taskText.2012", "params": ""}, {"id": 10140001, "cond": "0,0,1", "prev_id": 0, "type": 14, "title_id": 114001, "show_progress": 0, "desc": "taskText.2016", "params": ""}]