package player

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 检测资源是否满足
func (this *Model) CheckCostByTypeObjs(tos []*g.TypeObj) bool {
	for _, to := range tos {
		if !this.CheckCostByTypeObjOne(to) {
			return false
		}
	}
	return true
}
func (this *Model) CheckCostByTypeObjOne(m *g.TypeObj) bool {
	switch m.Type {
	case ctype.CEREAL:
		return this.Cereal.Value >= m.Count
	case ctype.TIMBER:
		return this.Timber.Value >= m.Count
	case ctype.STONE:
		return this.Stone.Value >= m.Count
	case ctype.BASE_RES:
		return this.Cereal.Value >= m.Count && this.Timber.Value >= m.Count && this.Stone.Value >= m.Count
	case ctype.EXP_BOOK: //经验书
		return this.ExpBook >= m.Count
	case ctype.CEREAL_C: //粮耗
		return int32(this.Cereal.opHour) >= m.Count
	case ctype.IRON: //铁
		return this.Iron >= m.Count
	case ctype.UP_SCROLL: //卷轴
		return this.UpScroll >= m.Count
	case ctype.FIXATOR: //固定器
		return this.Fixator >= m.Count
	}
	return false
}

// 检测通用条件 需要区域
func (this *Model) CheckCTypesNeedArea(cts []*g.TypeObj, index int32) bool {
	for _, ct := range cts {
		if !this.CheckCTypeOneNeedArea(ct, index) {
			return false
		}
	}
	return true
}
func (this *Model) CheckCTypes(cts []*g.TypeObj) bool {
	return this.CheckCTypesNeedArea(cts, this.MainCityIndex)
}

// 检测单个
func (this *Model) CheckCTypeOneNeedArea(ct *g.TypeObj, index int32) bool {
	switch ct.Type {
	case ctype.CEREAL, ctype.TIMBER, ctype.STONE, ctype.BASE_RES, ctype.GOLD, ctype.EXP_BOOK, ctype.IRON, ctype.UP_SCROLL, ctype.FIXATOR:
		return this.CheckCostByTypeObjOne(ct)
	case ctype.BUILD_LV: //建筑等级
		ids, builds := ct.GetIds(), []g.Build{}
		for _, id := range ids {
			builds = append(builds, this.room.GetWorld().GetAreaBuildsByID(index, id)...)
		}
		return array.Some(builds, func(m g.Build) bool { return m.GetLV() >= ct.Count })
	case ctype.CELL_COUNT: //领地数
		return ct.Count <= this.room.GetWorld().GetPlayerLandCount(this.Uid, ct.Id == 1)
	}
	return false
}
func (this *Model) CheckCTypeOne(ct *g.TypeObj) bool {
	return this.CheckCTypeOneNeedArea(ct, this.MainCityIndex)
}

// 改变资源
func (this *Model) ChangeCostByTypeObjs(tos []*g.TypeObj, change int32) {
	for _, to := range tos {
		this.ChangeCostByTypeObjOne(to, change)
	}
}
func (this *Model) ChangeCostByTypeObjOne(m *g.TypeObj, change int32) (val int32, add int32) {
	if m == nil {
		return
	}
	count := m.Count * change
	if count > 0 {
		// 资源变动更新到联盟 这里只统一处理添加资源 扣除资源的更新在交易模块处理
		this.UpdatePlrResAccChange(m.Type, count)
	}
	switch m.Type {
	case ctype.CEREAL:
		add = this.Cereal.Change(count, this.GetGranaryCap())
		val = this.Cereal.Value
	case ctype.TIMBER:
		add = this.Timber.Change(count, this.GetWarehouseCap())
		val = this.Timber.Value
	case ctype.STONE:
		add = this.Stone.Change(count, this.GetWarehouseCap())
		val = this.Stone.Value
	case ctype.BASE_RES:
		this.Cereal.Change(count, this.GetGranaryCap())
		this.Timber.Change(count, this.GetWarehouseCap())
		this.Stone.Change(count, this.GetWarehouseCap())
	case ctype.EXP_BOOK:
		add = this.ChangeExpBook(count)
		val = this.ExpBook
	case ctype.IRON:
		add = this.ChangeIron(count)
		val = this.Iron
	case ctype.UP_SCROLL:
		add = this.ChangeUpScroll(count)
		val = this.UpScroll
	case ctype.FIXATOR:
		add = this.ChangeFixator(count)
		val = this.Fixator
	case ctype.BUILD_LV:
		if m.Id > 3000 {
			this.room.GetWorld().SetAncientLv(m.Id, m.Count)
		} else {
			this.SetBuildLv(m.Id, m.Count)
		}
		val = m.Count
	case ctype.EQUIP:
		this.AddExtraUnlockEquip(m.Id)
	case ctype.PAWN:
		this.AddExtraUnlockPawn(m.Id)
	case ctype.STAMINA: //奖励点
		val = this.ChangeStamina(count)
		add = count
	case ctype.UP_RECRUIT: //招募加速
		this.UpRecruitPawnCount = ut.MaxInt32(0, this.UpRecruitPawnCount+count)
		val = this.UpRecruitPawnCount
		add = count
	case ctype.FREE_RECRUIT:
		// 添加免费次数即扣除已免费的次数
		this.FreeRecruitPawnCount -= count
		val = this.FreeRecruitPawnCount
		add = count
	case ctype.FREE_LEVING:
		this.FreeLevingPawnCount -= count
		val = this.FreeLevingPawnCount
		add = count
	case ctype.FREE_CURE:
		this.FreeCurePawnCount -= count
		val = this.FreeCurePawnCount
		add = count
	case ctype.FREE_FORGE:
		this.FreeForgeCount -= count
		val = this.FreeForgeCount
		add = count
	}
	return
}

// 检测并扣除
func (this *Model) CheckAndDeductCostByTypeObjs(tos []*g.TypeObj) (b bool) {
	if b = this.CheckCostByTypeObjs(tos); b {
		this.ChangeCostByTypeObjs(tos, -1)
	}
	return
}

// 检测并扣除资源
func (this *Model) CheckAndDeductCostByTypeObjOne(to *g.TypeObj) (b bool) {
	if b = this.CheckCostByTypeObjOne(to); b {
		this.ChangeCostByTypeObjOne(to, -1)
	}
	return
}

// 改变经验书数量
func (this *Model) ChangeExpBook(val int32) int32 {
	count := this.ExpBook + val
	if count < 0 {
		val = -this.ExpBook
	}
	this.ExpBook += val
	if val > 0 {
		this.AccTotalExpBook += val //记录累计
	}
	return val
}

// 改变铁数量
func (this *Model) ChangeIron(val int32) int32 {
	count := this.Iron + val
	if count < 0 {
		val = -this.Iron
	}
	this.Iron += val
	if val > 0 {
		this.AccTotalIron += val //记录累计
	}
	return val
}

// 改变卷轴数量
func (this *Model) ChangeUpScroll(val int32) int32 {
	count := this.UpScroll + val
	if count < 0 {
		val = -this.UpScroll
	}
	this.UpScroll += val
	if val > 0 {
		this.AccTotalUpScroll += val //记录累计
	}
	return val
}

// 改变固定器数量
func (this *Model) ChangeFixator(val int32) int32 {
	count := this.Fixator + val
	if count < 0 {
		val = -this.Fixator
	}
	this.Fixator += val
	if val > 0 {
		this.AccTotalFixator += val //记录累计
	}
	return val
}

// 根据通用类型返回该类型对应信息pb
func (this *Model) ToItemByTypeObjsPb(tos []*g.TypeObj) *pb.UpdateOutPut {
	return this.ToItemByTypeObjsPbOut(tos, &pb.UpdateOutPut{})
}
func (this *Model) ToItemByTypeObjsPbOut(tos []*g.TypeObj, items *pb.UpdateOutPut) *pb.UpdateOutPut {
	if items == nil {
		items = &pb.UpdateOutPut{}
	}
	for _, m := range tos {
		switch m.Type {
		case ctype.CEREAL:
			items.Cereal = this.Cereal.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Cereal))
		case ctype.TIMBER:
			items.Timber = this.Timber.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Timber))
		case ctype.STONE:
			items.Stone = this.Stone.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Stone))
		case ctype.BASE_RES:
			items.Cereal = this.Cereal.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Cereal))
			items.Timber = this.Timber.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Timber))
			items.Stone = this.Stone.ToPb()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Stone))
		case ctype.EXP_BOOK:
			items.ExpBook = int32(this.ExpBook)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_ExpBook))
		case ctype.IRON:
			items.Iron = int32(this.Iron)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Iron))
		case ctype.UP_SCROLL:
			items.UpScroll = int32(this.UpScroll)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_UpScroll))
		case ctype.FIXATOR:
			items.Fixator = int32(this.Fixator)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Fixator))
		case ctype.EQUIP:
			this.ceriUnlockMutex.RLock()
			items.UnlockEquipIds = array.Map(this.UnlockEquipIds, func(m int32, _ int) int32 { return int32(m) })
			this.ceriUnlockMutex.RUnlock()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Equip))
		case ctype.PAWN:
			this.ceriUnlockMutex.RLock()
			items.UnlockPawnIds = array.Map(this.UnlockPawnIds, func(m int32, _ int) int32 { return int32(m) })
			this.ceriUnlockMutex.RUnlock()
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Pawn))
		case ctype.UP_RECRUIT:
			items.UpRecruitPawnCount = int32(this.UpRecruitPawnCount)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_UpRecruit))
		case ctype.FREE_RECRUIT:
			items.FreeRecruitPawnCount = int32(this.FreeRecruitPawnCount)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_FreeRecruit))
		case ctype.FREE_LEVING:
			items.FreeLevingPawnCount = int32(this.FreeLevingPawnCount)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_FreeLeving))
		case ctype.FREE_CURE:
			items.FreeCurePawnCount = int32(this.FreeCurePawnCount)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_FreeCure))
		case ctype.FREE_FORGE:
			items.FreeForgeCount = int32(this.FreeForgeCount)
			items.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_FreeForge))
		}
	}
	return items
}

// 获取区域临时的信息
func (this *Model) GetTempAreaInfo(index, land int32, monsterEquipMap map[int32]map[string]interface{}) map[string]interface{} {
	msg := this.tempAreas.Get(index)
	if msg != nil {
		return msg
	}
	dis := this.room.GetWorld().GetToMapCellDis(this.MainCityIndex, index)
	msg = config.GetAreaConfInfo(land, dis, index, monsterEquipMap)
	this.tempAreas.Set(index, msg)
	return msg
}

// 获取军队数量
func (this *Model) GetArmyMaxCount() int32 {
	return this.GetEffectInt(effect.ARMY_COUNT) + this.room.GetWorld().GetPlayerPolicyEffectIntByUid(this.Uid, effect.ARMY_COUNT)
}

// 返回要塞自动支援配置信息 以数组的形式
func (this *Model) ToFortAutoSupports() []map[string]interface{} {
	arr := []map[string]interface{}{}
	obj := this.room.GetWorld().GetFortAutoSupports(this.Uid)
	for index, val := range obj {
		arr = append(arr, map[string]interface{}{
			"index":  index,
			"isAuto": val,
		})
	}
	return arr
}

// 返回要塞自动支援配置信息 以数组Pb的形式
func (this *Model) ToFortAutoSupportsPb() []*pb.ForAutoSupport {
	arr := []*pb.ForAutoSupport{}
	obj := this.room.GetWorld().GetFortAutoSupports(this.Uid)
	for index, val := range obj {
		arr = append(arr, &pb.ForAutoSupport{
			Index: int32(index),
			Val:   val,
		})
	}
	return arr
}

// 通知有新的宝箱
func (this *Model) NotifyHasTreasure(val bool) {
	if this.hasNewTreasure != val {
		this.hasNewTreasure = val
		this.PutNotifyQueue(constant.NQ_NEW_TREASURE, &pb.OnUpdatePlayerInfoNotify{Data_50: val})
	}
}

// 玩家总经济变动
func (this *Model) UpdatePlrResAccChange(tp, count int32) {
	if !ItemTypeIsRes(tp) {
		// 该物品不是资源
		return
	}
	count *= constant.RES_TRANSIT_CAP[tp]
	this.room.GetWorld().ChangePlayerResAcc(this.Uid, count)
}

// 物品类型是否为资源
func ItemTypeIsRes(tp int32) bool {
	switch tp {
	case ctype.CEREAL, ctype.TIMBER, ctype.STONE, ctype.EXP_BOOK, ctype.IRON, ctype.UP_SCROLL, ctype.FIXATOR:
		return true
	default:
		return false
	}
}

// 物品类型是否为基础三资
func ItemTypeIsBaseRes(tp int32) bool {
	switch tp {
	case ctype.CEREAL, ctype.TIMBER, ctype.STONE:
		return true
	default:
		return false
	}
}
