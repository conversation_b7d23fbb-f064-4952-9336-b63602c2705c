package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/recharge"
	"time"

	"github.com/huyangv/vmqant/log"
)

type NotFinishOrderInfo struct {
	CpOrderId string `json:"cp_order_id" bson:"cp_order_id"` //uid
	OrderId   string `json:"order_id" bson:"order_id"`       //订单号
	ProductId string `json:"product_id" bson:"product_id"`   //商品id
	Quantity  int    `json:"quantity" bson:"quantity"`       //数量
	Platform  string `json:"platform" bson:"platform"`       //平台
}

// 未完成订单转为pb
func (this *User) NotFinishOrderToPb() []*pb.OrderInfo {
	arr := []*pb.OrderInfo{}
	for _, v := range this.NotFinishOrders {
		order := &pb.OrderInfo{
			CpOrderId: v.CpOrderId,
			OrderId:   v.OrderId,
			ProductId: v.ProductId,
			Quantity:  int32(v.Quantity),
			Platform:  v.Platform,
		}
		arr = append(arr, order)
	}
	return arr
}

// 订阅信息转为pb
func (this *UserSubInfo) ToPb() *pb.UserSubInfo {
	return &pb.UserSubInfo{
		OrderUid:      this.OrderUid,
		ProductId:     this.ProductId,
		SurplusTime:   ut.MaxInt64(this.EndTime-ut.Now(), 0),
		CurrencyType:  this.CurrencyType,
		PayAmount:     ut.Float32Round(this.PayAmount, 2),
		IsOneTime:     this.IsOneTime,
		LastAwardTime: this.LastAwardTime,
	}
}

func (this *User) UserSubInfoToPb() []*pb.UserSubInfo {
	arr := []*pb.UserSubInfo{}
	this.SubscriptionLock.RLock()
	for _, v := range this.SubscriptionInfo {
		if info := v.ToPb(); info.SurplusTime > 0 {
			arr = append(arr, info)
		}
	}
	this.SubscriptionLock.RUnlock()
	return arr
}

// 更新玩家订阅信息
func (this *User) UpdateUserSubInfo(orderUid, productId string, endTime, lastAwardTime int64, auto bool, currencyType string, payAmount float32, isOneTime bool, module *Lobby) {
	this.SubscriptionLock.Lock()
	userSubInfo := array.Find(this.SubscriptionInfo, func(m *UserSubInfo) bool { return m.OrderUid == orderUid })
	needTa := false
	if userSubInfo != nil {
		now := ut.Now()
		if userSubInfo.EndTime <= now && endTime >= now {
			needTa = true
		}
		// 玩家已订阅则更新时间
		userSubInfo.EndTime = endTime
		userSubInfo.Auto = auto
		if userSubInfo.LastAwardTime < lastAwardTime {
			userSubInfo.LastAwardTime = lastAwardTime
		}
		if userSubInfo.CurrencyType == "" {
			userSubInfo.CurrencyType = currencyType
			userSubInfo.PayAmount = payAmount
		}
	} else {
		userSubInfo = &UserSubInfo{
			OrderUid:     orderUid,
			ProductId:    productId,
			EndTime:      endTime,
			Auto:         auto,
			CurrencyType: currencyType,
			PayAmount:    payAmount,
			IsOneTime:    isOneTime,
		}
		if lastAwardTime > 0 {
			userSubInfo.LastAwardTime = lastAwardTime
		} else {
			userSubInfo.LastAwardTime = GetMonthCardUpdateTime() - ut.TIME_DAY
		}
		this.SubscriptionInfo = append(this.SubscriptionInfo, userSubInfo)
		needTa = true
	}
	this.SubscriptionLock.Unlock()
	// 通知客户端
	this.NotifyUserSub(module)
	if needTa {
		// TA上报
		ta.UserSet(0, this.UID, this.DistinctId, 0, map[string]interface{}{"subscribe_state": userSubInfo.ProductId})
	}
}

// 移除玩家订阅
func (this *User) DelUserSubInfo(orderUid string, module *Lobby) (ret *UserSubInfo) {
	now := ut.Now()
	hasSub, delIndex := false, -1
	this.SubscriptionLock.Lock()
	for i, info := range this.SubscriptionInfo {
		if info.OrderUid == orderUid {
			delIndex = i
			ret = info
		} else if info.EndTime > now {
			hasSub = true
		}
	}
	// 从列表中删除
	this.SubscriptionInfo = append(this.SubscriptionInfo[:delIndex], this.SubscriptionInfo[delIndex+1:]...)
	this.SubscriptionLock.Unlock()
	// 通知客户端
	this.NotifyUserSub(module)
	if delIndex >= 0 && !hasSub {
		// 订阅移除且无生效订阅时 Ta上报
		ta.UserSet(this.SID, this.UID, this.DistinctId, 0, map[string]interface{}{"subscribe_state": slg.TA_SUB_STATE_NONE})
	}
	this.FlagUpdateDB()
	return
}

// 订阅状态检测
func (this *User) CheckUserSubInfos(now int64, module *Lobby) {
	if len(this.SubscriptionInfo) == 0 {
		return
	}
	delSubInfoList := []string{}
	this.SubscriptionLock.RLock()
	for _, info := range this.SubscriptionInfo {
		if now > info.EndTime {
			// 已过期
			if info.Auto {
				// 开启自动续费 再检测订阅是否续订
				this.subInfoCheck(info, module)
			}
		} else if !info.IsOneTime {
			// 未过期 检测订阅订单是否退款
			if !this.subInfoCheck(info, module) {
				delSubInfoList = append(delSubInfoList, info.OrderUid)
			}
		}
	}
	this.SubscriptionLock.RUnlock()
	for _, delOrderUid := range delSubInfoList {
		this.DelUserSubInfo(delOrderUid, module)
	}
}

// 单个订阅检测
func (this *User) subInfoCheck(info *UserSubInfo, moudule *Lobby) bool {
	endTime, auto, err, isApiErr := HandleSubCheck(this, info.OrderUid)
	if err != nil {
		if !isApiErr {
			return false
		}
	} else if info.EndTime != endTime || info.Auto != auto {
		info.EndTime = endTime
		info.Auto = auto
		this.NotifyUserSub(moudule)
	}
	return true
}

// 检测是否有免广告订阅
func (this *User) FreeAdSubCheck(now int64) bool {
	this.SubscriptionLock.RLock()
	defer this.SubscriptionLock.RUnlock()
	for _, info := range this.SubscriptionInfo {
		if info.EndTime < now {
			continue
		} else if _, ok := slg.SUB_PRODUCTID_MAP[info.ProductId]; ok {
			return true
		}
	}
	return false
}

// 通知订阅更新
func (this *User) NotifyUserSub(module *Lobby) {
	if module != nil && this.IsOnline() {
		this.PutNotifyQueue(59, &pb.OnUpdatePlayerInfoNotify{Data_59: this.UserSubInfoToPb()})
	}
}

// 领取月卡奖励
func (this *User) GetMonthCardAward(tp string) []*pb.TypeObj {
	now := ut.Now()
	this.SubscriptionLock.Lock()
	defer this.SubscriptionLock.Unlock()
	items := []*g.TypeObj{}
	for _, info := range this.SubscriptionInfo {
		if info.EndTime < now {
			continue
		} else if GetSubTypeByProductId(info.ProductId) == tp {
			if info.LastAwardTime >= GetMonthCardUpdateTime() {
				// 已领取过
				continue
			}
			subType := GetSubTypeByProductId(info.ProductId)
			itemCfg := slg.SUB_DAILY_AWARD_MAP[subType]
			if len(itemCfg) == 0 {
				log.Warning("GetMonthCardAward itemCfg nil info.ProductId: %v", info.ProductId)
				continue
			}
			// TODO 配置获取奖励
			info.LastAwardTime = now
			for _, v := range itemCfg {
				if len(v) < 3 {
					continue
				}
				items = append(items, &g.TypeObj{Type: v[0], Count: v[2]})
			}
			// 更新订阅数据库
			go recharge.SubscriptionDb.UpdateSubAwardInfo(info.OrderUid, this.UID, now)
			go monthCardDb.InsertMonthCardAwardRecord(info.OrderUid, this.UID, info.ProductId, now, items, false)
			break
		}
	}
	if len(items) > 0 {
		this.ChangeUserItems(items, constant.GOLD_CHANGE_MONTH_CARD_DAILY_GET, nil)
	}
	return g.ToTypeObjsPb(items)
}

// 未领取月卡奖励补发
func (this *User) CheckMonthCardDailyAward() {
	now := ut.Now()
	lastDayUpdateTime := GetMonthCardUpdateTime() - ut.TIME_DAY
	this.SubscriptionLock.Lock()
	defer this.SubscriptionLock.Unlock()
	for _, info := range this.SubscriptionInfo {
		if info.EndTime == 0 { // 未生效
			continue
		}
		updateTime := lastDayUpdateTime
		if info.EndTime < updateTime {
			// 结束时间小于前一天的刷新时间
			endDate := time.UnixMilli(info.EndTime)
			endDayUpdateTime := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 6, 0, 0, 0, time.Local)
			if endDate.Before(endDayUpdateTime) {
				endDayUpdateTime = endDayUpdateTime.Add(-24 * time.Hour)
			}
			updateTime = endDayUpdateTime.UnixMilli()
		}
		if info.LastAwardTime >= updateTime { //已领取
			continue
		}
		if info.LastAwardTime == 0 {
			// 未兼容到 TODO
			log.Warning("CheckMonthCardDailyAward LastAwardTime zero uid: %v", this.UID)
			continue
		}
		subType := GetSubTypeByProductId(info.ProductId)
		itemCfg := slg.SUB_DAILY_AWARD_MAP[subType]
		if len(itemCfg) == 0 {
			log.Warning("CheckMonthCardDailyAward itemCfg nil info.ProductId: %v", info.ProductId)
			continue
		}
		// 计算待补发的天数
		date := time.UnixMilli(info.LastAwardTime)
		lastUpdateTime := time.Date(date.Year(), date.Month(), date.Day(), 6, 0, 0, 0, time.Local)
		if date.Before(lastUpdateTime) {
			lastUpdateTime = lastUpdateTime.Add(-24 * time.Hour)
		}
		days := int32((updateTime - lastUpdateTime.UnixMilli()) / ut.TIME_DAY)
		// TODO 补发奖励邮件 从配置获取奖励
		items := []*g.TypeObj{}
		for _, v := range itemCfg {
			if len(v) < 3 {
				continue
			}
			items = append(items, &g.TypeObj{Type: v[0], Count: v[2] * days})
		}
		lobbyModule.sendMailItemOne(0, slg.MAIL_MONTH_CARD_AWARD_ID, "", "", "-1", this.UID, items)
		info.LastAwardTime = now
		go monthCardDb.InsertMonthCardAwardRecord(info.OrderUid, this.UID, info.ProductId, now, items, true)
	}
}

// 玩家订阅退款
func UserSubRefund(orderUid string) {
	// 查询该月卡的所有领取记录
	records, err := monthCardDb.FindMonthCardAwardRecords(orderUid)
	if err != nil || records == nil {
		log.Warning("UserSubRefund FindMonthCardAwardRecords orderUid: %v err: %v", orderUid, err)
		return
	}
	userAwardMap := map[string][]*g.TypeObj{}
	for _, v := range records {
		items := userAwardMap[v.UserId]
		if items == nil {
			items = []*g.TypeObj{}
		}
		userAwardMap[v.UserId] = g.MergeTypeObjsCount(items, v.Items...)
	}

	// 每个领取过的玩家扣除对应物品
	for uid, v := range userAwardMap {
		pbItems := &pb.TypeObjList{List: g.ToTypeObjsPb(v)}
		pbBytes, _ := pb.ProtoMarshal(pbItems)
		lobbyModule.InvokeUserFuncNR(uid, slg.RPC_USER_REFUND_REDUCE_ITEM, orderUid, pbBytes)
	}
}
