package chat

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"

	"github.com/huyangv/vmqant/log"
)

func (this *Chat) InitRpc() {
	this.GetServer().RegisterGO(slg.RPC_LEAVE, this.leave)
	this.GetServer().RegisterGO(slg.RPC_SEND_CHAT, this.rpcSendChat)
	this.GetServer().RegisterGO(slg.RPC_DEL_TEAM, this.rpcDelTeam)
	this.GetServer().RegisterGO(slg.RPC_GET_FRIEND_CHAT, this.rpcGetFriendChat)
	this.GetServer().RegisterGO(slg.RPC_SEND_FRIEND_CHAT, this.rpcSendFriendChat)
	this.GetServer().RegisterGO(slg.RPC_LIKE_JWM, this.rpcLikeJwm)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_LANGUAGE, this.rpcLikeJwm)
}

// 玩家离线
func (this *Chat) leave(uid, teamUid, lang string) (result interface{}, err string) {
	chanLang := slg.LANG_CHANNEL_MAP[lang]
	UserOffline(uid, teamUid, chanLang)
	return
}

// 发送聊天
func (this *Chat) rpcSendChat(chatBytes []byte) (result interface{}, err string) {
	chat := &pb.LobbyChatInfo{}
	e := pb.ProtoUnMarshal(chatBytes, chat)
	if e != nil {
		err = e.Error()
		log.Error("rpcSendCha err: %v", err)
	}
	SendChat(chat)
	return
}

// 队伍解散
func (this *Chat) rpcDelTeam(teamUid string) (result interface{}, err string) {
	channel := CHANNEL_TYPE_TEAM + "_" + teamUid
	DelChannel(channel)
	return
}

// 获取好友聊天
func (this *Chat) rpcGetFriendChat(uid, channel string) (result []byte, err string) {
	rst := &pb.LOBBY_HD_GETFRIENDCHATS_S2C{Uid: uid}
	chatList := GetFriendChat(channel)
	if chatList != nil {
		rst.Chats = chatList.ToPb()
	}
	result, err = pb.ProtoMarshal(rst)
	return
}

// 发送好友聊天
func (this *Chat) rpcSendFriendChat(channel string, chatBytes []byte) (result interface{}, err string) {
	chat := &pb.ChatInfo{}
	e := pb.ProtoUnMarshal(chatBytes, chat)
	if e != nil {
		err = e.Error()
		log.Error("rpcSendFriendChat channel: %v, err: %v", channel, err)
	}
	AddFriendChat(channel, chat)
	return
}

// 发送好友聊天
func (this *Chat) rpcLikeJwm(count int) (result interface{}, err string) {
	global.SumLikeJwmCount += count
	return
}

// 使用语言更新
func (this *Chat) rpcChangeLanguage(uid, oldLang string) (result interface{}, err string) {
	// 退出之前的语言频道
	langChan := CHANNEL_TYPE_LANG + "_" + oldLang
	LeaveChannel(uid, langChan)
	return
}
