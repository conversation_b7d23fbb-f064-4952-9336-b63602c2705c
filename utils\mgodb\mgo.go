package mgo

import (
	"context"
	"time"

	slg "slgsrv/server/common"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	Database = "slgsrv"
	Client   *mongo.Client
	// mutex    = new(deadlock.RWMutex)
)

// 初始化
func Init(url, dbname, serverType string) {
	Database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  // 只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) // 指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  // 使用最大的连接数
	var err error
	if Client, err = mongo.Connect(ctx, opt); err == nil {
		log.Info("mongodb init success! " + url + "[" + Database + "]")
	} else if err == mongo.ErrNoDocuments {
		panic("mongodb init error! ErrNoDocuments")
	} else {
		panic(err.Error())
	}
	// 初始化只添加login节点数据库的索引 game节点的在房间初始化添加
	if serverType == "match" || serverType == "development" {
		// 用户表添加索引
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"rank_score": -1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"nickname": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"distinct_id": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"google_openid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"facebook_openid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER, map[string]int{"apple_openid": 1})
		// 充值表添加索引
		AddIndex(slg.DB_COLLECTION_NAME_RECHARGE, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_RECHARGE, map[string]int{"order_id": 1})
		// 账号注销表
		AddIndex(slg.DB_COLLECTION_NAME_ACCOUNT_DEL_RECORD, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_ACCOUNT_DEL_RECORD, map[string]int{"loginType": 1, "openid": 1, "time": -1})
		// 订阅表
		AddIndex(slg.DB_COLLECTION_NAME_SUBSCRIPTION, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_SUBSCRIPTION, map[string]int{"order_id": 1, "platform": 1})
		AddIndex(slg.DB_COLLECTION_NAME_SUBSCRIPTION, map[string]int{"token": 1, "platform": 1})
		// 好友聊天表
		AddIndex(slg.DB_COLLECTION_NAME_FRIEND_CHAT, map[string]int{"channel": 1})
		// 图鉴评论表
		AddIndex(slg.DB_COLLECTION_NAME_GALLERY_COMMENT, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_GALLERY_COMMENT, map[string]int{"type": 1, "id": 1, "user_id": 1})
		// 对局记录表
		AddIndex(slg.DB_COLLECTION_NAME_GAME_RECORD, map[string]int{"end_time": -1})
		AddIndex(slg.DB_COLLECTION_NAME_GAME_RECORD, map[string]int{"uid": 1, "sid": 1})
		// 兑换码领取表
		AddIndex(slg.DB_COLLECTION_NAME_GIFT_CLAIM, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_GIFT_CLAIM, map[string]int{"code_uid": 1})
		// 队伍表
		AddIndex(slg.DB_COLLECTION_NAME_TEAM, map[string]int{"uid": 1})
		// 皮肤物品溯源表
		AddIndex(slg.DB_COLLECTION_NAME_SKIN_ITEM_TRACK, map[string]int{"uid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_SKIN_ITEM_TRACK, map[string]int{"owner": 1})
		// 皮肤合成记录表
		AddIndex(slg.DB_COLLECTION_NAME_SKIN_ITEM_COMPOSE, map[string]int{"uid": 1})
		// 自定义房间表
		AddIndex(slg.DB_COLLECTION_NAME_CUSTOM_ROOM, map[string]int{"sid": 1})
		AddIndex(slg.DB_COLLECTION_NAME_CUSTOM_ROOM, map[string]int{"opened": -1})
		// 月卡奖励记录表
		AddIndex(slg.DB_COLLECTION_NAME_MONTH_CARD_AWARD, map[string]int{"order_uid": 1})
	} else if serverType == "game" {
		// 区服表添加唯一索引
		AddUniqueIndex(slg.DB_COLLECTION_NAME_ROOM, "id", 1)
	} else if serverType == "mail" || serverType == "development" {
		// 邮件基础信息表
		AddUniqueIndex(slg.DB_COLLECTION_NAME_MAIL, "uid", 1)
		AddIndex(slg.DB_COLLECTION_NAME_MAIL, map[string]int{"create_time": -1})
		AddIndex(slg.DB_COLLECTION_NAME_MAIL, map[string]int{"receiver": 1})
		AddIndex(slg.DB_COLLECTION_NAME_MAIL, map[string]int{"receiver_map": 1})
		AddIndex(slg.DB_COLLECTION_NAME_MAIL, map[string]int{"sid": 1})
		// 玩家邮件信息表
		AddUniqueIndex(slg.DB_COLLECTION_NAME_USER_MAIL, "uid", 1)
		AddIndex(slg.DB_COLLECTION_NAME_USER_MAIL, map[string]int{"user_id": 1})
		AddIndex(slg.DB_COLLECTION_NAME_USER_MAIL, map[string]int{"mail_uid": 1})
	}
}

func GetCollectionClone(table string) *mongo.Collection {
	collection, _ := Client.Database(Database).Collection(table).Clone()
	return collection
}

func CreateCollection(table string) error {
	err := Client.Database(Database).CreateCollection(context.TODO(), table)
	if err != nil {
		log.Error("CreateCollection error! table="+table, err.Error())
	}
	return err
}

func GetCollection(table string) *mongo.Collection {
	return Client.Database(Database).Collection(table)
}

// 创建一个
func CreateClient(url string) *mongo.Client {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  // 只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) // 指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(20)                  // 使用最大的连接数
	var err error
	var cli *mongo.Client
	if cli, err = mongo.Connect(ctx, opt); err == nil {
		return cli
	} else {
		log.Error(err.Error())
	}
	return nil
}

// 添加索引（复合）
func AddIndex(collectionName string, indexFields map[string]int) {
	col := GetCollection(collectionName)
	indexView := col.Indexes()
	// 获取索引列表
	cursor, err := indexView.List(context.Background())
	if err != nil {
		log.Error("addIndexs List collectionName: %v, err: %v", collectionName, err)
		return
	}
	defer cursor.Close(context.Background())

	// 构建符合索引
	indexName := ""
	keys := bson.D{}
	for field, sort := range indexFields {
		if indexName != "" {
			indexName += "_"
		}
		keys = append(keys, bson.E{Key: field, Value: sort})
		indexName += field + "_" + ut.Itoa(sort)
	}

	// 遍历索引列表
	for cursor.Next(context.Background()) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			log.Error("addIndexs Decode collectionName: %v, err: %v", collectionName, err)
			continue
		}
		if index != nil && ut.String(index["name"]) == indexName {
			// 添加过的索引就不再添加了
			return
		}
	}
	_, err = indexView.CreateOne(context.Background(), mongo.IndexModel{
		Keys: keys,
	})
	if err != nil {
		log.Error("addIndexs collectionName: %v, indexFields: %v, err: %v", collectionName, indexFields, err)
	} else {
		log.Info("addIndexs collectionName: %v, indexFields: %v", collectionName, indexFields)
	}
}

// 添加唯一索引
func AddUniqueIndex(colName, field string, sort int) {
	col := GetCollection(colName)
	indexView := col.Indexes()
	// 获取索引列表
	cursor, err := indexView.List(context.Background())
	if err != nil {
		log.Error("AddUniqueIndex List colName: %v, err: %v", colName, err)
		return
	}
	defer cursor.Close(context.Background())
	indexName := field + "_" + ut.Itoa(sort)
	// 遍历索引列表
	for cursor.Next(context.Background()) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			log.Error("AddUniqueIndex Decode collectionName: %v, err: %v", field, err)
			continue
		}
		if index != nil && ut.String(index["name"]) == indexName {
			// 添加过的索引就不再添加了
			return
		}
	}
	_, err = indexView.CreateOne(context.Background(), mongo.IndexModel{
		Keys:    bson.M{field: sort},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Error("AddUniqueIndex colName: %v, filed: %v, err: %v", colName, field, err)
	} else {
		log.Info("AddUniqueIndex colName: %v, filed: %v", colName, field)
	}
}
