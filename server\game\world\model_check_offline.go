package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	ut "slgsrv/utils"
	rds "slgsrv/utils/redis"
	"time"

	"github.com/huyangv/vmqant/log"
)

// 检测玩家是否很久没有上线了
func (this *Model) UpdatePlayerOfflineForLong() {
	if this.room.IsGameOver() {
		return
	}
	var offlineTime int64
	ok := 0
	now := time.Now().UnixMilli()
	var tplr *TempPlayer = nil
	this.allTempPlayers.RLock()
	playerList := []*TempPlayer{}
	for _, m := range this.allTempPlayers.Map {
		playerList = append(playerList, m)
	}
	this.allTempPlayers.RUnlock()
	for _, m := range playerList {
		offlineTime = now - ut.If(m.OfflineTime == 0, m.CreateTime, m.OfflineTime)
		cellCount := len(m.OwnCells)
		// fmt.Println("uid=" + m.Uid + ", cellCount=" + ut.Itoa(cellCount))
		if cellCount == 0 || m.BattleRecordInfo[bdtype.DIE_COUNT] > 0 {
			continue //被攻陷了 或者已经沦陷过 不删除
		} else if m.IsGiveupGame {
			continue //已放弃的不删除
		} else if cellCount <= 4 { //如果土地小于等于4 超过1天就删除 并且不发放邮件
			if offlineTime < ut.TIME_DAY*1 {
			} else if ok = this.CheckCanDeletePlayer(m, false); ok != 0 {
				tplr = m
				break
			}
		} else if cellCount <= 5 { //如果土地小于等于5 超过1天就删除 发放邮件
			if offlineTime < ut.TIME_DAY*1 {
			} else if ok = this.CheckCanDeletePlayer(m, false); ok != 0 {
				tplr = m
				break
			}
		} else if cellCount <= 6 { //如果土地小于等于6 超过2天就删除 发放邮件
			if offlineTime < ut.TIME_DAY*2 {
			} else if ok = this.CheckCanDeletePlayer(m, false); ok != 0 {
				tplr = m
				break
			}
		} else if cellCount <= 8 { //如果土地小于等于8 超过4天就删除 发放邮件
			if offlineTime < ut.TIME_DAY*4 {
			} else if ok = this.CheckCanDeletePlayer(m, false); ok != 0 {
				tplr = m
				break
			}
		} else if cellCount <= 12 { //如果土地小于等于12 超过8天就删除 发放邮件
			if offlineTime < ut.TIME_DAY*8 {
			} else if ok = this.CheckCanDeletePlayer(m, false); ok != 0 {
				tplr = m
				break
			}
		}
	}

	if tplr != nil {
		this.deletePlayerByOfflineForLong(tplr, offlineTime, ok == 2)
	}
}

func (this *Model) CheckCanDeletePlayer(tplr *TempPlayer, force bool) int {
	mainIndex, uid := tplr.MainCityIndex, tplr.Uid
	if area := this.GetArea(mainIndex); area != nil && (area.Owner != uid || area.IsBattle()) {
		return 0 //如果当前位置已经不是我的或者在战斗中 就不删除
	} else if this.room.IsPlayerOnline(uid) {
		return 0 //如果在线也不删除
	}
	isCanAddMainIndex := this.CheckMainCityIndex(mainIndex, uid)
	if !isCanAddMainIndex && !force {
		return 0 //如果这个位置不能还回去了 也不删除
	} else if isCanAddMainIndex {
		return 2 //表示可以还回去
	}
	return 1
}

func (this *Model) deletePlayerByOfflineForLong(tplr *TempPlayer, time int64, isCanAddMainIndex bool) {
	mainIndex, uid := tplr.MainCityIndex, tplr.Uid
	// 删除被占领玩家的所有士兵和行军
	this.RemovePlayerAllArmy(uid)
	this.RemovePlayerAllMarch(uid)
	this.RemovePlayerAllTransit(uid)
	this.RemoveDrillPawn(mainIndex)      //删除这个区域的训练士兵
	this.RemovePawnLvingArea(mainIndex)  //删除这个区域的士兵练级
	this.RemovePawnCuringArea(mainIndex) //删除这个区域的士兵治疗
	this.RepatriateTransitByTargetIndex(mainIndex, uid)
	// 先删除 所有土地
	for _, index := range tplr.OwnCells {
		area := this.GetArea(index)
		if area == nil {
			continue
		} else if area.IsBattle() { //如果在战斗就只是设置拥有者
			area.Owner = ""
		} else {
			this.UpdateAreaOwnerInfoByArea(area, "", 0) //归还大自然
		}
	}
	// 直接删除所有地块 并通知
	this.RemoveCells(tplr.OwnCells)
	// 删除联盟申请
	this.CleanPlayerAlliApply(uid, true)
	// 删除在市场里面卖的东西
	this.room.GetBazaar().CleanPlayerBazaarRes(uid)
	// 如果有联盟从联盟里面删除 如果是盟主就解散盟
	if alli := this.GetAlliance(tplr.AllianceUid); alli != nil {
		if alli.Creater == uid {
			this.RemoveAlliance(alli, 100011)
		} else {
			this.RemoveAlliMember(alli, uid, "offline")
		}
	}
	// 删除各种记录
	this.room.GetRecord().RemoveAllRecordByUID(uid)
	// 删除玩家
	this.room.DeletePlayer(uid)
	// 删除临时缓存
	this.DelWorldPbPlayer(uid)
	this.allTempPlayers.Lock()
	delete(this.allTempPlayers.Map, uid)
	this.allTempPlayers.Unlock()
	// 这个位置还回去
	if isCanAddMainIndex {
		this.room.AddMainCityIndex(mainIndex)
	}
	// 记录删除信息
	cellCount := len(tplr.OwnCells)
	this.room.RecordDeletePlayer(uid, mainIndex, int32(cellCount), time)
	// // 保存数据库
	// this.room.SaveDB()
	// 通知
	this.PutNotifyQueue(constant.NQ_DELETE_PLAYER, &pb.OnUpdateWorldInfoNotify{Data_44: uid})
	// 通知大厅服
	this.room.InvokeLobbyRpcNR(rds.GetUserLid(uid), slg.RPC_SET_USER_PLAYSID, uid, 0)
	log.Info("DeletePlayer sid=" + ut.Itoa(this.room.GetSID()) + ", uid=" + uid + ", name=" + tplr.Nickname + ", index=" + ut.Itoa(mainIndex) + ", cell=" + ut.Itoa(cellCount))
}

// 永久从服务器删除玩家
func (this *Model) DeletePlayerByGame(tplr *TempPlayer) bool {
	if ok := this.CheckCanDeletePlayer(tplr, true); ok != 0 {
		offlineTime := time.Now().UnixMilli() - ut.If(tplr.OfflineTime == 0, tplr.CreateTime, tplr.OfflineTime)
		this.deletePlayerByOfflineForLong(tplr, offlineTime, ok == 2)
		return true
	}
	return false
}

// 排位区 检测超过3天 如果一次没有登陆就回收
func (this *Model) UpdatePlayerOfflineForLongByRank() {
	if this.room.IsGameOver() {
		return
	}
	now := time.Now().UnixMilli()
	var tplr *TempPlayer = nil
	var maxDay int64 = ut.TIME_DAY * 3
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		// log.Info("UpdatePlayerOfflineForLongByRank uid: %v, offlineTime: %v, CreateTime: %v, IsGiveupGame: %v", m.Uid, m.OfflineTime, m.CreateTime, m.IsGiveupGame)
		if m.OfflineTime > 0 || m.IsGiveupGame || len(m.OwnCells) != 4 {
			continue //已放弃的不删除
		} else if now-m.CreateTime < maxDay {
			continue
		} else if area := this.GetArea(m.MainCityIndex); area != nil && (area.Owner != m.Uid || area.IsBattle()) {
			continue //如果当前位置已经不是我的或者在战斗中 就不删除
		} else if this.room.IsPlayerOnline(m.Uid) {
			continue //如果在线也不删除
		}
		log.Info("UpdatePlayerOfflineForLongByRank uid: %v", m.Uid)
		tplr = m
		break
	}
	this.allTempPlayers.RUnlock()
	if tplr != nil { //如果一次没登陆 直接永久删除
		this.deletePlayerByOfflineForLong(tplr, tplr.CreateTime, this.CheckMainCityIndex(tplr.MainCityIndex, tplr.Uid))
	}
}

// 玩家放弃对局
func (this *Model) DeletePlayerByGiveup(uid, lid string) (int, int, string) {
	tplr := this.GetTempPlayer(uid)
	if tplr == nil {
		return 0, 0, ecode.PLAYER_NOT_EXIST.String()
	}
	mainIndex, uid := tplr.MainCityIndex, tplr.Uid
	// 删除所有地块的免战
	for _, index := range tplr.OwnCells {
		this.RemoveAvoidWarArea(index)
	}
	score := tplr.LandScoreTop + tplr.AlliScoreTop
	_, rank, _ := this.GetPlayerScoreList(uid, 0)
	// 删除联盟申请
	this.CleanPlayerAlliApply(uid, true)
	// 如果有联盟从联盟里面删除 如果是盟主就解散盟
	if alli := this.GetAlliance(tplr.AllianceUid); alli != nil {
		if alli.Creater == uid {
			this.RemoveAlliance(alli, 100011)
		} else {
			this.RemoveAlliMember(alli, uid, "giveup")
		}
	}
	// 标记已经放弃
	tplr.IsNeedUpdateDB = true
	tplr.IsGiveupGame = true
	// 通知大厅服
	data, err := ut.RpcInterfaceMap(this.room.InvokeLobbyRpc(lid, slg.RPC_PLAYER_GIVEUP_GAME, uid, this.room.GetSID(), this.room.GetCreateTime(), score, rank, tplr.ToStatistics()))
	if err != "" {
		log.Error("DeletePlayerByGiveup sid=%v, uid=%v, err=%v", this.room.GetSID(), uid, err)
	}
	// 通知
	this.PutNotifyQueue(constant.NQ_PLAYER_GIVEUP_GAME, &pb.OnUpdateWorldInfoNotify{Data_58: uid})
	// 清理产出
	if tplr.CityOutputMap.Count() > 0 {
		tplr.CityOutputMap.Clean()
		this.PutNotifyQueue(constant.NQ_PLAYER_CITY_OUTPUT, &pb.OnUpdateWorldInfoNotify{Data_31: &pb.PlayerCityOutputInfo{
			Uid:       tplr.Uid,
			OutputMap: tplr.ToCityOutputPb(),
		}})
	}
	log.Info("DeletePlayerByGiveup sid=" + ut.Itoa(this.room.GetSID()) + ", uid=" + uid + ", name=" + tplr.Nickname + ", index=" + ut.Itoa(mainIndex))
	return ut.Int(data["rankScore"]), ut.Int(data["passNewbieIndex"]), ""
}
