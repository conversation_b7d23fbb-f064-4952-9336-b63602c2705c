package chat

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	"sort"

	"github.com/sasha-s/go-deadlock"
)

var (
	historyMap = ut.NewMapLock[int, *RoomHistoryCache]() //对局历史map
)

const (
	GAME_HISTORY_CACHE_REFRESH_TIME = ut.TIME_MINUTE * 5
)

type RoomHistoryCache struct {
	List       []*pb.GameHistory //历史对局列表
	updateTime int64             //缓存上次更新时间
	deadlock.RWMutex
}

// 获取指定分页历史对局
func GetGameHistoryList(serverType, page int) (list []*pb.GameHistory, total int) {
	cache := historyMap.Get(serverType)
	if cache == nil {
		cache = &RoomHistoryCache{List: []*pb.GameHistory{}, updateTime: ut.Now()}
		historyMap.Set(serverType, cache)
		cache.initFromDb(serverType)
	} else {
		cache.updateFromDb(serverType)
	}
	start := (page - 1) * slg.SHOW_HISTORY_SERVER_PAGE_NUM
	tail := start + slg.SHOW_HISTORY_SERVER_PAGE_NUM
	total = len(cache.List)
	if tail > total {
		tail = total
	}
	list = cache.List[start:tail]
	return
}

// 更新缓存
func (this *RoomHistoryCache) updateFromDb(serverType int) {
	now := ut.Now()
	if this.updateTime+GAME_HISTORY_CACHE_REFRESH_TIME < now {
		this.initFromDb(serverType)
		this.updateTime = now
	}
}

// 初始化该房间类型的历史缓存
func (this *RoomHistoryCache) initFromDb(serverType int) {
	dataList, err := db_room.FindGameOverAll(serverType)
	if err != "" || dataList == nil {
		return
	}
	this.Lock()
	defer this.Unlock()
	this.List = []*pb.GameHistory{}
	for _, v := range dataList {
		if v.GameOverInfo == nil {
			continue
		} else if serverType == slg.NORMAL_SERVER_TYPE && GetRoomSubType(v.Id) == 0 {
			continue //普通服不计入
		}
		this.List = append(this.List, dbDataToPb(v))
	}
	sort.Slice(this.List, func(i, j int) bool {
		return this.List[i].EndTime > this.List[j].EndTime
	})
}

func dbDataToPb(data r.RoomTableData) *pb.GameHistory {
	pbData := &pb.GameHistory{
		Id:         int32(data.Id),
		CreateTime: int64(data.CreateTime),
		WinType:    int32(data.GameOverInfo.WinType),
		WinName:    data.GameOverInfo.WinName,
		EndTime:    int64(data.GameOverInfo.EndTime),
		Winners:    []*pb.GameOverWinnerInfo{},
		LandCount:  data.GameOverInfo.WinCellCount,
	}
	if data.WinCond != nil && len(data.WinCond) > 0 {
		pbData.WinCondType = int32(data.WinCond[0])
	}
	if data.GameOverInfo.WinnerList != nil {
		// 获胜者信息
		for _, v := range data.GameOverInfo.WinnerList {
			pbData.Winners = append(pbData.Winners, &pb.GameOverWinnerInfo{
				Uid:         v.UID,
				Name:        v.Name,
				Headicon:    v.Headicon,
				OccupyCount: v.OccupyCount,
				Score:       v.Score,
				Job:         v.Job,
				KillPawn:    v.KillPawn,
				DeadPawn:    v.DeadPawn,
			})
		}
		sort.Slice(pbData.Winners, func(i, j int) bool { // 按积分降序
			return pbData.Winners[i].Score > pbData.Winners[j].Score
		})
	} else if data.GameOverInfo.Winners != nil {
		// 获胜者信息 兼容老数据
		for _, v := range data.GameOverInfo.Winners {
			if len(v) < 2 {
				continue
			}
			pbData.Winners = append(pbData.Winners, &pb.GameOverWinnerInfo{
				Uid:  v[0],
				Name: v[1],
			})
		}
	}
	if winAlli := data.GameOverInfo.WinAlliInfo; winAlli != nil {
		// 获胜联盟信息
		pbData.WinAlliInfo = &pb.GameOverWinAlliInfo{
			Uid:      winAlli.UID,
			Name:     winAlli.Name,
			Headicon: winAlli.Headicon,
		}
	}
	return pbData
}

func GetRoomSubType(sid int32) int32 {
	return sid / slg.ROOM_SUB_TYPE_FLAT % (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT)
}
