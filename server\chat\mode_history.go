package chat

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	r "slgsrv/server/game/room"
	ut "slgsrv/utils"
	"sort"

	"github.com/sasha-s/go-deadlock"
)

var (
	historyMap = ut.NewMapLock[int, *RoomHistoryCache]() //对局历史map
)

const (
	GAME_HISTORY_CACHE_REFRESH_TIME = ut.TIME_MINUTE * 5
)

type RoomHistoryCache struct {
	List       []*pb.GameHistory //历史对局列表
	updateTime int               //缓存上次更新时间
	deadlock.RWMutex
}

// 获取指定分页历史对局
func GetGameHistoryList(serverType, page int) (list []*pb.GameHistory, total int) {
	cache := historyMap.Get(serverType)
	if cache == nil {
		cache = &RoomHistoryCache{List: []*pb.GameHistory{}, updateTime: ut.Now()}
		historyMap.Set(serverType, cache)
		cache.initFromDb(serverType)
	} else {
		cache.updateFromDb(serverType)
	}
	start := (page - 1) * slg.SHOW_HISTORY_SERVER_PAGE_NUM
	tail := start + slg.SHOW_HISTORY_SERVER_PAGE_NUM
	total = len(cache.List)
	if tail > total {
		tail = total
	}
	list = cache.List[start:tail]
	return
}

// 更新缓存
func (this *RoomHistoryCache) updateFromDb(serverType int) {
	now := ut.Now()
	if this.updateTime+GAME_HISTORY_CACHE_REFRESH_TIME < now {
		this.initFromDb(serverType)
		this.updateTime = now
	}
}

// 初始化该房间类型的历史缓存
func (this *RoomHistoryCache) initFromDb(serverType int) {
	dataList, err := db_room.FindGameOverAll(serverType)
	if err != "" || dataList == nil {
		return
	}
	this.Lock()
	defer this.Unlock()
	this.List = []*pb.GameHistory{}
	for _, v := range dataList {
		if v.GameOverInfo == nil {
			continue
		} else if serverType == slg.NORMAL_SERVER_TYPE && GetRoomSubType(v.Id) == 0 {
			continue //普通服不计入
		}
		this.List = append(this.List, dbDataToPb(v))
	}
	sort.Slice(this.List, func(i, j int) bool {
		return this.List[i].EndTime > this.List[j].EndTime
	})
}

func dbDataToPb(data r.RoomTableData) *pb.GameHistory {
	pbData := &pb.GameHistory{
		Id:         int32(data.Id),
		CreateTime: int64(data.CreateTime),
		WinType:    int32(data.GameOverInfo.WinType),
		WinName:    data.GameOverInfo.WinName,
		EndTime:    int64(data.GameOverInfo.EndTime),
	}
	if data.WinCond != nil && len(data.WinCond) > 0 {
		pbData.WinCondType = int32(data.WinCond[0])
	}
	if data.GameOverInfo.Winners != nil {
		pbData.Winners = map[string]string{}
		for _, v := range data.GameOverInfo.Winners {
			pbData.Winners[v[0]] = v[1]
		}
	}
	return pbData
}

func GetRoomSubType(sid int32) int32 {
	return sid / slg.ROOM_SUB_TYPE_FLAT % (slg.ROOM_TYPE_FLAG / slg.ROOM_SUB_TYPE_FLAT)
}
