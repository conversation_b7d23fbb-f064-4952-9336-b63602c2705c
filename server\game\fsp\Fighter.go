package fsp

import (
	"math"
	"sort"
	"strings"

	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/behavior"
	"slgsrv/server/game/common/astar"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/sasha-s/go-deadlock"
)

type Fighter struct {
	entity      g.Pawn
	ctrl        *FSPBattleController
	searchRange *astar.SearchRange
	astar       *astar.AStarRange // A星寻路

	behavior          *behavior.BehaviorTree           // 行为树
	blackboard        map[int32]map[string]interface{} // 携带行为树数据
	blackboardMutex   *deadlock.RWMutex
	canAttackTargets  []g.CanAttackTargetInfo // 当前所有可以攻击的目标
	canAttackFighters []g.IFighter
	attackTarget      g.IFighter // 当前攻击目标

	tempAttackRangePointMap map[string][]*ut.Vec2   // 同一个位置和攻击范围 那么可攻击点位就一样
	tempMovePathMap         map[string][][]*ut.Vec2 // 同一个位置和目标 那么移动路径就一样
	tempAttackAnimTimes     [][]float64             // 攻击动画时间
	tempLastPoint           *ut.Vec2

	camp        int32 // 所属阵营
	attackIndex int32 // 出手顺序
	waitRound   int32 // 等待回合
	roundCount  int32 // 当前回合数
	attackCount int32 // 攻击次数
	// beTargetedList        []g.IFighter            //以该单位为目标的攻击者列表
	// attackTargetWeight    int64                   //攻击目标权重

	tempRandomVal      int32
	tempMaxSearchCount int32 // 临时的是最大搜索次数
	cDodgeCount        int32 // 回合内连续闪避次数
}

func (this *Fighter) MD5() string {
	return ut.Itoa(this.attackIndex) + "_" + this.GetPoint().ID() + "_" + ut.Itoa(this.entity.GetID()) + "_" + ut.Itoa(this.camp) + "_" + ut.Itoa(this.entity.GetCurHP()) + "_" + ut.Itoa(this.entity.GetMaxHP())
}

func (this *Fighter) Init(pawn g.Pawn, camp, roundCount int32, ctrl *FSPBattleController) *Fighter {
	this.entity = pawn
	this.camp = camp
	this.ctrl = ctrl
	this.roundCount = roundCount
	this.blackboard = map[int32]map[string]interface{}{}
	this.blackboardMutex = new(deadlock.RWMutex)
	this.behavior = behavior.NewBehaviorTree().Load(pawn.GetBehaviorId(), this)
	this.searchRange = new(astar.SearchRange).Init(ctrl.CheckIsBattleArea)
	this.astar = new(astar.AStarRange).Init(ctrl.CheckHasPassToState)
	this.tempLastPoint = pawn.GetPoint().Clone()
	// this.beTargetedList = []g.IFighter{}
	if this.roundCount == 0 {
		this.attackCount = 0
		// 初始怒气
		this.entity.InitAnger()
		// // 清空buff列表
		// if !this.entity.IsFighting() {
		// 	this.entity.CleanAllBuffs()
		// }
	}
	return this
}

func (this *Fighter) Strip() *g.FigherStrip {
	return &g.FigherStrip{
		Uid:          this.GetUID(),
		Camp:         this.camp,
		AttackIndex:  this.attackIndex,
		WaitRound:    this.waitRound,
		AttackTarget: this.GetAttackTargetUID(),
		RoundCount:   this.roundCount,
		AttackCount:  this.attackCount,
		Buffs:        this.entity.BuffsClone(),
	}
}

func (this *Fighter) ToDB() *g.FigherStrip {
	return &g.FigherStrip{
		Uid:          this.GetUID(),
		Camp:         this.camp,
		AttackIndex:  this.attackIndex,
		WaitRound:    this.waitRound,
		AttackTarget: this.GetAttackTargetUID(),
		RoundCount:   this.roundCount,
		AttackCount:  this.attackCount,
	}
}

func (this *Fighter) ToPb() *pb.FighterInfo {
	return &pb.FighterInfo{
		Uid:          this.GetUID(),
		Camp:         this.camp,
		AttackIndex:  this.attackIndex,
		WaitRound:    this.waitRound,
		AttackTarget: this.GetAttackTargetUID(),
		RoundCount:   this.roundCount,
		AttackCount:  this.attackCount,
		Buffs:        this.entity.ToBuffsPb(),
	}
}

func FightersJsonToPb(fighters []*g.FigherStrip) []*pb.FighterInfo {
	arr := []*pb.FighterInfo{}
	for _, f := range fighters {
		arr = append(arr, &pb.FighterInfo{
			Uid:          f.Uid,
			Camp:         f.Camp,
			AttackIndex:  f.AttackIndex,
			WaitRound:    f.WaitRound,
			AttackTarget: f.AttackTarget,
			RoundCount:   f.RoundCount,
			AttackCount:  f.AttackCount,
			Buffs:        f.ToBuffsPb(),
		})
	}
	return arr
}

func (this *Fighter) GetEntity() g.Pawn                    { return this.entity }
func (this *Fighter) GetRoundCount() int32                 { return this.roundCount }
func (this *Fighter) GetAttackCount() int32                { return this.attackCount }
func (this *Fighter) GetEnterDir() int32                   { return this.entity.GetEnterDir() }
func (this *Fighter) GetCamp() int32                       { return this.camp }
func (this *Fighter) GetPawnType() int32                   { return this.entity.GetPawnType() }
func (this *Fighter) GetID() int32                         { return this.entity.GetID() }
func (this *Fighter) GetUID() string                       { return this.entity.GetUID() }
func (this *Fighter) GetArmyUID() string                   { return this.entity.GetArmyUid() }
func (this *Fighter) GetLV() int32                         { return this.entity.GetLV() }
func (this *Fighter) GetPoint() *ut.Vec2                   { return this.entity.GetPoint() }
func (this *Fighter) GetLastPoint() *ut.Vec2               { return this.tempLastPoint }
func (this *Fighter) GetCtrl() g.BattleCtrl                { return this.ctrl }
func (this *Fighter) GetBaseJson() map[string]interface{}  { return this.entity.GetBaseJson() }
func (this *Fighter) GetAttrJson() map[string]interface{}  { return this.entity.GetAttrJson() }
func (this *Fighter) GetAttack() int32                     { return this.entity.GetAttack() }               // 获取攻击力
func (this *Fighter) GetInstabilityMaxAttack() int32       { return this.entity.GetInstabilityMaxAttack() } // 获取攻击力
func (this *Fighter) GetAttackSpeed() int32                { return this.entity.GetAttackSpeed() }          // 出手顺序
func (this *Fighter) GetAttackIndex() int32                { return this.attackIndex }                      // 出手顺序
func (this *Fighter) GetTempAttackIndex() int32            { return this.entity.GetAttackIndex() }          // 出手顺序
func (this *Fighter) GetAttackTarget() g.IFighter          { return this.attackTarget }
func (this *Fighter) GetSearchRange() *astar.SearchRange   { return this.searchRange }
func (this *Fighter) GetAstar() *astar.AStarRange          { return this.astar }
func (this *Fighter) GetSkillByType(tp int32) g.PawnSkill  { return this.entity.GetSkillByType(tp) }
func (this *Fighter) GetActiveSkill() g.PawnSkill          { return this.entity.GetActiveSkill() }  // 获取主动技能
func (this *Fighter) GetEquipEffects() []*g.EquipEffectObj { return this.entity.GetEquipEffects() } // 获取装备效果列表
func (this *Fighter) GetAttackRange() int32                { return this.entity.GetAttackRange() }  // 攻击距离
func (this *Fighter) GetOwner() string                     { return this.entity.GetOwner() }
func (this *Fighter) GetCurHp() int32                      { return this.entity.GetCurHP() }
func (this *Fighter) GetMaxHp() int32                      { return this.entity.GetMaxHP() }
func (this *Fighter) SetCamp(camp int32)                   { this.camp = camp }
func (this *Fighter) SetTempRandomVal(val int32)           { this.tempRandomVal = val }
func (this *Fighter) GetTempRandomVal() int32              { return this.tempRandomVal }

func (this *Fighter) IsPawn() bool {
	return !this.IsBuild() && !this.IsTower() && !this.IsNoncombat() && !this.IsFlag()
}
func (this *Fighter) IsBuild() bool     { return this.GetPawnType() == constant.PAWN_TYPE_BUILD }
func (this *Fighter) IsTower() bool     { return this.GetPawnType() == constant.PAWN_TYPE_TOWER }
func (this *Fighter) IsNoncombat() bool { return this.GetPawnType() == constant.PAWN_TYPE_NONCOMBAT }
func (this *Fighter) IsFlag() bool      { return this.entity.GetID() == 3601 } // 是否军旗
func (this *Fighter) IsHero() bool      { return this.entity.IsHero() }
func (this *Fighter) IsBoss() bool      { return this.entity.IsBoss() }

func (this *Fighter) GetPoints() []*ut.Vec2 {
	if this.IsBoss() {
		return this.ctrl.GetAreaMainPoints()
	}
	return []*ut.Vec2{this.GetPoint()}
}

func (this *Fighter) SetLV(val int32) {
	this.entity.SetLv(val)
}

func (this *Fighter) UpdateLv(val int32) {
	this.entity.UpdateLv(val)
}

// 获取攻击动画时间
func (this *Fighter) GetAttackAnimTimes() [][]float64 {
	if this.tempAttackAnimTimes == nil {
		attackAnimTimeStr := ut.String(this.GetAttrJson()["attack_anim_time"])
		if portrayal := this.entity.GetPortrayal(); portrayal != nil {
			if s := ut.String(portrayal.GetJson()["attack_anim_time"]); s != "" {
				attackAnimTimeStr = s
			}
		} else if skinId := this.entity.GetSkinID(); skinId > 0 {
			if json := config.GetJsonData("pawnSkin", skinId); json != nil {
				if s := ut.String(json["attack_anim_time"]); s != "" {
					attackAnimTimeStr = s
				}
			}
		}
		this.tempAttackAnimTimes = array.Map(strings.Split(attackAnimTimeStr, "|"), func(str string, _ int) []float64 { return ut.StringToFloats(str, ",") })
	}
	return this.tempAttackAnimTimes
}

func (this *Fighter) AmendMaxHp(ignoreBuffType int32) int32 {
	return this.entity.AmendMaxHp(ignoreBuffType)
}

func (this *Fighter) InitSkillAttackAnimTime() {
	if skill := this.entity.GetActiveSkill(); skill != nil {
		skill.InitAttackAnimTime(this.entity)
	}
}

func (this *Fighter) AmendAttack(attack int32) int32 {
	return this.entity.AmendAttack(attack, 0)
}

func (this *Fighter) GetActAttack() int32 {
	return this.AmendAttack(this.entity.GetAttack())
}

func (this *Fighter) GetIgnoreBuffAttack(tp int32) int32 {
	return this.entity.AmendAttack(this.entity.GetAttack(), tp)
}

// 获取不稳定攻击的随机攻击力 目前只限陌刀
func (this *Fighter) GetInstabilityRandomAttack(index int32) int32 {
	minDamage, maxDamage := this.GetActAttack(), this.GetInstabilityMaxAttack()
	val := ut.RoundInt32(float64(maxDamage-minDamage) / 3.0)
	a, b := minDamage+val, minDamage+val*2
	arr := [][]int32{{minDamage, a}, {a + 1, b}, {b + 1, maxDamage}, {maxDamage, maxDamage}}
	v := arr[ut.ClampInt32(index-1, 0, 3)]
	return this.ctrl.GetRandom().GetInt32(v[0], v[1])
}

// 获取装备效果列表
func (this *Fighter) GetEquipEffectByType(tp int32) *g.EquipEffectObj {
	if this.entity == nil {
		return nil
	}
	return this.entity.GetEquipEffectByType(tp)
}

// 检测并获取画像技能
func (this *Fighter) CheckPortrayalSkill(id int32) *g.PortrayalSkill {
	if this.entity == nil || !this.IsPawn() {
		return nil
	} else if skill := this.entity.GetPortrayalSkill(); skill != nil && skill.Id == id {
		return skill
	}
	return nil
}

func (this *Fighter) GetPortrayalSkill() *g.PortrayalSkill {
	if this.entity == nil || !this.IsPawn() {
		return nil
	}
	return this.entity.GetPortrayalSkill()
}

func (this *Fighter) GetPortrayalSkillID() int32 {
	if skill := this.GetPortrayalSkill(); skill != nil {
		return skill.Id
	}
	return 0
}

// 设置位置
func (this *Fighter) SetPoint(point *ut.Vec2) {
	this.tempLastPoint.Set(this.entity.GetPoint())
	this.entity.SetPoint(point)
}

func (this *Fighter) ToBlackboardMap() map[int32]map[string]interface{} {
	this.blackboardMutex.RLock()
	defer this.blackboardMutex.RUnlock()
	dataMap := map[int32]map[string]interface{}{}
	for key, m := range this.blackboard {
		data := map[string]interface{}{}
		for k, v := range m {
			data[k] = v
		}
		dataMap[key] = data
	}
	return dataMap
}

func (this *Fighter) ToBlackboardMapPb() map[int32]*pb.BlackboardInfo {
	this.blackboardMutex.RLock()
	defer this.blackboardMutex.RUnlock()
	dataMap := map[int32]*pb.BlackboardInfo{}
	for key, m := range this.blackboard {
		data := &pb.BlackboardInfo{BoolMap: map[string]bool{}, IntMap: map[string]int32{}, VecArray: map[string]*pb.Vec2List{}, StringMap: map[string]string{}, Vec2Map: map[string]*pb.Vec2{}}
		for k, v := range m {
			if flag, ok := v.(bool); ok {
				data.BoolMap[k] = flag
			} else if flag, ok := v.(int32); ok {
				data.IntMap[k] = flag
			} else if flag, ok := v.(int); ok {
				data.IntMap[k] = int32(flag)
			} else if flag, ok := v.([]*ut.Vec2); ok {
				vecList := &pb.Vec2List{}
				for _, v := range flag {
					vecList.List = append(vecList.List, pb.NewVec2(v))
				}
				data.VecArray[k] = vecList
			} else if flag, ok := v.(string); ok {
				data.StringMap[k] = flag
			} else if flag, ok := v.(*ut.Vec2); ok {
				data.Vec2Map[k] = pb.NewVec2(flag)
			}
		}
		dataMap[int32(key)] = data
	}
	return dataMap
}

func (this *Fighter) SetBlackboardMap(val map[int32]map[string]interface{}) {
	this.blackboardMutex.Lock()
	defer this.blackboardMutex.Unlock()
	this.blackboard = val
}

// 设置出手顺序
func (this *Fighter) SetAttackIndex(val int32) {
	this.attackIndex = val
}

// 获取技能目标
func (this *Fighter) GetSkillTargetByType(tp int32) int32 {
	if skill := this.GetSkillByType(tp); skill != nil {
		return skill.GetTarget()
	}
	return -1
}

// 开始战斗时的检测
func (this *Fighter) CheckBattleBeginTrigger() {
	if this.roundCount != 0 {
		return
	}
	this.roundCount = 1
	uid, heroSkillId := this.GetUID(), this.GetPortrayalSkillID()
	oldMaxHp := this.entity.GetInitMaxHP()
	// 陌刀专属：前3次攻击最大值
	if INSTABILITY_ATTACK := this.GetSkillByType(constant.PAWN_SKILL_TYPE_INSTABILITY_ATTACK); INSTABILITY_ATTACK != nil && INSTABILITY_ATTACK.GetIntensifyType() == 1 {
		this.AddBuff(bufftype.STEADY_ATTACK, uid, 1).Times = 3
	}
	// 检测装备
	equipEffects := this.GetEquipEffects()
	for _, m := range equipEffects {
		if m.Type == eeffect.LOW_HP_SHIELD { // 藤盾：检测血量低于50%增加护盾
			this.AddBuff(bufftype.CHECK_LOW_HP, uid, 1)
		} else if m.Type == eeffect.LOW_HP_ATTACk { // 战斧：检测血量低于50%增加攻击力
			this.AddBuff(bufftype.CHECK_LOW_HP_ATTACK, uid, 1)
		} else if m.Type == eeffect.BATTLE_BEGIN_SHIELD { // 玉佩：为2格内友军添加护盾
			this.AddBuff(bufftype.CHECK_JADE_PENDANT, uid, 1)
		} else if m.Type == eeffect.CONTINUE_ACTION { // 沙漏：添加是否有行动2回合的buff
			this.AddBuff(bufftype.CONTINUE_ACTION, uid, 1).Value = float64(m.Odds)
		} else if m.Type == eeffect.THOUSAND_UMBRELLA { // 千机伞：随机翻倍
			this.AddBuffValue(bufftype.THOUSAND_UMBRELLA, uid, float64(this.ctrl.GetRandom().Get(0, 1)))
		} else if m.Type == eeffect.CENTERING_HELMET { // 定心盔: 每5回合恢复生命
			this.AddBuffValue(bufftype.CHECK_CENTERING_HELMET, uid, 5)
		} else if m.Type == eeffect.CRIMSONGOLD_SHIELD { // 赤金盾: 每3回合获得护盾
			this.AddBuffValue(bufftype.CHECK_CRIMSONGOLD_SHIELD, uid, 3)
		} else if m.Type == eeffect.OBSIDIAN_ARMOR { // 黑曜铠: 前几回合减伤
			this.AddBuffValue(bufftype.OBSIDIAN_ARMOR_DEFENSE, uid, m.Value)
		}
	}
	// 周泰：濒死时概率恢复生命
	if heroSkillId == hero.ZHOU_TAI {
		this.AddBuff(bufftype.DYING_RECOVER, uid, 1)
	}
	// 乐进：先登闪避建筑攻击
	if heroSkillId == hero.YUE_JIN {
		this.AddBuff(bufftype.PRESTAGE, uid, 1)
	}
	// 高顺：陷阵加生命和攻击力
	if heroSkillId == hero.GAO_SHUN {
		camp, armyUid, fighters := this.camp, this.entity.GetArmyUid(), this.ctrl.GetFighters()
		val := this.GetPortrayalSkill().GetValue()
		for _, m := range fighters {
			if m.GetCamp() == camp && m.GetPawnType() < constant.PAWN_TYPE_MACHINE && m.GetArmyUID() == armyUid {
				hp := m.GetMaxHp()
				m.AddBuffValue(bufftype.BREAK_ENEMY_RANKS, uid, val)
				add := m.GetMaxHp() - hp
				if add > 0 {
					m.OnHeal(add, false)
				}
			}
		}
	}
	// 于禁：造成的伤害提高，受到的伤害提高
	if heroSkillId == hero.YU_JIN {
		camp, armyUid, fighters := this.camp, this.entity.GetArmyUid(), this.ctrl.GetFighters()
		val := this.GetPortrayalSkill().GetValue()
		for _, m := range fighters {
			if m.GetCamp() == camp && m.GetPawnType() < constant.PAWN_TYPE_MACHINE && m.GetArmyUID() == armyUid {
				m.AddBuffValue(bufftype.RESOLUTE, uid, val)
			}
		}
	}
	// 孙尚香 每5回合释放技能
	if heroSkillId == hero.SUN_SHANGXIANG {
		this.AddBuffValue(bufftype.CHECK_LITTLE_GIRL, uid, this.GetPortrayalSkill().GetParamsFloat64())
	}
	// 霍去病 跳斩回怒
	if heroSkillId == hero.HUO_QUBING {
		camp, armyUid, fighters := this.camp, this.entity.GetArmyUid(), this.ctrl.GetFighters()
		val := this.GetPortrayalSkill().GetValue()
		for _, m := range fighters {
			if m.GetCamp() == camp && m.GetID() == 3401 && m.GetEntity() != nil && m.GetArmyUID() == armyUid {
				m.AddBuffValue(bufftype.CHECK_JUMPSLASH_ADD_ANGER, uid, val)
			}
		}
	}
	// 辛弃疾 回首
	if heroSkillId == hero.XIN_QIJI {
		heroSkill := this.GetPortrayalSkill()
		this.AddBuffValue(bufftype.KERIAN, uid, heroSkill.GetValue()).SetRound(heroSkill.GetParamsInt())
	}
	// 调整血量
	if newMaxHp := this.GetMaxHp(); oldMaxHp != newMaxHp {
		this.entity.SetCurHP(ut.MinInt32(this.entity.GetCurHP()+ut.MaxInt32(newMaxHp-oldMaxHp, 0), newMaxHp))
	}
}

// 重置状态
func (this *Fighter) BeginAction() {
	this.blackboardMutex.Lock()
	this.blackboard = map[int32]map[string]interface{}{}
	this.blackboardMutex.Unlock()
	this.CleanCanAttackTargets()
	if !this.behavior.IsCanRun() { // 如果不能行动
		this.SetRoundEnd()
		return
	} else if this.attackTarget != nil { // 检测攻击目标是否还存在
		camp, heroSkillId := this.camp, this.GetPortrayalSkillID()
		if this.GetEquipEffectByType(eeffect.SILVER_SNAKE_WHIP) != nil {
			this.ChangeAttackTarget(nil) // 银蛇鞭 每次都要重置目标
		} else if this.IsHasBuffs(bufftype.CHAOS, bufftype.RAGE) {
			this.ChangeAttackTarget(nil) // 混乱、狂怒 每次都要重置目标
		} else if heroSkillId == hero.CAO_XIU || heroSkillId == hero.HUANG_ZHONG {
			this.ChangeAttackTarget(nil) // 曹休 黄忠 每次都要重置目标
		} else if this.attackTarget.IsDie() {
			this.ChangeAttackTarget(nil) // 如果目标死亡 就重置目标
		} else if !this.attackTarget.IsBuild() {
			this.ChangeAttackTarget(this.ctrl.GetFighter(this.attackTarget.GetUID()))
		} else if array.Some(this.ctrl.GetFighters(), func(m g.IFighter) bool { return m.GetCamp() != camp && !m.IsDie() && (m.IsPawn() || m.IsFlag()) }) {
			this.ChangeAttackTarget(nil) // 如果当前是建筑 那么看是否有敌对士兵可以攻击 就重置目标
		}
	}
	// 检测下回合是否可以继续 如果有就删除 没有就添加
	if !this.RemoveBuff(bufftype.CONTINUE_ACTION) {
		if e := this.GetEquipEffectByType(eeffect.CONTINUE_ACTION); e != nil && e.Odds > 0 && this.ctrl.GetRandom().ChanceInt32(e.Odds) {
			this.AddBuff(bufftype.CONTINUE_ACTION, this.GetUID(), 1)
			this.AddRoundEndDelayTime(250)
		}
	}
	// 重置回合内连续闪避次数
	this.ResetCDodgeCount()
	slg.Log(ut.Itoa(this.ctrl.GetCurrentFrameIndex()) + " >>>>>>>>> " + ut.Itoa(this.GetID()) + "(" + this.GetPoint().Join(",") + ") [" + this.GetUID() + "] " + ut.Itoa(this.GetAttackIndex()) + " " + ut.Itoa(this.GetCamp()) + " " + ut.Itoa(this.ctrl.GetRandom().GetSeed()))
}

// 回合结束
func (this *Fighter) EndAction() {
	this.roundCount += 1
	this.CleanCanAttackTargets()
	this.CleanBlackboard()
	this.CleanRoundBuffs()
	slg.Log(ut.Itoa(this.ctrl.GetCurrentFrameIndex()) + " <<<<<<<<< " + ut.Itoa(this.GetID()) + "(" + this.GetPoint().Join(",") + ") [" + this.GetUID() + "] ")
	slg.Log("-")
}

// 清理持续一回合的buff
func (this *Fighter) CleanRoundBuffs() {
	this.RemoveMultiBuff(bufftype.HIT_SUCK_BLOOD, bufftype.ROYAL_BLUE_DODGE, bufftype.ROYAL_BLUE_DAMAGE)
	// 虎痴 每回合衰减20%
	if buff := this.GetBuff(bufftype.TIGER_MANIA); buff != nil {
		buff.Value -= 20
		if buff.Value <= 0 {
			this.RemoveBuff(bufftype.TIGER_MANIA)
		}
	}
	// 三斧 增加次数
	if buff := this.GetBuff(bufftype.THREE_AXES); buff != nil {
		if buff.Value < 3 {
			buff.Value += 1
		} else {
			this.RemoveBuff(bufftype.THREE_AXES)
		}
	}
}

// 刷新buff
func (this *Fighter) UpdateBuff() {
	isJJStandShield, isJJ := false, this.entity != nil && this.entity.GetSkinID() == 3202105
	isPoisonedWine, isObsidianArmorDefense, isKerian, isSorrowfulDream := false, false, false, false
	anticipationDefenseValue := 0.0
	var tondenBuff *g.BuffObj = nil
	this.entity.BuffsLock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		m := buffs[i]
		if m.Type == bufftype.SORROWFUL_DREAM {
			isSorrowfulDream = true // 是否有 愁梦
		}
		if !m.UpdateRound(-1) {
			continue
		} else if m.Type == bufftype.POISONED_WINE {
			isPoisonedWine = true
			continue
		} else if m.Type == bufftype.STAND_SHIELD && isJJ {
			isJJStandShield = true // 判断是否重盾机甲的立盾
			continue
		} else if m.Type == bufftype.TONDEN_BEGIN {
			tondenBuff = m
		} else if m.Type == bufftype.OBSIDIAN_ARMOR_DEFENSE {
			isObsidianArmorDefense = true // 黑曜铠 防御结束
		} else if m.Type == bufftype.KERIAN {
			isKerian = true // 金戈结束
		} else if m.Type == bufftype.ANTICIPATION_DEFENSE {
			anticipationDefenseValue = m.Value // 李牧 蓄势减伤 结束
		}
		this.entity.RemoveBuffByIndexNoLock(i)
	}
	this.entity.BuffsUnlock()
	if isJJStandShield {
		this.SetBlackboard(0, "isPlayJJShieldEnd", true)
	}
	if isPoisonedWine {
		this.SetBlackboard(0, "isPoisonedWineEnd", true)
	}
	uid := this.GetUID()
	// 屯垦令结束 添加恢复buff
	if tondenBuff != nil {
		this.AddBuffValue(bufftype.TONDEN_RECOVER, tondenBuff.Provider, tondenBuff.Value)
	}
	// 黑曜铠减伤结束
	if isObsidianArmorDefense {
		this.AddBuff(bufftype.OBSIDIAN_ARMOR_NEGATIVE, uid, 1)
	}
	// 金戈 结束了 看是否有愁梦 如果没有就添加
	if isKerian && !isSorrowfulDream {
		this.AddBuff(bufftype.SORROWFUL_DREAM, uid, 1)
	}
	// 蓄势 结束了 添加攻击
	if anticipationDefenseValue > 0 {
		this.AddBuffValue(bufftype.ANTICIPATION_ATTACK, uid, anticipationDefenseValue)
	}
}

// 执行行为树
func (this *Fighter) BehaviorTick(dt int32) {
	if this.behavior.IsCanRun() {
		this.behavior.Tick(dt)
	} else {
		this.SetRoundEnd()
	}
}

func (this *Fighter) GetBlackboardData(id int32, key string) interface{} {
	this.blackboardMutex.Lock()
	defer this.blackboardMutex.Unlock()
	data := this.blackboard[id]
	if data == nil {
		data = map[string]interface{}{}
		this.blackboard[id] = data
	}
	return data[key]
}

func (this *Fighter) SetBlackboard(id int32, key string, val interface{}) {
	this.blackboardMutex.Lock()
	defer this.blackboardMutex.Unlock()
	data := this.blackboard[id]
	if data == nil {
		data = map[string]interface{}{}
		this.blackboard[id] = data
	}
	data[key] = val
}

// 清理数据
func (this *Fighter) CleanBlackboard(retainKeys ...string) {
	this.blackboardMutex.Lock()
	defer this.blackboardMutex.Unlock()
	var retainMap map[string]interface{}
	if data := this.blackboard[0]; data != nil {
		retainMap = map[string]interface{}{}
		for _, k := range retainKeys {
			retainMap[k] = data[k]
		}
	}
	this.blackboard = map[int32]map[string]interface{}{}
	this.blackboard[0] = retainMap
}

// 是否回合结束了
func (this *Fighter) IsRoundEnd() bool {
	return ut.Bool(this.GetBlackboardData(0, "isRoundEnd")) || this.IsDie()
}

// 设置回合结束
func (this *Fighter) SetRoundEnd() {
	this.SetBlackboard(0, "isRoundEnd", true)
}

// 添加回合结束延迟时间
func (this *Fighter) AddRoundEndDelayTime(val int32) {
	time := ut.Int32(this.GetBlackboardData(0, "addRoundEndDelayTime"))
	this.SetBlackboard(0, "addRoundEndDelayTime", time+val)
}

// 添加回合结束后的动作时间
func (this *Fighter) AddRoundEndActionTime(hitTime int32, sumTime int32) {
	this.SetBlackboard(0, "addRoundEndActionHitTime", hitTime)
	this.AddRoundEndDelayTime(sumTime)
}

// 设置战斗结束延迟时间
func (this *Fighter) SetBattleOverDelay(val int32) {
	this.SetBlackboard(0, "battleOverDelay", val)
}

func (this *Fighter) UpdateBattleOver(dt int32) bool {
	time := ut.Int32(this.GetBlackboardData(0, "battleOverDelay"))
	if time == 0 {
		return false
	}
	time -= dt
	if time <= 0 {
		this.SetBlackboard(0, "battleOverDelay", -1)
	} else {
		this.SetBlackboard(0, "battleOverDelay", time)
	}
	return true
}

func (this *Fighter) IsBattleOver() bool {
	return ut.Int32(this.GetBlackboardData(0, "battleOverDelay")) == -1
}

func (this *Fighter) GetWaitRound() int32 { return this.waitRound }
func (this *Fighter) SetWaitRound(val int32) {
	this.waitRound = val
}

func (this *Fighter) AddRoundCount(val int32) {
	this.roundCount += val
}

// 添加攻击次数
func (this *Fighter) AddAttackCount(val int32) {
	this.attackCount += val
}

func (this *Fighter) SetAttackCount(val int32) {
	this.attackCount = val
}

func (this *Fighter) CleanCanAttackTargets() {
	this.canAttackTargets = nil
	this.canAttackFighters = nil
	this.tempAttackRangePointMap = nil
	this.tempMovePathMap = nil
}

func (this *Fighter) GetCanAttackFighters() []g.IFighter {
	if this.canAttackFighters == nil {
		this.GetCanAttackTargets()
	}
	if this.canAttackFighters == nil {
		return []g.IFighter{}
	}
	return this.canAttackFighters
}

// 获取攻击列表
func (this *Fighter) GetCanAttackTargets() []g.CanAttackTargetInfo {
	if this.canAttackTargets != nil {
		return this.canAttackTargets
	}
	// log.Info("GetCanAttackTargets uid: %v", this.GetUID())
	this.tempMaxSearchCount = 1000
	this.canAttackTargets = []g.CanAttackTargetInfo{}
	this.canAttackFighters = []g.IFighter{}
	this.tempAttackRangePointMap = map[string][]*ut.Vec2{}
	this.tempMovePathMap = map[string][][]*ut.Vec2{}
	camp := this.camp
	// 是否有混乱
	isChaos := this.ctrl.GetRandom().Chance(int(this.GetBuffValue(bufftype.CHAOS)))
	fighters := this.ctrl.GetFighters()
	uid := this.GetUID()
	for _, m := range fighters {
		if m.GetUID() == uid {
			continue
		}
		can := ut.If(isChaos, m.GetCamp() == camp, m.GetCamp() != camp)
		if can && !m.IsDie() && (m.IsPawn() || m.IsFlag()) {
			this.canAttackFighters = append(this.canAttackFighters, m)
		}
	}
	// 如果没有攻击对象了 那么看是否可以攻击中心
	if len(this.canAttackFighters) == 0 && this.ctrl.GetMainCamp() != this.camp {
		doors := this.ctrl.GetMainDoors()
		for _, m := range doors {
			this.canAttackFighters = append(this.canAttackFighters, m)
		}
	}
	// 先排个距离序 为了后面更效率的计算权重
	point := this.GetPoint()
	sort.Slice(this.canAttackFighters, func(i, j int) bool {
		fi, fj := this.canAttackFighters[i], this.canAttackFighters[j]
		wi := helper.GetPointToPointDis(fi.GetPoint(), point)*1000 + fi.GetAttackIndex()
		wj := helper.GetPointToPointDis(fj.GetPoint(), point)*1000 + fj.GetAttackIndex()
		return wi < wj
	})
	// 开始计算权重
	heroSkillId := this.GetPortrayalSkillID()
	isCx, isHz, isTower := heroSkillId == hero.CAO_XIU, heroSkillId == hero.HUANG_ZHONG, this.IsTower()
	for _, m := range this.canAttackFighters {
		this.addCanAttackTarget(m, isCx, isHz, isTower)
	}
	// 根据权重排序 找出最优的目标
	sort.Slice(this.canAttackTargets, func(i, j int) bool {
		return this.canAttackTargets[i].Weight > this.canAttackTargets[j].Weight
	})
	// for _, m := range this.canAttackTargets {
	// 	fmt.Println(m.Point.ID(), m.Weight)
	// }
	// fmt.Println("--------------------------------------------")
	return this.canAttackTargets
}

// 获取可攻击的点位
func (this *Fighter) GetTargetCanAttackPoints(target g.IFighter) []*ut.Vec2 {
	ctrl := this.GetCtrl()
	attackRange := this.entity.GetAttackRange()
	attackRangeKey := target.GetUID() + "_" + ut.Itoa(attackRange)
	points := this.tempAttackRangePointMap[attackRangeKey]
	if points == nil {
		points = []*ut.Vec2{}
		pointMap := map[string]bool{}
		targetPoints := target.GetPoints()
		for _, targetPoint := range targetPoints {
			arr := this.searchRange.Search(targetPoint, attackRange, false)
			for _, m := range arr {
				id := m.ID()
				if !pointMap[id] {
					pointMap[id] = true
					points = append(points, m)
				}
			}
		}
		// 筛选位置
		point := this.GetPoint()
		points = array.Filter(points, func(m *ut.Vec2, _ int) bool { return m.Equals(point) || !ctrl.CheckHasFighter(m.X, m.Y) })
		this.tempAttackRangePointMap[attackRangeKey] = points
	}
	return points
}

func (this *Fighter) addCanAttackTarget(target g.IFighter, isCx bool, isHz bool, isTower bool) {
	targetPoint := target.GetPoint()
	point := this.entity.GetPoint()
	attackRange := this.entity.GetAttackRange()
	addTarget := true
	// 获取距离
	dis := this.GetMinDis(target)
	// 是否克制
	isRestrain := this.GetSkillTargetByType(constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN) == target.GetPawnType()
	// 计算权重分
	var weight, moveWeight int64
	var attackTargetPoint *ut.Vec2
	paths := []*ut.Vec2{}
	if dis <= attackRange {
		if isCx {
			weight = 2000000 + int64(dis) // 在攻击范围内 就只看距离 曹休看最远的
		} else if isHz {
			weight = 2000000 + int64((1.0-target.GetHPRatio())*100.0) // 看生命比最低的
			weight = weight*100 + (99 - int64(dis))
		} else if isTower {
			weight = 2000000 + int64(ut.If(target.IsHasBuff(bufftype.PRESTAGE), 1, 0)) // 箭塔优先攻击有先登的目标
			weight = weight*100 + (99 - int64(dis))
		} else {
			weight = 2000000 + (99 - int64(dis)) // 在攻击范围内 就只看距离
		}
		this.tempMaxSearchCount = 0 // 如果已经有在攻击范围内的了就不要在搜索了
	} else if this.tempMaxSearchCount > 0 && this.entity.GetMoveRange() > 0 {
		points := this.GetTargetCanAttackPoints(target)
		_paths, frontWeight, _attackTargetPoint, _, _moveWeight := this.findPaths(point, points, this.entity.GetMoveRange(), this.GetSearchDir(target), targetPoint)
		if _attackTargetPoint == nil {
			// log.Info("abandom target fighterUid: %v, targetUid: %v", this.GetUID(), target.GetUID())
			// 未获取到锁定点位 放弃该目标
			return
		}
		if frontWeight == 0 {
			frontWeight = int(99 - dis) // 无路可走看距离
		}
		weight = 1000000 + int64(frontWeight)
		paths = _paths
		moveWeight = int64(_moveWeight)
		attackTargetPoint = _attackTargetPoint
	} else {
		weight = 1000000 + (99 - int64(dis)) // 只看距离
	}
	// weight = weight*10 + int64(ut.If(isRestrain, 1, 0))
	// weight = weight * 100 + points.length
	weight = weight*10 + int64(target.GetAttackSpeed())
	weight = weight*1000 + (999 - int64(target.GetAttackIndex()))
	// 判断目标被集火的战斗力是否足够 未在攻击范围内的才做火力判断
	// if (target.CheckTargetBeAttackedEnough() && dis > attackRange) {
	// 	minWeightFighter := target.GetBeTargetedMinWeightFighter()
	// 	if (minWeightFighter != nil) {
	// 		if (weight <= minWeightFighter.GetAttackTargetWeight()) {
	// 			// 火力足够且权重小于目标列表最小权重 放弃该目标
	// 			addTarget = false
	// 		}
	// 	}
	// }
	if addTarget {
		this.canAttackTargets = append(this.canAttackTargets, g.CanAttackTargetInfo{
			Target: target,
			UID:    target.GetUID(),
			Point:  targetPoint,
			Dis:    dis,
			Paths:  paths,
			// Points:            points,
			AttackIndex:       target.GetAttackIndex(),
			IsRestrain:        isRestrain,
			Weight:            weight,
			AttackTargetPoint: attackTargetPoint,
			MoveWeight:        moveWeight,
		})
	}
}

// // 找出最佳路径
func (this *Fighter) findPaths(point *ut.Vec2, points []*ut.Vec2, moveRange, searchDir int32, targetFighterPoint *ut.Vec2) ([]*ut.Vec2, int, *ut.Vec2, int, int) {
	camp := this.camp
	areaSize := this.ctrl.GetAreaSize()
	paths, weight, frontWeight, moveWeight, lockWeight := []*ut.Vec2{}, 0, 0, 0, 0
	var targetPoint *ut.Vec2
	for i, l := 0, len(points); i < l; i++ {
		target := points[i]
		searchKey := point.ID() + "_" + target.ID() + "_" + ut.Itoa(searchDir) + "_" + ut.Itoa(moveRange) + "_" + ut.Itoa(camp)
		pathsArray := this.tempMovePathMap[searchKey]
		if pathsArray == nil {
			pathsArray = this.astar.Search(point, target, searchDir, moveRange, camp, this.tempMaxSearchCount)
			this.tempMovePathMap[searchKey] = pathsArray
		}
		pLen := len(pathsArray)
		if pLen == 0 {
			continue
		} else if l := len(pathsArray[pLen-1]); l == 0 || !pathsArray[pLen-1][l-1].Equals(target) {
			continue // 到不了就不动了
		}
		if int32(pLen) < this.tempMaxSearchCount {
			this.tempMaxSearchCount = int32(pLen) // 记录最小搜索次数
		}
		// fightPower := this.GetFightPower(targetFighter)
		w := 99 - pLen // 最短回合数
		w = w*100 + this.getPathArrayWeight(pathsArray)
		w = w*100 + (99 - int(helper.GetPointToPointDis(targetFighterPoint, pathsArray[0][len(pathsArray[0])-1]))) // 路径最后一个位置 和 目标位置 的距离  越近越好
		fw := w
		w = w*100 + (99 - int(helper.GetPointToPointDis(point, target))) // 自己起点位置 和 攻击目标位置 的距离  越近越好
		// w = w*100000 + fightPower
		w = w*1000 + int(target.ToIndex(areaSize))
		lw := w
		if pLen > 1 {
			// 大于1回合到达则与锁定该点的单位比较权重
			lockPointInfo := this.ctrl.GetLockMovePointFighter(target)
			// if lockPointInfo != nil {
			// 	log.Info("findPaths pointLock uid: %v point: %v w: %v tw: %v", lockPointInfo.Fighter.GetUID(), target, w, lockPointInfo.Weight)
			// }
			if lockPointInfo != nil && lockPointInfo.Fighter != this && lockPointInfo.Fighter.GetCamp() == this.GetCamp() && lockPointInfo.Weight >= int64(w) {
				// 目标点已被同阵营其他单位锁定 当前权重更小 则路径权重设为无穷小
				w, lw = 0, 0
				if this.tempMaxSearchCount == int32(pLen) {
					// 最大搜索次数重置为默认
					this.tempMaxSearchCount = 1000
				}
			}
		}
		if lw > lockWeight {
			lockWeight = lw
			targetPoint = target
		}
		if w > weight {
			weight = w
			frontWeight = fw
			paths = pathsArray[0]
			// 计算移动后的权重
			if pLen == 1 {
				moveWeight = 2000000000 + (100 - len(pathsArray[0]))
			} else {
				moveWeight = 99 - (pLen - 1)
				mw, l := 9, len(pathsArray)-1
				if l > 1 {
					mw = len(pathsArray[1]) - 1
				}
				moveWeight = moveWeight*100 + mw*10 + (10 - len(pathsArray[l-1]) + 1)
				moveWeight = moveWeight*100 + (99 - int(helper.GetPointToPointDis(pathsArray[1][0], target))) // 移动后离目标的距离
				moveWeight = moveWeight*100 + (99 - int(helper.GetPointToPointDis(point, target)))            // 自己起点位置 和 攻击目标位置 的距离  越近越好
				moveWeight = moveWeight*1000 + int(target.ToIndex(areaSize))
			}
		}
	}
	return paths, frontWeight, targetPoint, weight, moveWeight
}

// 获取路径段权重
// 暂时只看第一段和最后一段 第一段长越好 最后一段越短越好
func (this *Fighter) getPathArrayWeight(pathsArray [][]*ut.Vec2) int {
	w, l := 9, len(pathsArray)
	if l > 1 {
		w = len(pathsArray[0]) - 1
	}
	return w*10 + (10 - len(pathsArray[l-1]) + 1)
}

// 获取可以被击退的点
func (this *Fighter) GetCanByReqelPoint(point *ut.Vec2, rang int32) *ut.Vec2 {
	p := this.GetPoint()
	d := helper.GetPointToPointDis(point, p)
	// 先找出目标周围可攻击的位置
	points := this.searchRange.Search(p, rang, false)
	// 筛选位置
	points = array.Filter(points, func(m *ut.Vec2, _ int) bool {
		return !this.ctrl.CheckHasFighter(m.X, m.Y) && helper.GetPointToPointDis(point, m) > d
	})
	if len(points) > 0 {
		areaSize := this.ctrl.GetAreaSize()
		sort.Slice(points, func(i, j int) bool {
			a, b := points[i], points[j]
			aw := point.Sub(a).MagSqr()*1000 + a.ToIndex(areaSize)
			bw := point.Sub(b).MagSqr()*1000 + b.ToIndex(areaSize)
			return aw > bw
		})
		return points[0]
	}
	return nil
}

// 是否在攻击范围
func (this *Fighter) CheckInAttackRange(target g.IFighter, attackRange int32) bool {
	return array.Some(this.GetPoints(), func(point *ut.Vec2) bool {
		return array.Some(target.GetPoints(), func(m *ut.Vec2) bool { return helper.GetPointToPointDis(point, m) <= attackRange })
	})
}

func (this *Fighter) CheckInMyAttackRange(target g.IFighter) bool {
	return this.CheckInAttackRange(target, this.GetAttackRange())
}

// 获取最近距离
func (this *Fighter) GetMinDis(target g.IFighter) int32 {
	minDis := helper.GetPointToPointDis(this.GetPoint(), target.GetPoint())
	if this.IsBoss() || target.IsBoss() {
		points := this.GetPoints()
		targetPoints := target.GetPoints()
		for _, point := range points {
			for _, m := range targetPoints {
				dis := helper.GetPointToPointDis(point, m)
				if dis < minDis {
					minDis = dis
				}
			}
		}
	}
	return minDis
}

// 获取可范围攻击的目标 只有士兵
func (this *Fighter) GetCanAttackPawnByRange(fighters []g.IFighter, rang, cnt int32, ignoreUid string) []g.IFighter {
	return this.GetCanAttackRangeFighter(fighters, rang, cnt, ignoreUid, func(m g.IFighter) bool { return m.IsFlag() })
}

// 获取可范围攻击的目标 包含军旗
func (this *Fighter) GetCanAttackFighterByRange(fighters []g.IFighter, rang, cnt int32, ignoreUid string) []g.IFighter {
	return this.GetCanAttackRangeFighter(fighters, rang, cnt, ignoreUid, nil)
}

// 获取可范围攻击的目标
func (this *Fighter) GetCanAttackRangeFighter(fighters []g.IFighter, rang, cnt int32, ignoreUid string, cb func(m g.IFighter) bool) []g.IFighter {
	targets, overlaps := this.GetCanAttackRangeTargets(this.GetPoint(), fighters, rang, ignoreUid, cb)
	tLen := int32(len(targets))
	if tLen < cnt && len(overlaps) > 0 {
		sort.Slice(overlaps, func(i, j int) bool { return overlaps[i].GetTempRandomVal() < overlaps[j].GetTempRandomVal() })
		return append(targets, overlaps[:ut.MinInt32(int32(len(overlaps)), cnt-tLen)]...)
	}
	return targets[:ut.MinInt32(tLen, cnt)]
}

// 根据位置获取目标
func (this *Fighter) GetCanAttackRangeFighterByPoint(point *ut.Vec2, fighters []g.IFighter, rang, cnt int32, cb func(m g.IFighter) bool) []g.IFighter {
	targets, overlaps := this.GetCanAttackRangeTargets(point, fighters, rang, "", cb)
	tLen := int32(len(targets))
	if tLen < cnt && len(overlaps) > 0 {
		sort.Slice(overlaps, func(i, j int) bool { return overlaps[i].GetTempRandomVal() < overlaps[j].GetTempRandomVal() })
		return append(targets, overlaps[:ut.MinInt32(int32(len(overlaps)), cnt-tLen)]...)
	}
	return targets[:ut.MinInt32(tLen, cnt)]
}

// 获取可范围攻击的目标
func (this *Fighter) GetCanAttackRangeTargets(point *ut.Vec2, fighters []g.IFighter, rang int32, ignoreUid string, cb func(m g.IFighter) bool) ([]g.IFighter, []g.IFighter) {
	if len(fighters) == 0 {
		return []g.IFighter{}, []g.IFighter{}
	}
	targets := []g.IFighter{}
	pointMap, overlaps := map[string]bool{}, []g.IFighter{}
	random := this.GetCtrl().GetRandom()
	for _, m := range fighters {
		p := m.GetPoint()
		if m.GetUID() == ignoreUid || m.IsDie() || (!m.IsPawn() && !m.IsFlag()) || helper.GetPointToPointDis(point, p) > rang {
			continue
		} else if cb != nil && cb(m) {
			continue
		} else if id := p.ID(); !pointMap[id] {
			pointMap[id] = true
			targets = append(targets, m) // 优先选择没有重叠的
		} else {
			m.SetTempRandomVal(random.GetInt32(1, 100)*10000 + m.GetAttackIndex())
			overlaps = append(overlaps, m) // 这里就是重叠一起的
		}
	}
	return targets, overlaps
}

func (this *Fighter) GetAttackTargetUID() string {
	if this.attackTarget != nil {
		return this.attackTarget.GetUID()
	}
	return ""
}

func (this *Fighter) SetAttackTarget(val g.IFighter) {
	this.attackTarget = val
}

// 切换攻击目标
func (this *Fighter) ChangeAttackTarget(val g.IFighter) g.IFighter {
	oldUid, newUId := "", ""
	if this.attackTarget != nil {
		oldUid = this.attackTarget.GetUID()
	}
	if val != nil {
		newUId = val.GetUID()
	}
	this.attackTarget = val
	// 切换目标
	if oldUid != newUId && val != nil {
		// 程咬金 记录目标
		if this.CheckPortrayalSkill(hero.CHENG_YAOJIN) == nil {
		} else if val.IsPawn() {
			buff := this.GetBuff(bufftype.THREE_AXES)
			if buff == nil {
				this.AddBuffValue(bufftype.THREE_AXES, newUId, 1)
			} else if buff.Provider != newUId {
				buff.Provider = newUId
				buff.Value = 1
			}
		} else {
			this.RemoveBuff(bufftype.THREE_AXES)
		}
	}
	return this.attackTarget
}

func (this *Fighter) GetHPRatio() float64 {
	return float64(this.entity.GetCurHP()) / float64(this.entity.GetMaxHP())
}

// 切换状态
func (this *Fighter) ChangeState(state int32) {
}

func (this *Fighter) UpdateAttrByBattle(id, lv int32) {
}

// 获取训练方向
func (this *Fighter) GetSearchDir(attackTarget g.IFighter) int32 {
	if attackTarget == nil {
		return this.GetEnterDir()
	}
	dirA, dirB := this.GetEnterDir(), attackTarget.GetEnterDir()
	if dirA == -1 { // 自己没有方向 去目标的反方向
		return (dirB + 2) % 4
	}
	return dirA
}

func (this *Fighter) IsDie() bool {
	return this.entity.IsDie()
}

// 是否满血
func (this *Fighter) IsFullHP() bool {
	return this.entity.GetCurHP() >= this.entity.GetMaxHP()
}

// 受击前处理
func (this *Fighter) HitPrepDamageHandle(damage, trueDamage int32) (int32, int32) {
	if damage <= 0 && trueDamage <= 0 {
		return damage, trueDamage
	}
	// 检测受击伤害提升
	hitDamageAmend := 1.0
	if this.GetEquipEffectByType(eeffect.DOUBLE_EDGED_SWORD) != nil {
		hitDamageAmend += 1 // 月牙
	}
	// 检测buff
	buffs := this.entity.GetBuffs()
	for _, m := range buffs {
		if m.Type == bufftype.ARMOR_PENETRATION { // 破甲
			hitDamageAmend += (m.Value * float64(m.Lv)) * 0.01
		} else if m.Type == bufftype.PARALYSIS_UP { // 毒弓 易损
			hitDamageAmend += m.Value
		} else if m.Type == bufftype.RESOLUTE { // 于禁 毅重
			hitDamageAmend += m.Value * 0.01
		} else if m.Type == bufftype.OBSIDIAN_ARMOR_NEGATIVE { // 黑曜铠
			hitDamageAmend += m.Value * 0.01
		}
	}
	// 计算
	if damage > 0 {
		damage = ut.RoundInt32(float64(damage) * hitDamageAmend)
	}
	if trueDamage > 0 {
		trueDamage = ut.RoundInt32(float64(trueDamage) * hitDamageAmend)
	}
	return damage, trueDamage
}

// 受击
func (this *Fighter) OnHit(damage, baseDamage int32) (int32, int32, int32) {
	if damage <= 0 {
		return damage, 0, 0
	}
	var val, heal, hitShield int32 = damage, 0, 0
	// 获取护盾buff
	buffs := this.getShieldBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		buff := buffs[i]
		value := int32(buff.Value)
		if val >= value {
			val -= value
			this.RemoveBuff(buff.Type)
		} else {
			buff.SetValue(float64(value - val))
			val = 0
			break
		}
	}
	hitShield = damage - val
	//
	hp := this.entity.GetCurHP()
	if val > hp {
		damage = damage - val + hp
		this.entity.SetCurHP(0)
	} else {
		this.entity.SetCurHP(hp - val)
	}
	// 鸩毒记录伤害
	if val > 0 {
		if buff := this.GetBuff(bufftype.POISONED_WINE); buff != nil {
			buff.ChangeValue(float64(val))
		}
	}
	// 阵亡时
	if this.IsDie() {
		// 周泰 有一定概率恢复10%生命
		buff := this.GetBuff(bufftype.DYING_RECOVER)
		if buff != nil && this.ctrl.GetRandom().ChanceInt32(buff.GetValueInt()) {
			buff.Value = math.Max(buff.Value-10, 50)
			heal += this.OnHeal(ut.RoundInt32(float64(this.GetMaxHp())*0.2), false) // 回复血量
			heal = this.doHitAfterByHPLess(heal)
		} else {
			this.ctrl.DoDieAfter(this)
		}
	} else {
		heal = this.doHitAfterByHPLess(heal)
	}
	slg.Log(ut.Itoa(this.ctrl.GetCurrentFrameIndex()) + " OnHit " + ut.Itoa(this.GetID()) + "(" + this.GetPoint().Join(",") + ") " + ut.Itoa(damage) + "_" + ut.Itoa(val) + " " + ut.Itoa(this.entity.GetCurHP()) + "/" + ut.Itoa(this.entity.GetMaxHP()) + " [" + this.GetUID() + "]")
	if this.entity != nil {
		this.ctrl.AddFighterBattleHitDamage(this.GetUID(), this.entity.GetOwner(), damage, baseDamage)
	}
	return damage, heal, hitShield
}

// 生命低于多少处理
func (this *Fighter) doHitAfterByHPLess(heal int32) int32 {
	if !this.IsPawn() {
		return heal
	}
	hpRatio, effects := this.GetHPRatio(), this.GetEquipEffects()
	for _, m := range effects {
		if m.Type == eeffect.LOW_HP_SHIELD { // 血量低于多少给护盾和回复血量
			if buff := this.GetBuff(bufftype.CHECK_LOW_HP); buff != nil && hpRatio <= buff.Value { // 检测血量是否低于多少
				this.RemoveMultiBuff(bufftype.CHECK_LOW_HP)                                                    // 先删除
				this.AddBuffValue(bufftype.LOW_HP_SHIELD, this.GetUID(), float64(this.entity.GetLV())*m.Value) // 添加buff
				heal += this.OnHeal(ut.RoundInt32(float64(this.entity.GetMaxHP())*0.3), false)                 // 回复血量
			}
		} else if m.Type == eeffect.LOW_HP_ATTACk { // 血量低于多少给攻击力和回复血量
			if buff := this.GetBuff(bufftype.CHECK_LOW_HP_ATTACK); buff != nil && hpRatio <= buff.Value {
				this.RemoveMultiBuff(bufftype.CHECK_LOW_HP_ATTACK) // 先删除
				// 添加buff 提高攻击力
				this.AddBuff(bufftype.LOW_HP_ADD_ATTACK, this.GetUID(), this.entity.GetLV())
				// 添加buff 增加吸血
				this.AddBuff(bufftype.LOW_HP_ADD_SUCKBLOOD, this.GetUID(), 1).Value = m.Value
			}
		}
	}
	heroSkill := this.entity.GetPortrayalSkill()
	if heroSkill != nil {
		// 许褚 添加buff
		if heroSkill.Id == hero.XU_CHU && hpRatio < 0.5 && !this.IsHasBuff(bufftype.NAKED_CLOTHES) {
			this.AddBuff(bufftype.NAKED_CLOTHES, this.GetUID(), 1)
		}
	}
	// 韬略 血量低于多少回血
	sb := this.GetStrategyBuff(31501)
	if sb == nil {
		sb = this.GetStrategyBuff(30702)
	}
	if sb != nil && hpRatio <= (sb.GetParams()*0.01) && !this.IsHasBuff(bufftype.S_RECORD_LOW_RECOVER_HP) {
		this.AddBuff(bufftype.S_RECORD_LOW_RECOVER_HP, this.GetUID(), 1)
		heal += this.OnHeal(ut.RoundInt32(float64(this.GetMaxHp())*sb.GetValue()*0.01), false) // 回复血量
	}
	// 韬略 血量低于多少加攻击力
	sb = this.GetStrategyBuff(40404)
	if sb != nil && hpRatio < (sb.GetParams()*0.01) && !this.IsHasBuff(bufftype.LOW_HP_ADD_ATTACK_S) {
		this.AddBuff(bufftype.LOW_HP_ADD_ATTACK_S, this.GetUID(), 1).Value = sb.GetValue()
	}
	return heal
}

// 回血
func (this *Fighter) OnHeal(val int32, suckbloodShield bool) int32 {
	if val == 0 {
		return val
	}
	curHp, maxHp := this.entity.GetCurHP(), this.entity.GetMaxHP()
	// 是否有增益韬略
	gain := this.GetStrategyValue(50012) + this.GetStrategyValue(40106) + this.GetStrategyValue(40205) + this.GetStrategyValue(40405)
	val += ut.RoundInt32(float64(val) * float64(gain) * 0.01)
	// 是否有减益buff
	if buff := this.CheckTriggerBuff(bufftype.SERIOUS_INJURY); buff != nil {
		val = ut.RoundInt32(float64(val) * (1 - buff.Value))
	}
	hp := curHp + val
	if hp <= maxHp {
	} else if suckbloodShield { // 是否有吸血护盾
		shield := hp - maxHp
		buff := this.GetBuffOrAdd(bufftype.SUCKBLOOD_SHIELD, this.GetUID())
		maxShield := this.GetLV() * 6
		if value := buff.GetValueInt(); value+shield > maxShield {
			shield = maxShield - value
		}
		if shield > 0 {
			shield += ut.RoundInt32(float64(shield) * float64(this.GetStrategyValue(30601)) * 0.01)
		}
		buff.ChangeValue(float64(shield))
		val = maxHp - curHp + shield
	} else {
		val = maxHp - curHp
	}
	this.entity.SetCurHP(ut.MinInt32(curHp+val, maxHp))
	slg.Log(ut.Itoa(this.ctrl.GetCurrentFrameIndex()) + " OnHeal " + ut.Itoa(this.GetID()) + "(" + this.GetPoint().Join(",") + ") " + ut.Itoa(val) + " " + ut.Itoa(this.entity.GetCurHP()) + "/" + ut.Itoa(this.entity.GetMaxHP()) + " [" + this.GetUID() + "]")
	this.ctrl.AddFighterBattleInfo(this.GetUID(), this.entity.GetOwner(), bdtype.HEAL_HP, val)
	return val
}

func (this *Fighter) IsHasAnger() bool { return this.entity.IsHasAnger() }

func (this *Fighter) GetAngerRatio() float64 {
	if this.entity.GetMaxAnger() == 0 {
		return 0
	}
	return float64(this.entity.GetCurAnger()) / float64(this.entity.GetMaxAnger())
}

func (this *Fighter) GetCurAnger() int32 { return this.entity.GetCurAnger() }

// 是否可超出怒气上限
func (this *Fighter) IsCanLimitMaxAnger() bool {
	return this.GetStrategyBuff(50014) != nil
}

// 是否无法添加怒气
func (this *Fighter) IsNotAddAngerByBuff() bool {
	if this.IsDie() || !this.entity.IsHasAnger() {
		return true
	}
	// 是否有盾和立盾的时候不能涨怒气 还有沉默
	uid := this.entity.GetUID()
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	return array.Some(this.entity.GetBuffs(), func(m *g.BuffObj) bool {
		return ((m.Type == bufftype.SHIELD || m.Type == bufftype.PROTECTION_SHIELD) && m.Provider == uid) ||
			m.Type == bufftype.RODELERO_SHIELD ||
			m.Type == bufftype.RODELERO_SHIELD_001 ||
			m.Type == bufftype.RODELERO_SHIELD_102 ||
			m.Type == bufftype.STAND_SHIELD ||
			m.Type == bufftype.SILENCE
	})
}

// 增加怒气
func (this *Fighter) AddAnger(v int32) {
	if v <= 0 || this.IsNotAddAngerByBuff() {
		return
	}
	this.addActAnger(v, this.IsCanLimitMaxAnger())
}

func (this *Fighter) addActAnger(val int32, canLimit bool) {
	anger := this.entity.GetCurAnger() + val
	if canLimit {
		this.entity.SetCurAnger(anger)
	} else if this.entity.GetCurAnger() < this.entity.GetMaxAnger() {
		this.entity.SetCurAnger(ut.MinInt32(this.entity.GetMaxAnger(), anger))
	}
}

// 扣除怒气
func (this *Fighter) DeductAnger(v int32) {
	this.entity.SetCurAnger(ut.MaxInt32(0, this.entity.GetCurAnger()-v))
}

// 设置怒气
func (this *Fighter) SetAnger(val int32) {
	this.entity.SetCurAnger(val)
}

// 设置满怒气
func (this *Fighter) SetFullAnger(ratio float64, check bool) {
	val := ut.RoundInt32(float64(this.entity.GetMaxAnger()) * ratio)
	if check {
		this.AddAnger(val)
	} else {
		this.addActAnger(val, this.IsCanLimitMaxAnger())
	}
}

// 是否可以释放技能
func (this *Fighter) IsCanUseSkill() bool {
	curAnger, maxAnger := this.entity.GetCurAnger(), this.entity.GetMaxAnger()
	if curAnger == 0 && maxAnger == 0 {
		return false
	}
	return curAnger >= maxAnger
}

// 是否免疫负面效果
func (this *Fighter) IsImmuneNegativeBuff() bool {
	if this.IsHasBuffs(bufftype.PROTECTION_NIE, bufftype.RESOLUTE) {
		return true
	} else if this.ctrl.GetRandom().ChanceInt32(this.GetStrategyValue(40203)) {
		return true // 韬略
	}
	return false
}

// 添加buff
func (this *Fighter) _addBuff(tp int32, provider string, lv int32) *g.BuffObj {
	if !this.IsPawn() {
		return g.EMPTY_BUFF // 只有士兵才有buff
	}
	json := config.GetJsonData("buffBase", tp)
	if json == nil {
		return g.EMPTY_BUFF
	} else if ut.Int(json["effect_type"]) != 0 {
	} else if this.IsImmuneNegativeBuff() {
		return g.EMPTY_BUFF // 如果有免疫负面效果 那么就不添加
	} else {
		// 给5格内的徐盛加怒气
		fighters := this.ctrl.GetHeroFighters(hero.XU_SHENG, this.camp, "")
		for _, m := range fighters {
			if helper.GetPointToPointDis(m.GetPoint(), this.GetPoint()) <= 5 {
				m.AddAnger(1)
			}
		}
	}
	this.entity.BuffsRLock()
	buff := array.Find(this.entity.GetBuffs(), func(m *g.BuffObj) bool { return m.Type == tp })
	this.entity.BuffsRUnlock()
	if buff == nil {
		buff = new(g.BuffObj)
		this.entity.AddBuff(buff)
	}
	return buff.Init(tp, provider, lv, ut.If(provider == this.GetUID(), int32(0), 1))
}

// 添加buff
func (this *Fighter) AddBuff(tp int32, provider string, lv int32) *g.BuffObj {
	buff := this._addBuff(tp, provider, lv)
	return this.checkUpShieldBuffValue(buff)
}

func (this *Fighter) AddBuffValue(tp int32, provider string, value float64) *g.BuffObj {
	buff := this._addBuff(tp, provider, 1)
	buff.Value = value
	return this.checkUpShieldBuffValue(buff)
}

// 检测提升护盾buff效果
func (this *Fighter) checkUpShieldBuffValue(buff *g.BuffObj) *g.BuffObj {
	if !buff.IsHasShield() {
		return buff
	}
	// 韬略 提高护盾值
	add := float64(this.GetStrategyValue(30601)) * 0.01
	// 装备 赤金盾
	if CRIMSONGOLD_SHIELD := this.GetEquipEffectByType(eeffect.CRIMSONGOLD_SHIELD); CRIMSONGOLD_SHIELD != nil {
		add += CRIMSONGOLD_SHIELD.Value * 0.01
	}
	buff.Value += math.Round(buff.Value * add)
	return buff
}

func (this *Fighter) IsHasNegativeBuff() bool {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	return array.Some(this.entity.GetBuffs(), func(m *g.BuffObj) bool { return m.GetEffectType() == 0 })
}

func (this *Fighter) IsHasBuff(tp int32) bool {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	return array.Some(this.entity.GetBuffs(), func(m *g.BuffObj) bool { return m.Type == tp })
}

func (this *Fighter) IsHasBuffs(types ...int32) bool {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	return array.Some(this.entity.GetBuffs(), func(m *g.BuffObj) bool { return array.Has(types, m.Type) })
}

func (this *Fighter) GetBuffs() []*g.BuffObj {
	return this.entity.GetBuffs()
}

func (this *Fighter) GetBuff(tp int32) *g.BuffObj {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	if buff := array.Find(this.entity.GetBuffs(), func(m *g.BuffObj) bool { return m.Type == tp }); buff != nil {
		return buff
	}
	return nil
}

func (this *Fighter) GetBuffValue(tp int32) float64 {
	if buff := this.GetBuff(tp); buff != nil {
		return buff.Value
	}
	return 0
}

func (this *Fighter) GetBuffOrAdd(tp int32, provider string) *g.BuffObj {
	buff := this.GetBuff(tp)
	if buff == nil {
		buff = this.AddBuff(tp, provider, 1)
	}
	return buff
}

// 获取护盾buffs列表 并从大到小排序
func (this *Fighter) getShieldBuffs() []*g.BuffObj {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	buffs := array.Filter(this.entity.GetBuffs(), func(m *g.BuffObj, _ int) bool { return m.IsHasShield() })
	sort.Slice(buffs, func(i, j int) bool {
		return buffs[i].Value > buffs[j].Value
	})
	return buffs
}

// 检测是否触发某个buff
func (this *Fighter) CheckTriggerBuff(tp int32) *g.BuffObj {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; buff.Type == tp {
			if buff.UpdateTimes(-1) {
				this.entity.RemoveBuffByIndexNoLock(i)
			}
			return buff
		}
	}
	return nil
}

// 检测是否触发某个buff
func (this *Fighter) CheckTriggerBuffOr(types ...int32) *g.BuffObj {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; array.Has(types, buff.Type) {
			if buff.UpdateTimes(-1) {
				this.entity.RemoveBuffByIndexNoLock(i)
			}
			return buff
		}
	}
	return nil
}

// 删除一个buff
func (this *Fighter) RemoveBuff(tp int32) bool {
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; buff.Type == tp {
			this.entity.RemoveBuffByIndexNoLock(i)
			return true
		}
	}
	return false
}

// 删除多个buff
func (this *Fighter) RemoveMultiBuff(types ...int32) {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; array.Has(types, buff.Type) {
			this.entity.RemoveBuffByIndexNoLock(i)
		}
	}
}

// 删除所有护盾buff
func (this *Fighter) RemoveShieldBuffs() {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; buff.Type == bufftype.STAND_SHIELD || buff.IsHasShield() {
			this.entity.RemoveBuffByIndexNoLock(i)
		}
	}
}

// 删除所有负面效果
func (this *Fighter) RemoveAllDebuff() {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; buff.GetEffectType() == 0 {
			this.entity.RemoveBuffByIndexNoLock(i)
		}
	}
}

// 删除指定某人施加的buff
func (this *Fighter) RemoveBuffByProvider(tp int32, provider string) bool {
	this.entity.BuffsLock()
	defer this.entity.BuffsUnlock()
	buffs := this.entity.GetBuffs()
	for i := int32(len(buffs)) - 1; i >= 0; i-- {
		if buff := buffs[i]; buff.Type == tp && buff.Provider == provider {
			this.entity.RemoveBuffByIndexNoLock(i)
			return true
		}
	}
	return false
}

// 刷新韬略效果
func (this *Fighter) UpdateStrategyEffect() {
	if this.entity == nil || !this.IsPawn() {
		return
	}
	oldMaxHp := int32(this.GetBuffValue(bufftype.S_RECORD_HP))
	if oldMaxHp == 0 {
		oldMaxHp = this.GetMaxHp()
	}
	// 重新添加韬略
	this.entity.CleanStrategyBuffs()
	strategyMap := this.ctrl.GetCampStrategyMap(this.camp)
	for _, m := range strategyMap {
		if this.CheckStrategyTarget(m) {
			this.entity.AddStrategyBuff(m)
		}
	}
	// 调整血量
	if newMaxHp := this.GetMaxHp(); oldMaxHp != newMaxHp {
		this.AddBuff(bufftype.S_RECORD_HP, this.GetUID(), 1).SetValue(float64(newMaxHp))
		this.entity.SetCurHP(ut.MinInt32(this.entity.GetCurHP()+ut.MaxInt32(newMaxHp-oldMaxHp, 0), newMaxHp))
	}
	// 韬略 全体前20回合造成的伤害提高10%
	if sv := this.GetStrategyValue(50032); sv > 0 {
		this.GetBuffOrAdd(bufftype.RECORD_ROUND_ADD_DAMAGE, this.GetUID())
	}
}

func (this *Fighter) CheckStrategyTarget(s *g.StrategyObj) bool {
	if s.TargetType == 0 { // 全体
		return true
	} else if s.TargetType == 1 { // 士兵id
		return this.GetID() == s.TargetValue
	} else if s.TargetType == 2 { // 士兵类型 1.枪兵 2.盾兵 3.弓兵 4.骑兵
		return this.entity.GetPawnType() == s.TargetValue
	} else if s.TargetType == 3 { // 军队
		armyUid := this.entity.GetArmyUid()
		return array.Some(s.GetFighters(), func(m g.IFighter) bool { return m.GetArmyUID() == armyUid })
	} else if s.TargetType == 4 { // 英雄
		return this.entity.IsHero()
	}
	return false
}

func (this *Fighter) GetStrategyBuff(tp int32) *g.StrategyObj {
	return this.entity.GetStrategyBuff(tp)
}

// 获取韬略数值
func (this *Fighter) GetStrategyValue(tp int32) int32 {
	return this.entity.GetStrategyValue(tp)
}

// 获取韬略数值
func (this *Fighter) GetStrategyParams(tp int32) int32 {
	if s := this.entity.GetStrategyBuff(tp); s != nil {
		return s.Params
	}
	return 0
}

// 是否有某个韬略
func (this *Fighter) IsHasStrategys(tps ...int32) bool {
	return this.entity.IsHasStrategys(tps...)
}

func (this *Fighter) GetCDodgeCount() int32 {
	return this.cDodgeCount
}

func (this *Fighter) AddCDodgeCount() {
	this.cDodgeCount++
}

func (this *Fighter) ResetCDodgeCount() {
	this.cDodgeCount = 0
}

// 获取护盾值
func (this *Fighter) GetShieldVal() int32 {
	var val int32 = 0
	buffs := this.getShieldBuffs()
	for i := len(buffs) - 1; i >= 0; i-- {
		val += int32(buffs[i].Value)
	}
	return val
}

// 修正克制
func (this *Fighter) AmendRestrainValue(val float64, tp int32) float64 {
	if this.GetID() == 3104 { // 目标暂时只有陌刀兵有修正克制
		if strategy := this.GetStrategyBuff(50004); strategy != nil {
			v := float64(strategy.Value) * 0.01
			if tp == constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN {
				return v + 1
			} else if tp == constant.PAWN_SKILL_TYPE_DEFENSE_RESTRAIN {
				return v
			}
		}
	}
	return val
}

// 获取吸血值
func (this *Fighter) GetSuckBloodValue() float64 {
	val := 0.0
	// 装备
	effects := this.GetEquipEffects()
	for _, m := range effects {
		if m.Type == eeffect.SUCK_BLOOD {
			val += m.Value
		}
	}
	// buff
	this.entity.BuffsRLock()
	defer this.entity.BuffsRUnlock()
	buffs := this.GetBuffs()
	for _, m := range buffs {
		if m.Type == bufftype.HIT_SUCK_BLOOD || m.Type == bufftype.LOW_HP_ADD_SUCKBLOOD || m.Type == bufftype.PROTECTION_NIE {
			val += m.Value
		}
	}
	return val
}

// 刷新最大血量记录
func (this *Fighter) UpdateMaxHpRecord(maxHp int32) {
	if buff := this.GetBuff(bufftype.S_RECORD_HP); buff != nil {
		buff.SetValue(float64(maxHp))
	}
}

// 获取宠物主人UID
func (this *Fighter) GetPetMaster() string {
	arr := strings.Split(this.GetUID(), "_")
	if len(arr) == 2 && arr[0] == "pet" {
		return arr[1]
	}
	return ""
}

// // 获取攻击目标权重
// func (this *Fighter) GetAttackTargetWeight() int64 {
// 	return this.attackTargetWeight
// }

// // 设置攻击目标权重
// func (this *Fighter) SetAttackTargetWeight(weight int64) {
// 	this.attackTargetWeight = weight
// }

// // 添加以该单位为攻击目标的Fighter
// func (this *Fighter) BeTargetedAddFighter(fighter g.IFighter) bool {
// 	for _, v := range this.beTargetedList {
// 		if v == fighter {
// 			return false
// 		}
// 	}
// 	this.beTargetedList = append(this.beTargetedList, fighter)
// 	//权重从小到大排序
// 	sort.Slice(this.canAttackTargets, func(i, j int) bool {
// 		return this.canAttackTargets[i].Weight < this.canAttackTargets[j].Weight
// 	})
// 	return true
// }

// // 移除以该单位为攻击目标的Fighter
// func (this *Fighter) BeTargetedRemoveFighter(fighter g.IFighter) bool {
// 	index := -1
// 	for i := 0; i < len(this.beTargetedList); i++ {
// 		if this.beTargetedList[i] == fighter {
// 			index = i
// 		}
// 	}
// 	if index >= 0 {
// 		this.beTargetedList = append(this.beTargetedList[0:index], this.beTargetedList[index+1:]...)
// 		return true
// 	}
// 	return false
// }

// // 获取以该单位为攻击目标权重最小的Fighter
// func (this *Fighter) GetBeTargetedMinWeightFighter() g.IFighter {
// 	return this.beTargetedList[0]
// }

// 获取该单位攻击指定目标的战斗力
func (this *Fighter) GetFightPower(target g.IFighter) int32 {
	attack := ut.Int32(this.GetAttrJson()["attack"])
	this.entity.GetAttack()
	// 修正对建筑攻击力
	defenderPawnType := target.GetPawnType()
	isBuild := defenderPawnType == constant.PAWN_TYPE_BUILD
	if isBuild {
		attack = 1
		attackRestrain := this.GetSkillByType(constant.PAWN_SKILL_TYPE_ATTACK_RESTRAIN)
		if attackRestrain != nil && attackRestrain.GetTarget() == defenderPawnType {
			attack = ut.RoundInt32(float64(attack) * attackRestrain.GetValue())
		}
	}
	return attack * this.entity.GetCurHP()
}

// // 判断该单位被集火是战斗力是否足够
// func (this *Fighter) CheckTargetBeAttackedEnough() bool {
// 	var fightPointSum, targetFightPointSum int
// 	for i := 0; i < len(this.beTargetedList); i++ {
// 		fighter := this.beTargetedList[i]
// 		fightPointSum += fighter.GetFightPower(this)
// 		targetFightPointSum += this.GetFightPower(fighter)
// 	}
// 	return fightPointSum > targetFightPointSum
// }
