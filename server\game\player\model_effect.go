package player

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
)

// 获取效果
func (this *Model) GetEffectInt(t int32) int32 {
	this.effectMutex.RLock()
	defer this.effectMutex.RUnlock()
	return int32(this.effects[t])
}
func (this *Model) GetEffectFloat(t int32) float64 {
	this.effectMutex.RLock()
	defer this.effectMutex.RUnlock()
	return this.effects[t]
}

func (this *Model) SetEffect(t int32, v float64) {
	this.effectMutex.Lock()
	defer this.effectMutex.Unlock()
	this.effects[t] = v
}

// 获取修建队列数量
func (this *Model) GetBTQueueMaxCount() int32 {
	return this.GetPolicyEffectInt(effect.BT_QUEUE) + constant.DEFAULT_BT_QUEUE_COUNT
}

// 获取修建建筑时间减少
func (this *Model) GetBuildCd() float64 {
	return this.GetPolicyEffectFloat(effect.BUILD_CD)
}

// 获取招募队列数量
func (this *Model) GetDrillQueueMaxCount() int32 {
	return this.GetPolicyEffectInt(effect.DRILL_QUEUE) + constant.BUILD_DRILL_PAWN_MAX_COUNT
}

// 获取训练队列数量
func (this *Model) GetLvingQueueMaxCount() int32 {
	return this.GetPolicyEffectInt(effect.LV_UP_QUEUE) + constant.BUILD_PAWN_LVING_MAX_COUNT
}

// 获取治疗队列数量
func (this *Model) GetCureQueueMaxCount() int32 {
	return this.GetPolicyEffectInt(effect.CURE_QUEUE) + constant.BUILD_PAWN_CURING_MAX_COUNT
}
