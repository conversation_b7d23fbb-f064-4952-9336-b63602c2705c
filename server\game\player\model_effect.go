package player

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/effect"
)

// 获取效果
func (this *Model) GetEffectInt(t int32) int32 {
	this.effectMutex.RLock()
	defer this.effectMutex.RUnlock()
	return int32(this.effects[t])
}
func (this *Model) GetEffectFloat(t int32) float64 {
	this.effectMutex.RLock()
	defer this.effectMutex.RUnlock()
	return this.effects[t]
}

func (this *Model) SetEffect(t int32, v float64) {
	this.effectMutex.Lock()
	defer this.effectMutex.Unlock()
	this.effects[t] = v
}

// 获取修建队列数量
func (this *Model) GetBTQueueMaxCount() int32 {
	return this.GetPolicyEffectInt(effect.BT_QUEUE) + constant.DEFAULT_BT_QUEUE_COUNT
}

// 获取修建建筑时间减少
func (this *Model) GetBuildCd() float64 {
	return this.GetPolicyEffectFloat(effect.BUILD_CD)
}
