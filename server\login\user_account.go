package login

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/common/ta"
	"slgsrv/server/lobby"
	ut "slgsrv/utils"
	rds "slgsrv/utils/redis"
	"strconv"
	"strings"

	"github.com/huyangv/vmqant/log"
)

// 创建账号
func createUserAccount(loginType, openId, sessionKey, guestId, distinctId, platform, os, nickname, headicon, inviteUid, lang, ip string) (err string, uid string) {
	uid = genUID()
	if loginType == slg.LOGIN_TYPE_GUEST {
		nickname = nickname + uid
	} else if nickname == "" || !checkNickname(nickname) {
		nickname = "User" + uid
	} else {
		nickname = ut.TruncateString(strings.Trim(nickname, " "), 14)
	}
	data := lobby.NewTableData(uid, loginType, guestId, openId, sessionKey, nickname, headicon, distinctId, platform, os)
	if e := db.InsertOne(data); e != "" {
		log.Error("创建游客账号 err: %v", e)
		err = ecode.DB_ERROR.String()
	} else {
		_os := GetOs(os)
		log.Info("createUserAccount uid: %v, nickname: %v, loginType: %v, platform: %v, os: %v, _os: %v, ip: %v", uid, nickname, loginType, platform, os, _os, ip)
		// 上报
		ta.Track(0, uid, distinctId, 0, "ta_register", map[string]interface{}{
			"register_time": data.CreateTime,
			"type":          loginType,
			"invite_uid":    inviteUid,
			"platform":      platform,
			"language":      lang,
			"os":            _os,
			"#ip":           ip,
		})
		// 设置用户属性
		ta.UserSet(0, uid, distinctId, 0, map[string]interface{}{
			"uid_register_time": data.CreateTime,
			"os":                _os,
			"platform":          platform,
			"ip":                ip,
		})
		// dh上报
		dh.Track(dh.DH_EVENT_NAME_REGISTER, map[string]interface{}{
			"user_info": map[string]interface{}{
				"bundle_id":   dh.APP_BUNDLE_ID,               //游戏包名
				"sub_package": "",                             //头条分包，没有的传空值
				"account":     data.Uid,                       //游戏账户，用来标记一个用户的一组user_id，微信小游戏可使用open_id
				"platform":    dh.DhPlatformSwitch(platform),  //平台，iOS android pc
				"session_id":  data.Uid + ut.String(ut.Now()), //由客户端初始化时生成（ 建议使用时间戳+设备信息or uuid  md5）一个唯一值，并传给后端
				"server_id":   dh.DhLogServerId,               //逻辑服ID， 大于等于10000的server_id会被当作测试环境数据处理
				"user_id":     data.Uid,                       //用户游戏角色ID，为8位数字id，全服唯一（不接卓杭sdk可以不限制，但尽量短一些）
			},
		})
	}
	return
}

// 登陆上报
func loginTrack(loginType, uid, distinctId, platform, lang, os string) {
	_os := GetOs(os)
	// 上报
	ta.Track(0, uid, distinctId, 0, "ta_login", map[string]interface{}{
		"type":     loginType,
		"platform": platform,
		"language": lang,
		"os":       _os,
	})
	if slg.IsDebug() {
		log.Info("loginTrack loginType: %v, uid: %v, distinctId: %v, platform: %v, lang: %v, os: %v", loginType, uid, distinctId, platform, lang, _os)
	}
}

func GetOs(os string) string {
	if os == "" {
		return "none"
	}
	arr := strings.Split(os, ";")
	if len(arr) >= 1 {
		return arr[0]
	}
	return "none"
}

// 获取登录游戏的token
func getGameToken(uid string) (accountToken string) {
	// 先更新登录时间
	time := ut.Now()
	// 重新生成token
	str := uid + strconv.Itoa(time)
	accountToken = ut.AESEncrypt(str)
	rds.RdsHSet(uid, rds.RDS_USER_FIELD_TOKEN, accountToken)
	return
}

// 生成唯一id
func genUID() string {
	uid := ut.UID8()
	for db.HasUid(uid) {
		uid = ut.UID8()
		log.Info("生成uid的时候发现一样的 uid=" + uid)
	}
	return uid
}

// 检测名字是否存在和合法
func checkNickname(nickname string) bool {
	if sensitive.CheckName(nickname) != 0 {
		return false //是否合法
	}
	return !db.HasNickname(nickname)
}
