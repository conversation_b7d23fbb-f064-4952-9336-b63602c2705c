package lobby

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"
)

type PopularityRecord struct {
	UID      string `json:"uid" bson:"uid"`
	Nickname string `json:"nickname" bson:"nickname"`
	Time     int64  `json:"time" bson:"time"`
	Value    int32  `json:"value" bson:"value"`
}

func (this *User) ToPopularityPb(lock bool) ([]*pb.PopularityInfo, []*pb.PopularityRecordInfo) {
	if lock {
		this.PopularityLock.RLock()
		defer this.PopularityLock.RUnlock()
	}
	list := array.Map(this.PopularityList, func(m []int32, _ int) *pb.PopularityInfo {
		return &pb.PopularityInfo{Value: pb.Int32Array(m)}
	})
	records := []*pb.PopularityRecordInfo{}
	for _, v := range this.PopularityRecords {
		record := &pb.PopularityRecordInfo{
			Uid:      v.UID,
			Nickname: v.Nickname,
			Value:    int32(v.Value),
			Time:     int64(v.Time),
		}
		records = append(records, record)
	}
	return list, records
}

func (this *User) CheckCanAddPopularityNotLock(uid string, now int64) string {
	if len(this.PopularityRecords) == 0 {
		return ""
	}
	var lastTime int64 //获取最后一次改变人气的时间
	var info *PopularityRecord = nil
	for _, m := range this.PopularityRecords {
		if m.Time > lastTime {
			lastTime = m.Time
		}
		if m.UID == uid {
			info = m
		}
	}
	if info != nil && now-info.Time < constant.ONE_USER_POPULARITY_CHANGE_INTERVAL {
		return ecode.ONE_USER_YET_CHANGE_POPULAR.String() //单人已经给过人气了
	} else if lastTime > ut.NowZeroTime() {
		return ecode.TODAY_YET_CHANGE_POPULAR.String() //今天已经有人给他加了
	}
	return ""
}

// 添加人气
func (this *User) AddPopularity(uid, nickName string, id int32) (arr []*pb.PopularityInfo, err string) {
	this.PopularityLock.Lock()
	defer this.PopularityLock.Unlock()
	// 先检测是否可以添加
	now := time.Now().UnixMilli()
	if err := this.CheckCanAddPopularityNotLock(uid, now); err != "" {
		return nil, err
	}
	json := config.GetJsonData("evaluateGift", id)
	if json == nil {
		return nil, ecode.EVALUATE_NOT_EXIST.String()
	} else if len(this.PopularityList) == 0 {
		this.PopularityList = append(this.PopularityList, []int32{101, 0})
	}
	if ut.Int32(json["type"]) == 1 {
		value := this.PopularityList[0]
		add := ut.If(value[0] == id, int32(1), -1)
		value[1] += add
		if value[1] == 0 {
			value[0] = 101
		} else if value[1] < 0 {
			value[0] = ut.If(value[0] == 101, int32(102), 101)
			value[1] = ut.AbsInt32(value[1])
		}
	} else if value := array.Find(this.PopularityList, func(m []int32) bool { return m[0] == id }); value != nil {
		value[1] += 1
	} else {
		this.PopularityList = append(this.PopularityList, []int32{id, 1})
	}
	// 添加记录
	if len(this.PopularityRecords) > 60 {
		this.PopularityRecords = this.PopularityRecords[1:]
	}
	this.PopularityRecords = append(this.PopularityRecords, &PopularityRecord{
		UID:      uid,
		Nickname: nickName,
		Value:    id,
		Time:     now,
	})
	arr, _ = this.ToPopularityPb(false)
	this.FlagUpdateDB()
	return arr, ""
}
