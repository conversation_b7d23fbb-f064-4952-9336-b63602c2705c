package world

import (
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/sasha-s/go-deadlock"
)

type PawnCuringQueueList struct {
	deadlock.RWMutex
	Map    map[int32]*AarePawnCuringQueue
	RbTree *ut.RbTreeContainer[*AarePawnCuringQueue]
}

type AarePawnCuringQueue struct {
	Index     int32
	PauseTime int64 //暂停时间
	List      []*PawnCuringInfo
}

// 士兵治疗信息
type PawnCuringInfo struct {
	UID  string       `bson:"uid"`  //士兵uid
	AUID string       `bson:"auid"` //军队uid
	Cost []*g.TypeObj `bson:"cost"` //费用

	StartTime      int64 `bson:"start_time"`       //开始时间
	OriginNeedTime int32 `bson:"origin_need_time"` //初始需要时间
	NeedTime       int32 `bson:"need_time"`        //实际需要时间
	Index          int32 `bson:"index"`            //区域位置
	Id             int32 `bson:"id"`               //配置id
	Lv             int32 `bson:"lv"`               //等级
}

func NewPawnCuringInfo(index, id, lv int32, auid, puid string, needTime int32) *PawnCuringInfo {
	return &PawnCuringInfo{
		UID:            puid,
		Id:             id,
		Lv:             lv,
		Index:          index,
		AUID:           auid,
		StartTime:      0,
		NeedTime:       needTime,
		OriginNeedTime: needTime,
	}
}

func NewAarePawnCuringQueue(index int32) *AarePawnCuringQueue {
	return &AarePawnCuringQueue{List: []*PawnCuringInfo{}, Index: index}
}

func (this *PawnCuringInfo) toPb(now int64) *pb.PawnCuringInfo {
	if now == 0 {
		now = time.Now().UnixMilli()
	}
	return &pb.PawnCuringInfo{
		Uid:         this.UID,
		Index:       this.Index,
		Auid:        this.AUID,
		NeedTime:    this.NeedTime,
		SurplusTime: int32(ut.Max(int(this.StartTime)+int(this.NeedTime)-int(now), 0)),
		Id:          this.Id,
		Lv:          this.Lv,
	}
}

func (this *PawnCuringQueueList) ToPb(index int32) []*pb.PawnCuringInfo {
	this.RLock()
	defer this.RUnlock()
	if obj := this.Map[index]; obj != nil {
		return array.Map(obj.List, func(m *PawnCuringInfo, _ int) *pb.PawnCuringInfo { return m.toPb(obj.PauseTime) })
	}
	return []*pb.PawnCuringInfo{}
}

func (this *PawnCuringQueueList) GetClone(index int32) []*PawnCuringInfo {
	this.RLock()
	defer this.RUnlock()
	if obj := this.Map[index]; obj != nil {
		return array.Clone(obj.List)
	}
	return []*PawnCuringInfo{}
}

func (this *Model) ToPawnCuringQueuePb(index int32) []*pb.PawnCuringInfo {
	return this.PawnCuringQueues.ToPb(index)
}

func (this *Model) ToPawnCuringQueueDB() map[int32]*AarePawnCuringQueue {
	this.PawnCuringQueues.RLock()
	defer this.PawnCuringQueues.RUnlock()
	obj := make(map[int32]*AarePawnCuringQueue, len(this.PawnCuringQueues.Map))
	for k, v := range this.PawnCuringQueues.Map {
		obj[k] = v
	}
	return obj
}

func (this *Model) GetPawnCuringQueue(index int32) []*PawnCuringInfo {
	this.PawnCuringQueues.RLock()
	defer this.PawnCuringQueues.RUnlock()
	if m := this.PawnCuringQueues.Map[index]; m != nil {
		return m.List
	}
	return nil
}

// 队列是否满了
func (this *Model) IsPawnCuringQueueFull(index, queueCnt int32) bool {
	queue := this.GetPawnCuringQueue(index)
	return queue != nil && len(queue) >= int(queueCnt)
}

// 是否在治疗队列中
func (this *Model) IsInCuringQueue(index int32, uid string) bool {
	queue := this.GetPawnCuringQueue(index)
	return queue != nil && array.Some(queue, func(m *PawnCuringInfo) bool { return m.UID == uid })
}

// 获取治疗信息
func (this *Model) GetPawnCuringInfo(index int32, uid string) *PawnCuringInfo {
	if uid == "" {
		return nil
	}
	queue := this.GetPawnCuringQueue(index)
	if queue == nil {
		return nil
	} else if m := array.Find(queue, func(m *PawnCuringInfo) bool { return m.UID == uid }); m != nil {
		return m
	}
	return nil
}

// 添加到队列
func (this *Model) PutPawnCuringQueue(index, id, lv int32, auid, puid string, needTime int32, cd float64, cost []*g.TypeObj) {
	needTime = ut.MaxInt32(1000, int32(float64(needTime)*1000*(1.0-cd)))
	info := NewPawnCuringInfo(index, id, lv, auid, puid, needTime)
	info.Cost = cost
	this.PawnCuringQueues.Lock()
	m := this.PawnCuringQueues.Map[index]
	if m == nil {
		m = NewAarePawnCuringQueue(index)
		this.PawnCuringQueues.Map[index] = m
	}
	queue := m.List
	if queue == nil {
		queue = []*PawnCuringInfo{}
	}
	queue = append(queue, info)
	this.PawnCuringQueues.Map[index].List = queue
	// 设置第一个的开始时间
	if len(queue) == 1 {
		queue[0].StartTime = time.Now().UnixMilli()
		endTime := queue[0].StartTime + int64(queue[0].NeedTime)
		this.PawnCuringQueues.RbTree.AddElement(int(endTime), m)
	}
	this.PawnCuringQueues.Unlock()
}

// 取消治疗
func (this *Model) CancelPawnCuringQueue(index int32, uid string) *PawnCuringInfo {
	this.PawnCuringQueues.Lock()
	defer this.PawnCuringQueues.Unlock()
	// 从队列中删除
	obj := this.PawnCuringQueues.Map[index]
	if obj == nil {
		return nil
	}
	queue := obj.List
	for i, m := range queue {
		if m.UID == uid {
			queue = append(queue[:i], queue[i+1:]...)
			var newEndTime int64
			if len(queue) == 0 {
				delete(this.PawnCuringQueues.Map, index)
			} else if m.StartTime > 0 {
				queue[0].StartTime = time.Now().UnixMilli()
				newEndTime = queue[0].StartTime + int64(queue[0].NeedTime)
				this.PawnCuringQueues.Map[index].List = queue
			} else {
				this.PawnCuringQueues.Map[index].List = queue
			}
			if m.StartTime > 0 {
				// 取消的是正在治疗中的 需要从红黑树删除
				endTime := m.StartTime + int64(m.NeedTime)
				this.PawnCuringQueues.RbTree.RemoveElement(int(endTime), obj)
				// 补位的治疗添加到红黑树
				if newEndTime > 0 {
					this.PawnCuringQueues.RbTree.AddElement(int(newEndTime), obj)
				}
			}
			return m
		}
	}
	return nil
}

// 检测更新士兵治疗队列
func (this *Model) CheckUpdatePawnCuringQueue() {
	now := time.Now().UnixMilli()
	// 遍历之前先检测最快的治疗是否结束
	notifyMap := map[int32]bool{}
	list := []*PawnCuringInfo{}
	this.PawnCuringQueues.RLock()
	minTreeNode := this.PawnCuringQueues.RbTree.FindMinElements()
	if minTreeNode == nil {
		this.PawnCuringQueues.RUnlock()
		return
	} else {
		if ut.Int64(minTreeNode.Key) > now {
			// 结束时间最快治疗都没结束
			this.PawnCuringQueues.RUnlock()
			return
		}
	}
	this.PawnCuringQueues.RUnlock()

	queueFinishMap := map[int32]bool{} //队列完成map 用于离线消息通知 k=>index
	this.PawnCuringQueues.Lock()
	for m := this.PawnCuringQueues.RbTree.FindMinElements(); m != nil; m = this.PawnCuringQueues.RbTree.FindMinElements() {
		if ut.Int64(m.Key) > now {
			// 最快的都未结束
			break
		}

		// 先删
		this.PawnCuringQueues.RbTree.Remove(m.Key)

		objList := m.Value.([]*AarePawnCuringQueue)
		if objList == nil {
			continue
		}

		for _, obj := range objList {
			queue := obj.List
			if len(queue) == 0 {
				continue
			}

			index := obj.Index
			if this.PawnCuringQueues.Map[index] == nil {
				continue
			}

			m := queue[0]                           //取出第一个开始
			queue = append(queue[:0], queue[1:]...) //删除第一个
			if len(queue) > 0 {
				queue[0].StartTime = m.StartTime + int64(m.NeedTime)
				this.PawnCuringQueues.Map[index].List = queue
				// 更新红黑树
				this.PawnCuringQueues.RbTree.AddElement(int(queue[0].StartTime+int64(queue[0].NeedTime)), obj)
			} else {
				delete(this.PawnCuringQueues.Map, index)
				queueFinishMap[index] = true
			}
			// 添加到通知队列
			notifyMap[index] = true
			list = append(list, m)
		}
	}
	this.PawnCuringQueues.Unlock()
	for _, m := range list {
		// 通知治疗完成
		this.AreaPawnCureComplete(m.Index, m.AUID, m.UID)
	}
	for index := range notifyMap {
		// 通知队列信息
		this.NotifyPawnCuringQueue(index)
	}

	// 离线消息通知
	if len(queueFinishMap) > 0 {
		for index := range queueFinishMap {
			if area := this.GetArea(index); area != nil {
				if area.Owner == "" {
					continue
				}
				this.OfflineNotify(area.Owner, constant.OFFLINE_MSG_TYPE_CURE_FINISH)
			}
		}
	}

}

// 通知
func (this *Model) NotifyPawnCuringQueue(index int32) {
	if area := this.GetArea(index); area != nil {
		this.room.PutPlayerNotifyQueue(constant.NQ_PAWN_CURING_QUEUE, area.Owner, &pb.OnUpdatePlayerInfoNotify{
			Data_82: this.PawnCuringQueues.ToPb(index),
		})
	}
}

// 暂停和恢复治疗
func (this *Model) PausePawnCuring(index int32, pauseTime int64) {
	this.PawnCuringQueues.Lock()
	obj := this.PawnCuringQueues.Map[index]
	if obj == nil {
		this.PawnCuringQueues.Unlock()
		return
	} else if pauseTime > 0 {
		// 暂停
		obj.PauseTime = pauseTime
		if len(obj.List) > 0 {
			endTime := obj.List[0].StartTime + int64(obj.List[0].NeedTime)
			this.PawnCuringQueues.RbTree.RemoveElement(int(endTime), obj)
		}
	} else if obj.PauseTime > 0 {
		// 恢复暂停
		now := time.Now().UnixMilli()
		for _, m := range obj.List {
			if m.StartTime > 0 {
				m.StartTime = now - (obj.PauseTime - m.StartTime)
				endTime := m.StartTime + int64(m.NeedTime)
				this.PawnCuringQueues.RbTree.AddElement(int(endTime), obj)
				break
			}
		}
		obj.PauseTime = 0
	}
	this.PawnCuringQueues.Unlock()
	// 通知队列信息
	this.NotifyPawnCuringQueue(index)
}

// 删除指定地块士兵治疗
func (this *Model) RemovePawnCuringArea(index int32) {
	this.PawnCuringQueues.Lock()
	defer this.PawnCuringQueues.Unlock()
	m := this.PawnCuringQueues.Map[index]
	if m != nil {
		// 从红黑树删除
		minEndTime := m.FindMinEndTime()
		this.PawnCuringQueues.RbTree.RemoveElement(int(minEndTime), m)
	}
	delete(this.PawnCuringQueues.Map, index)
}

// 删除士兵治疗
func (this *Model) _removePawnCuringPawn(index int32, puid string) string {
	this.PawnCuringQueues.Lock()
	defer this.PawnCuringQueues.Unlock()
	// 从队列中删除
	m := this.PawnCuringQueues.Map[index]
	if m == nil {
		return ""
	}
	queue := m.List
	for i := len(queue) - 1; i >= 0; i-- {
		d := queue[i]
		if d.UID != puid {
			continue
		}
		var newEndTime int64
		queue = append(queue[:i], queue[i+1:]...)
		if len(queue) > 0 {
			if d.StartTime > 0 {
				queue[0].StartTime = time.Now().UnixMilli() //下一个开始
				newEndTime = queue[0].StartTime + int64(queue[0].NeedTime)
			}
			this.PawnCuringQueues.Map[index].List = queue
		} else {
			delete(this.PawnCuringQueues.Map, index)
		}
		if d.StartTime > 0 {
			// 取消的是正在治疗中的 需要从红黑树删除
			endTime := d.StartTime + int64(d.NeedTime)
			this.PawnCuringQueues.RbTree.RemoveElement(int(endTime), m)
			if newEndTime > 0 && m.PauseTime == 0 { //没暂停的才加入
				// 补位的治疗添加到红黑树
				this.PawnCuringQueues.RbTree.AddElement(int(newEndTime), m)
			}
		}
		return d.UID
	}
	return ""
}

// 加速治疗
func (this *Model) SpeedUpPawnCuringQueue(index, speedUpTime int32) {
	this.PawnCuringQueues.Lock()
	obj := this.PawnCuringQueues.Map[index]
	if obj == nil {
		return
	}
	now := time.Now().UnixMilli()
	list := []*PawnCuringInfo{} // 加速完成的士兵列表
	queue := obj.List
	speedUpIndex := -1 // 加速完成的队列下标
	for i, m := range queue {
		needTime := m.NeedTime
		if m.StartTime > 0 {
			needTime -= int32(now - m.StartTime)
			// 加速的是正在治疗中的 需要从红黑树删除
			endTime := m.StartTime + int64(m.NeedTime)
			this.PawnCuringQueues.RbTree.RemoveElement(int(endTime), obj)
		}
		if speedUpTime >= needTime {
			// 当前治疗的士兵加速完成
			speedUpIndex = i
			list = append(list, m)
		} else {
			m.NeedTime -= speedUpTime
		}
		speedUpTime -= needTime
		if speedUpTime <= 0 {
			break
		}
	}
	if speedUpIndex >= 0 {
		// 有治疗中的士兵加速完成
		if speedUpIndex == len(queue)-1 {
			queue = []*PawnCuringInfo{}
			// 全部加速完成
			delete(this.PawnCuringQueues.Map, index)
		} else {
			// 已加速完成的从队列中删除
			queue = queue[speedUpIndex+1:]
			this.PawnCuringQueues.Map[index].List = queue
		}
	}
	if len(queue) > 0 {
		if queue[0].StartTime == 0 {
			queue[0].StartTime = now
		}
		// 补位的治疗添加到红黑树
		endTime := queue[0].StartTime + int64(queue[0].NeedTime)
		this.PawnCuringQueues.RbTree.AddElement(int(endTime), obj)
	}
	this.PawnCuringQueues.Unlock()

	for _, m := range list {
		// 通知治疗完成
		this.AreaPawnCureComplete(m.Index, m.AUID, m.UID)
	}
}

// 获取治疗信息
func (this *Model) GetCuringInfo(index int32, uid string) *PawnCuringInfo {
	this.PawnCuringQueues.RLock()
	defer this.PawnCuringQueues.RUnlock()
	m := this.PawnCuringQueues.Map[index]
	if m == nil {
		return nil
	}
	for _, v := range m.List {
		if v.UID == uid {
			return v
		}
	}
	return nil
}

func (this *Model) RemovePawnCuringPawn(index int32, puid string) {
	if uid := this._removePawnCuringPawn(index, puid); uid != "" {
		// 通知队列信息
		this.NotifyPawnCuringQueue(index)
	}
}

// 查询队列最快的结束时间
func (this *AarePawnCuringQueue) FindMinEndTime() int64 {
	if len(this.List) == 0 {
		return 0
	}
	return this.List[0].StartTime + int64(this.List[0].NeedTime)
}
