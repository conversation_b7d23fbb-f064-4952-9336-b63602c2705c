package chat

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
)

var Module = func() module.Module {
	return new(Chat)
}

type Chat struct {
	basemodule.BaseModule
}

func (this *Chat) GetType() string {
	return "chat" //很关键,需要与配置文件中的Module配置对应
}

func (this *Chat) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (this *Chat) OnAppConfigurationLoaded(app module.App) {
	this.BaseModule.OnAppConfigurationLoaded(app)
	if serverType := ut.String(app.GetSettings().Settings["ServerType"]); serverType == "chat" || serverType == "development" {
		sdk.InitAppleRootCerts()
		url := app.GetSettings().Settings["GameMongodbURL"].(string)
		dbname := app.GetSettings().Settings["GameMongodbDB"].(string)
		InitRoomDB(url, dbname)
	}
}

func (this *Chat) OnInit(app module.App, settings *conf.ModuleSettings) {
	this.BaseModule.OnInit(this, app, settings)
	this.InitRpc()
	this.GetServer().RegisterGO("HD_GetChats", this.getChats)                     //获取聊天信息
	this.GetServer().RegisterGO("HD_GetGameHistoryList", this.getGameHistoryList) //获取历史对局列表
	this.GetServer().RegisterGO("HD_GetLikeJwmCount", this.getLikeJwmCount)       //获取点赞九万亩数量
	this.InitHDGallery()
	this.InitHttp()
	InitChatChannel()
	InitGalleryModel()
	InitFriendChat()
	InitGlobalData()
}

func (this *Chat) Run(closeSig chan bool) {
	RunTick(this)
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", this.GetType())
	StopTick()
}

func (this *Chat) OnDestroy() {
	this.BaseModule.OnDestroy()
}

// 获取聊天列表
func (this *Chat) getChats(session gate.Session, msg *pb.CHAT_HD_GETCHATS_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	list := GetChatsOrCreate(msg.GetChannel(), uid, session)
	return pb.ProtoMarshal(&pb.CHAT_HD_GETCHATS_S2C{
		List: list,
	})
}

// 获取历史对局列表
func (this *Chat) getGameHistoryList(session gate.Session, msg *pb.CHAT_HD_GETGAMEHISTORYLIST_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	serverType, page := ut.Int(msg.GetServerType()), ut.Int(msg.GetPage())
	list, total := GetGameHistoryList(serverType, page)
	s2c := &pb.CHAT_HD_GETGAMEHISTORYLIST_S2C{Datas: list}
	if page == 1 {
		s2c.TotalPage = int32(total / slg.SHOW_HISTORY_SERVER_PAGE_NUM)
		if total%slg.SHOW_HISTORY_SERVER_PAGE_NUM > 0 {
			s2c.TotalPage++
		}
	} else {
		s2c.TotalPage = -1
	}
	return pb.ProtoMarshal(s2c)
}

// 点赞九万亩
func (this *Chat) getLikeJwmCount(session gate.Session, msg *pb.CHAT_HD_GETLIKEJWMCOUNT_C2S) (ret []byte, err string) {
	uid := checkSession(session)
	if uid == "" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	return pb.ProtoMarshal(&pb.CHAT_HD_GETLIKEJWMCOUNT_S2C{SumLikeJwmCount: int64(global.SumLikeJwmCount)})
}

func checkSession(session gate.Session) string {
	if session == nil {
		return ""
	} else {
		return session.GetUserID()
	}
}

func getInvokeRpcInfo(serverType, id string, params []interface{}) (string, []interface{}) {
	moduleType := serverType
	if serverType == slg.MACH_SERVER_TYPE_LOBBY || serverType == slg.MACH_SERVER_TYPE_GAME {
		moduleType = serverType + "@" + serverType + id
	}
	return moduleType, array.Map(params, func(m interface{}, _ int) interface{} { return ut.Bytes(m) })
}

// 发送Rpc到lobby
func (this *Chat) InvokeLobbyRpc(id, _func string, params ...interface{}) (result interface{}, err string) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	return this.InvokeWithCleanup(moduleType, _func, paramsBytesArr...)
}

// 发送Rpc到lobby
func (this *Chat) InvokeLobbyRpcNR(id, _func string, params ...interface{}) {
	moduleType, paramsBytesArr := getInvokeRpcInfo(slg.MACH_SERVER_TYPE_LOBBY, id, params)
	this.InvokeNR(moduleType, _func, paramsBytesArr...)
}
