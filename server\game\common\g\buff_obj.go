package g

import (
	"math"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/enums/bufftype"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

// buff
type BuffObj struct {
	Provider string  `json:"provider" bson:"provider"` // 施加者
	Value    float64 `json:"value" bson:"value"`

	maxRound   int32
	Type       int32 `json:"type" bson:"type"`
	Lv         int32 `json:"lv" bson:"lv"`
	Round      int32 `json:"round" bson:"round"` // 持续回合数 -1.表示永久
	NeedRound  int32 `json:"needRound" bson:"need_round"`
	Times      int32 `json:"times" bson:"times"` // 触发次数 -1.表示永久
	maxTimes   int32
	effectType int32 // 效果类型 0.负面 1.增益
}

var EMPTY_BUFF = new(BuffObj)

func (this *BuffObj) Init(t int32, provider string, lv, roundAdd int32) *BuffObj {
	this.Type = t
	this.Provider = provider
	this.Lv = ut.MaxInt32(lv, 1)
	json := config.GetJsonData("buffAttr", this.Type*1000+this.Lv)
	if json == nil {
		this.Lv = 1
		json = config.GetJsonData("buffAttr", this.Type*1000+this.Lv)
	}
	if json == nil {
		log.Error("buff init json is nil, type: %v, lv: %v", this.Type, this.Lv)
		return this
	}
	this.maxRound = ut.Int32(json["round"])
	this.maxRound = ut.If(this.maxRound > 0, this.maxRound+roundAdd, -1)
	this.maxTimes = ut.Int32(json["times"])
	this.Value = ut.Float64(json["value"])
	this.Round = this.maxRound
	this.NeedRound = ut.If(this.Round > 0, this.Round-roundAdd, -1)
	this.Times = this.maxTimes
	baseJson := config.GetJsonData("buffBase", this.Type)
	this.effectType = ut.Int32(baseJson["effect_type"])
	return this
}

func NewBuff(tp int32, lv int32, provider string, round int32, needRound int32, times int32, value float64) *BuffObj {
	buff := &BuffObj{
		Type:      tp,
		Lv:        lv,
		Provider:  provider,
		Round:     round,
		NeedRound: needRound,
		Times:     times,
		Value:     value,
	}
	json := config.GetJsonData("buffAttr", buff.Type*1000+buff.Lv)
	buff.maxRound = ut.Int32(json["round"])
	buff.maxTimes = ut.Int32(json["times"])
	baseJson := config.GetJsonData("buffBase", buff.Type)
	buff.effectType = ut.Int32(baseJson["effect_type"])
	return buff
}

func NewBuffByJson(data map[string]interface{}) *BuffObj {
	return NewBuff(
		ut.Int32(data["type"]),
		ut.Int32(data["lv"]),
		ut.String(data["provider"]),
		ut.Int32(data["round"]),
		ut.Int32(data["needRound"]),
		ut.Int32(data["times"]),
		ut.Float64(data["value"]),
	)
}

func NewBuffByPb(data *pb.BuffInfo) *BuffObj {
	return NewBuff(
		data.GetType(),
		data.GetLv(),
		data.GetProvider(),
		data.GetRound(),
		data.GetNeedRound(),
		data.GetTimes(),
		ut.Float64(data.GetValue()),
	)
}

func (this *BuffObj) Clone() *BuffObj {
	return NewBuff(
		this.Type,
		this.Lv,
		this.Provider,
		this.Round,
		this.NeedRound,
		this.Times,
		this.Value,
	)
}

func (this *BuffObj) ToJson() map[string]interface{} {
	return map[string]interface{}{
		"type":      this.Type,
		"lv":        this.Lv,
		"provider":  this.Provider,
		"round":     this.Round,
		"needRound": this.NeedRound,
		"times":     this.Times,
		"value":     this.Value,
	}
}

func (this *BuffObj) ToPb() *pb.BuffInfo {
	return &pb.BuffInfo{
		Type:      int32(this.Type),
		Lv:        int32(this.Lv),
		Provider:  this.Provider,
		Round:     int32(this.Round),
		NeedRound: int32(this.NeedRound),
		Times:     int32(this.Times),
		Value:     this.Value,
	}
}

func (this *BuffObj) GetEffectType() int32 { return this.effectType }

// 是否有护盾
func (this *BuffObj) IsHasShield() bool {
	return this.Type == bufftype.SHIELD ||
		this.Type == bufftype.PROTECTION_SHIELD ||
		this.Type == bufftype.LOW_HP_SHIELD ||
		this.Type == bufftype.ATTACK_SHIELD ||
		this.Type == bufftype.SUCKBLOOD_SHIELD ||
		this.Type == bufftype.RODELERO_SHIELD ||
		this.Type == bufftype.RODELERO_SHIELD_001 ||
		this.Type == bufftype.RODELERO_SHIELD_102 ||
		this.Type == bufftype.BATTLE_BEGIN_SHIELD ||
		this.Type == bufftype.KUROU_SHIELD ||
		this.Type == bufftype.SUCK_SHIELD ||
		this.Type == bufftype.ABNEGATION_SHIELD ||
		this.Type == bufftype.LONGITUDINAL_CLEFT_SHIELD ||
		this.Type == bufftype.CRIMSONGOLD_SHIELD ||
		this.Type == bufftype.BLACK_IRON_STAFF_SHIELD
}

func (this *BuffObj) UpdateRound(val int32) bool {
	if this.maxRound == -1 {
		return false
	}
	this.Round += val
	return this.Round <= 0
}

func (this *BuffObj) SetRound(val int32) *BuffObj {
	roundAdd := ut.MaxInt32(0, this.maxRound-this.NeedRound)
	this.maxRound = val
	this.NeedRound = ut.If(this.maxRound > 0, this.maxRound-roundAdd, -1)
	this.Round = this.maxRound
	return this
}

func (this *BuffObj) AddRound(val int32) *BuffObj {
	if val > 0 {
		this.SetRound(this.Round + val)
	}
	return this
}

func (this *BuffObj) UpdateTimes(val int32) bool {
	if this.maxTimes == -1 {
		return false
	}
	this.Times += val
	return this.Times <= 0
}

func (this *BuffObj) UpdateLv(lv int32) {
	this.Lv = lv
	json := config.GetJsonData("buffAttr", this.Type*1000+lv)
	this.Value = ut.Float64(json["value"])
}

func (this *BuffObj) GetValueInt() int32 {
	return int32(this.Value)
}

func (this *BuffObj) SetValue(val float64) {
	this.Value = val
}

func (this *BuffObj) ChangeValue(val float64) {
	this.Value += val
}

func (this *BuffObj) AddValueMul(mul float64) *BuffObj {
	this.Value = math.Round(this.Value * mul)
	return this
}
