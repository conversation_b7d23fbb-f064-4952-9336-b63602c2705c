package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

// 创建古城
func (this *Model) CreateAncientCities() {
	sizeX, sizeY := this.room.GetMapSize().X, this.room.GetMapSize().Y
	if this.HasCreateAnciet {
		return
	}
	this.HasCreateAnciet = true
	// 创建四个方位的古城
	for i := int32(0); i < 4; i++ {
		exist := false
		// 检测古城是否存在
		this.AncientCityMap.ForEach(func(info *AncientInfo, k int32) bool {
			if constant.ANCIENT_CITY_ID_DIR_MAP[info.CityId] == i {
				exist = true
				return false
			}
			return true
		})
		if exist {
			continue
		}
		this.CreateDirAncientCity(i, sizeX, sizeY)
	}
	// 消息通知
	this.PutNotifyQueue(constant.NQ_SYS_MSG, &pb.OnUpdateWorldInfoNotify{
		Data_54: &pb.SysMsgInfo{Id: int32(slg.ANCIENT_CREATE_NOTICE_ID)},
	})
}

// 创建指定方位的古城
func (this *Model) CreateDirAncientCity(dir, sizeX, sizeY int32) {
	borderCfg := constant.ANCIENT_CITY_BORDER_MAP[dir]
	if borderCfg == nil || len(borderCfg) < 4 {
		log.Error("CreateAncientCity borderCfg")
		return
	}
	log.Info("CreateDirAncientCity start dir: %v", dir)
	startX := ut.Int32(borderCfg[0] * ut.Float32(sizeX))
	endX := ut.Int32(borderCfg[1] * ut.Float32(sizeX))
	startY := ut.Int32(borderCfg[2] * ut.Float32(sizeY))
	endY := ut.Int32(borderCfg[3] * ut.Float32(sizeY))
	rowLen := endX - startX
	colLen := endY - startY
	pieceLenX := rowLen / 3
	pieceLenY := colLen / 3
	var maxWeight, rstIndex int32 = 0, -1
	// 将一个地图方位划分为9块 根据优先顺序搜索合适的古城位置
	for _, i := range constant.ANCIENT_CITY_AREA_SEARCH_SORT {
		var row, col int32 = i / 3, i % 3
		xL := startX + pieceLenX*col
		xR := xL + pieceLenX
		yL := startY + pieceLenY*row
		yR := yL + pieceLenY
		lastDis, reverse := int32(0), false
		for x := xL; x < xR; x++ {
			// 遍历时点位的移动方向 从上往下为0 顺时针
			var moveDir int32 = 3
			for n := yL; n < yR; n++ {
				y := n
				if reverse {
					// 上下交替遍历y轴 方便搜索四周时复用前一个相邻的点
					y = yR - (n - yL) - 1
				}
				if !this.CheckAncientCityAreaValid(x, y) {
					// 判断地块是否被占用 被占用则跳过最小距离数的遍历
					jump := constant.ANCIENT_CITY_SEARCH_DIS_MIN - 1
					n = ut.MinInt32(n+jump, yR)
					lastDis--
					continue
				}
				if n > yL {
					if reverse {
						moveDir = 0
					} else {
						moveDir = 2
					}
				}
				dis, _ := this.AncientCityPointSearch(x, y, lastDis, startX, endX, startY, endY, moveDir)
				lastDis = dis
				if dis >= constant.ANCIENT_CITY_SEARCH_DIS_MIN {
					// TODO 优化权重算法
					weight := dis
					// weight := dis * userNum
					if weight > maxWeight {
						maxWeight = weight
						rstIndex = y*this.room.GetMapSize().Y + x
					}
				}
			}
			reverse = !reverse
		}
		if rstIndex > 0 {
			this.CreateAncientCity(dir, rstIndex)
			log.Info("CreateDirAncientCity end")
			return
		}
	}
	// 分块搜索未找到合适位置 可能已被玩家占满 考虑遍历玩家地块搜索古城位置
	rstIndex = this.ForceSearchAncientCity(startX, endX, startY, endY)
	if rstIndex > 0 {
		this.CreateAncientCity(dir, rstIndex)
		log.Info("CreateDirAncientCity end by force")
		return
	}
	log.Error("CreateDirAncientCity end fail")
}

// 生成古城
func (this *Model) CreateAncientCity(dir, index int32) {
	area := this.GetArea(index)
	if area == nil {
		log.Error("CreateAncientCity area is nil index: %v", index)
		return
	}
	if area.Owner != "" {
		log.Error("CreateAncientCity cell is index: %v, owner: %v", index, area.Owner, area.Owner)
		area.Owner = ""
	}
	cityId, ok := constant.ANCIENT_CITY_DIR_ID_MAP[dir]
	if !ok {
		log.Error("CreateAncientCity cell cityId nil dir: %v", dir)
		return
	}
	size := this.room.GetMapSize()
	// 开始添加其他三个地块
	this.AddCellAndUpdateAreaDepend(index+1, "", -cityId)        //右
	this.AddCellAndUpdateAreaDepend(index+size.X, "", -cityId)   //上
	this.AddCellAndUpdateAreaDepend(index+size.Y+1, "", -cityId) //右上
	// 添加古城
	cell := this.AddCellAndUpdateAreaDepend(index, "", cityId)
	this.UpdateCellDepend(index)
	this.WorldPbChangeCellCityId("", cell.index, cell.CityByteId)
	// 添加古城建筑
	build := NewAreaBuild(
		area.index,
		ut.ID(),
		&ut.Vec2{X: 2, Y: 2},
		cityId,
		1,
	)
	area.AddBuild(build)
	// 添加城墙
	area.AddBuild(NewAreaBuild(
		area.index,
		ut.ID(),
		&ut.Vec2{X: 0, Y: 0},
		3000,
		0,
	))
	// 添加古城信息
	resInfo := NewAncientInfo(area.GetIndex(), area.CityId, build.Lv)
	this.AncientCityMap.Set(area.GetIndex(), resInfo)
	this.UpdateAreaInfoByBTCity(area, cityId) //刷新区域信息
}

// 古城点位搜索
func (this *Model) AncientCityPointSearch(x, y, lastDis, startX, endX, startY, endY, moveDir int32) (maxDis, userNum int32) {
	mapSize := this.room.GetMapSize().X
	uidMap := map[string]bool{}
	dis, block := int32(1), false
	if lastDis > 1 {
		// 当前点从上一个点的最大距离-1开始遍历
		dis = lastDis - 1
	}
	var d int32
	defer func() {
		userNum = int32(len(uidMap))
	}()
	for d = dis; d < mapSize; d++ {
		if x-d < startX || x+d > endX || y-d < startY || y+d > endY {
			if !block {
				maxDis = d - 1
			}
			return
		}
		// 每个方向搜索是否被阻挡 从左下开始 逆时针
		for dir := int32(0); dir <= 3; dir++ {
			if lastDis > 1 && d+1 == lastDis && moveDir >= 0 {
				// 上一个点的最大阻挡距离大于一 则初始遍历的距离有两个方向是无阻挡的不用再遍历
				switch moveDir {
				case 0:
					if dir == 2 || dir == 3 {
						continue
					}
				case 1:
					if dir == 1 || dir == 2 {
						continue
					}
				case 2:
					if dir == 0 || dir == 1 {
						continue
					}
				case 3:
					if dir == 0 || dir == 3 {
						continue
					}
				}
			}
			if !this.AncientCitySearchByDir(dir, d, x, y, uidMap) {
				maxDis = d
				return
				// if d < constant.ANCIENT_CITY_SEARCH_DIS_MIN {
				// 	// 阻挡距离过短 直接返回
				// 	maxDis = d
				// 	return
				// } else {
				// 	if !block {
				// 		// 阻挡距离达到要求 继续往外查找周围的玩家数量
				// 		block = true
				// 		maxDis = d
				// 	} else if d-maxDis >= constant.ANCIENT_CITY_SEARCH_DIS_MIN/2 {
				// 		// 前面已有阻挡 在规定范围内往外查找周围的玩家数量
				// 		return
				// 	}
				// }
			}
		}
	}
	return
}

// 古城点位搜索和调整
func (this *Model) AncientCitySearch(startP *ut.Vec2, searchedMap map[int32]bool) (maxDis, userNum, rstIndex int32) {
	mapSize := this.room.GetMapSize().X
	var dis int32
	var moveMaxCount, moveCount, minBlockDis int32 = 1000, 0, 0
	blockMap := map[int32]int32{}
	uidMap := map[string]bool{}
	rstIndex = startP.Y*this.room.GetMapSize().Y + startP.X
	// 逐渐扩大范围搜索
	for dis = 1; dis < mapSize; dis++ {
		if dis > maxDis {
			// 当前距离大于最大距离 更新结果
			rstIndex = startP.Y*this.room.GetMapSize().Y + startP.X
			userNum = int32(len(uidMap))
			maxDis = dis
		}
		block := false
		for dir := int32(0); dir <= 3; dir++ {
			// 每个方向
			if !this.AncientCitySearchByDir(dir, dis, startP.X, startP.Y, uidMap) {
				if blockMap[dir] == 0 {
					blockMap[dir] = dis
					block = true
				} else if dis < blockMap[dir] {
					blockMap[dir] = dis
					block = true
				}
				if minBlockDis == 0 {
					minBlockDis = dis
				} else if dis < minBlockDis {
					minBlockDis = dis
				}
			}
		}
		// minDis := 9999
		// for _, dis := range blockMap {
		// 	if dis < minDis {
		// 		minDis = dis
		// 	}
		// }
		if len(blockMap) == 4 || (blockMap[0] == dis && blockMap[2] == dis) || (blockMap[1] == dis && blockMap[3] == dis) {
			// 所有方向或对角方向都被阻挡 当前距离则为该位置调整后的最大距离
			return
		}
		if moveCount >= moveMaxCount {
			// 移动遍历次数上限
			return
		}
		if block {
			// 有阻挡时且距离过短 尝试移动古城位置
			for bDir := range blockMap {
				dirParams := constant.ANCIENT_CITY_DIR_MAP[bDir]
				if dirParams == nil {
					log.Error("AncientCitySearch dirParams nil dir: %v", dirParams)
					return
				}
				startP.X += dirParams[0]
				startP.Y += dirParams[1]
				blockMap[bDir]++
			}
			index := startP.X + startP.Y*this.room.GetMapSize().Y
			if searchedMap[index] {
				return
			}
			if dis <= constant.ANCIENT_CITY_SEARCH_DIS_MIN {
				// 有阻挡时且距离过短则清除记录
				for k := range blockMap {
					delete(blockMap, k)
				}
				for k := range uidMap {
					delete(uidMap, k)
				}
			}
			moveCount++
			dis -= 2
			dis = ut.MaxInt32(dis, 0)
		}
	}
	return
}

// 强制遍历地图 寻找可用的古城位置
func (this *Model) ForceSearchAncientCity(startX, endX, startY, endY int32) int32 {
	// 遍历该区域中间部分
	searchStartX := startX + (endX-startX)/3
	searchEndX := searchStartX + (endX-startX)/3
	searchStartY := startY + (endY-startY)/3
	searchEndY := searchStartY + (endY-startY)/3
	for x := searchStartX; x < searchEndX-1; x++ {
		for y := searchStartY; y < searchEndY-1; y++ {
			if this.AncientCityCheckAreaAvailable(x, y) {
				return x + y*this.room.GetMapSize().Y
			}
		}
	}
	// 全被占满则随机起始点遍历玩家地块生成古城
	cityX := ut.RandomInt32(searchStartX, searchEndX)
	cityY := ut.RandomInt32(searchStartY, searchEndY)
	for x := cityX; x < endX-1; x++ {
		for y := cityY; y < endY-1; y++ {
			if !this.CheckAncientCityArea(x, y) {
				this.UsePlayerCellsAsAncientCity(x, y)
				return x + y*this.room.GetMapSize().Y
			}
		}
	}
	// 随机起始点遍历仍未找到可用位置 遍历剩余区域
	for x := startX; x < endX-1; x++ {
		for y := startY; y < endY-1; y++ {
			if x >= cityX && y >= cityY {
				continue
			}
			if !this.CheckAncientCityArea(x, y) {
				this.UsePlayerCellsAsAncientCity(x, y)
				return x + y*this.room.GetMapSize().Y
			}
		}
	}
	return -1
}

// 判断点位是否可使用
func (this *Model) AncientCityCheckAreaAvailable(x, y int32) bool {
	index := x + y*this.room.GetMapSize().Y
	area := this.GetArea(index)
	if area == nil || area.Owner != "" || area.IsBattle() {
		return false
	}
	return true
}

// 判断古城本体所占点位是否可用
func (this *Model) CheckAncientCityAreaValid(x, y int32) bool {
	if !this.AncientCityCheckAreaAvailable(x, y) {
		return false
	}
	if !this.AncientCityCheckAreaAvailable(x+1, y) {
		// 右
		return false
	}
	if !this.AncientCityCheckAreaAvailable(x, y+1) {
		// 上
		return false
	}
	if !this.AncientCityCheckAreaAvailable(x+1, y+1) {
		// 右上
		return false
	}
	return true
}

// 判断点位地形是否可使用
func (this *Model) AncientCityCheckArea(x, y int32) bool {
	index := x + y*this.room.GetMapSize().Y
	area := this.GetArea(index)
	if area == nil || area.IsBattle() {
		return false
	}
	return true
}

// 判断古城本体所占点位是否可用 只判断地形是否可用 无论是否被玩家占领
func (this *Model) CheckAncientCityArea(x, y int32) bool {
	if this.AncientCityCheckArea(x, y) {
		return false
	}
	if this.AncientCityCheckArea(x+1, y) {
		// 右
		return false
	}
	if this.AncientCityCheckArea(x, y+1) {
		// 上
		return false
	}
	if this.AncientCityCheckArea(x+1, y+1) {
		// 右上
		return false
	}
	return true
}

// 古城按方向搜索
func (this *Model) AncientCitySearchByDir(dir, dis, x, y int32, uidMap map[string]bool) bool {
	dirParams := constant.ANCIENT_CITY_DIR_MAP[dir]
	if dirParams == nil || len(dirParams) < 4 {
		log.Error("AncientCitySearchByDir dirParams nil dir: %v", dir)
		return false
	}
	rst := true
	// 根据距离遍历指定方向的地块是否被阻挡
	for i := int32(0); i <= dis; i++ {
		curX := x + dirParams[0]*(dis-i) + dirParams[2]
		curY := y + dirParams[1]*i + dirParams[3]
		index := curX + curY*this.room.GetMapSize().Y
		area := this.GetArea(index)
		if area == nil || area.Owner != "" || area.IsBattle() {
			return false
		}
		// if dis < constant.ANCIENT_CITY_SEARCH_DIS_MIN {
		// 	// 有阻挡且距离较短直接返回
		// 	if area == nil || area.Owner != "" || area.IsBattle() {
		// 		return false
		// 	}
		// } else {
		// 	// 有阻挡距离较长 记录玩家uid
		// 	if area == nil || area.IsBattle() {
		// 		rst = false
		// 	} else if area.Owner != "" {
		// 		uidMap[area.Owner] = true
		// 		rst = false
		// 	}
		// }
	}
	return rst
}

// 使用玩家地块生成古城
func (this *Model) UsePlayerCellsAsAncientCity(x, y int32) {
	this.UsePlayerCellAsAncientCity(x + y*this.room.GetMapSize().Y)
	// 右
	this.UsePlayerCellAsAncientCity(x + 1 + y*this.room.GetMapSize().Y)
	// 上
	this.UsePlayerCellAsAncientCity(x + (y+1)*this.room.GetMapSize().Y)
	// 右上
	this.UsePlayerCellAsAncientCity(x + 1 + (y+1)*this.room.GetMapSize().Y)
}

// 使用玩家地块生成古城 单个地块
func (this *Model) UsePlayerCellAsAncientCity(index int32) {
	area := this.GetArea(index)
	if area != nil {
		log.Info("UsePlayerCellAsAncientCity index: %v, owner: %v", index, area.Owner)
		area.Owner = ""
	} else {
		log.Error("UsePlayerCellAsAncientCity nil index: %v", index)
	}
}
