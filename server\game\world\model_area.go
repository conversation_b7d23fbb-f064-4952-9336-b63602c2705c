package world

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bdtype"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	"slgsrv/server/game/player"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
)

func (this *Model) GetArea(index int32) *Area {
	// this.Areas.RLock()
	// defer this.Areas.RUnlock()
	return this.Areas.Map[index]
}

// 记录请求玩家
func (this *Model) RecordReqAreaPlayer(index int32, uid string) {
	this.reqAreaPlayers.Lock()
	uids := this.reqAreaPlayers.Map[index]
	if uids == nil {
		uids = []string{}
	}
	if !array.Has(uids, uid) {
		this.reqAreaPlayers.Map[index] = append(uids, uid)
	}
	this.reqAreaPlayers.Unlock()
}

// 获取请求的玩家列表
func (this *Model) GetReqAreaPlayers(index int32, ignores ...string) []string {
	this.reqAreaPlayers.RLock()
	defer this.reqAreaPlayers.RUnlock()
	uids := this.reqAreaPlayers.Map[index]
	if uids == nil {
		uids = []string{}
	}
	if len(ignores) == 0 {
		return uids
	}
	arr := []string{}
	for _, uid := range uids {
		if !array.Has(ignores, uid) {
			arr = append(arr, uid)
		}
	}
	return arr
}

// 清理玩家请求过区域的记录
func (this *Model) CleanAreaRecordReqPlayer(uid string) {
	this.reqAreaPlayers.Lock()
	for index, uids := range this.reqAreaPlayers.Map {
		this.reqAreaPlayers.Map[index] = array.Remove(uids, uid)
	}
	this.reqAreaPlayers.Unlock()
}

// 将玩家从某个区域删除
func (this *Model) RemoveReqAreaPlayer(index int32, uid string) {
	this.reqAreaPlayers.Lock()
	if uids := this.reqAreaPlayers.Map[index]; uids != nil {
		this.reqAreaPlayers.Map[index] = array.Remove(uids, uid)
	}
	this.reqAreaPlayers.Unlock()
}

// 记录观战玩家
func (this *Model) RecordAreaWatchPlayer(index int32, uid string) {
	players, notify := this.watchPlayers.Get(index), false
	if players == nil {
		players = []*pb.WatchPlayerInfo{}
	}
	if notify = !array.Some(players, func(m *pb.WatchPlayerInfo) bool { return m.Uid == uid }); notify {
		players = append(players, &pb.WatchPlayerInfo{Uid: uid, Time: int64(time.Now().UnixMilli())})
		this.watchPlayers.Set(index, players)
	}
	// 通知
	if notify {
		this.NotifyWatchPlayer(index, constant.NQ_AREA_PLAYER_CHANGE, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_67: players})
	}
}

// 将玩家从观战列表删除
func (this *Model) RemoveAreaWatchPlayer(uid string) {
	index, players, ok := this.watchPlayers.Find(func(v []*pb.WatchPlayerInfo, k int32) bool {
		return array.Some(v, func(m *pb.WatchPlayerInfo) bool { return m.Uid == uid })
	})
	if ok {
		// log.Info("RemoveAreaWatchPlayer uid: %v", uid)
		players = array.RemoveItem(players, func(m *pb.WatchPlayerInfo) bool { return m.Uid == uid })
		this.NotifyWatchPlayer(index, constant.NQ_AREA_PLAYER_CHANGE, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_67: players})
		if len(players) == 0 {
			this.watchPlayers.Del(index)
		} else {
			this.watchPlayers.Set(index, players)
		}
	}
}

// 检测是否有该玩家 在区域
func (this *Model) CheckAreaWatchPlayer(index int32, uid string) bool {
	players := this.watchPlayers.Get(index)
	if players == nil {
		return false
	}
	return array.Some(players, func(m *pb.WatchPlayerInfo) bool { return m.Uid == uid })
}

// 获取临时的区域信息
func (this *Model) GetTempAreaByPlayer(uid string, index int32) map[string]interface{} {
	land := this.Lands[index]
	if plr := this.room.GetPlayer(uid); plr != nil {
		return plr.GetTempAreaInfo(index, land)
	}
	dis := this.GetToMapCellDis(this.GetPlayerMainIndex(uid), index)
	return config.GetAreaConfInfo(land, dis, index)
}

// 获取临时的区域信息
func (this *Model) GetTempAreaByPlayerPb(uid string, index int32) *pb.AreaInfo {
	land := this.Lands[index]
	if plr := this.room.GetPlayer(uid); plr != nil {
		dataJson := plr.GetTempAreaInfo(index, land)
		return config.AreaConfInfoJsonToPb(dataJson)
	}
	dis := this.GetToMapCellDis(this.GetPlayerMainIndex(uid), index)
	dataJson := config.GetAreaConfInfo(land, dis, index)
	return config.AreaConfInfoJsonToPb(dataJson)
}

// 初始化主城
func (this *Model) InitAreaMainCity(area *Area, plr player.TableData, lang string) {
	area.Owner = plr.Uid
	area.CityId = constant.MAIN_CITY_ID
	area.Init(true)
	area.Builds = &BuildList{List: []*AreaBuild{}}
	area.Armys = &ArmyList{List: []*AreaArmy{}}
	// 添加一个城墙
	area.AddBuild(NewAreaBuild(area.index, ut.ID(), ut.NewVec2(-1, -1), constant.WALL_BUILD_ID, 1))
	// 添加一个中心大楼
	buildSize := area.GetBuildSize()
	pos := ut.NewVec2(buildSize.X/2, buildSize.Y/2) //中间位置
	area.AddBuild(NewAreaBuild(area.index, ut.ID(), pos, constant.MAIN_BUILD_ID, 1))
	// 添加两个士兵的军队 固定站在城门口
	var pawnId int32 = constant.DEFAULT_PAWN_ID
	_, _, skinId, _ := this.GetConfigPawnInfo(plr.Uid, pawnId)
	army := NewAreaArmy(area.index, ut.ID(), plr.Uid).Init(constant.DEFAULT_ARMY_NAME[lang] + "1")
	army.AddPawn(NewAreaPawn(area.index, army.EnterDir, ut.ID(), ut.NewVec2(7, 4), pawnId, army.Owner, army.Uid, this).Init(1, skinId))
	army.AddPawn(NewAreaPawn(area.index, army.EnterDir, ut.ID(), ut.NewVec2(9, 4), pawnId, army.Owner, army.Uid, this).Init(1, skinId))
	this.AddAreaArmy(area, army, -1)
	// 获取血量
	area.UpdateMaxHP()
	// 添加免战时间
	this.AddGeneralAvoidWarArea(area.index, constant.MAIN_AVOID_WAR_TIME)
}

// 刷新战场拥有者信息
func (this *Model) UpdateAreaOwnerInfo(index int32, owner string, cityId int32) {
	if area := this.GetArea(index); area != nil {
		this.UpdateAreaOwnerInfoByArea(area, owner, cityId)
	}
}
func (this *Model) UpdateAreaOwnerInfoByArea(area *Area, owner string, cityId int32) {
	isPlayer := area.Owner != "" //是否玩家
	area.Owner = owner
	area.CityId = cityId
	area.Init(true)
	area.UpdateMaxHP()
	// 添加免战时间
	if owner == "" || this.GetPlayerIsGiveupGame(owner) {
		this.RemoveAvoidWarArea(area.index) //删除免战
	} else if isPlayer {
		// 如果是玩家 没有免战
	} else if !this.GetPlayerNoAvoidWar(owner) && !area.IsAncient() { //占领遗迹没有免战
		this.AddGeneralAvoidWarArea(area.index, constant.OCCUPY_AVOID_WAR_TIME) //添加免战时间
	}
	// 如果还在战斗中 停止战斗
	if area.IsBattle() {
		area.BattleEnd([]string{})
	}
	// 遣返所有不是该区域的军队
	this.CheckAreaArmyNotBelongHere(area)
}

// 检测区域是否有不是该区域的军队 然后遣返
func (this *Model) CheckAreaArmyNotBelongHere(area *Area) {
	defendUIDMap := map[string]bool{}
	needSendMailArmys := [][]string{} //战斗中遣返 需要发送邮件
	indexText := helper.IndexToPoint(area.index, this.room.GetMapSize()).Join(",")
	area.FspMutex.RLock()
	defer area.FspMutex.RUnlock()
	isBattle := area.IsBattle()
	for i := int32(len(area.Armys.List)) - 1; i >= 0; i-- {
		army := area.Armys.List[i]
		if this.CheckIsOneAlliance(army.Owner, area.Owner) {
			defendUIDMap[army.Owner] = true
			continue
		} else if this.CheckArmyMarchingByUID(army.Uid) {
			continue
		} else if !isBattle { //没有战斗 直接遣返不不属于这个区域的军队
			this.RepatriateArmyToEmptyArea(area, army, i, "CheckAreaArmyNotBelongHere [battle:false]")
		} else if !this.CheckCanOccupyCell(area.index, army.Owner) { //在战斗中 遣返不可以攻击这个区域的军队
			area.RepatriateArmyByBattle(army)
			this.RepatriateArmyToEmptyArea(area, army, i, "CheckAreaArmyNotBelongHere [battle:true]")
			needSendMailArmys = append(needSendMailArmys, []string{army.Name, army.Owner})
		}
	}
	// 发送邮件
	for _, arr := range needSendMailArmys {
		this.room.SendMailOne(100012, "", indexText+"|"+arr[0], "-1", arr[1])
	}
	if len(needSendMailArmys) > 0 && area.Owner != "" {
		defendUIDMap[area.Owner] = true
		for uid := range defendUIDMap {
			this.room.SendMailOne(100013, "", indexText, "-1", uid)
		}
	}
}

// 检测四周的地块军队 是否可以留在该地块
func (this *Model) CheckAreaArmyNotBelongHereByIndexs(needCheckIndexs []int32) {
	checkIndexs := helper.GetIrregularPointsOuter(needCheckIndexs, this.room.GetMapSize())
	for _, index := range checkIndexs {
		idx := this.AmendIndex(index)
		if area := this.GetArea(idx); area != nil {
			this.CheckAreaArmyNotBelongHere(area)
		}
	}
}

// 遣返军队到最近的空地
func (this *Model) RepatriateArmyToEmptyArea(area *Area, army *AreaArmy, i int32, from string) {
	if tArea := this.GetPlayerAndAlliNotFullArmyArea(army.Owner, area.index); tArea != nil {
		this.AddMarchArmy(area.index, army, tArea.index, -constant.MARC_AUTO_REVOKE_SPEED, true, false, false) //遣返
		log.Info("遣返不是该区域的士兵 %v, index:%v, uid:%v", from, area.index, army.Uid)
	} else {
		// 没有可遣返的地方 直接删除
		area.RemoveArmyByIndex(army.Uid, i)
		// 记录解散
		this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_REMOVE_ARMY, army.Owner, army.Uid, army.Name, army.AIndex, 0)
		log.Info("删除士兵 %v, index:%v, uid:%v", from, area.index, army.Uid)
	}
}

// 强制撤回到最近空地
func (this *Model) ForceRevokeArmyToEmptyArea(area *Area, army *AreaArmy) {
	if tArea := this.GetPlayerNotFullArmyArea(army.Owner, area.index); tArea != nil {
		this.AddMarchArmy(area.index, army, tArea.index, -constant.MARC_FORCE_REVOKE_SPEED, false, true, false) //遣返
		log.Info("强制撤回的士兵 index:%v, uid:%v", area.index, army.Uid)
	} else {
		// 没有可遣返的地方 直接删除
		area.RemoveArmy(army.Uid)
		// 记录解散
		this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_REMOVE_ARMY, army.Owner, army.Uid, army.Name, army.AIndex, 0)
		log.Info(" ForceRevokeArmyToEmptyArea 删除士兵 %v, index:%v, uid:%v", area.index, army.Uid)
	}
}

// 刷新区域信息
func (this *Model) UpdateAreaInfoByBTCity(area *Area, cityId int32) {
	area.CityId = cityId
	area.UpdateCityBuild()
	area.UpdateMaxHP() //刷新最大血量
	// 如果是要塞恢复所有士兵血量
	if area.IsRecoverPawnHP() && !area.IsBattle() {
		area.RecoverAllPawnHP()
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ALL_PAWN_HP, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_24: area.ToArmysPb(),
		})
	}
}

// 刷新士兵携带时光旗后的生命
func (this *Model) CheckUpdateTimeFlagHP(runDay int32) {
	uids := []string{}
	this.allTempPlayers.RLock()
	for _, m := range this.allTempPlayers.Map {
		if array.Some(m.Equips, func(m *g.EquipInfo) bool { return m.GetEquipEffectByType(eeffect.TODAY_ADD_HP) != nil }) {
			uids = append(uids, m.Uid) //只检测有加生命的 时光旗
		}
	}
	this.allTempPlayers.RUnlock()
	indexMap := map[int32]*Area{}
	for _, uid := range uids {
		dist := this.GetPlayerArmyDist(uid)
		for index := range dist {
			area := this.GetArea(index)
			if area == nil || area.IsBattle() {
				continue
			} else if area.CheckUpdateTimeFlagHP(uid) {
				indexMap[index] = area
			}
		}
	}
	for _, area := range indexMap {
		this.TagUpdateDBByIndex(area.index)
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ALL_PAWN_HP, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_24: area.ToArmysPb()})
	}
	log.Info("CheckUpdateTimeFlagHP playerCount: %v, areaCount: %v", len(uids), len(indexMap))
}

// ////////////////////////////////////////////////////////建筑/////////////////////////////////////////////////////////////////////
// 获取区域建筑
func (this *Model) GetAreaBuildByUid(index int32, uid string) *AreaBuild {
	if area := this.GetArea(index); area != nil {
		return area.GetBuildByUID(uid)
	}
	return nil
}

// 获取建筑
func (this *Model) GetAreaBuildById(index, id int32) *AreaBuild {
	if area := this.GetArea(index); area != nil {
		return area.GetAreaBuildById(id)
	}
	return nil
}

// 获取建筑等级
func (this *Model) GetAreaBuildLvById(index, id int32) int32 {
	if build := this.GetAreaBuildById(index, id); build != nil {
		return build.Lv
	}
	return 0
}

// 获取建筑列表
func (this *Model) GetAreaBuildsByID(index, id int32) []g.Build {
	if area := this.GetArea(index); area != nil {
		return area.GetAreaBuildsById(id)
	}
	return []g.Build{}
}

// 获取建筑信息
func (this *Model) ToAreaBuildShortInfo(index int32, uid string) map[string]interface{} {
	if build := this.GetAreaBuildByUid(index, uid); build != nil {
		return build.ToShortInfo()
	}
	return map[string]interface{}{}
}

// 建筑建造完成
func (this *Model) AreaBuildBTComplete(index int32, uid string, lv int32) g.Build {
	area := this.GetArea(index)
	if area == nil {
		return nil
	}
	build := area.GetBuildByUID(uid)
	if build == nil || build.IsMaxLv() {
		return nil
	}
	this.UpdateBuildLv(area, build, lv)
	return build
}

func (this *Model) SetBuildLv(index, id, lv int32) g.Build {
	area := this.GetArea(index)
	if area == nil {
		return nil
	}
	build := area.GetAreaBuildById(id)
	if build == nil || lv > 20 {
		return nil
	}
	this.UpdateBuildLv(area, build, lv)
	return build
}

func (this *Model) UpdateBuildLv(area *Area, build *AreaBuild, lv int32) {
	build.Lv = ut.MinInt32(lv, 20) //最多20级
	build.UpdateAttrJson()
	this.AreaBuildUpComplete(area, build)
}

func (this *Model) AreaBuildUpComplete(area *Area, build *AreaBuild) {
	// 如果是城墙还需要刷新血量
	plr := this.GetTempPlayer(area.Owner)
	if plr == nil {
	} else if build.Id == constant.WALL_BUILD_ID { //城墙
		plr.TowerLvMap.Store(build.GetBuildPawnId(), build.Lv)
		area.UpdateMaxHP()
		this.PutNotifyQueue(constant.NQ_PLAYER_TOWER_LV, &pb.OnUpdateWorldInfoNotify{
			Data_45: &pb.UpdateTowerLvInfo{
				Uid:        plr.Uid,
				TowerLvMap: plr.ToTowerLvMapPb(),
			},
		}) //通知
	} else if build.Id == constant.PAVILION_BUILD_ID || build.Id == constant.FRONTIER_BUILD_ID { //里停 边塞
		plr.TowerLvMap.Store(build.GetBuildPawnId(), build.Lv)
		this.UpdateAreaMaxHP(plr, ut.If(build.Id == constant.FRONTIER_BUILD_ID, constant.FORT_CITY_ID, int32(0)))
		this.PutNotifyQueue(constant.NQ_PLAYER_TOWER_LV, &pb.OnUpdateWorldInfoNotify{
			Data_45: &pb.UpdateTowerLvInfo{
				Uid:        plr.Uid,
				TowerLvMap: plr.ToTowerLvMapPb(),
			},
		})
	} else if build.Id == constant.ALLI_BUILD_ID { //大使馆
		this.UpdatePlayerAlliMemberEmbassyLv(plr.AllianceUid, plr.Uid, build.Lv)
	}
	// 标记更新数据库
	this.TagUpdateDBByIndex(area.index)
	this.NotifyAreaUpdateInfo(area.index, constant.NQ_BUILD_UP, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_5: build.ToShortInfoPb()})
	//
	// log.Info("AreaBuildUpComplete owner: %v, id: %v, lv: %v", area.Owner, build.Id, build.Lv)
}

// 刷新战斗中的血量
func (this *Model) UpdateAreaMaxHP(plr *TempPlayer, id int32) {
	for _, index := range plr.OwnCells {
		area := this.GetArea(index)
		if area == nil {
			continue
		}
		cityId := ut.AbsInt32(area.CityId)
		if cityId == constant.MAIN_CITY_ID {
			continue
		} else if cityId != constant.FORT_CITY_ID {
			cityId = 0 //只要不是要塞其他全都是哨站
		}
		if cityId == id {
			area.UpdateMaxHP()
		}
	}
}

// 兼容等级0的建筑
func (this *Model) CompatiBuildLv0(index int32, uids []string) {
	area := this.GetArea(index)
	if area == nil {
		return
	}
	for _, m := range area.Builds.List {
		if m.Lv == 0 && !array.Has(uids, m.Uid) {
			log.Error("CompatiBuildLv0 ", m.Uid, m.Id)
			this.UpdateBuildLv(area, m, 1)
		}
	}
}

// 获取所有建筑的效果
func (this *Model) GetAreaAllBuildEffect(index int32) map[int32]*g.EffectObj {
	if area := this.GetArea(index); area != nil {
		return area.GetAllBuildEffect()
	}
	return map[int32]*g.EffectObj{}
}

// 获取某个建筑的效果
func (this *Model) GetAreaBuildEffect(index int32, uid string) map[int32]*g.EffectObj {
	if area := this.GetArea(index); area != nil {
		return area.GetBuildEffect(uid)
	}
	return map[int32]*g.EffectObj{}
}

// 获取玩家主城的建筑列表Pb
func (this *Model) GetPlayerMainBuildsPb(index int32) []*pb.AreaBuildInfo {
	area := this.GetArea(index)
	if area == nil {
		return []*pb.AreaBuildInfo{}
	}
	return area.ToBuildsShortDataPb()
}

// 获取某个玩家建筑的等级
func (this *Model) GetPlayerBuildLvById(uid string, id int32) int32 {
	area := this.GetArea(this.GetPlayerMainIndex(uid))
	if area == nil {
		return 0
	} else if build := area.GetAreaBuildById(id); build != nil {
		return build.Lv
	}
	return 0
}

// 获取玩家的建筑等级之和
func (this *Model) GetPlayerBuildsSumLvById(uid string, id int32) int32 {
	area := this.GetArea(this.GetPlayerMainIndex(uid))
	if area == nil {
		return 0
	}
	var lv int32
	builds := area.GetAreaBuildsById(id)
	for _, m := range builds {
		lv += m.GetLV()
	}
	return lv
}

// ////////////////////////////////////////////////////////军队/////////////////////////////////////////////////////////////////////
// 添加军队
func (this *Model) AddAreaArmy(area *Area, army *AreaArmy, dir int32) {
	area.AddArmyMutex.Lock()
	defer area.AddArmyMutex.Unlock()
	this.SetArmyPawnPosition(area, army, dir) //设置士兵的位置
	area.addArmy(army, dir)
	// 战斗中
	area.BattleSettleMutex.RLock()
	if area.IsBattle() {
		area.AddArmyToBattle(army)
	} else if attacker := this.GetAreaAttacker(area); attacker != "" { //有攻击者 直接触发战斗
		this.TriggerAreaBattle(area, attacker, area.GetPassPoints()[ut.ClampInt32(dir, 0, 3)])
	} else if army.GetActPawnCount() > 0 { //有士兵才通知
		if area.IsRecoverPawnHP() {
			army.RecoverAllPawn() //恢复士兵血量
		}
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_ADD_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_10: army.ToPb(area.ToArmyState(army))})
	}
	area.BattleSettleMutex.RUnlock()
	this.TagUpdateDBByIndex(area.index)
}

// 添加军队 不触发战斗
func (this *Model) AddAreaArmyNoBattle(area *Area, army *AreaArmy, dir int32) {
	area.AddArmyMutex.Lock()
	defer area.AddArmyMutex.Unlock()
	this.SetArmyPawnPosition(area, army, dir) //设置士兵的位置
	area.addArmy(army, dir)
	if army.GetActPawnCount() > 0 { //有士兵才通知
		if area.IsRecoverPawnHP() {
			army.RecoverAllPawn() //恢复士兵血量
		}
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_ADD_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_10: army.ToPb(area.ToArmyState(army))})
	}
	this.TagUpdateDBByIndex(area.index)
}

// 通知玩家的军队分布信息
func (this *Model) NotifyPlayerArmyDistInfo(uid string) {
	this.room.PutPlayerNotifyQueue(constant.NQ_ARMY_DIST, uid, &pb.OnUpdatePlayerInfoNotify{Data_2: this.GetPlayerArmyDistArrayPb(uid, false)})
}

// 设置士兵位置
func (this *Model) SetArmyPawnPosition(area *Area, army *AreaArmy, dir int32) {
	pawnCount := army.GetPawnCount()
	if pawnCount == 0 || dir < 0 || dir >= 4 {
		return
	} else if this.CheckIsOneAlliance(area.Owner, army.Owner) { //在自己区域或者联盟区域的时候 直接设置在战斗区域
		points := helper.GetPawnPointsByDoor(area.GetIdleBattlePointsToMapBool(army.Uid), area.GetAreaSize(), area.GetDoorPoints(), dir, pawnCount)
		army.SetPawnPoints(points)
	} else {
		army.SetPawnPoints([]*ut.Vec2{area.GetPassPoints()[dir]}) //设置到关口位置
	}
}

// 招募士兵完成
func (this *Model) AreaPawnDrillComplete(index int32, auid string, id, lv int32) {
	area, army := this.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return
	} else if ut.ChanceInt32(this.GetPlayerPolicyEffectIntByUid(area.Owner, effect.XL_2LV)) {
		lv = 2 //有一定几率直接2级
	}
	// 队伍士兵数已满
	if army.GetPawnCount() >= constant.ARMY_PAWN_MAX_COUNT {
		army.CleanDrillAndCuringPawn()
		return
	}
	// 删除训练中的
	army.RemoveDrillPawn(id)
	// 获取配置
	equipId, equipAttrs, skinId, attackSpeed := this.GetConfigPawnInfo(army.Owner, id)
	// 创建士兵
	pawn := NewAreaPawn(area.index, army.EnterDir, ut.ID(), area.GetDrillPawnPoint(), id, army.Owner, army.Uid, this).Init(lv, skinId)
	pawn.ChangeEquip(equipId, equipAttrs, true)
	if attackSpeed != 0 {
		pawn.AttackSpeed = attackSpeed
	}
	// 添加士兵
	army.AddPawn(pawn)
	// 主动刷新玩家粮耗
	this.UpdatePlayerOutput(army.Owner)
	// 添加玩家累计的士兵数量
	this.AddPlayerAccTotalPawnCount(army.Owner, id, 1)
	// 标记更新数据库
	this.TagUpdateDBByIndex(area.index)
	// 战斗中的话 就不通知 用帧同步通知
	if !area.IsBattle() {
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_19: army.ToPb(area.ToArmyState(army)),
		})
	} else if army.IsFighting() { //如果在战斗中直接添加士兵
		area.AddPawnToBattle(pawn)
	} else { //否则就添加军队
		area.AddArmyToBattle(army)
	}
	// 触发任务
	this.TriggerTask(army.Owner, tctype.RECRUIT_PAWN_COUNT, 1, 0)
	this.TriggerTask(army.Owner, tctype.RECRUIT_PAWN_APPOINT, 1, id)
	this.TriggerTask(army.Owner, tctype.ARMY_PAWN_COUNT, this.GetMaxPawnCountInArmy(army.Owner), 0)
	this.TriggerTask(army.Owner, tctype.RECRUIT_PAWN_TYPE, 1, pawn.GetPawnType())
	if ut.Int(pawn.attrJson["attack_range"]) > 1 {
		this.TriggerTask(army.Owner, tctype.RECRUIT_RANGED_PAWN, 1, 2)
	}
	// 添加防作弊积分
	this.AddPlayerAntiCheatSocre(army.Owner, constant.ANTI_CHEAT_DRILL_PAWN_SCORE)
	// 上报
	this.TaTrack(army.Owner, 0, "ta_recruitPawn", map[string]interface{}{
		"role_id": pawn.Id,
		"role_lv": pawn.Lv,
	})
}

// 士兵练级完成
func (this *Model) AreaPawnLvingComplete(index int32, auid, puid string, lv int32) {
	t := time.Now().UnixMilli()
	defer func() {
		if dt := time.Now().UnixMilli() - t; dt > 100 {
			log.Error("AreaPawnLvingComplete index: %v, auid: %v, puid: %v, lv: %v, time: %v", index, auid, puid, lv, dt)
		}
	}()
	area, army := this.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return
	} else if pawn := army.GetPawnByUID(puid); pawn != nil {
		pawn.UpdateLv(lv)
		// 标记更新数据库
		this.TagUpdateDBByIndex(area.index)
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_19: army.ToPb(area.ToArmyState(army)),
		})
		// 触发任务
		this.TriggerTask(army.Owner, tctype.UPLV_PAWN_APPOINT, lv, pawn.Id)
		this.TriggerTask(army.Owner, tctype.UPLV_PAWN, lv, 0)
		if lv >= 6 {
			this.AddPlayerMaxLvPawnCount(army.Owner, 1)
		}
		// 添加防作弊积分
		this.AddPlayerAntiCheatSocre(army.Owner, constant.ANTI_CHEAT_LVING_PAWN_SCORE)
		// 上报
		this.TaTrack(army.Owner, 0, "ta_drillPawn", map[string]interface{}{
			"role_id":    pawn.Id,
			"role_lv":    pawn.Lv,
			"use_scroll": false,
		})
	}
}

// 治疗士兵完成
func (this *Model) AreaPawnCureComplete(index int32, auid, puid string) {
	area, army := this.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return
	}
	// 从医馆中移除受伤的士兵
	injuryPawn := this.RemoveInjuryPawn(puid, area.Owner)
	if injuryPawn == nil {
		log.Warning("AreaPawnCureComplete injuryPawn nil, index: %v, puid: %v, userId: %v", index, puid, area.Owner)
		// 删除军队里面的
		army.RemoveCuringPawn(puid)
		return
	}
	// 队伍士兵数已满
	if army.GetPawnCount() >= constant.ARMY_PAWN_MAX_COUNT {
		army.CleanDrillAndCuringPawn()
		return
	}
	// 删除军队里面的
	army.RemoveCuringPawn(puid)
	// 获取配置
	equipId, equipAttrs, skinId, attackSpeed := this.GetConfigPawnInfo(army.Owner, injuryPawn.Id)
	// 创建士兵
	pawn := NewAreaPawn(area.index, army.EnterDir, injuryPawn.Uid, area.GetDrillPawnPoint(), injuryPawn.Id, army.Owner, army.Uid, this).Init(injuryPawn.Lv, skinId)
	pawn.ChangeEquip(equipId, equipAttrs, true)
	if attackSpeed != 0 {
		pawn.AttackSpeed = attackSpeed
	}
	pawn.CureCount = injuryPawn.CureCount + 1
	// 添加士兵
	army.AddPawn(pawn)
	// 主动刷新玩家粮耗
	this.UpdatePlayerOutput(army.Owner)

	// 标记更新数据库
	this.TagUpdateDBByIndex(area.index)
	// 战斗中的话 就不通知 用帧同步通知
	if !area.IsBattle() {
		this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_19: army.ToPb(area.ToArmyState(army)),
		})
	} else if army.IsFighting() { //如果在战斗中直接添加士兵
		area.AddPawnToBattle(pawn)
	} else { //否则就添加军队
		area.AddArmyToBattle(army)
	}

	// 触发任务

	// // 添加防作弊积分
	// this.AddPlayerAntiCheatSocre(army.Owner, constant.ANTI_CHEAT_DRILL_PAWN_SCORE)
	// 上报
	// this.TaTrack(army.Owner, 0, "ta_recruitPawn", map[string]interface{}{
	// 	"role_id": pawn.Id,
	// 	"role_lv": pawn.Lv,
	// })
}

// 屯田完成
func (this *Model) CellTondenComplete(index int32, auid string) (treasureIds []int32) {
	t := time.Now().UnixMilli()
	defer func() {
		if dt := time.Now().UnixMilli() - t; dt > 100 {
			log.Error("CellTondenComplete index: %v", index)
		}
	}()
	area, army := this.GetAreaAndArmy(index, auid)
	if area == nil || army == nil {
		return
	}
	army.EndCellTonden()
	if army.Owner != area.Owner {
		return
	}
	armyMap := map[string][]*AreaArmy{}
	armyMap[area.Owner] = []*AreaArmy{army}
	treasuresMap := area.SendTreasures(area.Owner, area.Owner, armyMap, 0, false)
	if treasuresMap != nil {
		// 通知宝箱信息
		if arr := treasuresMap[area.Owner]; arr != nil {
			tondenEndInfo := &pb.CellTondenEndInfo{Auid: auid}
			treasureIds = []int32{}
			tondenEndInfo.Treasures = array.Map(arr, func(m *g.TreasureInfo, _ int) *pb.TreasureBaseInfo {
				id := int32(m.ID)
				treasureIds = append(treasureIds, id)
				return &pb.TreasureBaseInfo{Id: id, Uid: m.UID}
			})
			this.NotifyAreaUpdateInfo(index, constant.NQ_CELL_TONDEN_END, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_80: tondenEndInfo})
		}
	} else {
		// 没有宝箱也通知
		this.NotifyAreaUpdateInfo(index, constant.NQ_CELL_TONDEN_END, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_80: &pb.CellTondenEndInfo{Auid: auid}})
	}
	// 标记更新数据库
	this.TagUpdateDBByIndex(area.index)
	this.NotifyAreaUpdateInfo(area.index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_19: army.ToPb(area.ToArmyState(army)),
	})
	// 触发任务

	// 添加防作弊积分
	this.AddPlayerAntiCheatSocre(army.Owner, constant.ANTI_CHEAT_CELL_TONDEN_SCORE)
	// 上报
	return
}

// 获取区域和军队
func (this *Model) GetAreaAndArmy(index int32, armyUid string) (*Area, *AreaArmy) {
	area := this.GetArea(index)
	if area == nil {
		return nil, nil
	}
	return area, area.GetArmyByUid(armyUid)
}

// 获取区域和士兵
func (this *Model) GetAreaAndPawn(index int32, armyUid string, uid string) (*Area, *AreaPawn) {
	area, army := this.GetAreaAndArmy(index, armyUid)
	if army == nil {
		return area, nil
	}
	return area, army.GetPawnByUID(uid)
}

// 获取区域和屯田中的军队
func (this *Model) GetAreaAndTondenArmy(index int32) (*Area, *AreaArmy) {
	area := this.GetArea(index)
	if area == nil {
		return nil, nil
	}
	return area, area.GetTondenArmy()
}

// 创建一个临时的士兵
func (this *Model) NewTempPawn(index int32, uid string, point *ut.Vec2, owner, armyUid string, id, lv int32) g.Pawn {
	return NewAreaPawn(index, -1, uid, point, id, owner, armyUid, this).Init(lv, 0)
}

// 获取培养士兵的资源消耗和时间
func GetPawnResCost(id, lv int32) ([]*g.TypeObj, int32) {
	arr := []*g.TypeObj{}
	var needTime int32
	// 获取招募消耗资源
	json := config.GetJsonData("pawnBase", id)
	if json == nil {
		return arr, 0
	}
	arr = append(arr, g.StringToTypeObjs(json["drill_cost"])...)
	needTime = ut.Int32(json["drill_time"])
	// 获取升级消耗资源
	for i := lv - 1; i >= 1; i-- {
		attrId := id*1000 + i
		attrJson := config.GetJsonData("pawnAttr", attrId)
		arr = g.MergeTypeObjsCount(arr, g.StringToTypeObjs(attrJson["lv_cost"])...)
		needTime += ut.Int32(attrJson["lv_time"])
	}
	return arr, needTime
}

// 获取士兵阵亡积分
func GetPawnDeadScore(id, lv int32) float32 {
	costArr, _ := GetPawnResCost(id, lv)
	res := float32(0)
	for _, obj := range costArr {
		if obj.Type == ctype.EXP_BOOK {
			res += float32(obj.Count) * bdtype.ALLI_SCORE_PAWN_DEAD_COST_BOOK
		} else {
			res += float32(obj.Count) * bdtype.ALLI_SCORE_PAWN_DEAD_COST_RES
		}
	}
	return res * bdtype.ALLI_SCORE_PAWN_DEAD_PARAM
}

// 获取士兵战损积分
func GetPawnResCostScore(id, lv int32) float64 {
	if id == -1 {
		// 阵亡单位为建筑 lv为最大血量
		return float64(lv) * bdtype.ALLI_SCORE_BUILD_PARAM
	}
	costArr, _ := GetPawnResCost(id, lv)
	res := float64(0)
	for _, obj := range costArr {
		if obj.Type == ctype.EXP_BOOK {
			res += float64(obj.Count) * bdtype.ALLI_SCORE_PAWN_COST_BOOK
		} else {
			res += float64(obj.Count) * bdtype.ALLI_SCORE_PAWN_COST_RES
		}
	}
	return res * bdtype.ALLI_SCORE_PAWN_PARAM
}

// ////////////////////////////////////////////////////////战斗/////////////////////////////////////////////////////////////////////
// 是否满了 军队
func (this *Model) IsAreaFullArmy(area *Area, owner string) bool {
	return this.GetAreaFullArmyCount(area, owner) >= 0
}

// 实际是否满了 这里还会算上正在赶来的军队
func (this *Model) IsAreaFullArmyAct(area *Area, owner string) bool {
	count := this.GetAreaFullArmyCount(area, owner)
	return count >= 0 || this.GetGoToAreaMarchCount(area, owner) >= ut.AbsInt32(count)
}

// 是否满 这里还会算上增援数量
func (this *Model) IsAreaFullArmyAndAddTimesByMove(area *Area, owner string, armyCount int32) (string, int32) {
	count := this.GetAreaFullArmyCount(area, owner)
	if count >= 0 {
		return ecode.AREA_FULL_ARMY.String(), 0
	}
	canAddCount := ut.AbsInt32(count)
	gotoCount, gotoCountAll, isFriend := this.GetGoToAreaMarchCountAndAll(area, owner)
	if gotoCount+armyCount > canAddCount {
		return ecode.MANY_GOTO_MARCH.String(), 0
	} else if !area.IsBattle() {
		return "", canAddCount - gotoCount
	}
	var canAddPawnTimes int32
	if isFriend {
		canAddPawnTimes = area.GetDefenderCanAddPawnTimes()
	} else {
		canAddPawnTimes = area.GetAttackerCanAddPawnTimes()
	}
	if gotoCountAll+armyCount > canAddPawnTimes {
		return ecode.BATTLE_ARMY_ACC_LIMIT.String(), 0 //目标地块累计防守军队上限
	}
	return "", ut.MinInt32(canAddCount-gotoCount, canAddPawnTimes-gotoCountAll)
}

// 是否满 这里还会算上增援数量 屯田
func (this *Model) IsAreaFullArmyAndAddTimesByTonden(area *Area, owner string, armyUID string) (string, int32) {
	count, canAddCount := this.GetAreaArmyCountByOwner(area, owner)
	if count+canAddCount <= 0 {
		return ecode.AREA_FULL_ARMY.String(), 0
	}
	gotoCount, _, _ := this.GetGoToAreaMarchCountAndAll(area, owner)
	isCheckGoto := false
	if count == 0 { //当前地块没有士兵
		isCheckGoto = true
	} else if armyUID != "" { //当前地块有士兵
		isCheckGoto = area.GetArmyByUid(armyUID) == nil //如果选的不是当前地块的士兵 那么就需要判断是否能够前往
	}
	if isCheckGoto && gotoCount+1 > canAddCount {
		return ecode.MANY_GOTO_MARCH.String(), 0
	}
	return "", canAddCount - gotoCount
}

// 获取当前区域剩余的军队数量 根据是否盟友计算
func (this *Model) GetAreaFullArmyCount(area *Area, owner string) int32 {
	def, atk := this.GetAreaArmyCount(area)
	if this.CheckIsOneAlliance(area.Owner, owner) {
		return def - area.maxArmyCount //友方剩余数量
	}
	return atk - area.maxArmyCount //敌方剩余数量
}

// 获取当前区域军队数量 防，攻
func (this *Model) GetAreaArmyCount(area *Area) (def int32, atk int32) {
	list := area.GetArmysClone()
	for _, m := range list {
		if march := this.GetMarchByArmyUID(m.Uid); march != nil && (march.AutoRevoke || march.ForceRevoke) && march.TargetIndex != area.index {
			continue //如果是强制遣返或强制撤离 并且目标不是这里 就不算位置
		} else if this.CheckIsOneAlliance(area.Owner, m.Owner) {
			def += 1
		} else {
			atk += 1
		}
	}
	return
}

// 获取当前区域自己的军队数量
func (this *Model) GetAreaArmyCountByOwner(area *Area, owner string) (int32, int32) {
	var cnt, sumCount int32 = 0, 0
	list := area.GetArmysClone()
	for _, m := range list {
		if march := this.GetMarchByArmyUID(m.Uid); march != nil && (march.AutoRevoke || march.ForceRevoke) && march.TargetIndex != area.index {
			continue //如果是强制遣返或强制撤离 并且目标不是这里 就不算位置
		} else if m.Owner == owner {
			cnt += 1
		}
		if this.CheckIsOneAlliance(area.Owner, m.Owner) {
			sumCount += 1
		}
	}
	return cnt, area.maxArmyCount - sumCount
}

// 获取区域里面的攻击者
func (this *Model) GetAreaAttacker(area *Area) string {
	uid := area.Owner
	list := area.GetArmysClone()
	for _, m := range list {
		if !this.CheckArmyMarchingByUID(m.Uid) && !this.CheckIsOneAlliance(m.Owner, uid) {
			return m.Owner
		}
	}
	return ""
}

// 触发区域战斗
func (this *Model) TriggerAreaBattle(area *Area, attacker string, enterPoint *ut.Vec2) {
	now := time.Now().UnixMilli() + 10 //这里加点时间不然会在行军记录后面
	isMainCity := area.CityId == constant.MAIN_CITY_ID
	if this.IsCellTondenArea(area.index) {
		// 屯田中则取消屯田
		this.RemoveCellTonden(area.index)
	}
	// 如果是还未被占领的 就获取攻击者的对应配置
	if area.Owner == "" {
		if area.IsAncient() {
			// 古城遗迹
			pbData := this.GetAncientCityTempArea(area)
			area.CurHp = pbData.Hp[0]
			area.MaxHp = pbData.Hp[1]
			// 军队
			for _, army := range pbData.Armys {
				area.addArmy(NewAreaArmy(area.index, army.Uid, "").FromPbData(army, this), -1)
			}
		} else {
			// 普通野地
			data := this.GetTempAreaByPlayer(attacker, area.index)
			arr := data["armys"].([]map[string]interface{})
			for _, army := range arr {
				area.addArmy(NewAreaArmy(area.index, army["uid"].(string), "").FromData(army, this), -1)
			}
			hp := data["hp"].([]int32)
			area.CurHp = hp[0]
			area.MaxHp = hp[1]
		}
	} else if count := this.GetAreaFullArmyCount(area, area.Owner); count < 0 { //如果没有满
		count = ut.AbsInt32(count)
		// 这里检测周围是否有要塞 如果有将要塞的士兵移动过来
		armys := this.GetAreaAdjoinAutoFortArmys(area)
		// 是否主城 然后是否有速速回防
		if isMainCity && this.GetPlayerPolicyEffectFloatByUid(area.Owner, effect.BACK_COURT) > 0 {
			arr := this.GetPlayerNotMainIdleArmys(area.Owner, area.index, armys)
			armys = append(armys, arr...)
		}
		if len(armys) > 0 {
			for i, l := int32(0), int32(len(armys)); i < count && i < l; i++ {
				army := armys[i]
				if army.Owner != area.Owner || this.CheckArmyMarchingByUID(army.Uid) {
					continue
				}
				// 加入行军
				this.AddMarchArmy(army.AIndex, army, area.index, -3.0, false, false, false)
			}
			this.NotifyPlayerArmyDistInfo(area.Owner) //通知军队分布情况
		}
	}
	// 如果是主城暂停士兵训练
	if isMainCity {
		this.PausePawnLving(area.index, now)
	}
	area.BattleTime = now
	area.BattleBegin(attacker, enterPoint)
	this.AddBattleDist(area)
	this.NotifyAreaUpdateInfo(area.index, constant.NQ_AREA_BATTLE_BEGIN, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_15: area.ToPb()})
}

// 战斗结束
func (this *Model) AreaBattleEnd(index int32, attacker string, fighters []string, atkReinforceCount, defReinforceCount int32, datas []*g.BattleRecordData, battleInfo map[string]map[int32]int32, battlePlayerUids []string, treasuresMap map[string][]*g.TreasureInfo,
	deadInfoMap map[string][]*g.PawmDeadRecord, alliUidMap map[string]string, lostTreasureMap map[string]map[int32]int32) {
	fighter := ""
	if len(fighters) > 0 {
		fighter = fighters[0]
	}
	area := this.GetArea(index)
	isBattleEndTime := area.CheckBattleEndTime() //提前获取是否战斗时间到了
	// 记录战斗信息
	this.RecordBattleInfo(area, attacker, fighter, atkReinforceCount, defReinforceCount, datas, battleInfo, deadInfoMap, alliUidMap)
	uid, needCheckIndexs := "", []int32{}
	oldOwner := area.Owner
	// 占领
	if len(fighters) > 0 {
		uid, needCheckIndexs = this.OccupyCell(index, fighters, battlePlayerUids)
		// 占领古城
		if area.IsAncient() {
			this.AncientOccupyReset(area, oldOwner)
		}
		this.CheckGameOverByLandCount(uid)
	} else {
		area.UpdateMaxHP() //更新血量
	}
	// 检测行军
	this.CheckAllMarchTargetCanAddArmy(true, index)
	// 删除战斗分布
	this.RemoveBattleDist(index)
	// 是否有军队要自动返回的
	this.CheckArmyAutoBack(area)
	// 调整位置
	this.AdjustmentPawnPos(area)
	// 如果是主城恢复训练
	if area.CityId == constant.MAIN_CITY_ID {
		this.PausePawnLving(index, 0)
	}
	// 如果是可以恢复血量的
	if area.IsRecoverPawnHP() {
		area.RecoverAllPawnHP()
	}
	// 通知
	msg := &pb.BattleEndInfo{Attacker: fighter}
	if area.IsHasHp() {
		msg.Data = area.ToPb() //有血量直接发送区域信息
	} else {
		msg.Data = &pb.AreaInfo{}
		msg.Data.Index = int32(-1)
	}
	msg.Data.IsBattleEndTime = isBattleEndTime
	if treasuresMap == nil {
		msg.NoTreasureByNotStamina = true
	} else if arr := treasuresMap[fighter]; arr != nil {
		msg.Treasures = array.Map(arr, func(m *g.TreasureInfo, _ int) *pb.TreasureBaseInfo {
			return &pb.TreasureBaseInfo{Id: int32(m.ID), Uid: m.UID}
		})
	}
	this.TagUpdateDBByIndex(index)
	this.NotifyAreaUpdateInfo(index, constant.NQ_AREA_BATTLE_END, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_16: msg})
	// 如果是时间到了
	if fighter == "" && isBattleEndTime {
		// 遣返所有不是该区域的军队
		this.CheckAreaArmyNotBelongHere(area)
		// 添加免战 野地就不添加免战了
		if area.Owner != "" {
			this.AddAvoidWarAreaEndTime(area.index, constant.OCCUPY_AVOID_WAR_TIME/2, constant.AVOID_WAR_TYPE_BATTLE_TIMEOUT)
			// 如果是主城则设置免战减免为-1 避免达到一定地块数量减少免战时间
			if plr := this.GetTempPlayer(area.Owner); plr != nil && area.CityId == constant.MAIN_CITY_ID {
				plr.IsNeedUpdateDB = true
				plr.AvoidWarReduce = -1
			}
		}
	}
	// 检测四周的地块军队 是否可以留在该地块
	if len(needCheckIndexs) > 0 {
		this.CheckAreaArmyNotBelongHereByIndexs(needCheckIndexs)
	}
	// 新手区野地战斗战损补偿
	if this.room.GetType() == slg.ROOKIE_SERVER_TYPE {
		if oldOwner == "" && !area.IsAncient() {
			for userUid, deadInfo := range deadInfoMap {
				this.HandlePlayerCompensate(userUid, deadInfo, treasuresMap[userUid])
			}
		}
	}
	// 阵亡损失宝箱补偿
	if lostTreasureMap != nil {
		for userUid, resMap := range lostTreasureMap {
			this.HandlePlayerLostTresureCompensate(userUid, resMap)
		}
	}
}

// 记录战斗信息
func (this *Model) RecordBattleInfo(area *Area, attacker, fighter string, atkReinforceCount, defReinforceCount int32,
	datas []*g.BattleRecordData, battleInfo map[string]map[int32]int32,
	deadInfoMap map[string][]*g.PawmDeadRecord, alliUidMap map[string]string) {
	recordUid := ut.ID()
	battleEndTime := time.Now().UnixMilli()
	record := this.room.GetRecord()
	battleCostTime := time.Now().UnixMilli() - area.BattleTime
	// 添加战斗记录
	record.AddBattleRecord(recordUid, area.index, area.BattleTime, battleEndTime, datas)
	pawnOwnerMap := map[string]string{} //士兵uid=>owner
	// 找出本次战斗参与过的所有军队
	playerHasArmy := map[string]bool{}
	lossMan, pawnBattleInfos := int32(0), []map[string]interface{}{} // 上报信息
	if battlePreArmys := area.ToBattlePreArmys(); battlePreArmys != nil {
		records := []map[string]interface{}{}
		for _, m := range battlePreArmys {
			uid := m.Uid
			armyOwner := m.Owner
			if armyOwner == "" {
				continue //如果是野怪
			}
			playerHasArmy[armyOwner] = true
			endTime := m.EndTime
			if endTime <= 0 {
				endTime = battleEndTime
			}
			curPawnCount, pawnMap := int32(0), map[string]*AreaPawn{}
			if army := area.GetArmyByUid(uid); army != nil {
				curPawnCount = army.GetActPawnCount() //获取当前区域剩下的军队
				// 上报信息
				army.Pawns.RLock()
				for _, p := range army.Pawns.List {
					pawnMap[p.Uid] = p
				}
				army.Pawns.RUnlock()
			}
			// 计算战斗数据
			battleInfoMap := map[int32]int32{}
			isOneAlliance := this.CheckIsOneAlliance(area.Owner, armyOwner)
			pawns := m.Pawns
			for _, pawn := range pawns {
				pawnUID := pawn.Uid
				if obj := battleInfo[pawnUID]; obj != nil {
					for k, v := range obj {
						battleInfoMap[k] += v
					}
				}
				pawnOwnerMap[pawnUID] = armyOwner
				pawnBattleInfo := battleInfo[pawnUID]
				if pawnBattleInfo == nil {
					pawnBattleInfo = map[int32]int32{}
				}
				hpRemain := 0
				if p := pawnMap[pawnUID]; p != nil {
					hpRemain = int(float64(p.GetCurHP()) / float64(p.GetMaxHP()) * 100)
				}
				// Ta数据记录
				pawnBattleInfos = append(pawnBattleInfos, map[string]interface{}{
					"battle_type":          ut.If(isOneAlliance, 2, 1),
					"role_uid":             pawnUID,
					"role_id":              pawn.Id,
					"role_lv":              pawn.Lv,
					"role_count":           1,
					"equip_id":             pawn.EquipId,
					"equip_count":          1,
					"portrayal_id":         pawn.PortrayalId,
					"portrayal_count":      1,
					"role_damage":          pawnBattleInfo[bdtype.SUM_DAMAGE],
					"role_heal":            pawnBattleInfo[bdtype.HEAL_HP],
					"role_damageMitigated": pawnBattleInfo[bdtype.HIT_DAMAGE_MITIGATED],
					"role_damageTaken":     pawnBattleInfo[bdtype.HIT_DAMAGE_TAKEN],
					"role_hpRemain":        hpRemain,
					"battle_costTime":      battleCostTime,
					"kill_count":           pawnBattleInfo[bdtype.SUM_KILL],
					"kill_pawn_count":      pawnBattleInfo[bdtype.KILL_PAWN],
				})
			}
			// 军队战斗记录
			records = append(records, map[string]interface{}{
				"uid":          uid,
				"owner":        m.Owner,
				"name":         m.Name,
				"beginTime":    m.BeginTime,
				"endTime":      endTime,
				"curPawnCount": curPawnCount,
				"sumPawnCount": m.PawnCount,
				"battleInfo":   battleInfoMap,
				"deadInfo":     deadInfoMap,
			})
			// 记录死亡士兵数
			lossMan += ut.MaxInt32(0, m.PawnCount-curPawnCount)
		}
		if len(records) > 0 {
			record.AddArmyBattleRecords(recordUid, area.index, records)
		}
		area.battlePreMutex.Lock()
		area.battlePreArmys = nil
		area.battlePreMutex.Unlock()
	}
	cityId, cityLv := area.GetBuildPawnInfo()
	// 计算玩家战斗数据
	plrValidMap := map[string]map[int32]int32{}   //玩家战斗有效数据
	plrInvalidMap := map[string]map[int32]int32{} //玩家战斗无效数据
	plyScoreMap := map[string]float64{}
	alliBattleValMap := map[string]float64{}
	pawnScoreParamMap := map[string]float64{} // 士兵得分参数map
	pawnScoreMap := map[string]float64{}      // 士兵得分map
	for pawnUid, pawnBattleInfo := range battleInfo {
		owner := pawnOwnerMap[pawnUid]
		alliId := alliUidMap[owner]
		if owner == "" {
			// 没有拥有者 先判断是否建筑
			isBuild := false
			for bType := range pawnBattleInfo {
				if bType == bdtype.BUILD_DAMAGE {
					isBuild = true
					// 兼容建筑单位owner
					owner = area.Owner
					pawnOwnerMap[pawnUid] = owner
					alliId = alliUidMap[owner]
					// 添加到列表
					pawnBattleInfos = append(pawnBattleInfos, map[string]interface{}{
						"battle_type":          2,
						"role_uid":             pawnUid,
						"role_id":              cityId,
						"role_lv":              cityLv,
						"role_count":           1,
						"role_damage":          pawnBattleInfo[bdtype.SUM_DAMAGE],
						"role_heal":            pawnBattleInfo[bdtype.HEAL_HP],
						"role_damageMitigated": pawnBattleInfo[bdtype.HIT_DAMAGE_MITIGATED],
						"role_damageTaken":     pawnBattleInfo[bdtype.HIT_DAMAGE_TAKEN],
						"battle_costTime":      battleCostTime,
					})
					break
				}
			}
			if !isBuild {
				// 没有拥有者且不是建筑 则为野怪 不参加数据记录
				continue
			}
		}
		for bType, val := range pawnBattleInfo {
			if area.Owner != "" && alliId != "" {
				// 玩家地块则计算积分参数
				if param, ok := bdtype.ALLI_SCORE_PARAM[bType]; ok {
					// 士兵积分参数
					pawnScoreParamMap[pawnUid] += float64(val) * param
					// 联盟积分参数
					alliBattleValMap[alliId] += float64(val) * param
				}
				validMap := plrValidMap[owner]
				if validMap == nil {
					validMap = map[int32]int32{}
					plrValidMap[owner] = validMap
				}
				validMap[bType] += val
			} else {
				invalidMap := plrInvalidMap[owner]
				if invalidMap == nil {
					invalidMap = map[int32]int32{}
					plrInvalidMap[owner] = invalidMap
				}
				invalidMap[bType] += val
			}
		}
	}
	if area.Owner != "" {
		// 玩家地块的战斗计算战斗积分
		for owner, deadInfoList := range deadInfoMap {
			// 算出阵亡士兵总积分
			totalScore := float64(0)
			for i := len(deadInfoList) - 1; i >= 0; i-- {
				deadInfo := deadInfoList[i]
				totalScore += GetPawnResCostScore(deadInfo.Id, deadInfo.Lv)
				if deadInfo.Id < 0 {
					// 累计到总积分后删除哨塔的阵亡数据
					deadInfoList = append(deadInfoList[:i], deadInfoList[i+1:]...)
				}
				// 计算士兵阵亡数据
				// plyScoreMap[owner] += GetPawnDeadScore(deadInfo.Id, deadInfo.Lv)
			}
			// 其他联盟的玩家根据数据瓜分积分
			alli := alliUidMap[owner]
			// 获取其他联盟参与瓜分积分的总战斗数据
			var otherAlliTotalVal float64
			for alliUid, battleVal := range alliBattleValMap {
				if alli != alliUid {
					otherAlliTotalVal += battleVal
				}
			}
			// 累计每个士兵获得积分
			for pawnUid, param := range pawnScoreParamMap {
				owner := pawnOwnerMap[pawnUid]
				alliId := alliUidMap[owner]
				if alliId == "" || alliId == alli {
					continue
				}
				// 当前士兵获得的积分为 = 当前士兵战斗数据/非阵亡士兵的其他联盟的总战斗数据 * 阵亡积分
				addPawnScore := param / otherAlliTotalVal * totalScore
				pawnScoreMap[pawnUid] += addPawnScore
				plyScoreMap[owner] += addPawnScore
			}
		}

		// 在玩家的地块上战斗则添加战斗积分记录
		record.AddBattleScoreRecords(recordUid, area.BattleTime, battleEndTime, alliUidMap, deadInfoMap, plrValidMap, plrInvalidMap, plyScoreMap, playerHasArmy)
	}

	// 累计玩家战斗信息
	for uid := range alliUidMap {
		if uid != "" {
			ply := this.GetTempPlayer(uid)
			if ply != nil {
				ply.battleRecordMutex.Lock()
				if validMap := plrValidMap[uid]; validMap != nil {
					for k, v := range validMap {
						ply.BattleRecordInfo[k] += v
					}
				}
				if invalidMap := plrInvalidMap[uid]; invalidMap != nil {
					for k, v := range invalidMap {
						ply.BattleRecordInfo[k] += v
					}
				}
				if deadInfoMap != nil {
					if deadList, ok := deadInfoMap[uid]; ok {
						ply.BattleRecordInfo[bdtype.PAWN_DEAD] += int32(len(deadList))
					}
				}
				ply.battleRecordMutex.Unlock()
			}
		}
	}

	// 上报士兵信息战斗积分赋值
	for _, v := range pawnBattleInfos {
		uid := ut.String(v["role_uid"])
		v["battle_score"] = pawnScoreMap[uid]
	}

	area.BattleTime = 0
	var field_type int32 = 0
	if area.Owner == "" {
		field_type = this.GetLandType(area.index)
	}
	this.TaTrack(attacker, 0, "ta_attackFieldEnd", map[string]interface{}{
		"isWin":               fighter == attacker,
		"lossMan":             lossMan,
		"battle_costTime":     battleCostTime,
		"atk_reinforce_count": atkReinforceCount,
		"def_reinforce_count": defReinforceCount,
		"land_count":          this.GetPlayerLandCount(attacker, false),
		"field": map[string]interface{}{
			"field_lv":    this.GetLandLv(area.index),
			"field_dis":   this.GetToMapCellDis(this.GetPlayerMainIndex(attacker), area.index),
			"field_type":  field_type,
			"field_index": area.index,
		},
	})
	this.TaTrack(attacker, 0, "ta_pawnBattleInfo2", map[string]interface{}{
		"isWin":           fighter == attacker,
		"field_index":     area.index,
		"battle_costTime": battleCostTime,
		"city_id":         area.CityId,
		"city_lv":         cityLv,
		"roles":           pawnBattleInfos,
	})
}

// 调整战斗过后的士兵位置
func (this *Model) AdjustmentPawnPos(area *Area) {
	list := area.GetArmysClone()
	for _, army := range list {
		if this.CheckArmyMarchingByUID(army.Uid) {
			continue
		}
		pawnList := army.GetPawnsClone()
		for _, pawn := range pawnList {
			point := pawn.Point
			if area.CheckIsBattleArea(point.X, point.Y) && !area.GetBanPlacePawnPointMap()[point.ID()] {
				continue
			}
			dir := helper.GetMinDisIndex(point, area.GetDoorPoints())
			if points := helper.GetPawnPointsByDoor(area.GetIdleBattlePointsToMapBool(""), area.GetAreaSize(), area.GetDoorPoints(), dir, 1); len(points) > 0 {
				pawn.SetPoint(points[0])
			}
		}
	}
}

// 检测军队是否需要自动返回的
func (this *Model) CheckArmyAutoBack(area *Area) {
	list := area.GetArmysClone()
	for _, army := range list {
		if this.CheckArmyMarchingByUID(army.Uid) || army.AutoBackIndex == -1 {
			continue
		}
		var target *Area = nil
		if army.AutoBackIndex == -2 { //返回到最近的
			target = this.GetPlayerNotFullArmyMinFortAndMain(army.Owner, army.AIndex)
		} else if a := this.GetArea(army.AutoBackIndex); a != nil && !a.IsBattle() && this.CheckIsOneAlliance(army.Owner, a.Owner) && !this.IsAreaFullArmyAct(a, army.Owner) {
			target = a
		}
		if target == nil || target.index == area.index {
			continue
		}
		speed := 0.0
		// 是否可以加速 城边5格内加速
		if mainIndex := this.GetPlayerMainIndex(army.Owner); area.index != mainIndex && target.index != mainIndex {
		} else if dis := this.GetToMapCellDis(area.index, target.index); dis <= int32(len(constant.MAIN_CITY_MARCH_SPEED)) {
			speed -= constant.MAIN_CITY_MARCH_SPEED[dis]
		}
		// 添加行军
		army.AutoBackIndex = -1
		this.AddMarchArmy(area.index, army, target.index, speed, false, false, false)
	}
}

// 这个只会在下面使用 因为下面前面加了锁
func (this *Model) ToBattleDist() map[int32][]string {
	msg := map[int32][]string{}
	for index, uids := range this.BattleDist.Map {
		arr := []string{}
		for _, uid := range uids {
			arr = append(arr, uid)
		}
		msg[index] = arr
	}
	return msg
}

// 调用前必须加锁
func (this *Model) ToBattleDistPb() map[int32]*pb.UidsList {
	msg := map[int32]*pb.UidsList{}
	for index, uids := range this.BattleDist.Map {
		arr := &pb.UidsList{}
		for _, uid := range uids {
			arr.List = append(arr.List, uid)
		}
		msg[int32(index)] = arr
	}
	return msg
}

// 添加战斗分布
func (this *Model) AddBattleDist(area *Area) {
	uids := area.GetHasArmyPlayers()
	this.BattleDist.Lock()
	this.BattleDist.Map[area.index] = uids
	this.BattleDist.Unlock()
	// 通知
	this.PutNotifyQueue(constant.NQ_BATTLE_DIST, &pb.OnUpdateWorldInfoNotify{Data_17: &pb.BattleDistInfo{
		Index: int32(area.index),
		Uids:  uids,
	}})
}

// 删除战斗分布
func (this *Model) RemoveBattleDist(index int32) {
	this.BattleDist.Lock()
	delete(this.BattleDist.Map, index)
	this.BattleDist.Unlock()
	// 通知
	this.PutNotifyQueue(constant.NQ_BATTLE_DIST, &pb.OnUpdateWorldInfoNotify{Data_17: &pb.BattleDistInfo{
		Index: int32(index),
		Uids:  nil,
	}})
}

// 获取古城区域信息
func (this *Model) GetAncientCityTempArea(area *Area) *pb.AreaInfo {
	index := area.GetIndex()
	val, ok := this.AncientCityAreaMap.Load(index)
	if ok {
		return val.(*pb.AreaInfo)
	}
	tempAreaInfo := config.GetAncientCityAreaConfigPb(index, area.CityId)
	tempAreaInfo.Builds = area.ToBuildsPb()
	tempAreaInfo.Hp = area.ToHPPb()
	this.AncientCityAreaMap.Store(index, tempAreaInfo)
	return tempAreaInfo
}
