package player

import (
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 刷新可以做的任务
func (this *Model) CheckUpdateCanTodayTask() {
	if len(this.Tasks.List) > 0 {
		return
	}
	// 删除没有的
	finishMap := map[int32]bool{}
	for i := len(this.TodayTasks.Finishs) - 1; i >= 0; i-- {
		id := this.TodayTasks.Finishs[i]
		if json := config.GetJsonData("todayTask", id); json != nil {
			finishMap[id] = true
		} else {
			this.TodayTasks.Finishs = append(this.TodayTasks.Finishs[:i], this.TodayTasks.Finishs[i+1:]...)
		}
	}
	// 删除已经完成的任务且重复的
	ids := map[int32]bool{}
	for i := len(this.TodayTasks.List) - 1; i >= 0; i-- {
		m := this.TodayTasks.List[i]
		prevId := m.GetPrevID()
		if finishMap[m.ID] || ids[m.ID] || (prevId != 0 && !finishMap[prevId]) {
			this.TodayTasks.List = append(this.TodayTasks.List[:i], this.TodayTasks.List[i+1:]...)
		} else {
			ids[m.ID] = true
		}
	}
	// 新增可以做的任务
	datas := config.GetJson("todayTask").Datas
	runDay := this.room.GetRunDay()
	for _, m := range datas {
		id, prev_id := ut.Int32(m["id"]), ut.Int32(m["prev_id"])
		if finishMap[id] || (prev_id != 0 && !finishMap[prev_id]) {
			continue
		} else if !ids[id] {
			newTask := g.NewTaskInfo(m)
			newTask.ServerRunDay = runDay
			this.InitTaskProgress(newTask)
			this.TodayTasks.List = append(this.TodayTasks.List, newTask)
		}
	}
	// fmt.Println(this.TodayTasks.List)
}

// 获取任务
func (this *Model) GetTodayTask(id int32) *g.TaskInfo {
	this.TodayTasks.RLock()
	defer this.TodayTasks.RUnlock()
	if task := array.Find(this.TodayTasks.List, func(m *g.TaskInfo) bool { return m.ID == id }); task != nil {
		return task
	}
	return nil
}

// 是否已经完成了
func (this *Model) InTodayTaskFinish(id int32) bool {
	this.TodayTasks.RLock()
	defer this.TodayTasks.RUnlock()
	return array.Has(this.TodayTasks.Finishs, id)
}

// 完成一个任务
func (this *Model) FinishTodayTask(task *g.TaskInfo) {
	this.TodayTasks.Lock()
	defer this.TodayTasks.Unlock()
	// 先删除任务列表里面的
	this.TodayTasks.List = array.RemoveItem(this.TodayTasks.List, func(m *g.TaskInfo) bool { return m.ID == task.ID })
	// 添加到完成列表
	this.TodayTasks.Finishs = append(this.TodayTasks.Finishs, task.ID)
	// 添加下一个任务
	this.CheckUpdateCanTodayTask()
}
