package behavior

import (
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
)

// 寻找最近目标
type SearchCanAttackTarget struct {
	BaseAction
}

func (this *SearchCanAttackTarget) OnTick(dt int32) Status {
	allTargets := array.Filter(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo, _ int) bool { return this.target.CheckInMyAttackRange(m.Target) })
	if len(allTargets) > 0 {
		if this.target.GetEquipEffectByType(eeffect.SILVER_SNAKE_WHIP) != nil {
			return this.randomTarget(allTargets)
		} else if this.target.IsHasBuff(bufftype.RAGE) {
			if ok, it := this.randomMinDisTargetInAR(allTargets); ok {
				this.target.ChangeAttackTarget(it.Target)
				return SUCCESS
			}
		}
		heroSkillId := this.target.GetPortrayalSkillID()
		isCx, isHz := heroSkillId == hero.CAO_XIU, heroSkillId == hero.HUANG_ZHONG
		sort.Slice(allTargets, func(i, j int) bool {
			a, b := allTargets[i], allTargets[j]
			var aw, bw int32
			if isCx { //曹休 最远目标
				aw, bw = a.Dis, b.Dis
			} else if isHz { //黄忠 生命比最低的目标
				aw = int32((1.0 - a.Target.GetHPRatio()) * 100.0)
				bw = int32((1.0 - b.Target.GetHPRatio()) * 100.0)
			}
			aw = aw*10 + ut.If(a.IsRestrain, int32(1), 0)
			bw = bw*10 + ut.If(b.IsRestrain, int32(1), 0)
			aw = aw*1000 + (999 - a.AttackIndex)
			bw = bw*1000 + (999 - b.AttackIndex)
			return aw > bw
		})
		this.target.ChangeAttackTarget(allTargets[0].Target)
		return SUCCESS
	}
	return FAILURE
}

func (this *SearchCanAttackTarget) randomTarget(allTargets []g.CanAttackTargetInfo) Status {
	it := allTargets[this.GetCtrl().GetRandom().Get(0, len(allTargets)-1)]
	this.target.ChangeAttackTarget(it.Target)
	return SUCCESS
}

// 随机最近的目标
func (this *SearchCanAttackTarget) randomMinDisTargetInAR(allTargets []g.CanAttackTargetInfo) (ok bool, it g.CanAttackTargetInfo) {
	arr := []g.CanAttackTargetInfo{}
	minDis := int32(100000)
	for _, m := range allTargets {
		dis := this.target.GetMinDis(m.Target)
		if dis < minDis {
			minDis = dis
			arr = []g.CanAttackTargetInfo{m}
		} else if dis == minDis {
			arr = append(arr, m)
		}
	}
	if l := len(arr); l > 0 {
		return true, arr[this.GetCtrl().GetRandom().Get(0, l-1)]
	}
	ok = false
	return
}
