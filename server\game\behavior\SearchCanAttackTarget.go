package behavior

import (
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
)

// 寻找最近目标
type SearchCanAttackTarget struct {
	BaseAction
}

func (this *SearchCanAttackTarget) OnTick(dt int32) Status {
	allTargets := array.Filter(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo, _ int) bool { return this.checkInAttackRange(m.Point) })
	if len(allTargets) > 0 {
		heroSkillId := this.target.GetPortrayalSkillID()
		isCx, isHz := heroSkillId == hero.CAO_XIU, heroSkillId == hero.HUANG_ZHONG
		sort.Slice(allTargets, func(i, j int) bool {
			a, b := allTargets[i], allTargets[j]
			var aw, bw int32
			if isCx { //曹休 最远目标
				aw, bw = a.<PERSON><PERSON>, b.<PERSON>s
			} else if isHz { //黄忠 生命比最低的目标
				aw = int32((1.0 - a.Target.GetHPRatio()) * 100.0)
				bw = int32((1.0 - b.Target.GetHPRatio()) * 100.0)
			}
			aw = aw*10 + ut.If(a.IsRestrain, int32(1), 0)
			bw = bw*10 + ut.If(b.IsRestrain, int32(1), 0)
			aw = aw*1000 + (999 - a.AttackIndex)
			bw = bw*1000 + (999 - b.AttackIndex)
			return aw > bw
		})
		this.target.ChangeAttackTarget(allTargets[0].Target)
		return SUCCESS
	}
	return FAILURE
}

func (this *SearchCanAttackTarget) checkInAttackRange(target *ut.Vec2) bool {
	return helper.GetPointToPointDis(this.target.GetPoint(), target) <= this.target.GetEntity().GetAttackRange()
}
