package http

import (
	"context"
	slg "slgsrv/server/common"
	"slgsrv/server/common/sdk"
	ut "slgsrv/utils"
	mgo "slgsrv/utils/mgodb"
	"time"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 删除离线过久的玩家tick
func RunDelOfflineUsersTick() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		lastTime := time.Now().UnixMilli()
		for isRunning {
			<-tiker.C
			// 每天凌晨4点检查物理机磁盘并清理日志
			timeFour := ut.TodayHourTime(4)
			now := time.Now().UnixMilli()
			if lastTime < timeFour && now > timeFour {
				HandleDelOfflineUsers()
			}
			lastTime = now
		}
	}()
}

// 处理Apple账号转移
func RunAppleUserTransferTick() {
	sdk.InitAppleTransfer()
	go func() {
		pageSize := 100 //每页处理的玩家数量
		lastUid := ""
		filter := bson.M{"finish": false, "transfer_sub": bson.M{"$ne": ""}}
		log.Info("RunAppleUserTransferTick start")
		transferCol := mgo.GetCollection(slg.DB_COLLECTION_NAME_APPLE_TRANSFER)
		userCol := mgo.GetCollection(slg.DB_COLLECTION_NAME_USER)
		for isRunning {
			if lastUid != "" {
				filter["uid"] = bson.M{"$gt": lastUid} // 分页依据
			}

			findOptions := options.Find()
			findOptions.SetSort(bson.M{"uid": 1}) // 按照uid升序
			findOptions.SetLimit(int64(pageSize))

			cur, err := transferCol.Find(context.TODO(), filter, findOptions)
			if err != nil {
				log.Error("find user err: %v", err)
				return
			}

			hasMore := false
			for cur.Next(context.TODO()) {
				var info sdk.TransferInfo
				err := cur.Decode(&info)
				if err != nil {
					log.Error("decode transfer info err: %v", err)
					continue
				}

				lastUid = info.Uid
				log.Info("uid: %v, start get newOpenId", info.Uid)
				hasMore = true

				// 获取新的openId
				openId, e := sdk.GetAppleNewOpenId(info.TransferSub)
				if e != "" || openId == "" {
					log.Info("GetAppleNewOpenId err: %v, uid: %v", e, info.Uid)
					continue
				}

				// 更新到数据库
				transferCol.UpdateOne(context.TODO(), bson.M{"uid": info.Uid}, bson.M{"$set": bson.M{"finish": true}})
				userCol.UpdateOne(context.TODO(), bson.M{"uid": info.Uid}, bson.M{"$set": bson.M{"apple_openid": openId}})
				log.Info("get newOpenId finish uid: %v, newOpenId: %v", info.Uid, openId)
			}

			if !hasMore {
				// 遍历完毕
				log.Info("RunAppleUserTransferTick finish")
				break
			}
		}
	}()
}

// 删除离线过久的玩家
func HandleDelOfflineUsers() {
	log.Info("HandleDelOfflineUsers start")
	now := time.Now().UnixMilli()
	delCount := 0
	// 遍历用户表 查询90天未登录的玩家
	var lastId primitive.ObjectID
	lastLoginTime := now - slg.DEL_USER_NOT_LOGIN_DAYS
	var err error
	var rstList []map[string]interface{}
	// 每次获取100个 最多查询10000次
	for i := 0; i < 10000; i++ {
		rstList, err = findOfflineUser(lastId, lastLoginTime, 100)
		if err != nil || len(rstList) == 0 {
			break
		}
		models := []mongo.WriteModel{}
		for i, v := range rstList {
			if accGameCounts := ut.IntArray(v["acc_total_game_counts"]); len(accGameCounts) > 1 {
				if accGameCounts[1] > 1 {
					// 游戏场次大于1不删除
					continue
				}
			}
			id, ok := v["_id"].(primitive.ObjectID)
			if !ok {
				continue
			}
			models = append(models, mongo.NewDeleteOneModel().SetFilter(bson.M{"_id": id}))
			delCount++
			if i == len(rstList)-1 {
				lastId = id
			}
		}
		bulkOption := options.BulkWrite().SetOrdered(false)
		mgo.GetCollection(slg.DB_COLLECTION_NAME_USER).BulkWrite(context.TODO(), models, bulkOption)
	}
	log.Info("HandleDelOfflineUsers finish user count: %v, costTime: %vms", delCount, time.Now().UnixMilli()-now)
}

// 查询长时间未登录的用户
func findOfflineUser(lastId primitive.ObjectID, lastLoginTime int64, size int) (rst []map[string]interface{}, err error) {
	filterAndList := []bson.M{
		{
			// 上次登录小于指定时间
			"$or": []bson.M{
				{"last_login_time": bson.M{"$lt": lastLoginTime}},
				{"last_login_time": bson.M{"$exists": false}},
			},
		},
		{
			// 未充值过
			"$or": []bson.M{
				{"recharge_count": bson.M{"$lte": 0}},
				{"recharge_count": bson.M{"$exists": false}},
			},
		},
		{
			// 对局次数小于等于1
			"$or": []bson.M{
				{"acc_total_game_count": bson.M{"$lte": 1}},
				{"acc_total_game_count": bson.M{"$exists": false}},
			},
		},
	}
	if !lastId.IsZero() {
		filterAndList = append(filterAndList, bson.M{"_id": bson.M{"$lt": lastId}})
	}
	filter := bson.M{
		"$and": filterAndList,
	}
	limit := int64(size)
	cursor, err := mgo.GetCollection(slg.DB_COLLECTION_NAME_USER).Find(context.TODO(), filter, &options.FindOptions{
		Projection: bson.M{
			"_id":                   1,
			"uid":                   1,
			"acc_total_game_counts": 1,
		},
		Sort: bson.M{
			"_id": -1,
		},
		Limit: &limit,
	})
	if err != nil {
		return nil, err
	}

	err = cursor.All(context.TODO(), &rst)
	cursor.Close(context.TODO())
	return
}
