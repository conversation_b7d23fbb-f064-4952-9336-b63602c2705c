package lobby

import (
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"google.golang.org/protobuf/reflect/protoreflect"
)

func (this *Lobby) RpcUserRegister(id string, f any) {
	this.GetServer().RegisterGO(id, f)
	this.RpcUserFuncMap[id] = f
}

func (this *Lobby) InitUserRpc() {
	this.RpcUserFuncMap = map[string]any{}
	// 用户信息相关
	this.RpcUserRegister(slg.RPC_GET_APPLY_USER_BY_CREATEPLAYER, this.getApplyUserByCreatePlayer)
	this.RpcUserRegister(slg.RPC_GET_USER_INFO, this.getUserInfo)
	this.RpcUserRegister(slg.RPC_SET_TRESURE_LOST_COUNT, this.setTreasureLostCount)
	this.RpcUserRegister(slg.RPC_USER_ANTI_CHEAT_CHECK, this.rpcUserAntiCheatCheck)
	this.RpcUserRegister(slg.RPC_USER_BAN_ACCCOUNT_CHECK, this.rpcBanAccountCheck)

	// 人气相关
	this.RpcUserRegister(slg.RPC_GET_USER_POPULARITY, this.getUserPopularityRpc)
	this.RpcUserRegister(slg.RPC_CHANGE_USER_POPULARITY, this.changeUserPopularityRpc)

	// 好友相关
	this.RpcUserRegister(slg.RPC_USER_APPLY_FRIEND_BY_OTHER, this.userApplyFriendByOther)
	this.RpcUserRegister(slg.RPC_USER_APPLY_RESPONSE_BY_OTHER, this.userApplyResponseByOther)
	this.RpcUserRegister(slg.RPC_USER_DEL_FRIEND_BY_OTHER, this.userDelFriendByOther)
	this.RpcUserRegister(slg.RPC_USER_RECEIVE_FRIEND_MSG, this.rpcUserReceiveFriendMsg)
	this.RpcUserRegister(slg.RPC_GET_USER_BASE_INFO, this.rpcGetUserBaseInfo)
	this.RpcUserRegister(slg.RPC_ADD_INVITE_FRIEND, this.rpcAddInviteFriend)
	this.RpcUserRegister(slg.RPC_USER_UPDATE_FRIEND_INFO, this.rpcUpdateFriendInfo)
	this.RpcUserRegister(slg.RPC_FRIEND_SEND_GIFT, this.rpcFriendSendGift)
	this.RpcUserRegister(slg.RPC_SKIN_ITEM_TRACK, this.rpcSkinItemTrack)

	// 组队相关
	this.RpcUserRegister(slg.RPC_ADD_TEAM_INVITE_INFO, this.rpcAddTeamInviteInfo)
	this.RpcUserRegister(slg.RPC_USER_KICK_BY_TEAM, this.rpcUserKickByTeam)
	this.RpcUserRegister(slg.RPC_SET_USER_TEAMID, this.rpcSetUserTeamId)
	this.RpcUserRegister(slg.RPC_CHECK_USER_TEAM, this.rpcCheckUserTeam)
	this.RpcUserRegister(slg.RPC_MATCH_CHECK_APPLY_USER, this.rpcCheckMatchApplyUser)
	this.RpcUserRegister(slg.RPC_SET_USER_TEAM_FORCE, this.rpcSetUserTeamForce)

	// 结算相关
	this.RpcUserRegister(slg.RPC_USER_GAMEOVER_SETTLE, this.rpcUserGameOverSettle)

	// 支付相关
	this.RpcUserRegister(slg.RPC_DEL_REPEAT_USE_SUB, this.rpcDelRepeatUseSub)
	this.RpcUserRegister(slg.RPC_USER_REFUND_REDUCE_ITEM, this.rpcRefundReduceItem)

	// 活动相关
	this.RpcUserRegister(slg.RPC_GET_DC_FOLLOW_GIFT, this.rpcGetDcFollowGift)

	// 通知
	this.RpcUserRegister(slg.RPC_SEND_NOTIFY, this.rpcSendNotifyNotify)
}

// 处理user功能
func (this *Lobby) InvokeUserFunc(uid string, _func string, params ...any) (result any, err string) {
	lid := rds.GetUserLid(uid)
	if lid == "" {
		log.Error("InvokeUserFunc lobby full uid: %v, _func: %v, params: %v", uid, _func, params)
		return nil, ecode.CUR_LOBBY_FULL.String()
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcUserFuncMap[_func]
		if function == nil {
			log.Error("InvokeUserFunc not found %v", _func)
			return nil, "not found " + _func
		}
		result, err = RunLocalRpcFunc(function, params...)
	} else {
		result, err = this.InvokeLobbyRpc(lid, _func, params...)
	}
	return
}

// 处理user功能 不需要回复
func (this *Lobby) InvokeUserFuncNR(uid string, _func string, params ...any) {
	lid := rds.GetUserLid(uid)
	if lid == "" {
		log.Error("InvokeUserFuncNR lobby full uid: %v, _func: %v, params: %v", uid, _func, params)
		return
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcUserFuncMap[_func]
		if function == nil {
			log.Error("InvokeUserFuncNR not found %v", _func)
			return
		}
		RunLocalRpcFunc(function, params...)
	} else {
		this.InvokeLobbyRpcNR(lid, _func, params...)
	}
}

// 处理user功能 只处理在线玩家
func (this *Lobby) InvokeOnlineUserFunc(uid string, _func string, params ...any) (result any, err string) {
	lid, e := rds.RdsHGet(rds.RDS_USER_LID_MAP_KEY, uid)
	if e != nil || lid == "" || lid == "0" {
		return nil, ecode.NOT_BIND_UID.String()
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcUserFuncMap[_func]
		if function == nil {
			log.Error("InvokeUserFunc not found %v", _func)
			return nil, "not found " + _func
		}
		result, err = RunLocalRpcFunc(function, params...)
	} else {
		result, err = this.InvokeLobbyRpc(lid, _func, params...)
	}
	return
}

// 处理user功能 只处理在线玩家 不需要回复
func (this *Lobby) InvokeOnlineUserFuncNR(uid string, _func string, params ...any) {
	lid, e := rds.RdsHGet(rds.RDS_USER_LID_MAP_KEY, uid)
	if e != nil || lid == "" || lid == "0" {
		return
	}
	params = append([]any{uid}, params...)
	if lid == this.GetLid() {
		function := this.RpcUserFuncMap[_func]
		if function == nil {
			log.Error("InvokeUserFuncNR not found %v", _func)
			return
		}
		RunLocalRpcFunc(function, params...)
	} else {
		this.InvokeLobbyRpcNR(lid, _func, params...)
	}
}

// 获取人气信息
func (this *Lobby) getUserPopularityRpc(uid string) (result []byte, err string) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	s2c := &pb.LOBBY_HD_GETUSERPOPULARITY_S2C{}
	s2c.List, s2c.Records = user.ToPopularityPb(true)
	return pb.ProtoMarshal(s2c)
}

// 改变用户人气
func (this *Lobby) changeUserPopularityRpc(uid, otherUid, otherNickName string, id int32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	s2c := &pb.LOBBY_HD_CHANGEUSERPOPULARITY_S2C{}
	s2c.List, err = user.AddPopularity(otherUid, otherNickName, id)
	if err != "" {
		return
	}
	return pb.ProtoMarshal(s2c)
}

// 处理好友申请
func (this *Lobby) userApplyFriendByOther(uid, otherUid, otherNickName, headIcon string, playSid int32, applied bool) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	err = user.ApplyFriendByOhter(otherUid, otherNickName, headIcon, playSid, applied)
	result = map[string]any{
		"nickName":        user.Nickname,
		"headIcon":        user.HeadIcon,
		"playSid":         user.GetPlaySid(),
		"lastOfflineTime": user.GetOnlineState(),
	}
	return
}

// 处理好友申请回复
func (this *Lobby) userApplyResponseByOther(uid, otherUid, otherNickName, headIcon string, playSid int32, agree bool) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	err = user.ApplyResponseByOhter(otherUid, otherNickName, headIcon, playSid, agree)
	result = map[string]any{
		"nickname":        user.Nickname,
		"headIcon":        user.HeadIcon,
		"playSid":         user.GetPlaySid(),
		"lastOfflineTime": user.GetOnlineState(),
	}
	return
}

// 处理好友删除
func (this *Lobby) userDelFriendByOther(uid string, friendUid string) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	user.DelFriend(friendUid)
	return
}

// 处理收到好友聊天
func (this *Lobby) rpcUserReceiveFriendMsg(uid, nickName string, chatNotifyBytes []byte) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	chatNotify := &pb.LOBBY_ONFRIENDCHAT_NOTIFY{}
	e := pb.ProtoUnMarshal(chatNotifyBytes, chatNotify)
	if e != nil {
		err = e.Error()
		return
	}
	friendInfo := user.GetFriend(chatNotify.GetUid())
	if friendInfo == nil {
		return nil, ecode.NOT_FRIENDS.String()
	}
	friendInfo.NotReadCount++
	if chatNotify.ChatInfo != nil {
		// 记录最后一条聊天记录
		friendInfo.LastChatInfo = &FriendChatInfo{
			UID:     chatNotify.GetUid(),
			Sender:  chatNotify.ChatInfo.GetSender(),
			Content: chatNotify.ChatInfo.GetContent(),
			Time:    chatNotify.ChatInfo.GetTime(),
			Emoji:   chatNotify.ChatInfo.GetEmoji(),
		}
	}
	if user.FlagUpdateDB() {
		user.NotifyUser(slg.LOBBY_FRIEND_CHAT, chatNotify)
	} else {
		// 离线通知
		user.OfflineNotify(constant.OFFLINE_MSG_TYPE_NEW_MSG, nickName, chatNotify.ChatInfo.Content, ut.String(chatNotify.ChatInfo.Emoji))
	}
	return
}

// 获取用户基本信息
func (this *Lobby) rpcGetUserBaseInfo(uid string) (ret map[string]any, err string) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	return map[string]any{
		"nickname": user.Nickname,
		"headIcon": user.HeadIcon,
	}, ""
}

// 添加邀请玩家
func (this *Lobby) rpcAddInviteFriend(uid string, inviteUid string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	user.AddInviteFriends(inviteUid)
	return
}

// 好友信息更新
func (this *Lobby) rpcUpdateFriendInfo(uid string, friendUid, nickname, headIcon string, sid, playSid int32, onlineState int64) (result []byte, err string) {
	user := GetUser(uid)
	if user == nil {
		// 不在线则不更新
		err = ecode.NOT_BIND_UID.String()
		return
	}
	friend := user.GetFriend(friendUid)
	if friend == nil { // 没有对方好友则删除好友
		user.DelFriend(friendUid)
		err = ecode.NOT_FRIENDS.String()
		return
	}
	friend.Nickname = nickname
	friend.HeadIcon = headIcon
	friend.PlaySid = playSid
	friend.LastOfflineTime = onlineState
	if user.FlagUpdateDB() {
		// 在线则通知
		user.NotifyUser(slg.LOBBY_FRIEND_UPDATE, friend.ToUpdateNotifyPb(time.Now().UnixMilli()))
	}
	ret := &pb.FriendInfo{
		Uid:         user.UID,
		Nickname:    user.Nickname,
		HeadIcon:    user.HeadIcon,
		PlaySid:     user.GetPlaySid(),
		OfflineTime: user.GetOnlineState(),
	}
	return pb.ProtoMarshal(ret)
}

// 获取用户信息 创建角色的时候
func (this *Lobby) getApplyUserByCreatePlayer(uid string, sid int32) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	user.SetPlaySid(sid)
	user.SID = 0
	user.FlagUpdateDB()
	user.FriendInfoNotify()
	result = map[string]any{
		"user": GetCreatePlayerInfo(user),
	}
	return
}

// 好友赠送礼物
func (this *Lobby) rpcFriendSendGift(userUid, friendUid, uid string, giftId, boxId, giftType int32) (result []byte, err string) {
	user := this.GetUserByDB(userUid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	// 检测是否是好友
	user.FriendsLock.RLock()
	friendInfo := array.Find(user.FriendsList, func(v *Friend) bool { return v.Uid == friendUid })
	user.FriendsLock.RUnlock()
	if friendInfo == nil {
		// 好友不存在
		return nil, ecode.NOT_FRIENDS.String()
	}
	if friendInfo.FriendGifts == nil {
		friendInfo.FriendGifts = &FriendGiftList{
			List: []*FriendGift{},
		}
	}
	// 添加礼物
	gift := &FriendGift{
		UID:      uid,
		Id:       giftId,
		BoxId:    boxId,
		GiftType: giftType,
		Time:     time.Now().UnixMilli(),
	}
	friendInfo.FriendGifts.Lock()
	friendInfo.FriendGifts.List = append(friendInfo.FriendGifts.List, gift)
	friendInfo.FriendGifts.Unlock()
	user.FlagUpdateDB()
	// 添加赠送记录
	record := &FriendGiftRecord{
		UID:      gift.UID,
		Id:       giftId,
		BoxId:    boxId,
		GiftType: giftType,
		Time:     gift.Time,
		Sender:   friendUid,
		Receiver: userUid,
		ItemUid:  uid,
	}
	friendGiftRecordDb.InsertFriendGiftRecord(record)
	// 通知
	user.NotifyUser(slg.LOBBY_FRIEND_SEND_GIFT, &pb.LOBBY_ONFRIENDSENDGIFT_NOTIFY{
		Uid:       gift.UID,
		Id:        giftId,
		GiftType:  giftType,
		BoxId:     boxId,
		Time:      gift.Time,
		FriendUid: friendUid,
	})
	return
}

// 封禁用户皮肤礼物处理
func (this *Lobby) rpcSkinItemTrack(userId, banUserId string, uidMap map[string]bool, isBan bool, nickname string) (result []byte, err string) {
	user := this.GetUserByDB(userId)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	user.HandleBanSkinItem(banUserId, uidMap, isBan, nickname)
	return
}

// ----------------------------- 组队 ----------------------------------

// 玩家添加组队邀请信息
func (this *Lobby) rpcAddTeamInviteInfo(uid, teamUid, otherUid, nickname, headicon string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if err = user.UserJoinTeamCheck(); err != "" {
		return
	}
	user.TeamInviteListLock.Lock()
	inviteInfo := array.Find(user.TeamInviteList, func(m *TeamInviteInfo) bool { return m.Uid == otherUid })
	if inviteInfo == nil {
		inviteInfo = NewTeamInviteInfo(teamUid, otherUid, nickname, headicon)
		user.TeamInviteList = append(user.TeamInviteList, inviteInfo)
	} else {
		inviteInfo.Uid = teamUid // 该玩家已邀请过 更新队伍uid
	}
	user.TeamInviteListLock.Unlock()
	if user.FlagUpdateDB() {
		user.NotifyUser(slg.LOBBY_TEAM_INVITE, &pb.LOBBY_ONTEAMINVITE_NOTIFY{InviteInfo: inviteInfo.ToPb()})
	}
	user.OfflineNotify(constant.OFFLINE_MSG_TYPE_TEAM_INVITE, nickname)
	return
}

// 玩家被踢出队伍
func (this *Lobby) rpcUserKickByTeam(uid, teamUid string, tp int) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if user.TeamUid == teamUid {
		user.KickByTeam(tp)
	}
	return
}

// 强制设置玩家队伍
func (this *Lobby) rpcSetUserTeamForce(uid, teamUid string) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if user.TeamUid != teamUid {
		this.InvokeTeamFunc(user.TeamUid, slg.RPC_USER_LEAVE_TEAM_FORCE, user.UID)
	}
	user.SetTeamUid(teamUid)
	if user.GetPlaySid() != 0 {
		user.SetPlaySid(0)
	}
	user.FlagUpdateDB()
	result = map[string]any{
		"nickname": user.Nickname,
		"headIcon": user.HeadIcon,
	}
	return
}

// 设置用户的队伍id
func (this *Lobby) rpcSetUserTeamId(uid, teamUid string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	user.SetTeamUid(teamUid)
	user.FlagUpdateDB()
	return
}

// 检测用户的队伍
func (this *Lobby) rpcCheckUserTeam(uid, teamUid string) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if uid != teamUid && user.TeamUid != teamUid {
		log.Info("rpcCheckUserTeam team diff teamUid: %v, user.TeamUid: %v", teamUid, user.TeamUid)
		user.KickByTeam(0)
		return nil, ecode.UNKNOWN.String()
	}
	var gameTotal int32
	if len(user.AccTotalGameCounts) > 1 {
		gameTotal = user.AccTotalGameCounts[1]
	}
	result = map[string]any{
		"rankScore":  user.RankScore,
		"gameTotal":  gameTotal,
		"createTime": user.CreateTime,
	}
	return
}

// 检测报名中的玩家
func (this *Lobby) rpcCheckMatchApplyUser(uid, teamUid string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if user.TeamUid != teamUid {
		log.Info("rpcCheckMatchApplyUser team diff teamUid: %v, user.TeamUid: %v", teamUid, user.TeamUid)
		user.KickByTeam(0)
		return nil, ecode.UNKNOWN.String()
	} else if playSid := user.GetPlaySid(); playSid != 0 {
		room := GetRoomById(playSid)
		if room != nil && !IsGameOver(room) {
			return nil, ecode.IN_GAME.String()
		}
	}
	return
}

// 队伍信息通知
func (this *Lobby) rpcSendNotifyNotify(uid, topic string, data []byte) (result []byte, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return
	}
	if user.IsOnline() {
		user.Session.SendNR(topic, data)
	}
	return
}

// 处理玩家对局结算
func (this *Lobby) rpcUserGameOverSettle(uid string, sid, tp, gameScore, landScore int32, isWin bool, rank, S int32, MS float64, runTime float64) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	addScore, addWarToken, giveUp, gameCount, isPassNewbie, alliScore, newbieRepeat := user.GameOverSettle(sid, tp, gameScore, landScore, isWin, rank, S, MS, runTime)
	if user.SID == 0 {
		log.Info("rpcUserGameOverSettle, uid: %v, sid: %v, playSid: %v", uid, sid, user.GetPlaySid())
		// 结算时还在大厅 则playsid置0
		user.SetPlaySid(0)
	}
	user.FlagUpdateDB()
	if !user.IsOnline() {
		// 离线通知
		user.OfflineNotify(constant.OFFLINE_MSG_TYPE_GAME_OVER, ut.String(sid))
	} else if user.SID == 0 {
		user.NotifyUser(slg.LOBBY_USER_GAME_OVER, &pb.LOBBY_ONUSERGAMEOVER_NOTIFY{})
	}
	result = map[string]any{
		"giveUp":       giveUp,
		"addScore":     addScore,
		"rankScore":    user.RankScore,
		"addWarToken":  addWarToken,
		"newbieRepeat": newbieRepeat,
		"teamUid":      user.TeamUid,
	}
	// 上报
	ta.Track(sid, uid, user.DistinctId, 0, "ta_gameOverSettle", map[string]any{
		"is_win":               isWin,
		"add_war_token":        addWarToken,
		"last_offline_time":    ut.If(user.IsOnline(), 0, user.LastOfflineTime),
		"total_game_count":     user.GetTotalGameCount(),
		"cur_total_game_count": gameCount,
		"alli_score":           alliScore,
		"land_score":           landScore,
		"add_score":            addScore,
		"is_pass_newbie":       isPassNewbie,
	})
	return
}

// 处理重复使用删除订阅
func (this *Lobby) rpcDelRepeatUseSub(uid, orderUid string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	subInfo := user.DelUserSubInfo(orderUid, this)
	if subInfo != nil {
		result, err = pb.ProtoMarshal(subInfo.ToPb())
	}
	return
}

// 退款扣除对应物品
func (this *Lobby) rpcRefundReduceItem(uid, orderUid string, itemBytes []byte) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	pbItems := &pb.TypeObjList{}
	pb.ProtoUnMarshal(itemBytes, pbItems)
	if len(pbItems.List) > 0 {
		for _, v := range pbItems.List {
			switch v.Type {
			case ctype.INGOT:
				this.ReduceIngot(user, v.Count, constant.GOLD_CHANGE_REFUND)
			case ctype.GOLD:
				this.ReduceGold(user, v.Count, constant.GOLD_CHANGE_REFUND)
			case ctype.FREE_AD:
				user.FreeAdCount -= v.Count
			}
		}
	}
	return
}

// DC关注礼物
func (this *Lobby) rpcGetDcFollowGift(uid string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	if user.ActivityRecord.Get(ctype.ACTIVITY_DC_FOLLOW) != 0 {
		// 已领取过
		err = ecode.YET_CLAIM.String()
		return
	}
	// 邮件奖励
	items := []*g.TypeObj{}
	for k, v := range slg.DC_FOLLOW_GIFT {
		items = append(items, &g.TypeObj{Type: k, Count: v})
	}
	_, err = this.InvokeMailRpc(slg.RPC_SEND_MAIL_ITEM_ONE, 0, slg.MAIL_GAME_DC_GIFT_ID, "", "", "-1", user.UID, items)
	if err == "" {
		user.ActivityRecord.Set(ctype.ACTIVITY_DC_FOLLOW, 1)
		if user.FlagUpdateDB() {
			notifyData := map[int32]int32{}
			user.ActivityRecord.ForEach(func(v, k int32) bool {
				notifyData[k] = v
				return true
			})
			user.PutNotifyQueue(constant.NQ_ACTIVITY_RECORD, &pb.OnUpdatePlayerInfoNotify{Data_77: notifyData})

		}
	} else {
		log.Error("rpcGetDcFollowGift err: %v, uid: %v", err, uid)
	}

	return
}

// 设置宝箱遗失补偿次数
func (this *Lobby) setTreasureLostCount(uid string, count int32) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	user.TreasureLostCount = count
	user.FlagUpdateDB()
	return
}

// 处理防作弊检测
func (this *Lobby) rpcUserAntiCheatCheck(uid string, rst bool) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.ChangeUserAntiCheatData(user, rst, 1)
	return
}

// 检测玩家是否被封禁
func (this *Lobby) rpcBanAccountCheck(uid string) (result map[string]any, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	rst := false
	if user.BanAccountEndTime > time.Now().UnixMilli() {
		rst = true
	}
	result = map[string]any{
		"rst": rst,
	}
	return
}

// 玩家通知
func (this *Lobby) SendUserNotify(uid, topic string, m protoreflect.ProtoMessage) {
	data, err := pb.ProtoMarshal(m)
	if err != "" {
		log.Error("RcpTeamNotify m: %v, err: %v", m, err)
		return
	}
	this.InvokeUserFuncNR(uid, slg.RPC_SEND_NOTIFY, topic, data)
}
