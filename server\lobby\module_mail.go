package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"strings"
	"time"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) InitHDMail() {
	this.GetServer().RegisterGO("HD_ExchangeGift", this.exchangeGift) //兑换礼包
}

// 兑换礼包
func (this *Lobby) exchangeGift(session gate.Session, msg *pb.LOBBY_HD_EXCHANGEGIFT_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		log.Error("not bind uid")
		return nil, ecode.NOT_BIND_UID.String()
	}
	code := msg.GetCode()
	if code == "" {
		return nil, ecode.PLEASE_INPUT_EXCHANGE_CODE.String()
	}
	// 获取兑换码礼包
	gift, e := giftDb.FindCodeGift(code)
	if e != "" {
		return nil, ecode.GIFT_NOT_EXIST.String()
	}
	sid := gift.SID
	if sid > 0 {
		if sid != user.SID {
			return nil, ecode.GIFT_NOT_EXIST.String() //区服不一致
		}
		room := GetRoomById(sid)
		if room == nil {
			return nil, ecode.ROOM_NOT_EXIST.String() //区服不存在
		} else if room.WinType > 0 {
			return nil, ecode.ROOM_OVER.String() //对局已结束
		}
	}
	if gift.Receiver != "" && !strings.Contains(gift.Receiver, user.UID) {
		return nil, ecode.GIFT_NOT_EXIST.String()
	} else if strings.Contains(gift.NoReceiver, user.UID) {
		return nil, ecode.GIFT_NOT_EXIST.String()
	} else if gift.Endtime != 0 && time.Now().UnixMilli() > gift.Endtime { //兑换码已过期
		return nil, ecode.GIFT_NOT_EXIST.String()
	}
	// 查询并更新领取状态
	hasClaim, err := giftClaimDb.UpdateGiftClaim(code, user.UID, gift.UID)
	if err != "" {
		return nil, ecode.EXCHANGE_FAIL.String() //数据库错误
	} else if hasClaim && !gift.IsForeverClaim {
		return nil, ecode.YET_CLAIM.String() //已领取
	}
	// 发送邮件
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, 0, 100004, "", code, "-1", user.UID, gift.Items)
	return
}
