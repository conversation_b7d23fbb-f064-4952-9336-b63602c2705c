package constant

import (
	"slgsrv/server/game/common/enums/ctype"
	ut "slgsrv/utils"
)

// 游戏结束后多少时间关闭
const CLOSE_GAME_TIME = ut.TIME_DAY * 3

// 多久保存一次玩家数据
const PLAYER_UPDATE_DB_INTERVAL = ut.TIME_MINUTE * 5

const (
	TILE_SIZE      = 80 // 一格的大小
	TILE_SIZE_HALF = 80 / 2

	MAIN_CITY_ID    = 1001 // 城市 主城id
	FORT_CITY_ID    = 2102 // 城市 要塞id
	CHANGAN_CITY_ID = 3001 // 城市 长安
	JINLING_CITY_ID = 3002 // 城市 金陵
	YANJING_CITY_ID = 3003 // 城市 燕京
	LUOYANG_CITY_ID = 3004 // 城市 洛阳

	WALL_BUILD_ID      = 2000 // 建筑 城墙id
	MAIN_BUILD_ID      = 2001 // 建筑 中心大楼id
	GRANARY_BUILD_ID   = 2002 // 建筑 粮仓id
	WAREHOUSE_BUILD_ID = 2003 // 建筑 仓库id
	BARRACKS_BUILD_ID  = 2004 // 建筑 兵营id
	ALLI_BUILD_ID      = 2005 // 建筑 联盟建筑id
	BAZAAR_BUILD_ID    = 2006 // 建筑 市场建筑id
	CERI_BUILD_ID      = 2007 // 建筑 研究所建筑id
	SMITHY_BUILD_ID    = 2008 // 建筑 铁匠铺建筑id
	STABLE_BUILD_ID    = 2009 // 建筑 马厩建筑id
	DRGR_BUILD_ID      = 2011 // 建筑 校场建筑id
	PAVILION_BUILD_ID  = 2012 // 建筑 里亭属id
	FRONTIER_BUILD_ID  = 2013 // 建筑 边塞营id
	MARKET_BUILD_ID    = 2014 // 建筑 联盟市场id
	HEROHALL_BUILD_ID  = 2015 // 建筑 英雄殿
	HOSPITAL_BUILD_ID  = 2016 // 建筑 医馆
	FORT_BUILD_ID      = 2102 // 建筑 要塞id
	TOWER_BUILD_ID     = 2103 // 建筑 箭塔id

	FARM_BUILD_ID   = 2201 // 建筑 农场
	TIMBER_BUILD_ID = 2202 // 建筑 伐木场
	QUARRY_BUILD_ID = 2203 // 建筑 采石场

	PAWN_CROSSBOW_ID = 3305 // 强弩兵ID
	AX_CAVALRY_ID    = 3406 // 斧骑兵ID

	INIT_RES_OUTPUT             = 120  // 初始的资源产量
	INIT_RES_CAP                = 1000 // 初始的资源容量
	INIT_RES_COUNT              = 700  // 初始的资源数量
	INIT_GOLD                   = 0    // 初始的金币数
	DEFAULT_BT_QUEUE_COUNT      = 2    // 默认修建队列
	DEFAULT_PAWN_ID             = 3101 // 默认创建的兵种id
	ARMY_PAWN_MAX_COUNT         = 9    // 军队士兵最大数量
	BUILD_DRILL_PAWN_MAX_COUNT  = 6    // 一个建筑训练士兵最大数量
	BUILD_PAWN_LVING_MAX_COUNT  = 6    // 一个建筑士兵练级最大数量
	BUILD_PAWN_CURING_MAX_COUNT = 6    // 一个建筑士兵治疗最大数量

	IN_DONE_BT_GOLD      = 30  // 立即完成修建需要的金币数
	IN_DONE_FORGE_GOLD   = 30  // 立即完成打造需要的金币数
	MODIFY_NICKNAME_GOLD = 500 // 修改昵称需要的金币数

	DEFAULT_MAX_ARMY_COUNT     = 5  // 区域中默认最大军队数量
	DEFAULT_AREA_MAX_HP        = 40 // 区域默认最大血量
	DEFAULT_MAX_ADD_PAWN_TIMES = 20 // 默认最大补兵次数

	CREATE_ALLI_MAX_LV   = 3                      // 大使馆多少级可以创建联盟
	ALLI_APPLY_MAX_COUNT = 3                      // 申请联盟个数限制
	ALLI_INIT_PERS       = CREATE_ALLI_MAX_LV * 2 // 联盟初始人数
	ALLI_MAX_PERS        = 40                     // 联盟最大人数
	ALLI_VOTE_MAX        = 4                      // 盟主投票次数上限
	ALLI_CONFIRN_TIME    = ut.TIME_DAY            // 盟主确认时间

	TRADINGRES_MAX_COUNT       = 200   // 交易中心最大交易条数
	TRANSIT_TIME               = 300.0 // 运送时间 格/小时
	REPLACEMENT_SERVICE_CHARGE = 60    // 交易中心出售给系统手续费比例
	REPLACEMENT_MIN_RES_COUNT  = 100   // 和大自然交换最少资源

	MARC_AUTO_REVOKE_SPEED  = 2 // 遣返行军提升速度
	MARC_FORCE_REVOKE_SPEED = 3 // 强制撤回行军提升速度

	MAIN_AVOID_WAR_TIME    = 5 * ut.TIME_DAY   // 主城的免战时间
	OCCUPY_AVOID_WAR_TIME  = 7 * ut.TIME_HOUR  // 占领后的免战时间
	OCCUPY_PLAYER_INTERVAL = ut.TIME_HOUR * 12 // 连续占领玩家间隔
	LATE_GAME_TIME         = ut.TIME_DAY * 9   // 游戏后期时间(距开服)

	BATTLE_MAX_TIME = ut.TIME_HOUR * 3 // 一场战斗最多持续时间

	ADD_OUTPUT_GOLD  = 50              // 商城购买添加产量需要的金币
	ADD_OUTPUT_RATIO = 1.2             // 商城购买添加产量比例
	ADD_OUTPUT_TIME  = 1 * ut.TIME_DAY // 商城购买添加产量持续时间

	RESET_POLICY_ADD_GOLD  = 2  // 政策每重置一次累计的值
	RESET_CERI_SLOT_GOLD   = 50 // 研究槽位每重置一次的值
	RESET_POLICY_SLOT_GOLD = 50 // 政策槽位每重置一次的值
	RESET_EQUIP_SLOT_GOLD  = 50 // 装备槽位每重置一次的值
	RESET_PAWN_SLOT_GOLD   = 50 // 士兵槽位每重置一次的值

	CAN_EXIT_ALLI_TIME = ut.TIME_HOUR * 12 // 多长时间可以退出联盟

	CREATE_ALLI_COST = "1,0,3000|2,0,2000|3,0,2000" // 创建联盟费用
	CREATE_ALLI_COND = 100                          // 创建联盟需要的领地数

	ONE_USER_POPULARITY_CHANGE_INTERVAL = ut.TIME_DAY * 30 // 单个玩家给其他玩家改变人气间隔

	UP_RECRUIT_PAWN_MAX_COUNT = 9 // 加速招募士兵数量
	FREE_TIME_MIN             = 3 // 免费招募/训练等操作 最小时间 单位秒

	LAND_DATA_BIT_COUNT        = 6  // 地图数据使用6位
	PLAYER_CELL_DATA_BIT_COUNT = 19 // 玩家地块数据使用19位
	PLAYER_CELL_CITY_BIT_COUNT = 8  // 玩家城市ID数据使用8位

	ALLI_EDIT_NOTICE_INTERVAL = ut.TIME_MINUTE // 修改联盟公告间隔
	ALLI_LOG_MAX_COUNT        = 50             // 获取联盟日志数量上限
	ALLI_CHAT_CHANNEL_MAX     = 5              // 联盟副频道数量上限

	MAIN_CITY_PERS_MIN_COUNT = 20 // 每个区域玩家数量小于多少就算满

	NOT_OCCUPY_BY_SERVER_RUNTIME = ut.TIME_DAY * 3 // 开服多久内不可攻占
	NOT_OCCUPY_BY_MAX_LAND_COUNT = 100

	VALID_BATTLE_PAWNS_MIN = 5 // 有效战斗最少士兵数

	OFFLINE_ATTACK_NOTIFY_CD_BASE = 5  // 被攻击离线通知基础CD 单位分钟
	OFFLINE_ATTACK_NOTIFY_CD_MUT  = 3  // 被攻击离线通知CD每次倍数
	OFFLINE_ATTACK_NOTIFY_CD_MAX  = 45 // 被攻击离线通知CD最大值 单位分钟

	GAME_EXTRA_SCORE = 600 // 奖励积分

	CANCEL_ARMY_AREA_CHECK_DIS = 10 // 军队遣返周边空地检测距离

	TRADE_PRICE_UP_MAX            = 1.2               // 商品价格上涨倍数上限
	TRADE_RECORD_MAX              = 200               // 同一商品最大记录数量
	TRADE_NOTICE_TIME             = ut.TIME_HOUR * 3  // 公示期持续时间
	TRADE_NOTICE_TIME_EXTRA       = ut.TIME_HOUR * 6  // 特殊时间段上架的公示期持续时间
	TRADE_NOTICE_PRICE_TIME       = ut.TIME_HOUR * 12 // 交易计价时间
	TRADE_NOTICE_S_TIME_START     = 0                 // 公示期特殊时间段开始 单位：时
	TRADE_NOTICE_S_TIME_END       = 4                 // 公示期特殊时间段结束 单位：时
	TRADE_DISCOUNT_PARAM          = 0.1               // 市场价涨/降价比例
	TRADE_MIN_PRICE_PARAM         = 1.05              // 市场价底价误差比例
	TRADE_MAX_PRICE_TRIGGER_PARAM = 5                 // 市场触发涨价最高倍数
	TRADE_MAX_PRICE_UP_PARAM      = 5                 // 市场对比初始汇率最高倍数
	TRADE_MIN_PRICE_DOWN_PARAM    = 0.2               // 市场对比初始汇率最低倍数

	OCCUPY_PLAYER_CELL_MIN_DIS = 5 // 攻占玩家领地要求最低距离

	CAN_MIN_MARCH_SPEED = 30 // 最低可修改的行军速度

	CITY_OUTPUT_TIME_INTERVAL = ut.TIME_HOUR * 1   // 城市产出时间间隔
	LIVER_EMPEROR_ONLINE_TIME = ut.TIME_HOUR * 120 // 肝帝成就在线时间

	DEFAULT_PET_ID = 4101 // 默认召唤的野兽
	SPEAR_PAWN_ID  = 3701 // 矛
	FIRE_PAWN_ID   = 3702 // 火

	MAX_MAP_MARK_COUNT = 10 // 个人标记数量

	CANCEL_SELL_CD_TIME = ut.TIME_MINUTE * 1 // 取消下架时间
)

// 逃兵间隔
var (
	DESERTER_INTERVAL_BEGIN = []int{ut.TIME_HOUR * 5, ut.TIME_HOUR * 6} // 第一次
	DESERTER_INTERVAL       = []int{ut.TIME_HOUR * 2, ut.TIME_HOUR * 3}
)

// 内政槽位配置 k=>建筑等级 v=>槽位序号
var POLICY_SLOT_CONF_MAP = map[int32]int32{
	3:  0,
	5:  1,
	10: 2,
	15: 3,
	20: 4,
}

// 装备槽位配置 k=>建筑等级 v=>槽位序号
var EQUIP_SLOT_CONF_MAP = map[int32]int32{
	1:  0,
	3:  1,
	5:  2,
	7:  3,
	10: 4,
	12: 5,
	14: 6,
	16: 7,
	18: 8,
	20: 9,
}

// 普通装备槽位配置 k=>建筑等级 v=>槽位序号
var EQUIP_NORMAL_SLOT_CONF_MAP = map[int32]int32{
	1:  0,
	3:  1,
	5:  2,
	7:  3,
	12: 5,
	14: 6,
	16: 7,
	20: 9,
}

// 普通装备槽位配置列表
var EQUIP_SLOT_CONF_NORMAL_LIST = []int32{1, 3, 5, 7, 12, 14, 16, 20}

// 专属装备槽位配置列表
var EQUIP_SLOT_CONF_EXCLUSIVE_LIST = []int32{10, 18}

// 专属装备槽位配置 k=>建筑等级 v=>槽位序号
var EXCLUSIVE_EQUIP_SLOT_CONF_MAP = map[int32]int32{
	10: 4,
	18: 8,
}

// 士兵槽位配置 k=>建筑等级 v=>槽位序号
var PAWN_SLOT_CONF_MAP = map[int32]int32{
	1: 0,
	2: 1,
	4: 2,
	7: 3,
}

// 联盟内政槽位配置
var ALLI_POLICY_SLOT_CONF = map[int32]int32{0: 50, 1: 300, 2: 600}

// 研究配置类型
const (
	CERI_CONF_TYPE_POLICY = 1 // 政策
	CERI_CONF_TYPE_PAWN   = 2 // 士兵
	CERI_CONF_TYPE_EQUIP  = 3 // 装备
)

// 城边3格内加速倍数
var MAIN_CITY_MARCH_SPEED = map[int32]float64{
	1: 3.5,
	2: 3,
	3: 2.5,
	4: 2,
	5: 1.5,
}

// 野地 宝箱分配比例
var TREASURE_ALLOT_RATIO = map[int32]float64{
	0: 0,
	1: 0.5,
	2: 0.45,
	3: 0.4,
	4: 0.35,
}

// 资源对应运送容量
var RES_TRANSIT_CAP = map[int32]int32{
	ctype.CEREAL:    1,
	ctype.TIMBER:    1,
	ctype.STONE:     1,
	ctype.EXP_BOOK:  100,
	ctype.IRON:      100,
	ctype.UP_SCROLL: 500,
	ctype.FIXATOR:   500,
}

// 默认的军队名字
var DEFAULT_ARMY_NAME = map[string]string{
	"cn": "编队",
	"hk": "編隊",
	"tw": "編隊",
	"en": "Team",
	"jp": "チーム",
	"kr": "팀",
}

// 领地数量减少主城免战时间
var REDUCE_AVOID_WAR = map[int32]int64{
	100: ut.TIME_DAY,
	200: ut.TIME_DAY * 2,
	300: MAIN_AVOID_WAR_TIME,
}

// 领地积分配置
var LAND_SCORE_CONF = map[int32][][]int32{
	1: {{50, 2}, {100, 1}},
	2: {{40, 4}, {80, 2}},
	3: {{30, 6}, {60, 3}},
	4: {{20, 8}, {40, 4}},
	5: {{10, 10}, {20, 5}},
}

// 离线消息通知类型
const (
	OFFLINE_MSG_TYPE_UNKNOWN       = iota
	OFFLINE_MSG_TYPE_BUILD         // 建造完成 1
	OFFLINE_MSG_TYPE_CITY          // 城市建造完成
	OFFLINE_MSG_TYPE_MARCH         // 行军完成 3
	OFFLINE_MSG_TYPE_CERI          // 研究完成 4
	OFFLINE_MSG_TYPE_FORGE         // 打造完成 5
	OFFLINE_MSG_TYPE_RESTORE_FORGE // 重铸完成
	OFFLINE_MSG_TYPE_RES_FULL      // 资源满了 7
	OFFLINE_MSG_TYPE_BE_ATTACK     // 受到攻击 8
	OFFLINE_MSG_TYPE_SERVER_OPEN   // 服务器开启 9
	OFFLINE_MSG_TYPE_OCCUPY        // 攻占成功 10
	OFFLINE_MSG_TYPE_ARMY_DEAD     // 军队团灭 11
	OFFLINE_MSG_TYPE_NEW_MSG       // 新消息 12
	OFFLINE_MSG_TYPE_GAME_OVER     // 对局结束 13
	OFFLINE_MSG_TYPE_DRILL         // 招募完成 14
	OFFLINE_MSG_TYPE_LV_UP         // 训练完成 15
	OFFLINE_MSG_TYPE_TEAM_INVITE   // 组队邀请 16
	OFFLINE_MSG_TYPE_MAIL          // 新邮件 17
	OFFLINE_MSG_TYPE_CURE_FINISH   // 治疗完成 18
)

// 离线消息开关类型
const (
	OFFLINE_OPT_TYPE_ALL     = iota // 总开关
	OFFLINE_OPT_TYPE_PROCESS        // 进度通知 (建造、研究、打造、招募、训练)
	OFFLINE_OPT_TYPE_ARMY           // 军队通知 (行军、阵亡)
	OFFLINE_OPT_TYPE_BATTLE         // 战斗通知 (受到攻击)
	OFFLINE_OPT_TYPE_SOCIAL         // 社交通知 (好友、邮件、组队)
	OFFLINE_OPT_TYPE_EVENT          // 节点通知 (爆仓、开服、结束)
)

// 离线消息对应开关类型map
var OFFLINE_MSG_OPT_MAP = map[int32]int32{
	// 进度通知 (建造、研究、打造、招募、训练)
	OFFLINE_MSG_TYPE_BUILD:         OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_CITY:          OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_CERI:          OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_FORGE:         OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_RESTORE_FORGE: OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_DRILL:         OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_LV_UP:         OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_OCCUPY:        OFFLINE_OPT_TYPE_PROCESS,
	OFFLINE_MSG_TYPE_CURE_FINISH:   OFFLINE_OPT_TYPE_PROCESS,
	// 军队通知 (行军)
	OFFLINE_MSG_TYPE_MARCH: OFFLINE_OPT_TYPE_ARMY,
	// 战斗通知 (受到攻击、军队阵亡)
	OFFLINE_MSG_TYPE_BE_ATTACK: OFFLINE_OPT_TYPE_BATTLE,
	OFFLINE_MSG_TYPE_ARMY_DEAD: OFFLINE_OPT_TYPE_BATTLE,
	// 社交通知 (好友、邮件、组队)
	OFFLINE_MSG_TYPE_NEW_MSG:     OFFLINE_OPT_TYPE_SOCIAL,
	OFFLINE_MSG_TYPE_TEAM_INVITE: OFFLINE_OPT_TYPE_SOCIAL,
	OFFLINE_MSG_TYPE_MAIL:        OFFLINE_OPT_TYPE_SOCIAL,
	// 节点通知 (爆仓、开服、结束)
	OFFLINE_MSG_TYPE_RES_FULL:    OFFLINE_OPT_TYPE_EVENT,
	OFFLINE_MSG_TYPE_SERVER_OPEN: OFFLINE_OPT_TYPE_EVENT,
	OFFLINE_MSG_TYPE_GAME_OVER:   OFFLINE_OPT_TYPE_EVENT,
}

// 资源名字
var RES_NAME_MAP = map[int]map[string]string{
	1: {
		"cn": "粮食",
		"hk": "糧食",
		"tw": "糧食",
		"en": "Crop",
		"jp": "食糧",
		"kr": "식량",
	},
	2: {
		"cn": "木头",
		"hk": "木頭",
		"tw": "木頭",
		"en": "Wood",
		"jp": "木材",
		"kr": "목재",
	},
	3: {
		"cn": "石头",
		"hk": "石頭",
		"tw": "石頭",
		"en": "Stone",
		"jp": "石材",
		"kr": "석재",
	},
}

// 资源地类型名字map
var LAND_RES_NAME_MAP = map[int]map[string]string{
	3: {
		"cn": "粮食",
		"hk": "糧食",
		"tw": "糧食",
		"en": "Crop",
	},
	4: {
		"cn": "木头",
		"hk": "木頭",
		"tw": "木頭",
		"en": "Wood",
	},
	5: {
		"cn": "石头",
		"hk": "石頭",
		"tw": "石頭",
		"en": "Stone",
	},
}

// 每日置换次数
var REPLACEMENT_TODAY_COUNT_MAP = map[uint8]int32{
	0: 3,
	1: 3,
	2: 3, // 排位区
}

// 市场初始交易汇率
var TRADE_INIT_PRICE_MAP = map[int32]map[int32]float64{
	1: { // 购买粮食
		2:  0.5,          // 木头
		3:  0.5,          // 石头
		7:  0.0006666666, // 经验书
		9:  0.0006666666, // 铁
		13: 0.0002222222, // 升级卷轴
		14: 0.0002222222, // 固定器
	},
	2: { // 购买木头
		1:  0.5,          // 粮食
		3:  0.5,          // 石头
		7:  0.0006666666, // 经验书
		9:  0.0006666666, // 铁
		13: 0.0002222222, // 升级卷轴
		14: 0.0002222222, // 固定器
	},
	3: { // 购买石头
		1:  0.5,          // 粮食
		2:  0.5,          // 木头
		7:  0.0006666666, // 经验书
		9:  0.0006666666, // 铁
		13: 0.0002222222, // 升级卷轴
		14: 0.0002222222, // 固定器
	},
	7: { // 购买经验书
		1:  1000,         // 粮食
		2:  1000,         // 木头
		3:  1000,         // 石头
		9:  1,            // 铁
		13: 0.3333333333, // 升级卷轴
		14: 0.3333333333, // 固定器
	},
	9: { // 购买铁
		1:  1000,         // 粮食
		2:  1000,         // 木头
		3:  1000,         // 石头
		7:  1,            // 经验书
		13: 0.3333333333, // 升级卷轴
		14: 0.3333333333, // 固定器
	},
	13: { // 购买升级卷轴
		1:  3000, // 粮食
		2:  3000, // 木头
		3:  3000, // 石头
		7:  3,    // 经验书
		9:  3,    // 铁
		14: 0.5,  // 固定器
	},
	14: { // 购买固定器
		1:  3000, // 粮食
		2:  3000, // 木头
		3:  3000, // 石头
		7:  3,    // 经验书
		9:  3,    // 铁
		13: 0.5,  // 升级卷轴
	},
}

// 市场交易物品影响量级
var TRADE_INFLUENCE_MAP = map[int32]float64{
	1:  0.001,
	2:  0.001,
	3:  0.001,
	7:  1,
	9:  1,
	13: 1,
	14: 1,
}

// 四大古城边境 左下开始逆时针
var ANCIENT_CITY_BORDER_MAP = map[int32][]float32{
	0: {0, 0.5, 0, 0.5},
	1: {0.5, 1, 0, 0.5},
	2: {0.5, 1, 0.5, 1},
	3: {0, 0.5, 0.5, 1},
}

// 古城按曼哈顿距离遍历四周的距离参数 左下开始逆时针
var ANCIENT_CITY_DIR_MAP = map[int32][]int32{
	0: {-1, -1, 0, 0},
	1: {1, -1, 1, 0},
	2: {1, 1, 1, 1},
	3: {-1, 1, 0, 1},
}

// 古城id对应方位map
var ANCIENT_CITY_ID_DIR_MAP = map[int32]int32{
	3001: 0,
	3002: 1,
	3003: 2,
	3004: 3,
}

// 古城方位对应idmap
var ANCIENT_CITY_DIR_ID_MAP = map[int32]int32{
	0: 3001,
	1: 3002,
	2: 3003,
	3: 3004,
}

// 古城位置遍历地块顺序
var ANCIENT_CITY_AREA_SEARCH_SORT = []int32{4, 3, 7, 1, 5, 6, 0, 8, 2}

var (
	ANCIENT_CITY_SEARCH_DIS_MIN        int32 = 10                 // 古城位置范围搜索时符合要求的最小距离
	ANCIENT_CITY_OCCUPY_LV_DOWN        int32 = 3                  // 古城被占领降级
	ANCIENT_CITY_MAX_LV                int32 = 20                 // 古城最高等级
	ANCIENT_CITY_SPEED_UP_PARAM        int32 = 40                 // 古城加速参数
	ANCIENT_CITY_SPEED_UP_TIME         int64 = ut.TIME_MINUTE * 6 // 古城加速单次时间
	ANCIENT_CONTRIBUTE_LOG_LIMIT             = 50                 // 古城捐献记录显示上限
	ANCIENT_CITY_CREATE_TIME                 = 10                 // 古城生成时间 单位时
	ANCIENT_CONTRIBUTE_COUNT_MAX       int32 = 40                 // 古城每级捐献次数上限
	ANCIENT_CONTRIBUTE_CD              int64 = ut.TIME_SECOND * 3 // 古城捐献CD
	ANCIENT_MAX_EFFECT_TIME            int64 = ut.TIME_DAY        // 古城初始占领满级效果持续时间
	ANCIENT_OCCUPY_WIN_SAME_TIME_COUNT       = 3                  // 同时占领X个古城直接获胜
	ANCIENT_OCCUPY_WIN_SAME_TIME_LV    int32 = 12                 // 同时占领X个古城直接获胜需要的等级
	ANCIENT_OCCUPY_NO_AVOID_WAR_COUNT        = 2                  // 同时占领X个古城取消免战

	ANCIENT_OCCUPY_TIME = []int{10, 0, 18, 0} // 无主古城可攻占时间
)

// 古城可捐献资源类型
var ANCIENT_CONTRIBUTE_RES_TYPE = map[int32]bool{
	1: true,
	2: true,
	3: true,
}

// 英雄槽位等级开启条件
var HERO_SLOT_LV_COND = []int{1, 10, 20}

// 养由基召唤时的对应等级
var SUMMON_LV = map[int32]int32{
	1: 1,
	2: 2,
	3: 4,
	4: 6,
	5: 8,
	6: 10,
}

// 战损补偿相关
var (
	COMPENSATE_MAIL_ID             = 100021                                            // 战损补偿邮件id
	COMPENSATE_TREASURE_PARAM      = 0.25                                              // 战损补偿宝箱资源系数
	COMPENSATE_MAIN_CITY_LV_PARAMS = []float64{0.6666666666666666, 0.3333333333333333} // 战损补偿主城等级相关系数
	COMPENSATE_THRESHOLD_INIT      = 500                                               // 战损补偿初始阈值
	COMPENSATE_THRESHOLD_ADD       = 500                                               // 战损补偿递增阈值
	COMPENSATE_THRESHOLD_MAX       = 5000                                              // 战损补偿最大阈值
	COMPENSATE_BOOK_TRANS_MAP      = map[int32]float64{
		ctype.IRON:      1,
		ctype.UP_SCROLL: 3,
		ctype.FIXATOR:   3,
	} // 战损补偿经验书转换系数

	COMPENSATE_LOST_TRESURE_MAIL_ID       = 100020 // 损失宝箱补偿邮件
	LOST_TRESURE_COMPENSATE_LIMIT   int32 = 1      // 损失宝箱补偿上限
)

// 行军类型
const (
	MARCH_TYPE_UNKOWN             = iota
	MARCH_TYPE_MOVE               // 移动
	MARCH_TYPE_CANCEL             // 撤回
	MARCH_TYPE_AUTO_REVOKE        // 自动遣返
	MARCH_TYPE_REMOVE_ARMY        // 解散军队
	MARCH_TYPE_MOVE_REVOKE        // 行军中遣返
	MARCH_TYPE_FORECE_REVOKE      // 强制撤回
	MARCH_TYPE_MOVE_FORECE_REVOKE // 行军中强制撤回
)

// 防作弊检查相关
var (
	ANTI_CHEAT_LV_UP_BUILD_SCORE        int32 = 15                  // 升级建筑增加积分
	ANTI_CHEAT_OCCUPY_WILD_SCORE        int32 = 20                  // 攻占野地增加积分
	ANTI_CHEAT_DRILL_PAWN_SCORE         int32 = 1                   // 招募士兵增加积分
	ANTI_CHEAT_LVING_PAWN_SCORE         int32 = 1                   // 升级士兵增加积分
	ANTI_CHEAT_CELL_TONDEN_SCORE        int32 = 1                   // 屯田增加积分
	ANTI_CHEAT_REST_REDUCE_TIME         int32 = ut.TIME_MINUTE * 12 // 每次休息减少积分的单位时间
	ANTI_CHEAT_REST_REDUCE_SCORE        int32 = 4                   // 每次休息减少积分
	ANTI_CHEAT_QUEST_OPTION_COUNT             = 4                   // 题目选项数量
	ANTI_CHEAT_QUEST_TIME_LIMIT         int64 = 90 * ut.TIME_SECOND // 防作弊回答限时
	ANTI_CHEAT_WRONG_ANSWER_REDUCE_TIME       = 20 * ut.TIME_SECOND // 回答错误减少倒计时
	ANTI_CHEAT_QUEST_NOT_PASS_LIMIT           = 3                   // 防作弊回答未通过次数上限 达到上限则测试失败
	ANTI_CHEAT_L_INIT                   int32 = 1000                // 防作弊检测初始阈值
	ANTI_CHEAT_L_PASS_ADD               int32 = 500                 // 防作弊检测每次通过增加的L值
	ANTI_CHEAT_REWARD                   int32 = 150                 // 防作弊最小档三资奖励
	ANTI_CHEAT_OPEN                           = true                // 防作弊检测开关

	ANTI_CHEAT_QUEST_ITEMS = []string{"item_1", "item_2", "item_3", "item_4", "item_5"} // 题目特征列表

	// 连续失败次数和封禁时间
	ANTI_CHEAT_BAN_TIME_MAP = map[int32]int64{
		1: ut.TIME_HOUR * 2,
		2: ut.TIME_HOUR * 8,
		3: ut.TIME_HOUR * 72,
		4: ut.TIME_HOUR * 240,
	}

	// 作弊封禁中点击触发次数和对应添加的连续失败次数
	ANTI_CHEAT_BAN_TAP_MAP = map[int]int{
		300: 1,
		600: 1,
	}

	// 作弊封禁中挂机时间和对应添加的连续失败次数
	ANTI_CHEAT_BAN_HANG_UP_MAP = map[int]int{
		ut.TIME_MINUTE * 15: 1,
		ut.TIME_MINUTE * 30: 1,
	}
)

// 免战类型
const (
	AVOID_WAR_TYPE_NORMAL         = iota // 普通免战
	AVOID_WAR_TYPE_BATTLE_TIMEOUT        // 战斗超时免战
)

// 屯田配置
var (
	CELL_TONDEN_DAILY_COUNT   int32   = 10 // 屯田每天次数上限
	CELL_TONDEN_REWARD_PARAM  float64 = 1  // 屯田奖励比例
	CELL_TONDEN_STAMINA_PARAM int32   = 3  // 屯田消耗奖励点倍率
)

// 医馆配置
var (
	INJURY_PAWN_ODDS_MAP = map[int32]int32{
		1: 0,
		2: 100,
		3: 100,
		4: 100,
		5: 100,
		6: 100,
	} // 阵亡士兵回到医馆百分比概率 k=>lv v=>概率

	INJURY_PAWN_MAX_COUNT       = 200  // 医馆士兵数量上限
	CURE_RES_INIT_PARAM         = 1.0  // 医疗士兵资源初始比例
	CURE_RES_COUNT_PARAM        = 0.2  // 医疗士兵资源次数叠加比例
	CURE_TIME_INIT_PARAM        = 0.5  // 医疗士兵时间初始比例
	CURE_TIME_COUNT_PARAM       = 0.5  // 医疗士兵时间次数叠加比例
	CURE_TIME_HOSPITAL_LV_PARAM = 0.02 // 医疗士兵医馆等级加速比例
	CURE_SPEED_UP_BOOK_PARAM    = 3    // 加速治疗士兵消耗书比例

	HOSPITAL_LEFT_WARN_MAP = map[int32]bool{
		20: true,
		10: true,
		1:  true,
	} // 医馆剩余容量提醒map

	HOSPITAL_FULL_WARN_MAP = map[int32]bool{
		20: true,
		10: true,
	} // 医馆容量已满累计提醒map
)

// 开荒方式
const (
	FARM_TYPE_NONE       = 0 // 未选择模式
	FARM_TYPE_NORMAL     = 1 // 普通模式
	FARM_TYPE_PROTECTION = 2 // 保护模式

	FARM_TYPE_PROTECTION_DIS = 6                    // 保护模式距离
	NEWBIE_DEFAULT_FARM_TYPE = FARM_TYPE_PROTECTION // 新手区默认为保护模式
	RANK_DEFAULT_FARM_TYPE   = FARM_TYPE_NORMAL     // 排位区默认为普通模式
	FREE_DEFAULT_FARM_TYPE   = FARM_TYPE_NORMAL     // 自由区默认为普通模式
)

// 熔炼相关
var (
	SMELTING_FIXATOR_COST int32 = 1    // 融炼装备需要的钉子
	SMELTING_EQUIP_TIME   int32 = 8000 // 融炼装备时间

	SMELT_EQUIP_LV_MAP = map[int]int32{ // 可融炼装备数量和对应等级
		1: 14,
		2: 20,
	}
)

// 政策任务配置map k=>政策id v=>对应任务id
var POLICY_TASK_MAP = map[int32][]int32{
	1031: {40004001, 40004002, 40004003},
}

// 政策添加物品配置map k=>政策id v=>[tp, id, count]
var POLICY_ADD_ITEM_MAP = map[int32][]int32{
	51: {ctype.UP_SCROLL, 0, 1},
	52: {ctype.FIXATOR, 0, 1},
}
