package player

import (
	"context"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	mgo "slgsrv/utils/mgodb"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Mongodb struct {
	table string
}

func (this *Mongodb) getCollection() *mongo.Collection {
	return mgo.GetCollection(this.table)
}

type TableData struct {
	Uid          string `bson:"uid"`
	Nickname     string `bson:"nickname"`
	HeadIcon     string `bson:"head_icon"`
	PersonalDesc string `bson:"personal_desc"` //个人简介
	AllianceUid  string `bson:"alliance_uid"`  //所在联盟
	Language     string `bson:"language"`      //使用语言
	FCMToken     string `bson:"fcm_token"`     //FCM令牌

	CaptureInfo             map[string]interface{}      `bson:"capture_info"`               //沦陷信息
	CurrForgeEquip          *ForgeEquipInfo             `bson:"curr_forge_equip"`           //当前打造信息
	CurSmeltEquipData       *SmeltEquipData             `bson:"cur_smelt_equip_data"`       //当前融炼信息
	ConfigPawnMap           map[int32]*g.PawnConfigInfo `bson:"config_pawn_map"`            //配置士兵的信息
	FortAutoSupports        map[int32]bool              `bson:"fort_auto_supports"`         //要塞自动支援配置
	AddOutputEndTime        map[int32]int64             `bson:"add_output_end_time"`        //增加产出的结束时间
	Policys                 map[int32]*g.PolicyInfo     `bson:"policys"`                    //当前政策 已弃用
	PolicySlots             map[int32]*g.CeriSlotInfo   `bson:"policy_slots"`               //当前政策槽位map
	HidePChatChannels       map[string]string           `bson:"hide_pchat_channels"`        //隐藏的私聊频道
	BeginDeserterInfo       *BeginDeserterData          `bson:"begin_deserter_info"`        //开始逃兵信息
	OccupyLandCountMap      map[int32][]int32           `bson:"occupy_land_count_map"`      //历史攻占野地数量 key=地块等级 val=数量
	OccupyLandDifficultyMap map[int32]int32             `bson:"occupy_land_difficulty_map"` //历史攻占野地难度数量
	CityOutputMap           map[int32][]*g.TypeObj      `bson:"city_output_map"`            //城市产出
	CompensateMap           map[int32]float64           `bson:"compensate_map"`             //补偿资源map
	BattleRecordInfo        map[int32]int32             `bson:"battle_record_info"`         //战斗记录信息
	PawnDrillMap            map[int32]int32             `bson:"pawn_drill"`                 //士兵招募数量
	PawnMaxLvMap            map[int32]int32             `bson:"pawn_max_lv"`                //满级士兵数量map
	PolicyUseMap            map[int32]bool              `bson:"policy_use_map"`             //本局使用过政策map
	EquipUseMap             map[int32]bool              `bson:"equip_use_map"`              //本局使用过装备map
	KillRecordMap           map[int32]int32             `bson:"kill_record_map"`            //击杀记录map
	EquipCeriSlots          map[int32]*g.CeriSlotInfo   `bson:"equip_ceri_slots"`           //装备研究槽位信息
	PawnCeriSlots           map[int32]*g.CeriSlotInfo   `bson:"pawn_ceri_slots"`            //士兵研究槽位信息

	UnlockPolicyIds  []int32                  `bson:"unlock_policy_ids"`  //当前额外解锁的政策列表
	UnlockPawnIds    []int32                  `bson:"unlock_pawn_ids"`    //当前额外解锁的兵种列表
	UnlockEquipIds   []int32                  `bson:"unlock_equip_ids"`   //当前额外解锁的装备列表
	BTQueues         []*BTInfo                `bson:"bt_queues"`          //建造队列
	Merchants        []*Merchant              `bson:"merchants"`          //商人数量
	Tasks            []*g.TaskInfo            `bson:"tasks"`              //任务列表
	TaskFinishs      []int32                  `bson:"task_finishs"`       //任务完成列表
	TodayTasks       []*g.TaskInfo            `bson:"today_tasks"`        //任务列表
	TodayTaskFinishs []int32                  `bson:"today_task_finishs"` //任务完成列表
	OtherTasks       []*g.TaskInfo            `bson:"other_tasks"`        //任务列表
	OtherTaskFinishs []int32                  `bson:"other_task_finishs"` //任务完成列表
	Equips           []*g.EquipInfo           `bson:"equips"`             //装备列表
	CeriSlots        []*CeriSlotInfo          `bson:"ceri_slots"`         //研究所槽位信息
	HeroSlots        []map[string]interface{} `bson:"hero_slots"`         //英雄槽位信息
	MapMarks         []*MapMarkInfo           `bson:"map_marks"`          //个人标记列表
	OfflineNotifyOtp []int32                  `bson:"offline_notify_opt"` //玩家离线通知设置
	InjuryPawnList   []*pb.InjuryPawnInfo     `bson:"injury_pawnList"`    //医馆受伤士兵列表

	CreateTime            int64 `bson:"create_time"`              //创建时间
	LastLoginTime         int64 `bson:"last_login_time"`          //最后一次登录时间
	OfflineTime           int64 `bson:"offline_time"`             //离线时间
	NextToDayTime         int64 `bson:"next_today_time"`          //明日开始时间
	LastOutputTime        int64 `bson:"last_output_time"`         //最后一次产出时间
	LastCityOutputTime    int64 `bson:"last_city_output_time"`    //最后一次城市产出时间
	LastUpdateStaminaTime int64 `bson:"last_update_stamina_time"` //最后刷新体力的时间
	LastTaTrackTime       int64 `bson:"last_ta_track_time"`       //最后一次上报时间
	AntiCheatLastTime     int64 `bson:"cheat_check_last_time"`    //防作弊检测阈上一次累计分数的时间
	AccTotalGiveResCount  int64 `bson:"acc_total_give_res_count"` //累计赠送资源数量
	LastOnlineTime        int64 `bson:"last_online_time"`         //最后一次的在线时长
	TopOnlineTime         int64 `bson:"top_online_time"`          //最高在线时长
	SumOnlineTime         int64 `bson:"sum_online_time"`          //累计在线时间
	ResAcc                int64 `bson:"res_acc"`                  //总经济

	Version          int32 `bson:"version"`             //版本 用于兼容数据
	LoginCount       int32 `bson:"login_count"`         //登录次数
	LoginDayCount    int32 `bson:"login_day_count"`     //登陆天数
	CLoginDayCount   int32 `bson:"c_login_day_count"`   //连续登录天数
	Stamina          int32 `bson:"stamina"`             //奖励点
	ExpBook          int32 `bson:"exp_book"`            //经验书
	AccTotalExpBook  int32 `bson:"acc_total_exp_book"`  //经验书累计
	Iron             int32 `bson:"iron"`                //铁
	AccTotalIron     int32 `bson:"acc_total_iron"`      //铁累计
	UpScroll         int32 `bson:"up_scroll"`           //卷轴
	AccTotalUpScroll int32 `bson:"acc_total_up_scroll"` //卷轴 累计
	Fixator          int32 `bson:"fixator"`             //固定器
	AccTotalFixator  int32 `bson:"acc_total_fixator"`   //固定器 累计
	Cereal           int32 `bson:"cereal"`              //粮食
	AccTotalCereal   int32 `bson:"acc_total_cereal"`    //粮食累计
	Timber           int32 `bson:"timber"`              //木头
	AccTotalTimber   int32 `bson:"acc_total_timber"`    //木头累计
	Stone            int32 `bson:"stone"`               //石头
	AccTotalStone    int32 `bson:"acc_total_stone"`     //石头累计
	CellTondenCount  int32 `bson:"cell_tonden_count"`   //当天已屯田次数
	FarmType         int32 `bson:"farm_type"`           //开荒模式

	MainCityIndex         int32 `bson:"main_city_index"`          //主城位置
	ReCreateMainCityCount int32 `bson:"recreate_main_city_count"` //重新创建主城次数
	Title                 int32 `bson:"title"`                    //当前称号
	SmelCount             int32 `bson:"smel_count"`               //当前融炼次数
	ResetPolicyNeedGold   int32 `bson:"reset_policy_need_gold"`   //当前重置政策需要的金币
	ExitAllianceCount     int32 `bson:"exit_alliance_count"`      //退出联盟次数
	TodayReplacementCount int32 `bson:"today_replacement_count"`  //每日置换次数
	UpRecruitPawnCount    int32 `bson:"up_recruit_pawn_count"`    //加速招募士兵数量
	FreeRecruitPawnCount  int32 `bson:"free_recruit_pawn_count"`  //已免费招募士兵数量
	FreeLevingPawnCount   int32 `bson:"free_leving_pawn_count"`   //已免费训练士兵数量
	FreeCurePawnCount     int32 `bson:"free_cure_pawn_count"`     //已免费治疗士兵数量
	FreeForgeCount        int32 `bson:"free_forge_count"`         //已免费打造/重铸数量
	AvoidWarReduce        int32 `bson:"avoid_war_reduce"`         //主城免战减少信息
	RodeleroCadetLv       int32 `bson:"rodelero_cadet_lv"`        //剑盾兵 见习勇士 层数
	LandScore             int32 `bson:"land_score"`               //领地积分
	LandScoreTop          int32 `bson:"land_score_top"`           //领地积分 历史最高
	AlliScore             int32 `bson:"alli_score"`               //联盟积分
	AlliScoreTop          int32 `bson:"alli_score_top"`           //联盟积分 历史最高

	CompensateCount        int32 `bson:"compensate_count"`        //补偿资源次数
	TreasureLostCount      int32 `bson:"treasure_lost_count"`     //宝箱遗失补偿次数
	MaxLandCount           int32 `bson:"max_land_count"`          //最大拥有的地块数量
	TodayOccupyCellCount   int32 `bson:"today_occupy_cell_count"` //每日打地数量
	AntiCheatS             int32 `bson:"cheat_check_s"`           //防作弊检测累计分数
	AntiCheatL             int32 `bson:"cheat_check_l"`           //防作弊检测阈值
	HospitalAbandomAcc     int32 `bson:"hospital_abandom_acc"`    //医馆满员后累计放弃的士兵数量
	HospitalFullNoticeFlag int32 `bson:"hospital_notice_flag"`    //医馆通知标记
	HospitalNoticeClose    bool  `bson:"hospital_notice_close"`   //医馆通知是否关闭

	IsGiveupGame bool `bson:"is_giveup_game"` //是否放弃了本次对局
	NoAvoidWar   bool `bson:"no_avoid_war"`   //取消免战
	IsSettled    bool `bson:"is_settled"`     //是否结算
}

func (this *TableData) ToWebData(wld g.World) map[string]interface{} {
	return map[string]interface{}{
		"uid":           this.Uid,
		"nickname":      this.Nickname,
		"mainCityIndex": this.MainCityIndex,
		"lastLoginTime": this.LastLoginTime,
		"offlineTime":   this.OfflineTime,
		"cereal":        this.Cereal,
		"timber":        this.Timber,
		"stone":         this.Stone,
		"expBook":       this.ExpBook,
		"iron":          this.Iron,
		"landCount":     wld.GetPlayerLandCount(this.Uid, false),
		"sumOnlineTime": this.SumOnlineTime,
		"loginDayCount": this.LoginDayCount,
		"createTime":    this.CreateTime,
		"allianceUid":   this.AllianceUid,
		"allianceName":  wld.GetAllianceNameByUID(this.AllianceUid),
		"heroSlots": array.Map(this.HeroSlots, func(m map[string]interface{}, _ int) int {
			heroJson := ut.MapInterface(m["hero"])
			if heroJson != nil {
				return ut.Int(heroJson["id"])
			}
			return 0
		}),
	}
}

// 插入单个
func (this *Mongodb) InsertOne(data TableData) (err string) {
	if _, e := this.getCollection().InsertOne(context.TODO(), data); e != nil {
		err = e.Error()
	}
	return
}

// 查询单个
func (this *Mongodb) FindByUid(uid string) (data TableData, err string) {
	if e := this.getCollection().FindOne(context.TODO(), bson.M{"uid": uid}).Decode(&data); e != nil {
		err = e.Error()
	}
	return
}

// 更新一条
func (this *Mongodb) UpdateOne(uid string, data TableData) (err string) {
	if _, e := this.getCollection().UpdateOne(context.TODO(), bson.M{"uid": uid}, bson.M{"$set": data}); e != nil {
		err = e.Error()
	}
	return
}

// 删除一条
func (this *Mongodb) DeleteOne(uid string) (err string) {
	if _, e := this.getCollection().DeleteOne(context.TODO(), bson.M{"uid": uid}); e != nil {
		err = e.Error()
	}
	return
}

// 获取数据总数
func (this *Mongodb) Find() (data map[string]TableData, err string) {
	cur, e := this.getCollection().Find(context.TODO(), bson.D{})
	if e != nil {
		return nil, e.Error()
	} else if e = cur.Err(); e != nil {
		return nil, e.Error()
	}
	defer func() {
		_ = cur.Close(context.TODO())
	}()
	data = map[string]TableData{}
	for cur.Next(context.TODO()) {
		var elem TableData
		if e = cur.Decode(&elem); e == nil {
			data[elem.Uid] = elem
		}
	}
	return
}
