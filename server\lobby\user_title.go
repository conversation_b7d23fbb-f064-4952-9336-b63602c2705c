package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"
)

type TitleInfo struct {
	Time     int64 `json:"time" bson:"time"`         //获取时间
	Duration int64 `json:"duration" bson:"duration"` //持续时间
	ID       int32 `json:"id" bson:"id"`
}

func (this *TitleInfo) Init(id int32) *TitleInfo {
	this.ID = id
	this.Time = time.Now().UnixMilli()
	if json := config.GetJsonData("title", id); json != nil {
		if durationDay := ut.Int64(json["duration"]); durationDay > 0 {
			this.Duration = durationDay * ut.TIME_DAY
		}
	}
	return this
}

func NewTitleInfo(id int32) *TitleInfo {
	return new(TitleInfo).Init(id)
}

// title数据转为pb
func (this *User) TitlesToPb() []*pb.TitleInfo {
	arr := []*pb.TitleInfo{}
	now := time.Now().UnixMilli()
	this.TitlesLock.RLock()
	for _, v := range this.Titles {
		titleInfo := &pb.TitleInfo{
			Id:         int32(v.ID),
			Time:       int64(v.Time),
			RemainTime: int64(ut.MaxInt64(0, v.Duration-(now-v.Time))),
		}
		arr = append(arr, titleInfo)
	}
	this.TitlesLock.RUnlock()
	return arr
}

// title数据克隆
func (this *User) TitlesClone() []*TitleInfo {
	this.TitlesLock.RLock()
	defer this.TitlesLock.RUnlock()
	return array.Map(this.Titles, func(m *TitleInfo, _ int) *TitleInfo { return m })
}

// 是否拥有称号
func (this *User) HasTitle(id int32) bool {
	this.TitlesLock.RLock()
	defer this.TitlesLock.RUnlock()
	return array.Some(this.Titles, func(m *TitleInfo) bool { return m.ID == id })
}

// 解锁称号
func (this *User) UnlockTitle(id int32) {
	this.TitlesLock.Lock()
	defer this.TitlesLock.Unlock()
	for _, m := range this.Titles {
		if m.ID == id {
			m.Init(id)
			this.FlagUpdateDB()
			return
		} else if tp1, tp2 := m.ID/1000, id/1000; tp1 != tp2 {
		} else if id > m.ID { //同一个类型的只保留最大的一个
			m.Init(id)
			this.FlagUpdateDB()
			return
		}
	}
	// 下面添加
	this.Titles = append(this.Titles, NewTitleInfo(id))
	this.FlagUpdateDB()
}

// 检测刷新称号
func (this *User) CheckUpdateTitle(now int64, lobby *Lobby) {
	notify := false
	this.TitlesLock.Lock()
	for i := len(this.Titles) - 1; i >= 0; i-- {
		if m := this.Titles[i]; m.Duration > 0 && now-m.Time >= m.Duration {
			// 如果当前正在使用这个称号应该去掉
			if this.LastTitle == m.ID {
				this.LastTitle = 0
				// 通知游戏服务器改动
				lobby.InvokeGameRpcNR(this.PlaySID, slg.RPC_CHANGE_USER_TITLE, this.UID, 0)
			}
			// 删除这个称号
			this.Titles = append(this.Titles[:i], this.Titles[i+1:]...)
			notify = true
		}
	}
	this.TitlesLock.Unlock()
	// 通知
	if notify {
		this.PutNotifyQueue(constant.NQ_UPDATE_TITLES, &pb.OnUpdatePlayerInfoNotify{
			Data_76: &pb.TitleListInfo{
				Title:  int32(this.LastTitle),
				Titles: this.TitlesToPb(),
			},
		})
	}
}
