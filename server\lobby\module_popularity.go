package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) initHDPopularity() {
	this.GetServer().RegisterGO("HD_GetUserPopularity", this.getUserPopularity)       //获取用户的人气信息
	this.GetServer().RegisterGO("HD_ChangeUserPopularity", this.changeUserPopularity) //改变用户的人气
}

// 获取用户的人气信息
func (this *Lobby) getUserPopularity(session gate.Session, msg *pb.LOBBY_HD_GETUSERPOPULARITY_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	s2c := &pb.LOBBY_HD_GETUSERPOPULARITY_S2C{}
	uid := msg.GetUid()
	if user.UID == uid {
		s2c.List, s2c.Records = user.ToPopularityPb(true)
	} else if user.UID != uid {
		bytes, e := ut.RpcBytes(this.InvokeUserFunc(uid, slg.RPC_GET_USER_POPULARITY))
		if e != "" {
			log.Error("getUserPopularity rpc uid: %v err: %v", uid, e)
			return nil, ecode.NOT_BIND_UID.String()
		}
		pb.ProtoUnMarshal(bytes, s2c)
	}
	return pb.ProtoMarshal(s2c)
}

// 改变用户的人气信息
func (this *Lobby) changeUserPopularity(session gate.Session, msg *pb.LOBBY_HD_CHANGEUSERPOPULARITY_C2S) (ret []byte, err string) {
	user := GetUserByOnline(session.GetUserID())
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	uid, id := msg.GetUid(), msg.GetId()
	if user.UID == uid {
		return nil, ecode.PLAYER_NOT_EXIST.String() //不能改变自己的人气
	}
	json := config.GetJsonData("evaluateGift", id)
	needGold, isBotany := int32(0), ut.Int32(json["type"]) == 3
	if json == nil {
		err = ecode.EVALUATE_NOT_EXIST.String()
		return
	} else if needGold = ut.Int32(json["gold"]); needGold > 0 && user.Gold < needGold {
		err = ecode.GOLD_NOT_ENOUGH.String()
		return //金币不足
	} else if isBotany && !user.HasBotany(id) {
		return nil, ecode.EVALUATE_NOT_EXIST.String()
	}
	bytes, err := ut.RpcBytes(this.InvokeUserFunc(uid, slg.RPC_CHANGE_USER_POPULARITY, user.UID, user.Nickname, id))
	if err != "" {
		log.Error("getUserPopularity rpc uid: %v err: %v", uid, err)
		return nil, err
	}
	// 扣除金币
	if needGold > 0 {
		if user.ChangeGold(-needGold, constant.GOLD_CHANGE_EVALUATE_COST) == -1 {
			// TODO 强制扣除?
			log.Warning("changeUserPopularity cost gold err uid: %v", user.UID)
		}
	}
	// 扣除植物
	if isBotany {
		user.ChangeBotany(id, -1, uid)
	}
	s2c := &pb.LOBBY_HD_CHANGEUSERPOPULARITY_S2C{}
	pb.ProtoUnMarshal(bytes, s2c)
	s2c.Gold = int32(user.Gold)
	s2c.UnlockBotanys = user.ToUnlockBotanysPb()
	return pb.ProtoMarshal(s2c)
}
