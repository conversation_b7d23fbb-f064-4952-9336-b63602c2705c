package behavior

import (
	"math"
	"slgsrv/server/game/common/astar"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/enums/hero"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/common/helper"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"sort"
	"strings"
)

type DashData struct {
	Time  int32
	Point *ut.Vec2
}

// 检测使用技能攻击
type CheckUseSkillAttack struct {
	BaseAction

	attackTargets []g.IFighter
	dashArray     []*DashData //冲撞时间记录
	tempNumber1   int32
	tempNumber2   int32

	_temp_vec2_1 *ut.Vec2
}

func (this *CheckUseSkillAttack) OnInit(conf map[string]interface{}) {
	this.target.InitSkillAttackAnimTime()
}

func (this *CheckUseSkillAttack) OnOpen() {
	this.attackTargets = []g.IFighter{}
	this.dashArray = []*DashData{}
	this.tempNumber1 = 0
	this.tempNumber2 = 0
	this._temp_vec2_1 = ut.NewVec2(0, 0)
	isSilence := this.target.IsHasBuff(bufftype.SILENCE)
	// 关羽检测是否切换了新目标
	if isSilence {
	} else if heroSkill := this.target.CheckPortrayalSkill(hero.GUAN_YU); heroSkill != nil {
		if attackTarget := this.target.GetAttackTarget(); attackTarget != nil && !attackTarget.IsBuild() && this.checkInAttackRange(attackTarget) {
			buff := this.target.GetBuff(bufftype.DAMAGE_SUPERPOSITION)
			if (buff == nil && !this.target.IsHasBuff(bufftype.WOOSUNG)) || (buff != nil && buff.Provider != attackTarget.GetUID()) {
				// 添加怒气
				this.target.SetFullAnger(heroSkill.GetParamsFloat64(), true)
				// 添加增伤
				this.target.AddBuff(bufftype.WOOSUNG, this.target.GetUID(), 1).SetValue(heroSkill.GetValue())
			}
		}
	}
	isCanUseSkill := this.target.IsCanUseSkill() && !isSilence
	// 秦良玉检测是否需要收矛
	if this.target.CheckPortrayalSkill(hero.QIN_LIANGYU) != nil {
		isCanUseSkill = this.target.IsHasBuff(bufftype.BARB) || isCanUseSkill
	}
	this.SetBlackboardData("isCanUseSkill", isCanUseSkill)
}

func (this *CheckUseSkillAttack) OnLeave(state Status) {
	if state == FAILURE { //表示技能释放完成 标记不能释放技能了
		this.SetBlackboardData("isCanUseSkill", false)
		this.checkRecoverHPForLeave()
		this.checkRecoverAngerForLeave()
	}
}

func (this *CheckUseSkillAttack) OnTick(dt int32) Status {
	if !ut.Bool(this.GetBlackboardData("isCanUseSkill")) {
		return SUCCESS
	}
	skill := this.target.GetActiveSkill()
	if skill == nil {
		return SUCCESS
	}
	currPoint := this.target.GetPoint()
	skillType, heroSkill, ctrl := skill.GetType(), this.target.GetEntity().GetPortrayalSkill(), this.GetCtrl()
	currAttackTime := ut.Int32(this.GetBlackboardData("currAttackTime")) //这个当前时间主要用于前端播放技能动作时间的记录
	this.SetBlackboardData("currAttackTime", currAttackTime+dt)
	// 根据技能类型写对应逻辑
	if skillType == 201 { //刺击 ===================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || attackTarget.IsBuild() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			target := attackTarget.GetPoint()
			dir := ctrl.NormalizeVec2(target.Sub(currPoint))
			// 开始获取一条线的目标
			this.attackTargets = append(this.attackTargets, attackTarget)
			targetUid := attackTarget.GetUID()
			maxDis := ut.If(skill.IsExclusiveIntensify(), skill.GetTarget()+1, skill.GetTarget())
			this._temp_vec2_1.Set(dir.Mul(maxDis).AddSelf(currPoint))
			targetPoint := target.Clone()
			cnt, maxCount := len(this.attackTargets), 4
			dis := helper.GetPointToPointDis(targetPoint, currPoint)
			for dis <= maxDis && cnt < maxCount {
				if ctrl.CheckIsBattleArea(targetPoint.X, targetPoint.Y) {
					arr := this.target.GetCanAttackRangeFighterByPoint(targetPoint, this.target.GetCanAttackFighters(), 0, int32(maxCount-cnt), func(m g.IFighter) bool { return m.GetUID() == targetUid })
					this.attackTargets = append(this.attackTargets, arr...)
				}
				targetPoint.AddSelf(dir)
				cnt = len(this.attackTargets)
				dis = helper.GetPointToPointDis(targetPoint, currPoint)
			}
		}
		done := true
		attackAmend := skill.GetValue()
		for i, l := 0, len(this.attackTargets); i < l; i++ {
			at := this.attackTargets[i]
			if !this.attackOne(skill, currPoint, at, dt, 0, map[string]interface{}{
				"attackAmend":    attackAmend,
				"attackSegments": i,
				"onHit": func(damage int32, val int32) {
					if i == 0 {
						// 每击中一个目标恢复最大生命值10%的生命
						r := this.target.GetStrategyValue(30101)
						if r == 0 {
							r = skill.GetParamsInt()
						}
						v := ut.RoundInt32(float64(this.target.GetMaxHp()-this.target.GetCurHp()) * float64(r) * 0.01)
						this.target.OnHeal(v, false)
						// 专属 加闪避
						if skill.IsExclusiveIntensify() {
							this.target.AddBuffValue(bufftype.DODGE, this.target.GetUID(), float64(len(this.attackTargets)*15))
						}
						// 韬略 给军队中恢复生命
						if strategyBuff := this.target.GetStrategyBuff(50001); strategyBuff != nil {
							var peltast g.IFighter = nil
							var minRatio float64 = 1
							arr, armyUid := ctrl.GetFighters(), this.target.GetEntity().GetArmyUid()
							for _, m := range arr {
								if m.GetEntity().GetArmyUid() != armyUid || m.IsDie() || m.IsFullHP() {
									continue
								} else if ratio := m.GetHPRatio(); ratio < minRatio {
									peltast = m
									minRatio = ratio
								}
							}
							if peltast != nil {
								peltast.OnHeal(ut.RoundInt32(float64(peltast.GetMaxHp()-peltast.GetCurHp())*strategyBuff.GetValue()*0.01), false)
							}
						}
						// 杨妙真 识破
						if this.CheckHeroSkillID(heroSkill, hero.YANG_MIAOZHEN) {
							at.AddBuff(bufftype.PENETRATE, this.target.GetUID(), 1)
						}
					}
				},
			}) {
				done = false
			}
		}
		if done {
			return FAILURE
		}
	} else if skillType == 202 { //投射 ===========================================================================================================
		isQly := this.CheckHeroSkillID(heroSkill, hero.QIN_LIANGYU)
		// 秦良玉
		if isQly && !ut.Bool(this.GetBlackboardData("spearthrowing")) {
			// 检测手上是否有矛
			if this.target.CheckTriggerBuff(bufftype.BARB) != nil {
				spear := ctrl.GetFighter("build_" + this.target.GetUID()) //找到矛的位置
				if spear != nil {
					// 根据矛找到敌方目标
					spearPoint := spear.GetPoint()
					if attackTarget := this.target.GetAttackTarget(); attackTarget == nil || attackTarget.IsDie() || !attackTarget.GetPoint().Equals(spearPoint) {
						if arr := spear.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 0, 1, ""); len(arr) > 0 {
							this.target.ChangeAttackTarget(arr[0])
						} else {
							this.target.ChangeAttackTarget(nil)
						}
					}
					// 删除矛
					this.SetBlackboardData("spearPoint", spearPoint.ID())
					spear.ChangeState(constant.PAWN_STATE_DIE)
					ctrl.RemoveNoncombat(spear.GetUID())
					// 时间
					arr := ut.StringToFloats(ut.String(heroSkill.Params), ",")
					this.SetBlackboardData("recycleSpearNeedTime", int32(arr[0]*1000))
					this.SetBlackboardData("recycleSpearHitTime", int32(arr[1]*1000))
				}
			}
			// 回收矛
			if spearPoint := ut.String(this.GetBlackboardData("spearPoint")); spearPoint != "" {
				attackTarget := this.target.GetAttackTarget()
				needTime := ut.Int32(this.GetBlackboardData("recycleSpearNeedTime"))
				currRecycleSpearTime := ut.Int32(this.GetBlackboardData("currRecycleSpearTime"))
				if currRecycleSpearTime >= needTime {
					if attackTarget == nil {
						this.target.SetFullAnger(float64(heroSkill.GetTarget())*0.01, true)
					}
					this.target.ChangeAttackTarget(nil)
					return FAILURE
				}
				this.SetBlackboardData("currRecycleSpearTime", currRecycleSpearTime+dt)
				if attackTarget != nil {
					hitTime := ut.Int32(this.GetBlackboardData("recycleSpearHitTime"))
					if currRecycleSpearTime >= hitTime {
						this.hitOne(attackTarget, 0, "_"+attackTarget.GetUID()+"_0", map[string]interface{}{"attackAmend": heroSkill.GetValue() * 0.01})
					}
				}
				return RUNNING
			}
		}
		if len(this.attackTargets) == 0 {
			// this.GetBlackboardData("attackTargets")
			rang := skill.GetTarget()
			targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
				return this.checkInRange(m, rang) && !m.IsBuild()
			})
			if len(targets) == 0 {
				return SUCCESS
			}
			sort.Slice(targets, func(i, j int) bool {
				a, b := targets[i], targets[j]
				aw, bw := helper.GetPointToPointDis(currPoint, a.GetPoint()), helper.GetPointToPointDis(currPoint, b.GetPoint())
				aw = aw*1000 + (999 - a.GetAttackIndex())
				bw = bw*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			this.attackTargets = append(this.attackTargets, targets[0])
		}
		attackTarget, attackAmend := this.attackTargets[0], skill.GetValue()
		// 张郃 伤害提高
		isZh := this.CheckHeroSkillID(heroSkill, hero.ZHANG_HE)
		if isZh {
			attackAmend += (attackAmend * heroSkill.GetValue() * 0.01)
		}
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": attackAmend, "getExtraDamge": func() int32 {
				if skill.IsExclusiveIntensify() { //投射还会额外造成当前区域自方单位数量x等级的伤害
					camp := this.target.GetCamp()
					cnt := int32(len(array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
						return m.GetCamp() == camp && m.IsPawn()
					})))
					// 韬略 最少多少个
					cnt = ut.MaxInt32(cnt, this.target.GetStrategyValue(30201))
					damage := ut.MinInt32(cnt, skill.GetParamsInt()) * this.target.GetEntity().GetLV()
					// 张郃 伤害提高
					if isZh {
						damage += ut.RoundInt32(float64(damage) * heroSkill.GetValue() * 0.01)
					}
					return damage
				}
				return 0
			},
			"onHit": func(damage int32, val int32) {
				if isQly { //秦良玉
					point := attackTarget.GetPoint()
					dir := ut.If(point.X-currPoint.X >= 0, -1, 1)
					ctrl.AddNoncombat("build_"+this.target.GetUID(), constant.SPEAR_PAWN_ID, 1, point, this.target.GetCamp(), this.target.GetOwner(), map[string]interface{}{"dir": dir})
					this.SetBlackboardData("spearthrowing", true)
					this.target.AddBuff(bufftype.BARB, this.target.GetUID(), 1) //加一个无矛的状态
					// 如果没死设置攻击目标
					if !attackTarget.IsDie() {
						this.target.ChangeAttackTarget(attackTarget)
					}
				} else if attackTarget.IsDie() && isZh { //张郃 给目标添加恐慌
					points := this.target.GetSearchRange().Search(attackTarget.GetPoint(), 1, true)
					uid, hasPanic := this.target.GetUID(), false
					arr := attackTarget.GetCanAttackRangeFighter(this.target.GetCanAttackFighters(), 2, heroSkill.Target, attackTarget.GetUID(), func(m g.IFighter) bool {
						return m.IsHasBuff(bufftype.STAND_SHIELD) || m.GetPawnType() == constant.PAWN_TYPE_MACHINE || m.IsFlag() //立盾，器械，军旗无法恐惧
					})
					for _, m := range arr {
						// 乱跑
						if p, ok := ctrl.SearchDiaupPoint(m.GetPoint(), points); ok {
							m.SetPoint(p)
							ctrl.UpdateFighterPointMap()
							hasPanic = true
						}
						// 添加恐慌buff
						m.AddBuff(bufftype.FEAR, uid, 1)
					}
					if hasPanic {
						this.target.AddRoundEndDelayTime(300)
					}
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 203 { //缴械 ===========================================================================================================
		if len(this.attackTargets) == 0 {
			at := this.target.GetAttackTarget()
			rang := this.target.GetEntity().GetAttackRange()
			if this.CheckHeroSkillID(heroSkill, hero.BAI_QI) { //白起优先缴械攻击力最高的目标
				targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
					return this.checkInRange(m, rang) && !m.IsBuild()
				})
				if len(targets) > 0 {
					sort.Slice(targets, func(i, j int) bool {
						a, b := targets[i], targets[j]
						aw, bw := a.GetActAttack(), b.GetActAttack()
						aw = aw*1000 + (999 - a.GetAttackIndex())
						bw = bw*1000 + (999 - b.GetAttackIndex())
						return aw > bw
					})
					at = targets[0]
				}
			}
			if at == nil || at.IsBuild() || at.IsHasBuff(bufftype.DESTROY_WEAPONS) { //如果当前目标上面已经有中毒效果了 就换一个目标
				at = array.Find(this.target.GetCanAttackFighters(), func(m g.IFighter) bool {
					return this.checkInRange(m, rang) && !m.IsBuild() && !m.IsHasBuff(bufftype.DESTROY_WEAPONS)
				})
				// 白起和周瑜 不管怎样都是要放的
				if (this.CheckHeroSkillID(heroSkill, hero.BAI_QI) || this.CheckHeroSkillID(heroSkill, hero.ZHOU_YU)) && at == nil {
					at = this.target.GetAttackTarget()
					if at == nil || at.IsBuild() {
						at = array.Find(this.target.GetCanAttackFighters(), func(m g.IFighter) bool { return this.checkInRange(m, rang) && !m.IsBuild() })
					}
				}
			}
			if at == nil {
				return SUCCESS
			}
			this.attackTargets = append(this.attackTargets, at)
		}
		attackTarget := this.attackTargets[0]
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": skill.GetValue(), "onHit": func(damage int32, val int32) {
				if damage >= 0 {
					uid := this.target.GetUID()
					camp := this.target.GetCamp()
					attackTarget.AddBuff(bufftype.DESTROY_WEAPONS, uid, skill.GetLv()) //给目标施加buff
					// 还会使目标1格内至多4个敌方缴械
					if skill.IsExclusiveIntensify() {
						arr := attackTarget.GetCanAttackPawnByRange(this.target.GetCanAttackFighters(), 1, 4, attackTarget.GetUID())
						for _, m := range arr {
							m.AddBuff(bufftype.DESTROY_WEAPONS, uid, skill.GetLv())
						}
					}
					// 白起
					if this.CheckHeroSkillID(heroSkill, hero.BAI_QI) {
						val := ut.RoundInt32(math.Max(0, float64(attackTarget.GetIgnoreBuffAttack(bufftype.DESTROY_WEAPONS)-attackTarget.GetActAttack())) * heroSkill.GetValue() * 0.01)
						if val > 0 {
							arr := attackTarget.GetCanAttackRangeFighter(ctrl.GetFighters(), heroSkill.GetParamsInt(), heroSkill.GetTarget(), "", func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
							for _, m := range arr {
								m.AddBuff(bufftype.GOD_WAR, uid, 1).Value = float64(val)
							}
						}
					} else if this.CheckHeroSkillID(heroSkill, hero.ZHOU_YU) { //周瑜
						arr := attackTarget.GetCanAttackRangeFighter(this.target.GetCanAttackFighters(), heroSkill.GetParamsInt(), heroSkill.GetTarget(), attackTarget.GetUID(), func(m g.IFighter) bool {
							return m.GetPawnType() == constant.PAWN_TYPE_MACHINE || m.IsFlag() //器械，军旗无法链索
						})
						arr = append(arr, attackTarget)
						for _, m := range arr {
							m.AddBuffValue(bufftype.WIRE_CHAIN, uid, float64(heroSkill.Value))
						}
					} else if this.CheckHeroSkillID(heroSkill, hero.LI_MU) { //李牧
						arr := this.target.GetCanAttackRangeFighter(ctrl.GetFighters(), this.target.GetAttackRange(), heroSkill.GetTarget(), "", func(m g.IFighter) bool { return m.GetCamp() != camp || m.IsFlag() })
						for _, m := range arr {
							m.AddBuffValue(bufftype.ANTICIPATION_DEFENSE, uid, heroSkill.GetValue())
						}
					}
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 216 { //旋风斩 ===========================================================================================================
		isLm := this.CheckHeroSkillID(heroSkill, hero.LV_MENG)
		// 吕蒙
		if isLm && int32(this.target.GetBuffValue(bufftype.CHECK_ABNEGATION)) < heroSkill.Target {
			needTime := int32(heroSkill.GetParamsFloat64() * 1000)
			if currAttackTime >= needTime {
				this.target.GetBuffOrAdd(bufftype.CHECK_ABNEGATION, this.target.GetUID()).Value += 1
				this.target.AddBuffValue(bufftype.ABNEGATION_SHIELD, this.target.GetUID(), 40)
				return FAILURE
			}
		} else {
			attackTarget := this.target.GetAttackTarget()
			if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
				return SUCCESS
			} else if len(this.attackTargets) == 0 {
				this.attackTargets = this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), skill.GetTarget(), 12, "")
				if len(this.attackTargets) == 0 { //如果获取不到额外的目标 就不释放技能
					return SUCCESS
				}
			}
			done, uid := true, this.target.GetUID()
			lastDamageAmend := skill.GetValue()
			for i, l := 0, len(this.attackTargets); i < l; i++ {
				at := this.attackTargets[i]
				if !this.attackOne(skill, currPoint, at, dt, 0, map[string]interface{}{
					"attackSegments":  i,
					"lastDamageAmend": lastDamageAmend,
					"onHit": func(damage int32, val int32) {
						if damage >= 0 {
							addRound := this.target.GetStrategyValue(33101) //加回合
							buff := at.AddBuff(bufftype.BLEED, uid, 1)      //给目标施加buff
							attack := ctrl.GetBaseAttackDamage(this.target, at, map[string]interface{}{"attackAmend": skill.GetParamsFloat64()})
							buff.SetValue(float64(attack))
							buff.AddRound(addRound)
							// 重伤buff
							buff2 := at.AddBuff(bufftype.SERIOUS_INJURY, uid, 1).SetRound(buff.Round)
							buff2.SetValue(0.2)
						}
					},
				}) {
					done = false
				}
			}
			if done {
				// 姜维 添加buff
				if this.CheckHeroSkillID(heroSkill, hero.JIANG_WEI) {
					buff := this.target.GetBuffOrAdd(bufftype.WISDOM_COURAGE, this.target.GetUID())
					buff.Value = math.Min(buff.Value+1, heroSkill.GetValue())
				} else if isLm { //吕蒙 克己加盾
					shield := float64(40 * len(this.attackTargets))
					if buff := this.target.GetBuff(bufftype.ABNEGATION_SHIELD); buff == nil || buff.Value < shield {
						this.target.AddBuffValue(bufftype.ABNEGATION_SHIELD, this.target.GetUID(), shield)
					}
				}
				return FAILURE
			}
		}
	} else if skillType == 219 { //乱舞 ===========================================================================================================
		if this.CheckHeroSkillID(heroSkill, hero.QIN_QIONG) { //秦琼
			attackTarget := this.target.GetAttackTarget()
			if currAttackTime == 0 {
				if this.target.IsHasBuff(bufftype.BEHEADED_GENERAL) {
					return SUCCESS //有斩将效果 就不再释放斩将
				}
				rang, attackRange := int32(4), this.target.GetAttackRange()
				targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
					return !m.IsDie() && this.checkInRange(m, rang) && !m.IsBuild() && (this.checkInRange(m, attackRange) || len(this.target.GetTargetCanAttackPoints(m)) > 0)
				})
				if len(targets) > 0 {
					sort.Slice(targets, func(i, j int) bool {
						a, b := targets[i], targets[j]
						aw, bw := ut.If(a.IsHero(), int32(1), 0), ut.If(b.IsHero(), int32(1), 0)
						aw = aw*1000 + a.GetActAttack()
						bw = bw*1000 + b.GetActAttack()
						aw = aw*100 + helper.GetPointToPointDis(currPoint, a.GetPoint())
						bw = bw*100 + helper.GetPointToPointDis(currPoint, b.GetPoint())
						aw = aw*1000 + (999 - a.GetAttackIndex())
						bw = bw*1000 + (999 - b.GetAttackIndex())
						return aw > bw
					})
					attackTarget = this.target.ChangeAttackTarget(targets[0])
					if !this.checkInAttackRange(attackTarget) {
						points := this.target.GetTargetCanAttackPoints(attackTarget)
						sort.Slice(points, func(i, j int) bool {
							a, b := points[i], points[j]
							return helper.GetPointDisSortVal(currPoint, a) > helper.GetPointDisSortVal(currPoint, b)
						})
						this.target.SetPoint(points[0])
					}
				} else if attackTarget == nil || !attackTarget.IsPawn() || !this.checkInAttackRange(attackTarget) {
					return SUCCESS
				}
			}
			attackAmend := 2 + this.target.GetStrategyValue(50018) //多的段数会提高伤害
			if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
				"attackAmend": attackAmend,
				"onHit": func(damage int32, val int32) {
					this.wriggleComplete(1, skill, heroSkill)
				},
			}) {
				return FAILURE
			}
		} else {
			if ut.Int32(this.GetTreeBlackboardData("batterCount")) == -1 {
				return SUCCESS //已经乱舞过了
			} else if len(this.attackTargets) == 0 {
				oPoint := currPoint.Clone()
				sr := new(astar.SearchRange).Init(ctrl.CheckIsBattleArea)
				rang, camp := skill.GetTarget(), this.target.GetCamp()
				maxCount, point := int32(0), ut.NewVec2(0, 0)
				// 先获取4格内的位置
				points := this.target.GetSearchRange().Search(oPoint, 4, true)
				for _, m := range points {
					if !m.Equals(oPoint) && ctrl.CheckHasFighter(m.X, m.Y) {
						continue
					}
					var cnt int32
					// 查找2格内的敌方目标
					ps := sr.Search(m, rang, false)
					for _, p := range ps {
						cnt += ctrl.GetFighterCountForCampByPoint(p, camp, true)
					}
					if cnt == 0 || cnt < maxCount || (cnt == maxCount && ctrl.GetRandom().Chance(50)) {
						continue
					}
					maxCount = cnt
					point.Set(m)
				}
				if maxCount > 0 {
					this.target.SetPoint(point)
				}
				this.attackTargets = this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), rang, 8, "")
				if len(this.attackTargets) == 0 { //如果获取不到额外的目标 就不释放技能
					this.target.SetPoint(oPoint)
					batterCount := ut.Int32(this.GetTreeBlackboardData("batterCount"))
					if batterCount > 0 {
						this.DeductSkillAnger()
					}
					this.wriggleComplete(batterCount, skill, heroSkill)
					return SUCCESS
				}
			}
			// 第一段突进
			params := ut.StringToFloats(skill.GetParamsString(), ",")
			if this.CheckHeroSkillID(heroSkill, hero.DIAN_WEI) {
				params = heroSkill.GetParamsFloats()
			}
			suddenTime := int32((params[0] + params[1]) * 1000)
			if currAttackTime >= suddenTime {
				tempCurrAttackTime := currAttackTime - suddenTime
				targetCount, done, count := len(this.attackTargets), false, int32(2)
				strategyCount := this.target.GetStrategyValue(50018)
				if heroSkill == nil {
					count += strategyCount
				} else if heroSkill.Id == hero.WEN_YUAN { //文鸳
					count = 1
				} else if heroSkill.Id == hero.DIAN_WEI { //典韦
					count += strategyCount
					if this.target.GetBuffValue(bufftype.COURAGEOUSLY) >= heroSkill.GetValue() {
						count += 1
					}
				}
				for i := int32(0); i < count; i++ {
					interval := 600 * i //攻击间隔
					if tempCurrAttackTime >= interval {
						ok, isEnd := true, i == count-1
						for ii := 0; ii < targetCount; ii++ {
							at := this.attackTargets[ii]
							if !this.attackOne(skill, currPoint, at, dt, i, map[string]interface{}{"lastDamageAmend": 0.35, "attackSegments": ii}) {
								ok = false
							}
						}
						if isEnd {
							done = ok
						}
					}
				}
				if done {
					batterCount := ut.Int32(this.GetTreeBlackboardData("batterCount")) + 1
					if this.CheckHeroSkillID(heroSkill, hero.WEN_YUAN) && batterCount < heroSkill.Target && ctrl.GetRandom().ChanceInt32(ut.If(batterCount <= (1+strategyCount), 100, heroSkill.Value)) { //文鸳
						ctrl.RemoveDieFighters()
						ctrl.UpdateFighterPointMap()
						this.target.CleanCanAttackTargets()
						this.target.SetFullAnger(1, false)
						this.target.CleanBlackboard("hitTargetAddAngerUids")
						this.SetTreeBlackboardData("isBatter", true)           //标记连击
						this.SetTreeBlackboardData("batterCount", batterCount) //记录连击次数
					} else {
						this.wriggleComplete(batterCount, skill, heroSkill)
					}
					return FAILURE
				}
			}
		}
	} else if skillType == 204 { //护盾 ===========================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil && len(this.target.GetCanAttackTargets()) > 0 {
			return SUCCESS
		} else if currAttackTime >= this.getNeedAttackTime(skill, currPoint, nil, "") {
			return FAILURE
		} else if currAttackTime >= skill.GetNeedHitTime() && !ut.Bool(this.GetBlackboardData("isHit")) {
			this.SetBlackboardData("isHit", true)
			camp, uid := this.target.GetCamp(), this.target.GetUID()
			arr := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool { return m.GetUID() != uid && m.GetCamp() == camp && m.IsPawn() })
			fighters := array.Filter(arr, func(m g.IFighter, _ int) bool { return helper.GetPointToPointDis(m.GetPoint(), currPoint) <= 5 })
			sort.Slice(fighters, func(i, j int) bool {
				a, b := fighters[i], fighters[j]
				aw := ut.RoundInt32((ut.If(a.IsHasBuffs(bufftype.SHIELD, bufftype.PROTECTION_SHIELD), 0.0, 1.0)+(1-a.GetHPRatio()))*100)*1000 + (999 - a.GetAttackIndex())
				bw := ut.RoundInt32((ut.If(b.IsHasBuffs(bufftype.SHIELD, bufftype.PROTECTION_SHIELD), 0.0, 1.0)+(1-b.GetHPRatio()))*100)*1000 + (999 - b.GetAttackIndex())
				return aw > bw
			})
			// 徐盛
			isXuSheng := this.CheckHeroSkillID(heroSkill, hero.XU_SHENG)
			// 额外护盾数量
			cnt := ut.If(skill.IsExclusiveIntensify(), int32(2), 1)
			cnt += this.target.GetStrategyValue(50006) //韬略
			fighters = fighters[0:ut.MinInt32(int32(len(fighters)), cnt)]
			fighters = append(fighters, this.target) //加上自己
			// 护盾效果提高
			buffId := ut.If(isXuSheng, int32(bufftype.PROTECTION_SHIELD), bufftype.SHIELD)
			addRound := this.target.GetStrategyValue(30602)
			for _, m := range fighters {
				m.RemoveMultiBuff(bufftype.SHIELD, bufftype.PROTECTION_SHIELD)
				// 给友方添加buff
				m.AddBuff(buffId, uid, skill.GetLv()).AddRound(addRound)
			}
			// 徐盛给同等数量的目标新增庇护效果
			if isXuSheng {
				arr = append(arr, this.target)
				sort.Slice(arr, func(i, j int) bool {
					a, b := arr[i], arr[j]
					aw := ut.If(a.IsHasBuff(bufftype.PROTECTION_NIE), int32(0), 1)*10 + ut.If(a.IsHasNegativeBuff(), int32(1), 0)
					aw = aw*1000 + ut.RoundInt32((1-a.GetHPRatio())*100)
					aw = aw*1000 + (999 - a.GetAttackIndex())
					bw := ut.If(b.IsHasBuff(bufftype.PROTECTION_NIE), int32(0), 1)*10 + ut.If(b.IsHasNegativeBuff(), int32(1), 0)
					bw = bw*1000 + ut.RoundInt32((1-b.GetHPRatio())*100)
					bw = bw*1000 + (999 - b.GetAttackIndex())
					return aw > bw
				})
				fighters = arr[0:ut.MinInt32(int32(len(arr)), heroSkill.GetTarget())]
				for _, m := range fighters {
					m.AddBuff(bufftype.PROTECTION_NIE, uid, 1).Value = heroSkill.GetValue()
					m.RemoveAllDebuff() //移除所有负面buff
				}
			}
		}
	} else if skillType == 205 { //立盾 ===========================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || attackTarget.IsBuild() {
			return SUCCESS
		} else if currAttackTime >= this.getNeedAttackTime(skill, currPoint, nil, "") {
			lv := skill.GetLv()
			if skill.IsExclusiveIntensify() { //四周每有一个敌方目标都多减少5%伤害
				cnt := int32(len(array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
					return helper.GetPointToPointDis(currPoint, m.GetPoint()) == 1 && !m.IsBuild()
				})))
				lv += ut.MinInt32(cnt, 4)
			}
			// 直接添加buff
			buff := this.target.AddBuff(bufftype.STAND_SHIELD, this.target.GetUID(), ut.MinInt32(10, lv))
			// 韬略 加回合
			buff.AddRound(this.target.GetStrategyValue(30701))
			return FAILURE
		}
	} else if skillType == 212 { //冲锋 ================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 {
			rang, attackRange := skill.GetTarget(), this.target.GetAttackRange()
			targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
				return !m.IsDie() && !m.IsBuild() && this.checkInRange(m, rang) && (this.checkInRange(m, attackRange) || len(this.target.GetTargetCanAttackPoints(m)) > 0)
			})
			if len(targets) > 0 {
				sort.Slice(targets, func(i, j int) bool {
					a, b := targets[i], targets[j]
					aw, bw := helper.GetPointToPointDis(currPoint, a.GetPoint()), helper.GetPointToPointDis(currPoint, b.GetPoint())
					aw = aw*1000 + (999 - a.GetAttackIndex())
					bw = bw*1000 + (999 - b.GetAttackIndex())
					return aw > bw
				})
				attackTarget = this.target.ChangeAttackTarget(targets[0])
				if !this.checkInAttackRange(attackTarget) {
					points := this.target.GetTargetCanAttackPoints(attackTarget)
					sort.Slice(points, func(i, j int) bool {
						a, b := points[i], points[j]
						return helper.GetPointDisSortVal(currPoint, a) > helper.GetPointDisSortVal(currPoint, b)
					})
					this.target.SetPoint(points[0])
				}
			} else if attackTarget == nil || !attackTarget.IsPawn() || !this.checkInAttackRange(attackTarget) {
				return SUCCESS
			}
		}
		add := float64(this.target.GetStrategyValue(30801)) * 0.01 //韬略
		attackAmend := skill.GetValue() + add
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": attackAmend,
			"onHit": func(damage int32, val int32) { //格挡下一次攻击
				var lv int32 = 1
				// 韬略加格挡次数
				if s := this.target.GetStrategyBuff(30802); s != nil {
					lv += s.Value
				}
				// // 尉迟恭 加1次
				// if this.CheckHeroSkillID(heroSkill, hero.MENG_TIAN) {
				// 	lv += 1
				// }
				buffId := GetBuffIDBySkin(bufftype.PARRY, this.target.GetEntity().GetViewID())
				this.target.AddBuff(buffId, this.target.GetUID(), lv) //直接添加buff
				// 张飞
				if this.CheckHeroSkillID(heroSkill, hero.ZHANG_FEI) {
					attackAmend2 := attackAmend * heroSkill.GetValue() * 0.01
					uid := this.target.GetUID()
					attackTarget.AddBuff(bufftype.TIMIDITY, uid, 1) //添加无法移动的buff
					arr := this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 2, heroSkill.Target, attackTarget.GetUID())
					for i, m := range arr {
						this.hitOne(m, 0, "_"+m.GetUID()+"_0", map[string]interface{}{"attackAmend": attackAmend2, "attackSegments": i, "getExtraDamge": func() int32 {
							if skill.IsExclusiveIntensify() { //冲锋时还会对目标造成自身最大生命5%的额外伤害
								return ut.RoundInt32(float64(this.target.GetEntity().GetMaxHP()) * 0.05)
							}
							return 0
						}})
						m.AddBuff(bufftype.TIMIDITY, uid, 1) //添加无法移动的buff
					}
				}
			},
			"getExtraDamge": func() int32 {
				if skill.IsExclusiveIntensify() { //冲锋时还会对目标造成自身最大生命5%的额外伤害
					return ut.RoundInt32(float64(this.target.GetEntity().GetMaxHP()) * 0.05)
				}
				return 0
			},
		}) {
			return FAILURE
		}
	} else if skillType == 215 { //震地之锤 ================================================================================================
		attackTarget, rang, cnt := this.target.GetAttackTarget(), int32(2), int32(12)
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			this.attackTargets = this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), rang, cnt, this.target.GetUID())
			if len(this.attackTargets) == 0 {
				return SUCCESS
			}
		}
		done, count := true, len(this.attackTargets)
		// 韬略 加回合
		addRound := this.target.GetStrategyValue(30901) //加回合
		dedAnger := this.target.GetStrategyValue(30902) //减怒气
		// 额外的目标
		for i := 0; i < count; i++ {
			t := this.attackTargets[i]
			if !this.attackOne(skill, currPoint, t, dt, 0, map[string]interface{}{
				"attackSegments":  i,
				"lastDamageAmend": skill.GetValue(),
				"notAddAnger":     true,
				"onHit": func(damage int32, val int32) {
					if damage >= 0 {
						if t.IsHasAnger() {
							// 添加沉默buff
							buff := t.AddBuff(bufftype.SILENCE, this.target.GetUID(), 1)
							buff.AddRound(addRound)
							// 减伤怒气
							if dedAnger > 0 {
								t.DeductAnger(ut.RoundInt32(float64(t.GetCurAnger()) * float64(dedAnger) * 0.01))
							}
						}
					}
				},
			}) {
				done = false
			}
		}
		if done {
			// 给友军回怒
			if skill.IsExclusiveIntensify() {
				camp := this.target.GetCamp()
				fighters := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
					return m.GetCamp() == camp && m.IsHasAnger() && m.GetAngerRatio() < 0.5
				})
				friends := this.target.GetCanAttackPawnByRange(fighters, rang, cnt, this.target.GetUID())
				for _, m := range friends {
					m.AddAnger(1)
				}
			}
			return FAILURE
		}
	} else if skillType == 218 { //守护之剑 ================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || attackTarget.IsBuild() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": skill.GetValue(),
			"onHit": func(damage int32, val int32) {
				if val > 0 {
					buffId := GetBuffIDBySkin(bufftype.RODELERO_SHIELD, this.target.GetEntity().GetViewID())
					this.target.AddBuffValue(buffId, this.target.GetUID(), math.Max(1, math.Round(float64(val)*skill.GetParamsFloat64())))
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 221 { //旋转飞斧 ================================================================================================
		if len(this.attackTargets) == 0 {
			rang := skill.GetTarget()
			targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool { return this.checkInRange(m, rang) && !m.IsBuild() })
			targetsLen := len(targets)
			if targetsLen == 0 {
				return SUCCESS
			}
			target := targets[ctrl.GetRandom().Get(0, targetsLen-1)]
			this.attackTargets = append(this.attackTargets, target)
			this.SetBlackboardData("catapult_index", len(this.attackTargets)-1)
			// 距离
			this.SetBlackboardData("target_dis", helper.GetPointToPointDis(currPoint, target.GetPoint()))
		}
		catapultIndex := ut.Int32(this.GetBlackboardData("catapult_index"))
		attackTarget, isExclusiveIntensify := this.attackTargets[catapultIndex], skill.IsExclusiveIntensify()
		targetDis := ut.Int32(this.GetBlackboardData("target_dis"))
		if targetDis > 1 {
			throwTime := int32(skill.GetParamsFloat64() * 1000)
			if this.CheckHeroSkillID(heroSkill, hero.MENG_HUO) { //孟获
				throwTime = int32(heroSkill.GetParamsFloat64() * 1000)
			}
			if currAttackTime >= throwTime {
				point := currPoint
				if catapultIndex >= 1 && len(this.attackTargets) >= 2 {
					point = this.attackTargets[catapultIndex-1].GetPoint()
				}
				if this.attackOne(skill, point, attackTarget, dt, catapultIndex, map[string]interface{}{"attackAmend": skill.GetValue()}) {
					catapultTimes := int32(ut.If(isExclusiveIntensify, 2, 1)) + this.target.GetStrategyValue(34001) //弹射次数
					if catapultIndex >= catapultTimes {
						return FAILURE
					}
					// 弹射3格内最近的一个目标
					atPoint, atUid := attackTarget.GetPoint(), attackTarget.GetUID()
					arr := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
						return atUid != m.GetUID() && !m.IsDie() && helper.GetPointToPointDis(atPoint, m.GetPoint()) <= 3
					})
					if len(arr) == 0 {
						return FAILURE
					}
					sort.Slice(arr, func(i, j int) bool {
						a, b := arr[i], arr[j]
						aw := helper.GetPointToPointDis(atPoint, a.GetPoint())*10000 + a.GetAttackIndex()
						bw := helper.GetPointToPointDis(atPoint, b.GetPoint())*10000 + b.GetAttackIndex()
						return aw < bw
					})
					this.attackTargets = append(this.attackTargets, arr[0])
					this.SetBlackboardData("catapult_index", len(this.attackTargets)-1)
				}
			}
		} else { //变为盾击
			shieldBashSkill := this.target.GetSkillByType(119)
			if this.attackOne(shieldBashSkill, currPoint, attackTarget, dt, 0, map[string]interface{}{
				"attackAmend": shieldBashSkill.GetValue(),
				"onHit": func(damage int32, val int32) {
					odds := shieldBashSkill.GetParamsInt() + this.target.GetStrategyParams(34001)
					if isExclusiveIntensify {
						odds += 20
					}
					if ctrl.GetRandom().ChanceInt32(odds) {
						attackTarget.AddBuff(bufftype.DIZZINESS, this.target.GetUID(), 1)
					}
				},
			}) {
				return FAILURE
			}
		}
	} else if skillType == 206 { //散射 ================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || attackTarget.IsBuild() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			count := ut.If(skill.IsExclusiveIntensify(), 4, skill.GetParamsInt())
			// 韬略 额外射一箭
			if ctrl.GetRandom().ChanceInt32(this.target.GetStrategyValue(31001)) {
				count += 1
			}
			// 黄忠
			if this.CheckHeroSkillID(heroSkill, hero.HUANG_ZHONG) {
				targets, overlaps := this.target.GetCanAttackRangeTargets(currPoint, this.target.GetCanAttackFighters(), 6, attackTarget.GetUID(), nil)
				targets = append(targets, overlaps...)
				sort.Slice(targets, func(i, j int) bool {
					a, b := targets[i], targets[j]
					aw := int32((1.0 - a.GetHPRatio()) * 100.0)
					bw := int32((1.0 - b.GetHPRatio()) * 100.0)
					aw = aw*1000 + (999 - a.GetAttackIndex())
					bw = bw*1000 + (999 - b.GetAttackIndex())
					return aw > bw
				})
				this.attackTargets = targets[:ut.MinInt32(int32(len(targets)), count)]
			} else {
				this.attackTargets = this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 6, count, attackTarget.GetUID())
			}
			if len(this.attackTargets) == 0 { //如果获取不到额外的目标 就不释放技能
				return SUCCESS
			}
		}
		// 第一个
		done := this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{})
		// 额外的目标
		for i, l := 0, len(this.attackTargets); i < l; i++ {
			if !this.attackOne(skill, currPoint, this.attackTargets[i], dt, 0, map[string]interface{}{"lastDamageAmend": skill.GetValue(), "attackSegments": 1}) {
				done = false
			}
		}
		if done {
			return FAILURE
		}
	} else if skillType == 207 { //连射 ===========================================================================================================
		attackTarget := this.target.GetAttackTarget()
		done := false
		if this.CheckHeroSkillID(heroSkill, hero.LIU_BEI) { //刘备
			atCount := int32(len(this.attackTargets))
			if atCount == 0 {
				camp, attackRange := this.target.GetCamp(), this.target.GetAttackRange()
				fighters := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
					return m.GetCamp() == camp && m.IsPawn() && helper.GetPointToPointDis(m.GetPoint(), currPoint) <= attackRange && !m.IsFullHP()
				})
				sort.Slice(fighters, func(i, j int) bool {
					a, b := fighters[i], fighters[j]
					aw := ut.RoundInt32((1-a.GetHPRatio())*100)*1000 + (999 - a.GetAttackIndex())
					bw := ut.RoundInt32((1-b.GetHPRatio())*100)*1000 + (999 - b.GetAttackIndex())
					return aw > bw
				})
				count := this.getLianSheCount(skill)
				if skill.IsExclusiveIntensify() {
					count += 1
				}
				this.attackTargets = fighters[0:ut.MinInt32(int32(len(fighters)), count)]
				atCount = int32(len(this.attackTargets))
				if atCount == 0 {
					return SUCCESS
				} else if cnt := count - atCount; cnt > 0 {
					for i := 0; i < int(cnt); i++ {
						this.attackTargets = append(this.attackTargets, this.attackTargets[ut.LoopValue(i, int(atCount))])
					}
				}
			}
			needTime := heroSkill.GetParamsInt()
			if currAttackTime >= needTime {
				tempCurrAttackTime := currAttackTime - needTime
				isMaxLv := this.target.GetEntity().IsMaxLv()
				for i := int32(0); i < atCount; i++ {
					interval := 270 * i //射击间隔
					if tempCurrAttackTime >= interval {
						at := this.attackTargets[i]
						ok := this.attackOne(skill, currPoint, at, dt, i, map[string]interface{}{
							"attackSegments": i,
							"isHeal":         true,
							"healVal": func() int32 {
								val := float64(at.GetMaxHp()) * heroSkill.GetValue() * 0.01
								if isMaxLv {
									val += val * float64(heroSkill.Target) * 0.01
								}
								return ut.RoundInt32(val)
							},
						})
						if i == atCount-1 {
							done = ok
						}
					}
				}
			}
		} else if this.CheckHeroSkillID(heroSkill, hero.LIU_CHONG) { //刘宠
			if attackTarget == nil || attackTarget.IsBuild() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
				return SUCCESS
			}
			count, accurateDelayTime := this.getLianSheCount(skill), ut.Int32(this.GetBlackboardData("accurateDelayTime"))
			for i := int32(0); i < count; i++ {
				interval := 500*i + accurateDelayTime //射击间隔
				if currAttackTime >= interval {
					currTime := currAttackTime - interval
					accurateKey := "lc_accurate_" + ut.Itoa(i)
					if currTime >= 450 && count < heroSkill.Target && !ut.Bool(this.GetBlackboardData(accurateKey)) {
						this.SetBlackboardData(accurateKey, true)
						if ctrl.GetRandom().ChanceInt32(heroSkill.Value) {
							this.SetBlackboardData("accurateDelayTime", accurateDelayTime+100)
							count += 1
							this.SetBlackboardData("lianSheCount", count)
						}
					}
					if ok := this.attackOne(skill, currPoint, attackTarget, dt, i, map[string]interface{}{
						"attackSegments": i,
						"attackAmend":    ut.If(i == 0, 1, skill.GetValue()),
						"notAddAnger":    i != 0, //后面的箭不涨怒气了
					}); i == count-1 {
						done = ok
					}
				}
			}
		} else if this.CheckHeroSkillID(heroSkill, hero.HUANG_YUEYING) { //黄月英
			return SUCCESS
		} else {
			if attackTarget == nil || attackTarget.IsBuild() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
				return SUCCESS
			}
			count := this.getLianSheCount(skill)
			for i := int32(0); i < count; i++ {
				interval := 500 * i //射击间隔
				if currAttackTime >= interval {
					if ok := this.attackOne(skill, currPoint, attackTarget, dt, i, map[string]interface{}{
						"attackSegments": i,
						"attackAmend":    ut.If(i == 0, 1, skill.GetValue()),
						"notAddAnger":    i != 0, //后面的箭不涨怒气了
					}); i == count-1 {
						done = ok
					}
				}
			}
		}
		if done {
			return FAILURE
		}
	} else if skillType == 210 { //毒箭 ===================================================================================================
		if len(this.attackTargets) == 0 {
			rang := this.target.GetEntity().GetAttackRange()
			at := this.target.GetAttackTarget()
			if this.CheckHeroSkillID(heroSkill, hero.LI_RU) { //李儒
				if at == nil || at.IsBuild() {
					return SUCCESS
				} else if at.IsHasBuff(bufftype.POISONED_WINE) {
					at = array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool {
						return this.checkInRange(m.Target, rang) && !m.Target.IsBuild() && !m.Target.IsHasBuff(bufftype.POISONED_WINE)
					}).Target
				}
			} else if at == nil || at.IsBuild() || at.IsHasBuff(bufftype.POISONING_MAX_HP) { //如果当前目标上面已经有中毒效果了 就换一个目标
				at = array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool {
					return this.checkInRange(m.Target, rang) && !m.Target.IsBuild() && !m.Target.IsHasBuff(bufftype.POISONING_MAX_HP)
				}).Target
				if at == nil {
					at = this.target.GetAttackTarget()
					if at == nil || at.IsBuild() {
						at = array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool { return this.checkInRange(m.Target, rang) && !m.Target.IsBuild() }).Target
					}
				}
			}
			if at == nil {
				return SUCCESS
			}
			this.attackTargets = append(this.attackTargets, at)
		}
		attackTarget := this.attackTargets[0]
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"onHit": func(damage int32, val int32) {
				if damage >= 0 {
					uid, lv := this.target.GetUID(), skill.GetLv()
					// 韬略
					if this.target.GetStrategyValue(31202) > 0 {
						lv += 100
					}
					round := attackTarget.AddBuff(bufftype.POISONING_MAX_HP, uid, lv).Round           //直接添加中毒buff
					attackTarget.AddBuff(bufftype.SERIOUS_INJURY, uid, skill.GetLv()).SetRound(round) //重伤buff
					// 专属 10x自身等级的概率 添加 麻痹buff
					if skill.IsExclusiveIntensify() && ctrl.GetRandom().ChanceInt32(10*this.target.GetLV()) {
						// 如果已经麻痹 则升级为易损
						if attackTarget.IsHasBuffs(bufftype.PARALYSIS, bufftype.PARALYSIS_UP) {
							attackTarget.RemoveMultiBuff(bufftype.PARALYSIS)
							attackTarget.AddBuff(bufftype.PARALYSIS_UP, uid, 1) //添加易损
						} else {
							attackTarget.AddBuff(bufftype.PARALYSIS, uid, 1)
						}
					}
					// 王异 毒雾
					if this.CheckHeroSkillID(heroSkill, hero.WANG_YI) {
						val := heroSkill.GetValue()
						arr := attackTarget.GetCanAttackPawnByRange(this.target.GetCanAttackFighters(), 2, 8, "")
						for _, m := range arr {
							m.ChangeAttackTarget(nil)
							m.AddBuff(bufftype.CHAOS, uid, 1).SetValue(val)
						}
					} else if this.CheckHeroSkillID(heroSkill, hero.LI_RU) { //李儒 鸩毒
						attackTarget.AddBuff(bufftype.POISONED_WINE, uid, heroSkill.Value-9)
					}
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 214 { //鼓舞 ===================================================================================================
		if this.CheckHeroSkillID(heroSkill, hero.YANG_YOUJI) { //养由基
			uid := "pet_" + this.target.GetUID()
			// 先看宠物是否存在
			if this.tempNumber1 == 0 {
				pet := array.Find(ctrl.GetFighters(), func(m g.IFighter) bool { return m.GetUID() == uid })
				if pet != nil {
					this.attackTargets = append(this.attackTargets, pet)
					this.tempNumber1 = this.getNeedAttackTime(skill, currPoint, pet, "")
				} else {
					arr := heroSkill.GetParamsFloats()
					this.tempNumber1 = int32(arr[0] * 1000)
					this.tempNumber2 = int32(arr[1] * 1000)
				}
			}
			needAttackTime := this.tempNumber1
			if currAttackTime >= needAttackTime {
				return FAILURE
			}
			// 存在就加血 不存在就召唤
			isFeed := len(this.attackTargets) > 0
			needHitTime := this.tempNumber2
			if isFeed {
				needHitTime = needAttackTime - dt
			}
			if currAttackTime >= needHitTime && !ut.Bool(this.GetBlackboardData("isHit")) {
				this.SetBlackboardData("isHit", true)
				if isFeed { //投喂食物
					pet := this.attackTargets[0]
					// 加30%血
					maxHp := pet.GetMaxHp()
					heal := ut.RoundInt32(float64(maxHp) * 0.3)
					// 专属装备效果：每次投喂食物会使其攻击力+2%，最大生命值+2%，至多叠加[20-50]次
					if skill.IsExclusiveIntensify() {
						buff := pet.GetBuffOrAdd(bufftype.FEED_INTENSIFY, this.target.GetUID())
						if buff.Value < float64(heroSkill.Target) {
							buff.Value += 1
							heal += ut.MaxInt32(0, pet.GetMaxHp()-maxHp)
						}
					}
					pet.OnHeal(heal, false)
				} else { //召唤
					lv := this.target.GetLV()
					if sv := constant.SUMMON_LV[lv]; sv > 0 {
						lv = sv
					}
					// 在四周找一下空位
					target, ok := ctrl.SearchIdlePoint(currPoint, this.target.GetAttackRange())
					if !ok {
						target = currPoint
					}
					petId := this.target.GetEntity().GetPetId()
					if petId == 0 {
						petId = constant.DEFAULT_PET_ID //默认野猪
					}
					pet := ctrl.AddPetPawn(uid, petId, lv, target, this.target.GetCamp(), this.target.GetOwner())
					if val := this.target.GetBuffValue(bufftype.FEED_INTENSIFY_RECORD); val > 0 {
						pet.AddBuffValue(bufftype.FEED_INTENSIFY, this.target.GetUID(), val)
					}
				}
			}
		} else if this.CheckHeroSkillID(heroSkill, hero.SUN_QUAN) {
			if len(this.attackTargets) == 0 {
				camp := this.target.GetCamp()
				rang := this.target.GetAttackRange()
				this.attackTargets = array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
					return m.GetCamp() == camp && m.IsPawn() && m.GetPawnType() != constant.PAWN_TYPE_MACHINE && helper.GetPointToPointDis(m.GetPoint(), currPoint) <= rang
				})
				if len(this.attackTargets) == 0 {
					return SUCCESS
				}
			}
			needAttackTime := this.getNeedAttackTime(skill, currPoint, nil, "")
			if currAttackTime >= needAttackTime && !ut.Bool(this.GetBlackboardData("isHit")) {
				fighters := this.attackTargets
				maxAttackFighterUids := ""
				sort.Slice(fighters, func(i, j int) bool {
					a, b := fighters[i], fighters[j]
					aw, bw := a.GetActAttack(), b.GetActAttack()
					if aw == bw {
						aw, bw = aw-a.GetAttackIndex(), bw-b.GetAttackIndex()
					}
					return aw > bw
				})
				cnt := 2 + this.target.GetLV()
				for i, l := 0, len(fighters); i < int(cnt) && i < l; i++ {
					maxAttackFighterUids += fighters[i].GetUID() + "|"
				}
				this.SetBlackboardData("isHit", true)
				this.SetBlackboardData("maxAttackFighterUids", maxAttackFighterUids)
			}
			if currAttackTime >= needAttackTime+int32(heroSkill.GetParamsFloat64()*1000) {
				maxAttackFighterUids := ut.String(this.GetBlackboardData("maxAttackFighterUids"))
				uid := this.target.GetUID()
				isIntensify := skill.IsExclusiveIntensify()
				heal := 1 + this.target.GetLV() + this.target.GetStrategyValue(31302)
				addRound := this.target.GetStrategyValue(50031)
				for _, m := range this.attackTargets {
					// 添加鼓舞buff
					round := m.AddBuff(bufftype.INSPIRE, uid, skill.GetLv()).AddRound(addRound).Round
					// 添加贤君buff
					if strings.Contains(maxAttackFighterUids, m.GetUID()) {
						m.AddBuffValue(bufftype.WORTHY_MONARCH, uid, heroSkill.GetValue()).SetRound(round)
						// 恢复怒气
						if isIntensify && m.GetUID() != uid {
							m.SetFullAnger(0.5, true)
						}
					}
					// 还会额外为友军回复(1+猎人等级)的生命
					if isIntensify && !m.IsFullHP() {
						m.OnHeal(heal, false)
					}
				}
				return FAILURE
			}
		} else if currAttackTime >= this.getNeedAttackTime(skill, currPoint, nil, "") {
			camp, uid := this.target.GetCamp(), this.target.GetUID()
			rang := this.target.GetAttackRange()
			fighters := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
				return m.GetCamp() == camp && m.IsPawn() && m.GetPawnType() != constant.PAWN_TYPE_MACHINE && helper.GetPointToPointDis(m.GetPoint(), currPoint) <= rang
			})
			isIntensify := skill.IsExclusiveIntensify()
			heal := 1 + this.target.GetLV() + this.target.GetStrategyValue(31302)
			addRound := this.target.GetStrategyValue(50031)
			isLhy, isMaxLv := this.CheckHeroSkillID(heroSkill, hero.LIANG_HONGYU), this.target.GetEntity().IsMaxLv()
			for _, m := range fighters {
				// 直接添加buff
				round := m.AddBuff(bufftype.INSPIRE, uid, skill.GetLv()).AddRound(addRound).Round
				// 梁红玉 添加减伤
				if isLhy {
					val := heroSkill.GetValue()
					if isMaxLv {
						val *= 2 //满级效果翻倍
					}
					m.AddBuffValue(bufftype.THUNDERS_DEFENSE, uid, val).SetRound(round)
				}
				if isIntensify {
					// 还会额外为友军回复(1+猎人等级)的生命
					if !m.IsFullHP() {
						m.OnHeal(heal, false)
					}
					// 梁红玉 添加恢复
					if isLhy {
						m.AddBuff(bufftype.THUNDERS_RECOVER, uid, 1).SetRound(round)
					}
				}
			}
			return FAILURE
		}
	} else if skillType == 112 { //满弦 ===================================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if ut.Bool(this.GetBlackboardData("isAttackFinish")) {
			return FAILURE
		}
		var dis int32
		if targetDis := this.GetBlackboardData("targetDis"); targetDis != nil {
			dis = ut.Int32(targetDis)
		} else {
			// 韬略 总是按最远距离计算
			if this.target.IsHasStrategys(50015) {
				dis = this.target.GetAttackRange()
			} else {
				dis = this.target.GetMinDis(attackTarget)
			}
			this.SetBlackboardData("targetDis", dis)
			// 曹休
			if !attackTarget.IsBuild() && this.CheckHeroSkillID(heroSkill, hero.CAO_XIU) && dis == this.target.GetAttackRange() {
				arr := heroSkill.GetParamsFloats()
				this.getNeedAttackTimeForBullet(int32(arr[0]*1000), int32(arr[1]*1000), currPoint, attackTarget.GetPoint(), "")
			} else {
				this.getNeedAttackTime(skill, currPoint, attackTarget, "")
			}
		}
		needAttackTime := this.getNeedAttackTime(skill, currPoint, attackTarget, "")
		if currAttackTime >= needAttackTime {
			if skill.IsExclusiveIntensify() && this.isCanRepel(attackTarget) { //需要击飞
				if len(this.dashArray) == 0 {
					actDis := helper.GetPointToPointDis(currPoint, attackTarget.GetPoint())
					if actDis < this.target.GetAttackRange() {
						if p := attackTarget.GetCanByReqelPoint(currPoint, 1); p != nil {
							attackTarget.SetPoint(p)
							ctrl.UpdateFighterPointMap()
							this.dashArray = []*DashData{{Point: currPoint, Time: actDis}}
							// 韬略 限制移动
							if this.target.GetStrategyValue(31401) > 0 {
								attackTarget.AddBuff(bufftype.IRREMOVABILITY, this.target.GetUID(), 1)
							}
						}
					}
				}
				if len(this.dashArray) > 0 && !this.diaupOne(attackTarget, dt, 300) {
					return RUNNING
				}
			}
			// 曹休
			lastDamageAmend := 1.0
			if this.CheckHeroSkillID(heroSkill, hero.CAO_XIU) && dis == this.target.GetAttackRange() {
				lastDamageAmend += heroSkill.GetValue() * 0.01
			}
			this.hitOne(attackTarget, 0, "_"+attackTarget.GetUID()+"_0", map[string]interface{}{
				"attackType":      "attack",
				"attackAmend":     1 + skill.GetValue()*float64(dis),
				"lastDamageAmend": lastDamageAmend,
			})
			this.SetBlackboardData("isAttackFinish", true)
		}
	} else if skillType == 220 { //火箭 ===================================================================================================
		if len(this.attackTargets) == 0 {
			at := this.target.GetAttackTarget()
			if at == nil || at.IsBuild() || at.IsBoss() || ctrl.CheckHasFighterById(at.GetPoint().X, at.GetPoint().Y, constant.FIRE_PAWN_ID) { //如果当前目标位置已经有火了 就换一个目标
				rang := skill.GetTarget()
				at = array.Find(this.target.GetCanAttackTargets(), func(m g.CanAttackTargetInfo) bool {
					return this.checkInRange(m.Target, rang) && !m.Target.IsBuild() && !m.Target.IsBoss() && !ctrl.CheckHasFighterById(m.Point.X, m.Point.Y, constant.FIRE_PAWN_ID)
				}).Target
			}
			if at == nil {
				return SUCCESS
			}
			this.attackTargets = append(this.attackTargets, at)
		}
		attackTarget := this.attackTargets[0]
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"onHit": func(_ int32, val int32) {
				point, camp, uid := attackTarget.GetPoint(), this.target.GetCamp(), this.target.GetUID()
				damage := int32(skill.GetValue()) + this.target.GetStrategyValue(33002)
				maxLv := 3 + skill.GetIntensifyType() + this.target.GetStrategyValue(33001)
				ctrl.AddNoncombat("build_"+point.ID()+"_"+uid, constant.FIRE_PAWN_ID, 1, point, camp, this.target.GetOwner(), map[string]interface{}{
					"hp": []int32{damage, maxLv},
				})
				// 陆逊 连营
				if this.CheckHeroSkillID(heroSkill, hero.LU_XUN) {
					odds, random, targets := heroSkill.Value, ctrl.GetRandom(), this.target.GetCanAttackFighters()
					fighters := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool { return m.GetID() == constant.FIRE_PAWN_ID && m.GetCamp() == camp })
					for _, m := range fighters {
						if random.ChanceInt32(odds) {
							dis, p := int32(10000), m.GetPoint()
							firePoint := ut.NewVec2(0, 0)
							for _, f := range targets {
								d := helper.GetPointToPointDis(f.GetPoint(), p)
								if d > 0 && d < dis {
									np := p.Add(ctrl.NormalizeVec2(f.GetPoint().Sub(p)))
									if ctrl.CheckIsBattleArea(np.X, np.Y) && !ctrl.CheckHasFighterById(np.X, np.Y, constant.FIRE_PAWN_ID) && ctrl.GetFighterCountForCampByPoint(np, camp, false) == 0 {
										firePoint.Set(np)
										dis = d
									}
								}
							}
							if dis != 10000 {
								ctrl.AddNoncombat("build_"+firePoint.ID()+"_"+uid, constant.FIRE_PAWN_ID, 1, firePoint, camp, this.target.GetOwner(), map[string]interface{}{
									"hp": []int32{damage, maxLv},
								})
							}
						}
					}
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 208 { //跳斩 ===================================================================================================
		attackTarget := this.target.GetAttackTarget()
		rang, attackRange := skill.GetTarget(), this.target.GetAttackRange()
		if currAttackTime == 0 {
			rang += this.target.GetStrategyValue(50017)
			targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
				return !m.IsDie() && this.checkInRange(m, rang) && !m.IsBuild() && (this.checkInRange(m, attackRange) || len(this.target.GetTargetCanAttackPoints(m)) > 0)
			})
			var target g.IFighter = nil
			if len(targets) > 0 {
				minw, random := int32(-1), ctrl.GetRandom()
				for _, m := range targets {
					w := (10000+m.GetCurHp())*10 + random.GetInt32(0, 9)
					if minw == -1 || w < minw {
						minw = w
						target = m
					}
				}
				attackTarget = this.target.ChangeAttackTarget(target)
			} else if attackTarget == nil || attackTarget.IsDie() || !attackTarget.IsPawn() || helper.GetPointToPointDis(attackTarget.GetPoint(), currPoint) > rang {
				attackTarget = nil
				target = nil
			} else {
				target = array.Find(this.target.GetCanAttackFighters(), func(m g.IFighter) bool { return m.GetUID() == attackTarget.GetUID() })
				attackTarget = target
			}
			if target != nil {
				points := this.target.GetTargetCanAttackPoints(target)
				if len(points) > 0 && !this.checkInAttackRange(target) {
					sort.Slice(points, func(i, j int) bool {
						a, b := points[i], points[j]
						return helper.GetPointDisSortVal(currPoint, a) > helper.GetPointDisSortVal(currPoint, b)
					})
					this.target.SetPoint(points[0])
				}
			}
		}
		if attackTarget == nil {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": skill.GetValue(),
			"onHit": func(damage int32, val int32) {
				// 给其他剑骑兵回怒 以及增加伤害
				if jumpslashDmg := this.target.GetBuffValue(bufftype.CHECK_JUMPSLASH_ADD_ANGER); jumpslashDmg > 0 {
					cmap, uid, armyUid := this.target.GetCamp(), this.target.GetUID(), this.target.GetArmyUID()
					arr := array.Filter(ctrl.GetFighters(), func(m g.IFighter, _ int) bool {
						return uid != m.GetUID() && m.GetCamp() == cmap && m.GetID() == 3401 && m.GetArmyUID() == armyUid && !m.IsDie()
					})
					if len(arr) > 0 {
						sort.Slice(arr, func(i, j int) bool { return arr[i].GetCurAnger() < arr[j].GetCurAnger() })
						if fighter := arr[0]; !fighter.IsCanUseSkill() {
							// 回怒
							fighter.AddAnger(1)
							// 添加buff
							buff := fighter.GetBuffOrAdd(bufftype.JUMPSLASH_DAMAGE, uid)
							buff.Value += jumpslashDmg
						}
					}
				}
				if !attackTarget.IsDie() {
					if this.CheckHeroSkillID(heroSkill, hero.ZHANG_LIAO) {
						this.target.AddRoundEndActionTime(480, 720)
					}
				} else if skill.IsExclusiveIntensify() { //被跳斩的目标如果阵亡，则会增涨满额怒气值
					if ctrl.GetRandom().ChanceInt32(90 + this.target.GetStrategyValue(31502)) {
						ctrl.RemoveDieFighters()
						ctrl.UpdateFighterPointMap()
						this.target.CleanCanAttackTargets()
						if ts := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
							return !m.IsDie() && this.checkInRange(m, rang) && !m.IsBuild() && (this.checkInRange(m, attackRange) || len(this.target.GetTargetCanAttackPoints(m)) > 0)
						}); len(ts) > 0 {
							this.target.SetFullAnger(1, false)
							this.target.CleanBlackboard("hitTargetAddAngerUids")
							this.SetTreeBlackboardData("isBatter", true) //标记连击
						}
					}
				}
			},
			"getExtraDamge": func() int32 { //外加目标10%已损失生命值
				return ut.RoundInt32(float64(attackTarget.GetEntity().GetMaxHP()-attackTarget.GetEntity().GetCurHP()) * 0.1)
			},
		}) {
			return FAILURE
		}
	} else if skillType == 209 { //横扫 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		}
		rang := ut.If(skill.IsExclusiveIntensify(), int32(3), 2)
		if this.CheckHeroSkillID(heroSkill, hero.MA_CHAO) {
			rang += 1 //马超 范围+1
		}
		if len(this.attackTargets) == 0 {
			cnt := ut.If(skill.IsExclusiveIntensify(), 20, 12)
			this.attackTargets = this.target.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), rang, int32(cnt), "")
			if len(this.attackTargets) == 0 { //如果获取不到额外的目标 就不释放技能
				return SUCCESS
			}
		}
		done := true
		lastDamageAmend, count := skill.GetValue(), len(this.attackTargets)
		isLvBu, isXy := this.CheckHeroSkillID(heroSkill, hero.LV_BU), this.CheckHeroSkillID(heroSkill, hero.XIANG_YU)
		// 吕布
		if isLvBu {
			v := heroSkill.GetParamsInt() * rang //阈值
			if int32(count) < v {
				lastDamageAmend *= float64(v) / float64(count)
			}
		} else if isXy {
			lastDamageAmend += lastDamageAmend * (heroSkill.GetValue() * float64(count) * 0.01)
		}
		// 额外的目标
		for i := 0; i < count; i++ {
			at := this.attackTargets[i]
			if isXy {
				key := "initDiaup_" + at.GetUID()
				isInitDiaup := ut.Bool(this.GetBlackboardData(key))
				if !isInitDiaup {
					if p := at.GetCanByReqelPoint(currPoint, 1); p != nil {
						at.SetPoint(p)
						ctrl.UpdateFighterPointMap()
						isInitDiaup = true
						this.SetBlackboardData(key, isInitDiaup)
					}
				}
				if isInitDiaup && this.delayDiaupOne(at, dt, 700, 250) {
					at.AddBuff(bufftype.OVERLORD, this.target.GetUID(), 1) //限制移动
				}
			}
			if !this.attackOne(skill, currPoint, at, dt, 0, map[string]interface{}{
				"lastDamageAmend": lastDamageAmend, "attackSegments": i, "noHeal": true, "onHit": func(damage int32, _ int32) {
					if i == 0 { //根据击中数量回血
						val := int32(count) * skill.GetParamsInt()
						if isLvBu {
							val = heroSkill.Value //吕布始终回200
						}
						// 还会根据当前吸血提升恢复
						val += ut.RoundInt32(float64(val) * this.target.GetSuckBloodValue() * 0.01)
						this.target.OnHeal(val, false)
					}
				},
			}) {
				done = false
			}
		}
		if done {
			if this.CheckHeroSkillID(heroSkill, hero.MA_CHAO) { //马超
				// 取生命值最少的目标
				sort.Slice(this.attackTargets, func(i, j int) bool {
					a, b := this.attackTargets[i], this.attackTargets[j]
					aw, bw := int32(ut.If(a.IsBuild(), 1, 0)), int32(ut.If(b.IsBuild(), 1, 0))
					aw = aw*10000 + a.GetCurHp()
					bw = bw*10000 + b.GetCurHp()
					aw = aw*1000 + a.GetAttackIndex()
					bw = bw*1000 + b.GetAttackIndex()
					return aw < bw
				})
				at := array.Find(this.attackTargets, func(m g.IFighter) bool { return !m.IsDie() })
				if at != nil {
					// 受击
					ctrl.OnHitBaseTrueDamage(this.target, at, map[string]interface{}{
						"attackAmend": heroSkill.GetValue() * 0.01,
						"doAfter":     true,
					})
					// 使结束的时候慢点
					this.target.AddRoundEndDelayTime(500)
				}
			}
			return FAILURE
		}
	} else if skillType == 211 { //劈砍 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"getTrueDamage": func() int32 {
				targetUid := attackTarget.GetUID()
				// 获取叠加buff
				buff := this.target.GetBuff(bufftype.DAMAGE_SUPERPOSITION)
				if buff == nil { //没有就添加buff
					buff = this.target.AddBuff(bufftype.DAMAGE_SUPERPOSITION, targetUid, 1) //直接添加buff
				} else if buff.Provider != targetUid {
					var val int32
					if skill.IsExclusiveIntensify() {
						val = ut.RoundInt32(buff.Value * 0.5) //切换目标时叠加伤害保留50%
					}
					buff = this.target.AddBuff(bufftype.DAMAGE_SUPERPOSITION, targetUid, 1) //重新添加buff
					buff.SetValue(float64(val))
				}
				buff.ChangeValue(float64(ctrl.GetBaseAttackDamage(this.target, attackTarget, map[string]interface{}{"attackAmend": skill.GetValue()})))
				trueDamage, addRatio := buff.GetValueInt(), float64(this.target.GetStrategyValue(31701))
				// 武圣
				if WOOSUNG := this.target.CheckTriggerBuff(bufftype.WOOSUNG); WOOSUNG != nil {
					addRatio += WOOSUNG.Value
				}
				if addRatio > 0 {
					trueDamage += ut.RoundInt32(float64(trueDamage) * addRatio * 0.01)
				}
				return trueDamage
			},
		}) {
			return FAILURE
		}
	} else if skillType == 213 { //冲撞 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || attackTarget.IsBoss() || (attackTarget.IsBuild() && !attackTarget.IsFlag()) || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.dashArray) == 0 {
			// this.getBlackboardData('dashArray')
			rang := skill.GetTarget()
			this.dashArray = append(this.dashArray, &DashData{Time: -1, Point: currPoint.Clone()})
			point1, point2 := attackTarget.GetPoint(), ut.NewVec2(0, 0)
			dir := point1.Sub(currPoint)
			size := ctrl.GetAreaSize()
			point2.X = ut.ClampInt32(currPoint.X+dir.X*rang, 0, size.X-1)
			point2.Y = ut.ClampInt32(currPoint.Y+dir.Y*rang, 0, size.Y-1)
			arr := skill.GetParamsFloats()
			if this.CheckHeroSkillID(heroSkill, hero.XU_CHU) || this.CheckHeroSkillID(heroSkill, hero.PEI_XINGYAN) {
				arr = heroSkill.GetParamsFloats()
			}
			delay, time := arr[0], arr[1]
			targetPoint := point1
			if !point1.Equals(point2) && ctrl.CheckDashPoint(point2, this.target.GetCamp()) {
				targetPoint = point2
				this.dashArray = append(this.dashArray, &DashData{Time: ut.RoundInt32((delay + time*0.5) * 1000.0), Point: point1.Clone()})
			}
			this.target.SetPoint(targetPoint)
			this.dashArray = append(this.dashArray, &DashData{Time: ut.RoundInt32((delay + time) * 1000.0), Point: targetPoint.Clone()})
		}
		done := true
		for _, m := range this.dashArray {
			if m.Time == -1 {
			} else if currAttackTime >= m.Time {
				m.Time = -1
				points := array.Map(this.dashArray, func(d *DashData, _ int) *ut.Vec2 { return d.Point })
				targets := this.target.GetCanAttackFighters()
				for _, t := range targets {
					if !t.GetPoint().Equals(m.Point) {
					} else if p, ok := ctrl.SearchDiaupPoint(t.GetPoint(), points); ok {
						t.SetPoint(p)
						t.RemoveShieldBuffs()
						ctrl.UpdateFighterPointMap()
						this.attackTargets = append(this.attackTargets, t)
					}
				}
			} else {
				done = false
			}
		}
		isPxy, isDz := this.CheckHeroSkillID(heroSkill, hero.PEI_XINGYAN), this.CheckHeroSkillID(heroSkill, hero.DONG_ZHUO)
		for i := len(this.attackTargets) - 1; i >= 0; i-- {
			target := this.attackTargets[i]
			if this.diaupOne(target, dt, 300) {
				add := 0.0
				// 击中
				this.hitOne(target, 0, "_"+target.GetUID()+"_0", map[string]interface{}{
					"attackSegments": i,
					"attackAmend":    skill.GetValue() + add,
					"getExtraDamge": func() int32 {
						if isPxy {
							dis := helper.GetPointToPointDis(target.GetPoint(), target.GetLastPoint())
							return ctrl.GetBaseAttackDamage(this.target, target, map[string]interface{}{"attackAmend": float64(heroSkill.GetTarget()) * 0.01}) * dis
						}
						return 0
					},
					"getTrueDamage": func() int32 {
						if isDz {
							// 董卓额外造成3%最大生命值的真实伤害
							return ut.RoundInt32(float64(target.AmendMaxHp(bufftype.TYRANNICAL)) * 0.04)
						}
						return 0
					},
					"onHit": func(damage int32, val int32) {
						if damage >= 0 {
							// 被击飞的目标还会眩晕1回合
							if skill.IsExclusiveIntensify() {
								target.AddBuff(bufftype.DIZZINESS, this.target.GetUID(), 1+this.target.GetStrategyValue(31802))
							}
							// 董卓 增加负面buff
							if isDz {
								buff := target.GetBuffOrAdd(bufftype.TYRANNICAL, this.target.GetUID())
								if buff.Value < heroSkill.GetValue() {
									buff.Value += 1
								}
							}
						}
					},
				})
				this.attackTargets = append(this.attackTargets[:i], this.attackTargets[i+1:]...)
			}
		}
		if done && len(this.attackTargets) == 0 {
			// 冲撞之后切换目标
			this.target.ChangeAttackTarget(nil)
			// 许褚
			if this.CheckHeroSkillID(heroSkill, hero.XU_CHU) {
				this.target.AddBuff(bufftype.TIGER_MANIA, this.target.GetUID(), 1).SetValue(heroSkill.GetValue() + 20)
			}
			return FAILURE
		}
	} else if skillType == 217 { //骑射 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		}
		moveRange := this.target.GetEntity().GetMoveRange()
		moveCount := ut.Int32(this.GetBlackboardData("moveCount"))
		if moveCount >= moveRange {
			return FAILURE
		}
		moveAttackDataKey := "moveAttackData_" + ut.Itoa(moveCount)
		pathPoint, _ := this.GetBlackboardData(moveAttackDataKey + "_point").(*ut.Vec2)
		pathTarget := ut.String(this.GetBlackboardData(moveAttackDataKey + "_target"))
		if pathPoint == nil {
			pathPoint, pathTarget = ctrl.GetBowmanMovePaths(this.target)
			if pathPoint == nil || pathTarget == "" {
				return ut.If(moveCount == 0, SUCCESS, FAILURE)
			}
			this.SetBlackboardData(moveAttackDataKey+"_needMoveTime", helper.GetMoveNeedTime([]*ut.Vec2{this.target.GetPoint(), pathPoint}, this.target.GetEntity().GetMoveVelocity()))
			this.SetBlackboardData(moveAttackDataKey+"_point", pathPoint)
			this.SetBlackboardData(moveAttackDataKey+"_target", pathTarget)
		}
		time := ut.Int32(this.GetBlackboardData(moveAttackDataKey + "_time"))
		if time == -2 { //移动完了开始攻击
			if attackTarget == nil {
				return SUCCESS
			} else if this.attackOne(skill, currPoint, attackTarget, dt, moveCount, map[string]interface{}{"lastDamageAmend": ut.If(moveCount == 0, 1, skill.GetValue())}) {
				next := moveCount + 1
				if next >= moveRange {
					return FAILURE
				}
				this.SetBlackboardData("moveCount", next)
			}
			return RUNNING
		} else if time == 0 {
			time = ut.Int32(this.GetBlackboardData(moveAttackDataKey+"_needMoveTime")) + currAttackTime
			this.SetBlackboardData(moveAttackDataKey+"_time", time)
		}
		if currAttackTime >= time {
			this.SetBlackboardData(moveAttackDataKey+"_time", -2)
			this.target.ChangeAttackTarget(ctrl.GetFighter(pathTarget))
			// movePathLen := ut.Int32(this.GetTreeBlackboardData("movePathLen"))
			// this.SetTreeBlackboardData("movePathLen", movePathLen+1)
			if this.CheckHeroSkillID(heroSkill, hero.XIA_HOUYUAN) {
				buff := this.target.GetBuffOrAdd(bufftype.LONG_RANGE_RAID, this.target.GetUID())
				if maxVal := heroSkill.GetValue(); buff.Value < maxVal {
					buff.Value += 1
				}
			}
			this.target.SetPoint(pathPoint)
			ctrl.UpdateFighterPointMap()
		}
	} else if skillType == 117 { //顺劈 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || !attackTarget.IsPawn() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			if ut.Bool(this.GetTreeBlackboardData("random_longitudinal_cleft")) {
				return SUCCESS
			}
			this.SetTreeBlackboardData("random_longitudinal_cleft", true)
			odds := skill.GetParamsInt()
			// 程咬金
			if this.CheckHeroSkillID(heroSkill, hero.CHENG_YAOJIN) {
				axes := this.target.GetBuffValue(bufftype.THREE_AXES)
				if axes == 2 {
					odds = 100 //只有第2斧 才是必定顺劈
				} else if axes == 1 || axes == 3 {
					odds = 0 //第1斧和第3斧 不触发顺劈
				}
			}
			if !ctrl.GetRandom().ChanceInt32(odds) {
				return SUCCESS
			} else if arr := attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 1, 4, attackTarget.GetUID()); len(arr) > 0 || odds == 100 {
				this.attackTargets = append(this.attackTargets, attackTarget)
				this.attackTargets = append(this.attackTargets, arr...)
			} else {
				return SUCCESS
			}
		}
		done, skillVal := true, skill.GetValue()
		if this.target.GetAttackRange() > 1 {
			skillVal *= 0.5
		}
		for i, l := 0, len(this.attackTargets); i < l; i++ {
			lastDamageAmend := skillVal
			if i == 0 {
				lastDamageAmend = 1.0
			}
			if !this.attackOne(skill, currPoint, this.attackTargets[i], dt, 0, map[string]interface{}{
				"attackSegments":  i,
				"lastDamageAmend": lastDamageAmend,
				"attackType":      "attack",
				"onHit": func(damage int32, _ int32) {
					if i == 0 {
						cnt := int32(len(this.attackTargets))
						// 每击中一个加15护盾
						if skill.IsExclusiveIntensify() && cnt > 1 {
							this.target.AddBuffValue(bufftype.LONGITUDINAL_CLEFT_SHIELD, this.target.GetUID(), float64(6*cnt))
						}
						// 程咬金 第二斧 恢复生命
						if this.CheckHeroSkillID(heroSkill, hero.CHENG_YAOJIN) && this.target.GetBuffValue(bufftype.THREE_AXES) == 2 {
							this.target.OnHeal(ut.RoundInt32(float64(this.target.GetMaxHp()*cnt*5)*0.01), false)
						}
					}
				},
			}) {
				done = false
			}
		}
		if done {
			return FAILURE
		}
	} else if skillType == 301 { //放毒 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			this.attackTargets = this.target.GetCanAttackPawnByRange(this.target.GetCanAttackFighters(), 1, 20, "")
			if len(this.attackTargets) == 0 { //如果获取不到额外的目标 就不释放技能
				return SUCCESS
			}
		}
		if currAttackTime >= this.getNeedAttackTime(skill, currPoint, nil, "") {
			uid := this.target.GetUID()
			for _, m := range this.attackTargets {
				round := m.AddBuff(bufftype.POISONING_CUR_HP, uid, 1).Round         //直接添加buff
				buff2 := m.AddBuff(bufftype.SERIOUS_INJURY, uid, 1).SetRound(round) //重伤buff
				buff2.SetValue(0.6)
			}
			return FAILURE
		}
	} else if skillType == 302 { //投石 ========================================================================================
		if len(this.attackTargets) == 0 {
			rang := skill.GetTarget()
			targets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
				return !m.IsDie() && this.checkInRange(m, rang) && !m.IsBuild()
			})
			if len(targets) == 0 {
				return SUCCESS
			}
			var target g.IFighter = nil
			var maxw int32
			for _, m := range targets {
				w := 10000 + m.GetEntity().GetCurHP()                          //最高血量
				w = w*100 + helper.GetPointToPointDis(currPoint, m.GetPoint()) //最远距离
				w = w*1000 + (999 - m.GetAttackIndex())                        //出手速度
				if w > maxw {
					maxw = w
					target = m
				}
			}
			this.attackTargets = append(this.attackTargets, target)
		}
		attackTarget := this.attackTargets[0]
		if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{"attackAmend": skill.GetValue()}) {
			return FAILURE
		}
	} else if skillType == 303 { //瞄准 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"getTrueDamage": func() int32 { //额外造成目标当前生命10%生命
				return ut.RoundInt32(float64(attackTarget.GetCurHp()) * skill.GetValue())
			},
		}) {
			return FAILURE
		}
	} else if skillType == 304 { //猛击 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"getExtraDamge": func() int32 { //额外造成目标最大生命1%生命
				return ut.RoundInt32(float64(attackTarget.GetMaxHp()) * skill.GetValue())
			},
			"onHit": func(damage int32, val int32) {
				if damage >= 0 {
					// 眩晕1回合
					attackTarget.AddBuff(bufftype.DIZZINESS, this.target.GetUID(), 1)
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 305 { //撕咬 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if currAttackTime == 0 && (attackTarget == nil || attackTarget.IsBuild() || !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if this.attackOne(skill, currPoint, attackTarget, dt, 0, map[string]interface{}{
			"attackAmend": skill.GetValue(),
			"onHit": func(damage int32, val int32) {
				if damage >= 0 {
					uid := this.target.GetUID()
					// 给目标施加buff
					buff := attackTarget.AddBuff(bufftype.BLEED, uid, 1)
					buff.SetValue(float64(ut.RoundInt32(float64(this.target.GetActAttack()) * skill.GetParamsFloat64())))
					// 重伤buff
					buff2 := attackTarget.AddBuff(bufftype.SERIOUS_INJURY, uid, 1).SetRound(buff.Round)
					buff2.SetValue(0.6)
				}
			},
		}) {
			return FAILURE
		}
	} else if skillType == 306 { //野蛮冲撞 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || (attackTarget.IsBuild() && !attackTarget.IsFlag()) || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.dashArray) == 0 {
			rang := skill.GetTarget()
			this.dashArray = append(this.dashArray, &DashData{Time: -1, Point: currPoint.Clone()})
			point1, point2 := attackTarget.GetPoint(), ut.NewVec2(0, 0)
			dir := point1.Sub(currPoint)
			size := ctrl.GetAreaSize()
			point2.X = ut.ClampInt32(currPoint.X+dir.X*rang, 0, size.X-1)
			point2.Y = ut.ClampInt32(currPoint.Y+dir.Y*rang, 0, size.Y-1)
			arr := skill.GetParamsFloats()
			delay, time := arr[0], arr[1]
			targetPoint := point1
			if !point1.Equals(point2) && ctrl.CheckDashPoint(point2, this.target.GetCamp()) {
				targetPoint = point2
				this.dashArray = append(this.dashArray, &DashData{Time: ut.RoundInt32((delay + time*0.5) * 1000.0), Point: point1.Clone()})
			}
			this.target.SetPoint(targetPoint)
			this.dashArray = append(this.dashArray, &DashData{Time: ut.RoundInt32((delay + time) * 1000.0), Point: targetPoint.Clone()})
		}
		done := true
		for _, m := range this.dashArray {
			if m.Time == -1 {
			} else if currAttackTime >= m.Time {
				m.Time = -1
				points := array.Map(this.dashArray, func(d *DashData, _ int) *ut.Vec2 { return d.Point })
				targets := this.target.GetCanAttackFighters()
				for _, t := range targets {
					if !t.GetPoint().Equals(m.Point) {
					} else if p, ok := ctrl.SearchDiaupPoint(t.GetPoint(), points); ok {
						t.SetPoint(p)
						t.RemoveShieldBuffs()
						ctrl.UpdateFighterPointMap()
						this.attackTargets = append(this.attackTargets, t)
					}
				}
			} else {
				done = false
			}
		}
		for i := len(this.attackTargets) - 1; i >= 0; i-- {
			target := this.attackTargets[i]
			if this.diaupOne(target, dt, 300) {
				// 击中
				this.hitOne(target, 0, "_"+target.GetUID()+"_0", map[string]interface{}{"attackAmend": skill.GetValue()})
				this.attackTargets = append(this.attackTargets[:i], this.attackTargets[i+1:]...)
			}
		}
		if done && len(this.attackTargets) == 0 {
			this.target.ChangeAttackTarget(nil) //冲撞之后切换目标
			return FAILURE
		}
	} else if skillType == 307 { //爪击 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || !attackTarget.IsPawn() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if len(this.attackTargets) == 0 {
			this.attackTargets = attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 1, 20, "")
			if len(this.attackTargets) == 0 {
				return SUCCESS
			}
		}
		done := true
		for i, l := 0, len(this.attackTargets); i < l; i++ {
			attackAmend := ut.If(i == 0, 1.0, skill.GetValue())
			if !this.attackOne(skill, currPoint, this.attackTargets[i], dt, 0, map[string]interface{}{"attackAmend": attackAmend, "attackSegments": i}) {
				done = false
			}
		}
		if done {
			return FAILURE
		}
	} else if skillType == 308 { //狂怒 ========================================================================================
		if this.target.IsHasBuff(bufftype.RAGE) {
			return SUCCESS //有狂怒效果 就不再释放
		} else if currAttackTime >= this.getNeedAttackTime(skill, currPoint, nil, "") {
			// 添加狂怒buff
			this.target.AddBuff(bufftype.RAGE, this.target.GetUID(), 1)
			return FAILURE
		} else if currAttackTime >= skill.GetNeedHitTime() && !ut.Bool(this.GetBlackboardData("isHit")) {
			this.SetBlackboardData("isHit", true)
			// 清楚所有负面buff
			this.target.RemoveAllDebuff()
		}
	} else if skillType == 309 { //怒角碎阵 ========================================================================================
		attackTarget := this.target.GetAttackTarget()
		if attackTarget == nil || !attackTarget.IsPawn() || (currAttackTime == 0 && !this.checkInAttackRange(attackTarget)) {
			return SUCCESS
		} else if currAttackTime >= skill.GetNeedHitTime() {
			if len(this.attackTargets) == 0 {
				oPoint := attackTarget.GetPoint().Clone()
				// 先获取4格内的位置
				points := attackTarget.GetSearchRange().Search(oPoint, 6, true)
				points = array.Filter(points, func(m *ut.Vec2, _ int) bool { return m.Equals(oPoint) || !ctrl.CheckHasFighter(m.X, m.Y) })
				// 如果只有一个单位
				if len(this.target.GetCanAttackFighters()) == 1 {
					attackTarget.SetPoint(points[ctrl.GetRandom().Get(0, len(points)-1)])
				} else {
					sr := new(astar.SearchRange).Init(ctrl.CheckIsBattleArea)
					rang, camp := skill.GetTarget(), this.target.GetCamp()
					maxCount, point := int32(0), ut.NewVec2(0, 0)
					for _, m := range points {
						var cnt int32
						// 查找2格内的敌方目标
						arr := sr.Search(m, rang, false)
						for _, p := range arr {
							cnt += ctrl.GetFighterCountForCampByPoint(p, camp, true)
						}
						if cnt == 0 || cnt < maxCount || (cnt == maxCount && ctrl.GetRandom().Chance(50)) {
							continue
						}
						maxCount = cnt
						point.Set(m)
					}
					if maxCount > 0 {
						attackTarget.SetPoint(point)
						this.attackTargets = attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), rang, 8, attackTarget.GetUID())
					}
				}
				// 不管怎样都要把目标加入
				this.attackTargets = append(this.attackTargets, attackTarget)
			}
			// 先击飞
			if this.diaupOne(attackTarget, dt, 300) {
				uid := this.target.GetUID()
				// 算出伤害
				trueDamage := ut.RoundInt32(float64(attackTarget.GetMaxHp()) * skill.GetValue() / float64(len(this.attackTargets)))
				// 获取眩晕回合数
				buff := this.target.GetBuffOrAdd(bufftype.RECORD_DIZZINESS_ROUND, uid)
				addRound := int32(buff.Value)
				buff.Value += 1
				// 击中
				for _, m := range this.attackTargets {
					_, damage := m.HitPrepDamageHandle(0, trueDamage)
					v, _, _ := m.OnHit(damage, damage)
					m.AddBuff(bufftype.DIZZINESS, uid, 1).AddRound(addRound) //添加眩晕
					// 记录数据
					ctrl.AddFighterBattleDamageInfo(uid, m, v)
				}
				// 切换目标
				this.target.ChangeAttackTarget(nil)
				return FAILURE
			}
		}
	} else if skillType == 310 { //疯狂爪击 ========================================================================================
		if len(this.attackTargets) == 0 {
			arr := []g.CanAttackTargetInfo{}
			minDis := int32(100000)
			allTargets := this.target.GetCanAttackTargets()
			for _, m := range allTargets {
				dis := this.target.GetMinDis(m.Target)
				if dis < minDis {
					minDis = dis
					arr = []g.CanAttackTargetInfo{m}
				} else if dis == minDis {
					arr = append(arr, m)
				}
			}
			if len(arr) == 0 {
				return SUCCESS
			}
			attackTarget := arr[ctrl.GetRandom().Get(0, len(arr)-1)].Target
			this.target.ChangeAttackTarget(attackTarget)
			this.attackTargets = attackTarget.GetCanAttackFighterByRange(this.target.GetCanAttackFighters(), 2, 8, attackTarget.GetUID())
			this.attackTargets = append(this.attackTargets, attackTarget)
		}
		// 启动
		launchTime := skill.GetParamsInt()
		if currAttackTime >= launchTime {
			tempCurrAttackTime := currAttackTime - launchTime
			targetCount, done, count := len(this.attackTargets), false, skill.GetTarget()
			for i := int32(0); i < count; i++ {
				interval := 300 * i //攻击间隔
				if tempCurrAttackTime >= interval {
					buff := this.target.GetBuffOrAdd(bufftype.RECORD_PAWSTRIKE, this.target.GetUID())
					lastDamageAmend := skill.GetValue() + buff.Value
					ok, isEnd := true, i == count-1
					for ii := 0; ii < targetCount; ii++ {
						at := this.attackTargets[ii]
						if !this.attackOne(skill, currPoint, at, dt, i, map[string]interface{}{"lastDamageAmend": lastDamageAmend, "attackSegments": ii}) {
							ok = false
						}
					}
					if isEnd {
						done = ok
					}
					if ok {
						key := "isPawStrikeComplete_" + ut.Itoa(i)
						if ok && !ut.Bool(this.GetBlackboardData(key)) {
							this.SetBlackboardData(key, true)
							buff.Value += 0.05
						}
					}
				}
			}
			if done {
				return FAILURE
			}
		}
	} else {
		return SUCCESS
	}
	// 表示已经使用技能 清空怒气
	if currAttackTime == 0 {
		this.SetTreeBlackboardData("isAttack", true) //记录已经攻击过了
		this.DeductSkillAnger()
	}
	return RUNNING
}

// 扣除技能怒气
func (this *CheckUseSkillAttack) DeductSkillAnger() {
	maxAnger := this.target.GetEntity().GetMaxAnger()
	if maxAnger == 0 {
		this.target.DeductAnger(1)
	} else {
		this.target.DeductAnger(maxAnger)
	}
}

// 击飞
func (this *CheckUseSkillAttack) diaupOne(attackTarget g.IFighter, dt int32, needDiaupTime int32) bool {
	key := "_" + attackTarget.GetUID() + "_diaup" //攻击顺序 用来区分目标的存储信息
	currDiaupTime := ut.Int32(this.GetBlackboardData("currDiaupTime" + key))
	if currDiaupTime >= needDiaupTime {
		return true
	}
	this.SetBlackboardData("currDiaupTime"+key, currDiaupTime+dt)
	return false
}

// 延迟击飞
func (this *CheckUseSkillAttack) delayDiaupOne(attackTarget g.IFighter, dt int32, delayTime int32, needDiaupTime int32) bool {
	key := "_" + attackTarget.GetUID() + "_diaup" //攻击顺序 用来区分目标的存储信息
	currDelayTime := ut.Int32(this.GetBlackboardData("currDiaupDelayTime" + key))
	if currDelayTime < delayTime {
		this.SetBlackboardData("currDiaupDelayTime"+key, currDelayTime+dt)
		return false
	}
	return this.diaupOne(attackTarget, dt, needDiaupTime)
}

func (this *CheckUseSkillAttack) attackOne(skill g.PawnSkill, point *ut.Vec2, attackTarget g.IFighter, dt int32, tag int32, data map[string]interface{}) bool {
	if attackTarget == nil {
		return true
	}
	key := "_" + attackTarget.GetUID() + "_" + ut.Itoa(tag) //攻击顺序 用来区分目标的存储信息
	needAttackTime := this.getNeedAttackTime(skill, point, attackTarget, key)
	currAttackTime := ut.Int32(this.GetBlackboardData("currAttackTime" + key))
	if currAttackTime >= needAttackTime {
		return true
	}
	// 增加时间
	this.SetBlackboardData("currAttackTime"+key, currAttackTime+dt)
	// 是否到击中的时间点
	needHitTime := ut.If(skill.GetBulletId() != 0, needAttackTime-dt, skill.GetNeedHitTime())
	if currAttackTime < needHitTime {
	} else if data != nil && ut.Bool(data["isHeal"]) {
		this.healOne(attackTarget, data)
	} else {
		this.hitOne(attackTarget, currAttackTime-skill.GetNeedHitTime(), key, data)
	}
	return false
}

// 击中
func (this *CheckUseSkillAttack) hitOne(attackTarget g.IFighter, _ int32, key string, data map[string]interface{}) {
	var isHit bool
	var _damage, val, heal, hitShield int32
	if !ut.Bool(this.GetBlackboardData("isHit" + key)) {
		hpRatioBefore := attackTarget.GetHPRatio()
		ctrl := this.GetCtrl()
		damage, trueDamage, baseDamage, isCrit := ctrl.GetAttackDamage(this.target, attackTarget, data)
		this.SetBlackboardData("isHit"+key, true)
		this.SetBlackboardData("isCrit"+key, isCrit)
		damage, trueDamage = attackTarget.HitPrepDamageHandle(damage, trueDamage)
		_damage = damage
		this.SetBlackboardData("damage"+key, damage) //这里记录供前端展示
		this.SetBlackboardData("trueDamage"+key, trueDamage)
		if !attackTarget.IsDie() {
			sumDamage := ut.MaxInt32(damage, 0) + trueDamage
			val, heal, hitShield = attackTarget.OnHit(sumDamage, baseDamage)
			// 处理攻击-后置
			var healVal, noShowHeal, noHeal int32
			if data != nil {
				if healValFunc, ok := data["healVal"].(func() int32); ok {
					healVal = healValFunc()
				}
				noShowHeal = ut.If(ut.Bool(data["noShowHeal"]), int32(1), 0)
				noHeal = ut.If(ut.Bool(data["noHeal"]), int32(1), 0)
			}
			heal += ctrl.DoAttackAfter(this.target, attackTarget, map[string]interface{}{
				"sumDamage":       sumDamage,
				"actDamage":       val,
				"trueDamage":      trueDamage,
				"hitShield":       hitShield,
				"healVal":         healVal,
				"noShowHeal":      noShowHeal,
				"noHeal":          noHeal,
				"lastDamageAmend": data["lastDamageAmend"],
				"attackType":      data["attackType"],
				"attackSegments":  data["attackSegments"],
			})
			this.SetBlackboardData("heal", heal)
			// 增加怒气
			if data != nil && ut.Bool(data["notAddAnger"]) {
			} else if val > 0 || damage == -2 || (damage == -1 && attackTarget.IsHasStrategys(50002)) {
				hitTargetAddAngerUids := ut.String(this.GetTreeBlackboardData("hitTargetAddAngerUids"))
				if !strings.Contains(hitTargetAddAngerUids, attackTarget.GetUID()) {
					attackTarget.AddAnger(1)
					if hitTargetAddAngerUids != "" {
						hitTargetAddAngerUids += "|"
					}
					this.SetTreeBlackboardData("hitTargetAddAngerUids", hitTargetAddAngerUids+attackTarget.GetUID())
				}
			}
			isHit = true
			// 记录数据
			ctrl.AddFighterBattleDamageInfo(this.target.GetUID(), attackTarget, val)
			// 触发任务
			ctrl.TriggerTaskAfterDamage(this.target, attackTarget, val, hpRatioBefore)
		}
	}
	// 这个回调放到后面 因为剑骑兵会清空数据
	if !isHit {
	} else if onHitFunc, ok := data["onHit"].(func(damage int32, val int32)); ok {
		onHitFunc(_damage, val)
	}
}

// 给目标回血
func (this *CheckUseSkillAttack) healOne(target g.IFighter, data map[string]interface{}) {
	if data != nil {
		if healValFunc, ok := data["healVal"].(func() int32); ok {
			target.OnHeal(healValFunc(), false)
		}
	}
}

// 获取攻击需要的时间
func (this *CheckUseSkillAttack) getNeedAttackTime(skill g.PawnSkill, point *ut.Vec2, attackTarget g.IFighter, key string) int32 {
	if skill.GetBulletId() == 0 || attackTarget == nil {
		return skill.GetNeedAttackTime()
	}
	return this.getNeedAttackTimeForBullet(skill.GetNeedAttackTime(), skill.GetNeedHitTime(), point, attackTarget.GetPoint(), key)
}

func (this *CheckUseSkillAttack) getNeedAttackTimeForBullet(needAttackTime, needHitTime int32, spoint *ut.Vec2, epoint *ut.Vec2, key string) int32 {
	time := ut.Int32(this.GetBlackboardData("needAttackTime" + key))
	if time == 0 {
		// 计算距离
		mag := int32(helper.GetPixelByPoint(spoint).Sub(helper.GetPixelByPoint(epoint)).Mag() * 1000.0)
		// 计算飞行时间 加上等待发射的时间
		time = int32(float64(mag)/float64(needAttackTime)*1000.0) + needHitTime
		this.SetBlackboardData("needAttackTime"+key, time)
	}
	return time
}

// 是否在攻击范围
func (this *CheckUseSkillAttack) checkInAttackRange(target g.IFighter) bool {
	return this.checkInRange(target, this.target.GetEntity().GetAttackRange())
}

// 是否在某个范围内
func (this *CheckUseSkillAttack) checkInRange(target g.IFighter, rang int32) bool {
	return this.target.CheckInAttackRange(target, rang)
}

// 是否可被击退
func (this *CheckUseSkillAttack) isCanRepel(target g.IFighter) bool {
	if target.GetPawnType() == constant.PAWN_TYPE_MACHINE || target.GetPawnType() == constant.PAWN_TYPE_BUILD {
		return false //器械和建筑不可击退
	}
	return !target.IsHasBuff(bufftype.STAND_SHIELD) //立盾状态不可击退
}

func (this *CheckUseSkillAttack) getMultiAttackTarget(count int32, rang int32) []g.IFighter {
	uid := ""
	if attackTarget := this.target.GetAttackTarget(); attackTarget != nil {
		uid = attackTarget.GetUID()
	}
	allTargets := array.Filter(this.target.GetCanAttackFighters(), func(m g.IFighter, _ int) bool {
		return m.GetUID() != uid && !m.IsDie() && !m.IsBuild() && this.checkInRange(m, rang)
	})
	point := this.target.GetPoint()
	sort.Slice(allTargets, func(i, j int) bool {
		a, b := allTargets[i], allTargets[j]
		aw := 100 - helper.GetPointToPointDis(point, a.GetPoint())
		bw := 100 - helper.GetPointToPointDis(point, b.GetPoint())
		aw = aw*1000 + (999 - a.GetAttackIndex())
		bw = bw*1000 + (999 - b.GetAttackIndex())
		return aw > bw
	})
	count = ut.MinInt32(int32(len(allTargets)), count)
	return allTargets[:count]
}

func (this *CheckUseSkillAttack) CheckHeroSkillID(skill *g.PortrayalSkill, id int32) bool {
	return skill != nil && skill.Id == id
}

// 乱舞完成
func (this *CheckUseSkillAttack) wriggleComplete(batterCount int32, skill g.PawnSkill, heroSkill *g.PortrayalSkill) {
	this.SetTreeBlackboardData("batterCount", -1)
	if batterCount > 0 {
		// 添加招架buff
		lv := ut.If(skill.IsExclusiveIntensify(), int32(2), 1) + this.target.GetStrategyValue(30502)
		buff := this.target.AddBuff(bufftype.TURNTHEBLADE, this.target.GetUID(), lv)
		// 加上韬略
		buff.Value = skill.GetValue() + float64(this.target.GetStrategyValue(30501))
		// 秦琼 提升招架几率
		if this.CheckHeroSkillID(heroSkill, hero.QIN_QIONG) {
			buff.Value += 20
			// 添加buff 斩将
			this.target.AddBuffValue(bufftype.BEHEADED_GENERAL, this.target.GetUID(), float64(heroSkill.Value))
		} else {
			this.target.ChangeAttackTarget(nil) //只要不是秦琼都重新选择目标
		}
	}
}

// 检测恢复生命
func (this *CheckUseSkillAttack) checkRecoverHPForLeave() {
	effects := this.target.GetEquipEffects()
	for _, m := range effects {
		if m.Type == eeffect.BELT_BLOODRAGE { //血怒腰带
			heal := ut.RoundInt32(float64(this.target.GetMaxHp()) * 0.05)
			this.target.OnHeal(heal, false)
		}
	}
}

// 检测回复怒气
func (this *CheckUseSkillAttack) checkRecoverAngerForLeave() {
	if this.target.CheckPortrayalSkill(hero.QIN_LIANGYU) != nil && !ut.Bool(this.GetBlackboardData("spearthrowing")) {
		return //如果是秦良玉收矛 就不回怒气
	}
	// 韬略 恢复怒气
	val := this.target.GetStrategyValue(10002) + this.target.GetStrategyValue(31602)
	// 韩当 恢复1怒气
	if this.target.CheckPortrayalSkill(hero.HAN_DANG) != nil {
		val += 1
	}
	if val > 0 {
		this.target.AddAnger(val)
	}
	if this.target.IsHasAnger() && !this.target.IsCanUseSkill() && !this.target.IsHasBuff(bufftype.SILENCE) { //满怒就不回复了
		// 双斧 几率恢复一半怒气
		if effect := this.target.GetEquipEffectByType(eeffect.RECOVER_ANGER); effect != nil && this.GetCtrl().GetRandom().ChanceInt32(effect.Odds) {
			this.target.SetFullAnger(0.5, true)
		}
	}
}

// 获取连射次数
func (this *CheckUseSkillAttack) getLianSheCount(skill g.PawnSkill) int32 {
	cnt := ut.Int32(this.GetBlackboardData("lianSheCount"))
	if cnt == 0 {
		cnt = skill.GetParamsInt() + this.target.GetStrategyValue(50027)
		if this.GetCtrl().GetRandom().ChanceInt32(this.target.GetStrategyValue(31101)) {
			cnt += 1
		}
		this.SetBlackboardData("lianSheCount", cnt)
	}
	return cnt
}
