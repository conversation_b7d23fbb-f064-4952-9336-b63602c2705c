package world

import (
	"time"

	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

const (
	DEBUG                = false
	SEASON_COUNT         = 4               // 一共4个季节
	SEASON_DURATION_TIME = 3 * ut.TIME_DAY // 一个季节持续的天数
)

// 季节模块
type SeasonModel struct {
	room g.Room

	Type           int   // 当前的季节类型 0.春 1.夏 2.秋 3.冬
	NextSeasonTime int64 // 下一个季节时间

	effectMap *ut.MapLock[int, float64]         // 当前季节的效果
	Policys   *ut.MapLock[int32, *g.PolicyInfo] // 季节政策
}

func NewSeasonModel(room g.Room) *SeasonModel {
	return &SeasonModel{
		room:      room,
		effectMap: ut.NewMapLock[int, float64](),
		Policys:   ut.NewMapLock[int32, *g.PolicyInfo](),
	}
}

func (this *SeasonModel) ToPb() *pb.SeasonInfo {
	return &pb.SeasonInfo{
		Type:                  int32(this.Type),
		NextSeasonSurplusTime: ut.MaxInt64(0, this.NextSeasonTime-time.Now().UnixMilli()),
		Policys:               this.ToPolicys(),
	}
}

func (this *SeasonModel) ToDB() map[string]interface{} {
	rst := map[string]interface{}{}
	rst["policys"] = this.ToPolicys()
	return rst
}

func (this *SeasonModel) GetType() int                  { return this.Type }
func (this *SeasonModel) GetEffectFloat(tp int) float64 { return this.effectMap.Get(tp) }
func (this *SeasonModel) IsEnd() bool                   { return this.NextSeasonTime == 0 }

func (this *SeasonModel) Init(db *Mongodb) *SeasonModel {
	createTime := this.room.GetCreateTime()
	if createTime == 0 {
		return this
	}
	nowZeroTime := ut.NowZeroTime()
	createZeroTime := ut.DateZeroTime(createTime)
	// 运行时间
	runTime := ut.MaxInt64(0, nowZeroTime-createZeroTime)
	// 定位季节 和 计算下一个季节来临时间
	if DEBUG {
		this.Type = 0
		this.NextSeasonTime = time.Now().UnixMilli() + ut.TIME_MINUTE
	} else {
		this.Type = ut.Min(SEASON_COUNT-1, int(runTime/SEASON_DURATION_TIME))
		this.NextSeasonTime = createZeroTime + int64(this.Type*SEASON_DURATION_TIME) + SEASON_DURATION_TIME
	}
	// 从数据库获取季节信息
	if data, e := db.FindSeasonInfo(); e == "" {
		policyMap := ut.MapInt32Int32(data.Data["policys"])
		for index, id := range policyMap {
			this.Policys.Set(index, g.NewPolicyInfo(id))
		}
	}
	// 刷新效果
	this.UpdateEffect(true)
	this.createAncientCities()
	log.Info("Season Init, RunTime: %v, Type: %v, NextTime: %v", runTime, this.Type, ut.DateFormat(this.NextSeasonTime, "2006-01-02 15:04:05"))
	return this
}

func (this *SeasonModel) Update(now int64) {
	this.createAncientCities()
	if now < this.NextSeasonTime {
		return
	} else if this.Type >= SEASON_COUNT-1 { // 最后一个季节
		this.NextSeasonTime = 0 // 强制结束游戏
		return
	}
	this.Type += 1
	if DEBUG {
		this.NextSeasonTime += ut.TIME_MINUTE
	} else {
		this.NextSeasonTime += SEASON_DURATION_TIME
	}
	this.UpdateEffect(false)
	// 触发季节开始添加物品
	go this.room.GetWorld().AddPolicySeasonItems()
	// 通知
	this.room.GetWorld().PutNotifyQueue(constant.NQ_UPDATE_SEASON, &pb.OnUpdateWorldInfoNotify{Data_65: this.ToPb()})
	log.Info("ChangeSeason Type: %v, NextTime: %v", this.Type, ut.DateFormat(this.NextSeasonTime, "2006-01-02 15:04:05"))
}

// 刷新效果
func (this *SeasonModel) UpdateEffect(init bool) {
	wld := this.room.GetWorld()
	// this.effectMap.Clean()
	switch this.Type {
	case 0: // 春
		// this.effectMap.Set(effect.RES_OUTPUT_PERCENT, 0.5) //基础产量增加50%
	case 1: // 夏
		// this.effectMap.Set(effect.TREASURE_AWARD, 0.25) //宝箱奖励提高25%
		// this.effectMap.Set(effect.MARCH_CD, 0.05)       //行军速度提高5%
	case 2: // 秋
		// this.effectMap.Set(effect.UPLVING_CD, 0.2) //训练士兵时间减少20%
		// this.effectMap.Set(effect.MARCH_CD, 0.1)   //行军速度提高10%
		// 清理免战
		wld.CleanGeneralAvoidWarArea()
	case 3: // 冬
		// this.effectMap.Set(effect.RES_OUTPUT_PERCENT, -0.5) //基础产量减少50%
		// this.effectMap.Set(effect.RECRUIT_COST, 2.0)        //招募士兵费用提高100%
		// this.effectMap.Set(effect.UPLVING_COST, 2.0)        //训练士兵费用提高100%
		// this.effectMap.Set(effect.UPLVING_CD, 0.2)          //训练士兵时间减少20%
		// 清理免战
		wld.CleanGeneralAvoidWarArea()
	}
	if this.Policys.Count() <= this.Type {
		for i := 0; i <= this.Type; i++ {
			// 刷新季节政策
			if this.Policys.Get(int32(i)) == nil {
				// 随机季节政策
				policyIds := g.RandomPolicyIds(config.GetJson("policy").Datas, 1)
				if len(policyIds) > 0 {
					id := policyIds[0]
					this.SetSeasonPolicy(int32(i), id)
				} else {
					log.Warning("UpdateEffect random nil")
				}
			}
		}
	}
	if !init {
		if this.room.IsConquer() {
			// 血战到底夏天通知盟主坐标
			if this.Type >= 1 {
				wld.NotifyAllAlliBaseInfo()
			}
		} else if this.Type >= 3 {
			// 其他模式冬季通知盟主坐标
			wld.NotifyAllAlliBaseInfo()
		}
	}
}

// 改变基础资源费用
func (this *SeasonModel) ChangeBaseResCost(list []*g.TypeObj, tp int) []*g.TypeObj {
	if len(list) == 0 {
		return list
	}
	up := this.GetEffectFloat(tp)
	if up > 0 {
		list = array.Map(list, func(m *g.TypeObj, _ int) *g.TypeObj {
			v := m.Clone()
			if v.Type <= ctype.STONE {
				v.Count = int32(ut.Round(float64(v.Count) * up))
			}
			return v
		})
	} else {
		list = array.Map(list, func(m *g.TypeObj, _ int) *g.TypeObj { return m.Clone() })
	}
	return list
}

// 创建古城
func (this *SeasonModel) createAncientCities() {
	wld := this.room.GetWorld()
	if this.Type >= 1 && !wld.GetHasCreateAncient() {
		createTime := this.room.GetCreateTime()
		if createTime == 0 {
			return
		}
		createZeroTime := ut.DateZeroTime(createTime)
		// 计算夏季开始时间
		summerStartTime := createZeroTime + SEASON_DURATION_TIME
		creaeteAncientTime := summerStartTime + int64(constant.ANCIENT_CITY_CREATE_TIME*ut.TIME_HOUR)
		// if DEBUG {
		// 	creaeteAncientTime = createTime + ut.TIME_MINUTE
		// }
		if time.Now().UnixMilli() >= creaeteAncientTime {
			// 进入夏季且10点之后才生成古城
			go wld.CreateAncientCities()
		}
	}
}

// 获取指定效果类型的政策id
func (this *SeasonModel) GetPolicyIdByType(tp int32) int32 {
	var policyId int32
	this.Policys.ForEach(func(v *g.PolicyInfo, k int32) bool {
		if effectType := v.GetEffect(); effectType > 0 && effectType == tp {
			policyId = v.ID
			return false
		}
		return true
	})
	return policyId
}

// 设置季节政策
func (this *SeasonModel) SetSeasonPolicy(season, id int32) {
	if season >= 4 {
		return
	}
	this.Policys.Set(season, g.NewPolicyInfo(id))
	// 激活政策获得物品
	this.room.GetWorld().AllPlrActivePolicyAddItems(id)
}

func (this *SeasonModel) ToPolicys() map[int32]int32 {
	policyMap := map[int32]int32{}
	this.Policys.ForEach(func(v *g.PolicyInfo, k int32) bool {
		policyMap[k] = v.ID
		return true
	})
	return policyMap
}
