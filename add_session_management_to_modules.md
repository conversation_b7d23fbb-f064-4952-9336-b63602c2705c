# 为其他模块添加服务器会话管理接口

## 🎯 **目标**
为 `lobby` 和 `game` 模块添加相同的服务器会话管理接口，实现统一的节点管理功能。

## 🔧 **实现步骤**

### 1. **在 lobby/http.go 中添加**

在 `InitHttp()` 方法中添加以下代码：

```go
// 在 InitHttp() 方法的末尾添加
func (this *Lobby) InitHttp() {
    // ... 现有的HTTP接口注册 ...
    
    // 节点管理相关 - 使用公共处理器
    sessionHandler := slg.NewServerSessionHTTPHandler()
    sessionHandler.RegisterHTTPHandlers(this.GetServer(), this.GetApp())
}
```

### 2. **在 game/http.go 中添加**

在 `InitHttp()` 方法中添加以下代码：

```go
// 在 InitHttp() 方法的末尾添加
func (this *Game) InitHttp() {
    // ... 现有的HTTP接口注册 ...
    
    // 节点管理相关 - 使用公共处理器
    sessionHandler := slg.NewServerSessionHTTPHandler()
    sessionHandler.RegisterHTTPHandlers(this.GetServer(), this.GetApp())
}
```

## 📋 **添加后可用的接口**

每个模块都会获得以下HTTP接口：

### 1. **获取所有节点信息**
```http
GET /http/getAllServerNodes
```

### 2. **强制删除服务器会话**
```http
POST /http/forceRemoveServerSession
参数: serverID=game1
```

### 3. **获取指定服务器会话信息**
```http
POST /http/getServerSessionInfo
参数: serverID=game1
```

### 4. **检查服务器健康状态**
```http
POST /http/checkServerHealth
参数: serverType=game
```

## 🔍 **使用场景**

### **场景1：game 节点重启后连接问题**
1. **从 match 节点诊断**:
   ```bash
   curl -X GET "http://match-server/http/getAllServerNodes"
   ```

2. **从 lobby 节点删除僵尸会话**:
   ```bash
   curl -X POST "http://lobby-server/http/forceRemoveServerSession" \
        -d "serverID=game1"
   ```

3. **从 game 节点检查自身状态**:
   ```bash
   curl -X POST "http://game-server/http/checkServerHealth" \
        -d "serverType=game"
   ```

### **场景2：跨模块连接问题排查**
- **lobby → game**: 在 lobby 节点删除 game 会话
- **match → game**: 在 match 节点删除 game 会话  
- **game → lobby**: 在 game 节点删除 lobby 会话

## ⚡ **快速部署命令**

### **为 lobby 模块添加**
```bash
# 1. 找到 lobby/http.go 中的 InitHttp() 方法
# 2. 在方法末尾添加以下代码：

// 节点管理相关 - 使用公共处理器
sessionHandler := slg.NewServerSessionHTTPHandler()
sessionHandler.RegisterHTTPHandlers(this.GetServer(), this.GetApp())
```

### **为 game 模块添加**
```bash
# 1. 找到 game/http.go 中的 InitHttp() 方法
# 2. 在方法末尾添加以下代码：

// 节点管理相关 - 使用公共处理器
sessionHandler := slg.NewServerSessionHTTPHandler()
sessionHandler.RegisterHTTPHandlers(this.GetServer(), this.GetApp())
```

## 🎯 **优势**

### 1. **统一管理**
- 所有模块使用相同的接口
- 统一的错误处理和日志记录
- 一致的返回格式

### 2. **代码复用**
- 核心逻辑在公共包中
- 减少重复代码
- 易于维护和升级

### 3. **灵活性**
- 每个模块都可以管理自己的会话
- 支持跨模块的会话清理
- 便于问题排查和调试

### 4. **扩展性**
- 易于添加新的会话管理功能
- 支持批量操作
- 可以添加更多的健康检查功能

## 🔧 **高级功能**

### **批量删除会话**
```http
POST /http/batchRemoveServerSessions
参数: serverIDs=["game1","game2","game3"]
```

### **按类型获取服务器**
```http
POST /http/getServersByType
参数: serverType=game
```

## 📊 **监控建议**

### 1. **定期健康检查**
```bash
# 每5分钟检查一次所有节点
*/5 * * * * curl -s http://match-server/http/getAllServerNodes | jq '.data.nodes'
```

### 2. **自动清理脚本**
```bash
#!/bin/bash
# 检测并清理僵尸连接
SERVERS=("match" "lobby" "game")
for server in "${SERVERS[@]}"; do
    # 检查节点状态并清理异常连接
    curl -s "http://${server}-server/http/getAllServerNodes" | \
    jq -r '.data.nodes | to_entries[] | select(.value | length == 0) | .key'
done
```

### 3. **告警机制**
- 当检测到连接异常时发送告警
- 记录所有删除操作的日志
- 监控删除操作的成功率

## 🎉 **部署完成后的效果**

部署完成后，你将拥有：

✅ **统一的节点管理接口**  
✅ **跨模块的会话清理能力**  
✅ **完整的健康检查功能**  
✅ **详细的操作日志记录**  
✅ **灵活的问题排查工具**  

这将大大提高系统的可维护性和故障恢复能力！
