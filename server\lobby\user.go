package lobby

import (
	"context"
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/common/dh"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sdk"
	"slgsrv/server/common/ta"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	lc "slgsrv/server/lobby/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"slgsrv/utils/geoip"
	rds "slgsrv/utils/redis"
	"sort"
	"strings"
	"sync/atomic"
	"time"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/reflect/protoreflect"
)

type GuideInfo struct {
	ID  int    `json:"id" bson:"id"`
	Tag string `json:"tag" bson:"tag"`
}

type User struct {
	Session gate.Session

	UID           string
	LoginType     string
	DistinctId    string //访客id 就是前端设备id
	DeviceOS      string //设备操作系统
	Platform      string //平台
	Nickname      string
	HeadIcon      string
	PersonalDesc  string //个人简介
	Referrer      string //谁邀请的我
	Ip            string //ip地址
	GuestId       string //游客id
	TeamUid       string //所在队伍uid
	AppsflyerId   string //AF id
	AdvertisingId string //AD id
	Language      string //语言
	FCMToken      string //FCM令牌
	SessionId     string //每次登录唯一id

	Portrayals           []*g.PortrayalInfo    //画像列表
	AccTotalGameCounts   []int32               //累计对局次数[胜利次数，总次数]
	AccTotalNewbieCounts []int32               //累计新手次数[胜利次数，总次数]
	AccTotalRankCounts   []int32               //累计排位次数[胜利次数，总次数]
	AccTotalFreeCounts   []int32               //累计自由次数[胜利次数，总次数]
	PopularityList       [][]int32             //评价列表
	PopularityRecords    []*PopularityRecord   //评价记录
	UnlockHeadIcons      []string              //解锁的头像列表
	UnlockPawnSkinIds    []int32               //解锁的士兵皮肤列表
	UnlockChatEmojiIds   []int32               //解锁的聊天表情列表
	UnlockCitySkinIds    []int32               //解锁城市皮肤列表
	UnlockBotanys        []*BotanyInfo         //解锁植物列表
	Titles               []*TitleInfo          //解锁的称号列表
	SkinItemList         []*SkinItem           //皮肤物品列表
	InviteFriends        []*InviteFriendInfo   //我邀请的好友列表
	Guides               []*GuideInfo          //新手引导列表
	WheelRecords         []*WheelRecord        //转动记录
	NotFinishOrders      []*NotFinishOrderInfo //已支付未完成的订单
	LastPawnSkins        []int32               //最后一次使用的士兵皮肤
	LastCitySkins        []int32               //最后一次使用的城市皮肤
	ApplyServers         []*ApplyInfo          //已报名区服
	HideRankScore        []int32               //评分[隐藏分，历史最高段位分]
	GiveupGames          []int32               //已放弃的对局

	OfflineNotifyOpt []int32           //离线推送设置
	SubscriptionInfo []*UserSubInfo    //订阅信息
	Blacklists       []*BlacklistInfo  //拉黑名单
	FriendsList      []*Friend         //好友列表
	FriendsApplyList []*ApplyFriend    //好友申请列表
	BanRecordList    []*BanRecord      //禁言/封禁记录
	TeamInviteList   []*TeamInviteInfo //收到队伍邀请列表
	AntiCheatData    []int32           //防作弊信息[FAIL, PASS]
	BanCheatTapData  []int32           //作弊封禁期间数据 [点击次数, 挂机时间]

	GeneralTasks            *GeneralTaskList                  //常规任务列表
	AchieveTasks            *AchieveTaskList                  //成就任务列表
	WheelRandomAwardMap     map[int32][]*g.TypeObj            //转盘随机奖励
	WheelRandomAwardRecords map[int32][]*WheelRandomAwardInfo //转盘随机奖励
	NicknameMap             map[int32]string                  //昵称map k=>区服id v=>昵称
	RechargeCountRecord     *ut.MapLock[string, int32]        //充值次数记录
	RechargeRefundRecord    *ut.MapLock[string, int32]        //充值退款记录
	SeasonRankScoreHistory  []map[string]interface{}          //赛季历史段位

	ActivityRecord   *ut.MapLock[int32, int32] //活动记录map
	RankRewardRecord *ut.MapLock[int32, bool]  //排位奖励领取记录
	PlantData        *PlantInfo                //种植信息
	BattlePass       *BattlePass               //战令

	// redisSub    *redis.PubSub                     //redis订阅管理
	natsSub     *nats.Subscription                //nats订阅管理
	notifyQueue chan *pb.OnUpdatePlayerInfoNotify //通知队列

	PortrayalsLock *deadlock.RWMutex //英雄画像锁
	HeadIconLock   *deadlock.RWMutex //头像锁
	PawnSkinLock   *deadlock.RWMutex //士兵皮肤锁
	ChatEmojiLock  *deadlock.RWMutex //聊天表情锁
	CitySkinLock   *deadlock.RWMutex //主城皮肤锁
	BotanyLock     *deadlock.RWMutex //植物锁
	TitlesLock     *deadlock.RWMutex //称号锁
	GuidesLock     *deadlock.RWMutex //新手引导锁
	GiveupGameLock *deadlock.RWMutex //放弃对局锁
	UserResLock    *deadlock.RWMutex //资源锁

	PopularityLock             *deadlock.RWMutex //人气锁
	InviteFriendsLock          *deadlock.RWMutex //我邀请的好友列表锁
	NotFinishOrdersLock        *deadlock.RWMutex //已支付未完成的订单锁
	WheelRandomAwardLock       *deadlock.RWMutex //随机奖励锁
	WheelRandomAwardRecordLock *deadlock.RWMutex //随机奖励记录锁
	FriendsLock                *deadlock.RWMutex //好友列表锁
	BlackListLock              *deadlock.RWMutex //黑名单列表锁
	TeamInviteListLock         *deadlock.RWMutex //收到队伍邀请列表锁
	SkinItemListLock           *deadlock.RWMutex //皮肤物品列表锁
	SubscriptionLock           *deadlock.RWMutex //订阅锁

	CreateTime              int64 //创建时间
	LastLoginTime           int64 //最后登陆时间
	LastOfflineTime         int64 //最后离线时间
	NextToDayTime           int64 //明日开始时间
	BannedChatEndTime       int64 //禁言结束时间
	LogoutApplyTime         int64 //注销申请时间
	BanAccountEndTime       int64 //封禁账号结束时间
	LastBuyOptionalHeroTime int64 //最后一次购买自选礼包时间
	dbLastUpdateTime        int64 //db上一次更新时间
	LastOfflineTaTrackTime  int64 //最后一次离线上报时间
	SumOnlineTime           int64 //累计在线时长
	WheelBeginTime          int64 //转动开始时间
	AccLikeJwmCount         int64 //累计点赞九万亩次数

	Version          int32 //用户版本用于兼容
	SID              int32 //所在游戏服
	Gold             int32 //金币
	AccTotalGold     int32 //累计获得的金币
	Ingot            int32 //元宝
	AccTotalIngot    int32 //累计
	WarToken         int32 //兵符
	AccTotalWarToken int32 //累计

	LastTitle       int32 //称号
	LoginDayCount   int32 //登录天数
	CLoginDayCount  int32 //连续登录天数
	ModifyNameCount int32 //修改昵称次数

	TodaySendTrumpetCount int32 //每日发送喇叭次数
	WheelSumCount         int32 //当前一共转动的次数
	WheelCurrCount        int32 //每日已转动次数
	WheelDayFreeCount     int32 //每日免费次数
	PlaySID               int32 //正在玩的服务器id
	CheckPraiseCount      int32 //记录触发好评弹窗x值
	FixedFlag             int32 //兼容处理位标记
	MaxLandCount          int32 //最大地块数
	MaxWheelMul           int32 //历史转盘最大倍数
	RankSeason            int32 //当前赛季
	RankScore             int32 //当前段位分
	RankCoin              int32 //段位积分
	AccTotalRankCoin      int32 //段位积分 累计获得

	BanAccountType        int32 //封禁账号类型
	BattleForecastCount   int32 //战斗预测次数
	ExpectPosition        int32 //期望的位置
	FarmType              int32 //开荒方式
	TodayBuyFreeGoldCount int32 //每日购买免费金币次数
	PassNewbieIndex       int32 //第X局通关了新手区
	TreasureLostCount     int32 //宝箱损失补偿次数
	FreeAdCount           int32 //免广告次数

	state        int32 //状态 0.离线 1.在线
	dbUpdateFlag int32 //更新标记 0.无需更新 1.需要更新 2.准备更新

	CarryNoviceData bool //是否携带新手村数据 false不携带 true携带
	WheelCanGetRet  bool //是否可以获取转动结果
}

func (this *User) SetDbUpdaateFlag(flag int32) {
	atomic.StoreInt32(&this.dbUpdateFlag, flag)
}

func (this *User) GetDbUpdaateFlag() int32 {
	return atomic.LoadInt32(&this.dbUpdateFlag)
}

func (this *User) SetDbLastUpdateTime(dbLastUpdateTime int64) {
	atomic.StoreInt64(&this.dbLastUpdateTime, dbLastUpdateTime)
}

func (this *User) GetDbLastUpdateTime() int64 {
	return atomic.LoadInt64(&this.dbLastUpdateTime)
}

func (this *User) SetState(flag int32) {
	atomic.StoreInt32(&this.state, flag)
}

func (this *User) GetState() int32 {
	return atomic.LoadInt32(&this.state)
}

type UserMap struct {
	deadlock.RWMutex
	Map map[string]*User
}

var (
	users                  = &UserMap{Map: map[string]*User{}} //当前所有连接的用户
	updateUserDBChan       = make(chan *User, 100)
	notifyFriendUpdateChan = make(chan *User, 100)
	userCount              int
	saveCount              int
)

// 创建用户
func CreateUser(data TableData) *User {
	user := &User{
		Version:                 data.Version,
		UID:                     data.Uid,
		LoginType:               data.LoginType,
		DistinctId:              data.DistinctId,
		DeviceOS:                data.DeviceOS,
		Platform:                data.Platform,
		SID:                     data.Sid,
		Gold:                    data.Gold,
		AccTotalGold:            ut.If(data.AccTotalGold == 0, data.Gold, data.AccTotalGold),
		Ingot:                   data.Ingot,
		AccTotalIngot:           data.AccTotalIngot,
		WarToken:                data.WarToken,
		Portrayals:              InitPortrayalsFormDB(data.Portrayals),
		AccTotalWarToken:        data.AccTotalWarToken,
		AccTotalGameCounts:      data.AccTotalGameCounts,
		AccTotalNewbieCounts:    data.AccTotalNewbieCounts,
		AccTotalRankCounts:      data.AccTotalRankCounts,
		AccTotalFreeCounts:      data.AccTotalFreeCounts,
		Nickname:                data.Nickname,
		HeadIcon:                data.HeadIcon,
		PersonalDesc:            data.PersonalDesc,
		CreateTime:              data.CreateTime,
		LastLoginTime:           data.LastLoginTime,
		LastOfflineTime:         data.LastOfflineTime,
		NextToDayTime:           data.NextToDayTime,
		LoginDayCount:           data.LoginDayCount,
		CLoginDayCount:          data.CLoginDayCount,
		SumOnlineTime:           data.SumOnlineTime,
		ModifyNameCount:         data.ModifyNameCount,
		PopularityList:          data.PopularityList,
		PopularityRecords:       data.PopularityRecords,
		UnlockHeadIcons:         data.UnlockHeadIcons,
		UnlockPawnSkinIds:       data.UnlockPawnSkinIds,
		UnlockChatEmojiIds:      data.UnlockChatEmojiIds,
		UnlockCitySkinIds:       data.UnlockCitySkinIds,
		UnlockBotanys:           data.UnlockBotanys,
		Titles:                  data.Titles,
		Referrer:                data.Referrer,
		InviteFriends:           data.InviteFriends,
		Guides:                  data.Guides,
		CarryNoviceData:         data.CarryNoviceData,
		TodaySendTrumpetCount:   data.TodaySendTrumpetCount,
		GeneralTasks:            &GeneralTaskList{List: InitGeneralTaskByDB(data.GeneralTasks), Finishs: data.GeneralFinishs, ToDayFinishs: data.GeneralToDayFinishs},
		AchieveTasks:            &AchieveTaskList{List: InitAchieveTaskByDB(data.AchieveTasks), Finishs: data.AchieveFinishs},
		WheelBeginTime:          data.WheelBeginTime,
		WheelSumCount:           data.WheelSumCount,
		WheelCurrCount:          data.WheelCurrCount,
		WheelRecords:            data.WheelRecords,
		WheelCanGetRet:          data.WheelCanGetRet,
		WheelDayFreeCount:       data.WheelDayFreeCount,
		WheelRandomAwardMap:     data.WheelRandomAwardMap,
		WheelRandomAwardRecords: data.WheelRandomAwardRecords,
		NicknameMap:             data.NicknameMap,
		PlaySID:                 data.PlaySID,
		NotFinishOrders:         data.NotFinishOrders,
		CheckPraiseCount:        data.CheckPraiseCount,
		RechargeCountRecord:     ut.FromMapLock(data.RechargeCountRecord),
		RechargeRefundRecord:    ut.FromMapLock(data.RechargeRefundRecord),
		FixedFlag:               data.FixedFlag,
		MaxLandCount:            data.MaxLandCount,
		LastTitle:               data.LastTitle,
		LastPawnSkins:           data.LastPawnSkins,
		LastCitySkins:           data.LastCitySkins,
		ApplyServers:            data.ApplySevers,
		MaxWheelMul:             data.MaxWheelMul,
		RankSeason:              data.RankSeason,
		RankScore:               data.RankScore,
		HideRankScore:           data.HideRankScore,
		SeasonRankScoreHistory:  data.SeasonRankScoreHistory,
		RankCoin:                data.RankCoin,
		AccTotalRankCoin:        data.AccTotalRankCoin,
		GiveupGames:             data.GiveupGames,
		Language:                data.Language,
		FCMToken:                data.FCMToken,
		BannedChatEndTime:       data.BannedChatEndTime,
		LogoutApplyTime:         data.LogoutApplyTime,
		BanAccountType:          data.BanAccountType,
		BanAccountEndTime:       data.BanAccountEndTime,
		OfflineNotifyOpt:        data.OfflineNotifyOpt,
		SubscriptionInfo:        data.SubscriptionInfo,
		Blacklists:              data.Blacklists,
		FriendsList:             data.FriendsList,
		FriendsApplyList:        data.FriendsApplyList,
		BattleForecastCount:     data.BattleForecastCount,
		GuestId:                 data.GuestId,
		BanRecordList:           data.BanRecordList,
		TeamUid:                 data.TeamUid,
		ExpectPosition:          data.ExpectPosition,
		TeamInviteList:          data.TeamInviteList,
		LastBuyOptionalHeroTime: data.LastBuyOptionalHeroTime,
		TodayBuyFreeGoldCount:   data.TodayBuyFreeGoldCount,
		PassNewbieIndex:         data.PassNewbieIndex,
		AppsflyerId:             data.AppsflyerId,
		AdvertisingId:           data.AdvertisingId,
		AccLikeJwmCount:         data.AccLikeJwmCount,
		LastOfflineTaTrackTime:  data.LastOfflineTaTrackTime,
		SkinItemList:            data.SkinItemList,
		TreasureLostCount:       data.TreasureLostCount,
		ActivityRecord:          ut.FromMapLock(data.ActivityRecord),
		RankRewardRecord:        ut.FromMapLock(data.RankRewardRecord),
		PlantData:               data.PlantData,
		AntiCheatData:           data.AntiCheatData,
		BattlePass:              data.BattlePass,
		FarmType:                data.FarmType,

		dbLastUpdateTime: int64(time.Now().UnixMilli()),

		PortrayalsLock: new(deadlock.RWMutex),
		HeadIconLock:   new(deadlock.RWMutex),
		PawnSkinLock:   new(deadlock.RWMutex),
		ChatEmojiLock:  new(deadlock.RWMutex),
		CitySkinLock:   new(deadlock.RWMutex),
		BotanyLock:     new(deadlock.RWMutex),
		TitlesLock:     new(deadlock.RWMutex),
		GuidesLock:     new(deadlock.RWMutex),
		GiveupGameLock: new(deadlock.RWMutex),
		UserResLock:    new(deadlock.RWMutex),

		PopularityLock:             new(deadlock.RWMutex),
		InviteFriendsLock:          new(deadlock.RWMutex),
		NotFinishOrdersLock:        new(deadlock.RWMutex),
		WheelRandomAwardLock:       new(deadlock.RWMutex),
		WheelRandomAwardRecordLock: new(deadlock.RWMutex),
		FriendsLock:                new(deadlock.RWMutex),
		BlackListLock:              new(deadlock.RWMutex),
		TeamInviteListLock:         new(deadlock.RWMutex),
		SkinItemListLock:           new(deadlock.RWMutex),
		SubscriptionLock:           new(deadlock.RWMutex),
	}
	// 老玩家兼容新增字段
	if user.NicknameMap == nil {
		user.NicknameMap = map[int32]string{}
	}
	if user.UnlockCitySkinIds == nil {
		user.UnlockCitySkinIds = []int32{}
	}
	if user.UnlockBotanys == nil {
		user.UnlockBotanys = []*BotanyInfo{}
	}
	if user.LastCitySkins == nil {
		user.LastCitySkins = []int32{}
	}
	if user.HideRankScore == nil {
		user.HideRankScore = []int32{0, 0}
	}
	if user.GiveupGames == nil {
		user.GiveupGames = []int32{}
	} else {
		user.GiveupGames = array.Delete(user.GiveupGames, func(m int32) bool { return m == 0 })
	}
	if user.AccTotalGameCounts == nil {
		data.AccTotalGameCount -= int32(len(user.GiveupGames))
		user.AccTotalGameCounts = []int32{data.AccTotalWinCount, ut.MaxInt32(data.AccTotalGameCount, data.AccTotalWinCount)}
	}
	if user.AccTotalNewbieCounts == nil {
		user.AccTotalNewbieCounts = []int32{0, 0}
		for sid := range user.NicknameMap {
			if sid/slg.ROOM_TYPE_FLAG == slg.ROOKIE_SERVER_TYPE {
				user.AccTotalNewbieCounts[1]++
			}
		}
		if user.AccTotalNewbieCounts[1] == 0 && user.AccTotalGameCounts[1] > 0 {
			user.AccTotalNewbieCounts[1] = 1
		}
	}
	if user.AccTotalRankCounts == nil {
		user.AccTotalRankCounts = []int32{0, 0}
	}
	if user.AccTotalFreeCounts == nil {
		user.AccTotalFreeCounts = []int32{0, 0}
	}
	if user.OfflineNotifyOpt == nil {
		user.OfflineNotifyOpt = []int32{0, 1, 2, 3, 4, 5}
	}
	if user.WheelRandomAwardMap == nil {
		user.WheelRandomAwardMap = map[int32][]*g.TypeObj{}
	}
	if user.WheelRandomAwardRecords == nil {
		user.WheelRandomAwardRecords = map[int32][]*WheelRandomAwardInfo{}
	}
	if user.BanRecordList == nil {
		user.BanRecordList = []*BanRecord{}
	}
	if len(user.PopularityList) == 0 {
		var val int32
		for _, m := range user.PopularityRecords {
			val += m.Value
			m.Value = ut.If(m.Value < 0, int32(102), 101)
		}
		id := ut.If(val < 0, int32(102), 101)
		user.PopularityList = [][]int32{{id, ut.AbsInt32(val)}}
	}
	if user.FriendsList != nil {
		for _, m := range user.FriendsList {
			m.chats = &FriendChatList{List: []*FriendChatInfo{}}
			// 兼容sid
			if m.PlaySid > 0 && IsRoomClose(m.PlaySid) {
				m.PlaySid = 0
			}
		}
	}
	if user.PlantData == nil {
		user.PlantData = &PlantInfo{ID: 0}
	}
	if user.AntiCheatData == nil {
		user.AntiCheatData = []int32{0, 0}
	}
	if user.BattlePass == nil {
		user.BattlePass = NewBattlePass()
	}
	// 兼容
	if user.PlaySID == 0 {
		// 找出一个在玩的服务器
		for id := range user.NicknameMap {
			if room := GetRoomById(id); room != nil && !IsGameOver(room) {
				if !user.IsGiveupGame(id) {
					user.SetPlaySid(id)
				}
				break
			}
		}
	}
	user.NicknameMap = map[int32]string{}
	return user
}

func (this *User) ToDB() bson.M {
	return bson.M{
		"version":                     this.Version,
		"distinct_id":                 this.DistinctId,
		"device_os":                   this.DeviceOS,
		"platform":                    this.Platform,
		"sid":                         this.SID,
		"login_type":                  this.LoginType,
		"gold":                        this.Gold,
		"acc_total_gold":              this.AccTotalGold,
		"ingot":                       this.Ingot,
		"acc_total_ingot":             this.AccTotalIngot,
		"war_token":                   this.WarToken,
		"portrayals":                  this.PortrayalsClone(),
		"acc_total_war_token":         this.AccTotalWarToken,
		"acc_total_game_counts":       this.AccTotalGameCounts,
		"acc_total_newbie_counts":     this.AccTotalNewbieCounts,
		"acc_total_rank_counts":       this.AccTotalRankCounts,
		"acc_total_free_counts":       this.AccTotalFreeCounts,
		"nickname":                    this.Nickname,
		"head_icon":                   this.HeadIcon,
		"personal_desc":               this.PersonalDesc,
		"create_time":                 this.CreateTime,
		"last_login_time":             this.LastLoginTime,
		"last_offline_time":           this.LastOfflineTime,
		"next_today_time":             this.NextToDayTime,
		"login_day_count":             this.LoginDayCount,
		"clogin_day_count":            this.CLoginDayCount,
		"sum_online_time":             this.SumOnlineTime,
		"modify_name_count":           this.ModifyNameCount,
		"popularity_list":             this.PopularityList,
		"popularity_records":          this.PopularityRecords,
		"unlock_head_icons":           this.HeadIconsClone(),
		"unlock_pawn_skin_ids":        this.PawnSkinsClone(),
		"unlock_chat_emoji_ids":       this.EmojisClone(),
		"unlock_city_skin_ids":        this.CitySkinsClone(),
		"unlock_botanys":              this.UnlockBotanysClone(),
		"titles":                      this.TitlesClone(),
		"referrer":                    this.Referrer,
		"invite_friends":              this.InviteFriends,
		"guides":                      this.GuidesClone(),
		"carry_novice_data":           this.CarryNoviceData,
		"today_send_trumpet_count":    this.TodaySendTrumpetCount,
		"general_tasks":               this.GeneralTasks.List,
		"general_finishs":             this.GeneralTasks.Finishs,
		"general_today_finishs":       this.GeneralTasks.ToDayFinishs,
		"achieve_tasks":               this.AchieveTasks.List,
		"achieve_finishs":             this.AchieveTasks.Finishs,
		"wheel_begin_time":            this.WheelBeginTime,
		"wheel_sum_count":             this.WheelSumCount,
		"wheel_curr_count":            this.WheelCurrCount,
		"wheel_records":               this.WheelRecords,
		"wheel_can_get_ret":           this.WheelCanGetRet,
		"wheel_day_free_count":        this.WheelDayFreeCount,
		"wheel_random_award_map":      this.ToWheelRandomAwardDB(),
		"wheel_random_award_records":  this.ToWheelRandomAwardRecordsDB(),
		"play_sid":                    this.GetPlaySid(),
		"not_finish_orders":           this.NotFinishOrders,
		"ip":                          this.Ip,
		"check_praise_count":          this.CheckPraiseCount,
		"recharge_count_record":       this.RechargeCountRecord.Clone(),
		"recharge_refund_record":      this.RechargeRefundRecord.Clone(),
		"fixed_flag":                  this.FixedFlag,
		"max_land_count":              this.MaxLandCount,
		"last_title":                  this.LastTitle,
		"last_pawn_skins":             this.LastPawnSkins,
		"last_city_skins":             this.LastCitySkins,
		"apply_info":                  this.ApplyServers,
		"max_wheel_mul":               this.MaxWheelMul,
		"rank_season":                 this.RankSeason,
		"rank_score":                  this.RankScore,
		"hide_rank_score":             this.HideRankScore,
		"season_rank_score_history":   this.ToSeasonRankScoreHistoryDB(),
		"rank_coin":                   this.RankCoin,
		"acc_total_rank_coin":         this.AccTotalRankCoin,
		"giveup_games":                this.GiveupGames,
		"language":                    this.Language,
		"fcm_token":                   this.FCMToken,
		"banned_chat_end_time":        this.BannedChatEndTime,
		"logout_apply_time":           this.LogoutApplyTime,
		"ban_account_type":            this.BanAccountType,
		"ban_account_end_time":        this.BanAccountEndTime,
		"offline_notify_opt":          this.OfflineNotifyOpt,
		"subscription_info":           this.SubscriptionInfo,
		"blacklists":                  this.Blacklists,
		"friends_list":                this.FriendsList,
		"friends_apply_list":          this.FriendsApplyList,
		"battle_forecast_count":       this.BattleForecastCount,
		"ban_record_list":             this.BanRecordList,
		"team_uid":                    this.TeamUid,
		"expect_position":             this.ExpectPosition,
		"team_invite_list":            this.TeamInviteList,
		"last_buy_optional_hero_time": this.LastBuyOptionalHeroTime,
		"today_buy_free_gold_count":   this.TodayBuyFreeGoldCount,
		"pass_newbie_index":           this.PassNewbieIndex,
		"appsflyer_id":                this.AppsflyerId,
		"advertising_id":              this.AdvertisingId,
		"acc_like_jwm_count":          this.AccLikeJwmCount,
		"last_offline_tatrack_time":   this.LastOfflineTaTrackTime,
		"skin_item_list":              this.SkinItemList,
		"treasure_lost_count":         this.TreasureLostCount,
		"activity_record":             this.ActivityRecord.Clone(),
		"rank_reward_record":          this.RankRewardRecord.Clone(),
		"plant_data":                  this.PlantData,
		"anti_cheat_data":             this.AntiCheatData,
		"battle_pass":                 this.BattlePass.Clone(),
		"farmType":                    this.FarmType,
	}
}

// 返回给后台
func (this *User) ToWeb(oid string) map[string]interface{} {
	return map[string]interface{}{
		"_id":                  oid,
		"version":              this.Version,
		"uid":                  this.UID,
		"create_time":          this.CreateTime,
		"last_login_time":      this.LastLoginTime,
		"last_offline_time":    this.LastOfflineTime,
		"sid":                  this.SID,
		"play_sid":             this.GetPlaySid(),
		"login_type":           this.LoginType,
		"gold":                 this.Gold,
		"ingot":                this.Ingot,
		"war_token":            this.WarToken,
		"nickname":             this.Nickname,
		"guest_id":             this.GuestId,
		"device_os":            this.DeviceOS,
		"platform":             this.Platform,
		"max_land_count":       this.MaxLandCount,
		"banned_chat_end_time": this.BannedChatEndTime,
		"ban_account_type":     this.BanAccountType,
		"ban_account_end_time": this.BanAccountEndTime,
		"rank_score":           this.RankScore,
		"ban_record_list":      this.BanRecordList,
		"login_day_count":      this.LoginDayCount,
		"acc_newbie":           this.AccTotalNewbieCounts[1],
		"acc_game":             this.AccTotalGameCounts[1],
		"sum_online_time":      this.SumOnlineTime,
		"country":              geoip.GetGeoInfo(this.Ip),
	}
}

// User转为pb格式
func (this *User) ToPb() *pb.UserInfo {
	wheelTime, _, freeCount := this.GetWheelNeedWaitTime(true)
	now := time.Now().UnixMilli()
	var logoutSurplusTime int32 = 0
	if this.LogoutApplyTime > 0 {
		logoutSurplusTime = int32(ut.Max(1000, int(this.LogoutApplyTime+slg.LOGOUT_APPLY_TIME-now)))
	}
	hasWheelAward := array.Some(this.WheelRecords, func(m *WheelRecord) bool {
		this.UpdateWheelNeedSid(m)
		return len(m.Items) > 0 && !m.IsClaim && (m.NeedSid == 0 || m.NeedSid == this.SID)
	})
	rechargeCountRecord := map[string]int32{}
	this.RechargeCountRecord.ForEach(func(v int32, k string) bool {
		rechargeCountRecord[k] = ut.MaxInt32(0, v-this.RechargeRefundRecord.Get(k))
		return true
	})
	activityMap := map[int32]int32{}
	this.ActivityRecord.ForEach(func(v, k int32) bool {
		activityMap[k] = v
		return true
	})
	userInfo := &pb.UserInfo{
		Uid:                        this.UID,
		Sid:                        this.SID,
		PlaySid:                    this.GetPlaySid(),
		CreateTime:                 this.CreateTime,
		LoginType:                  this.LoginType,
		Gold:                       this.Gold,
		Ingot:                      this.Ingot,
		WarToken:                   this.WarToken,
		LoginDayCount:              this.LoginDayCount,
		CloginDayCount:             this.CLoginDayCount,
		SumOnlineTime:              this.SumOnlineTime,
		Nickname:                   this.Nickname,
		HeadIcon:                   this.HeadIcon,
		PersonalDesc:               this.PersonalDesc,
		Title:                      this.LastTitle,
		ModifyNameCount:            this.ModifyNameCount,
		UnlockHeadIcons:            this.HeadIconsClone(),
		CarryNoviceData:            this.CarryNoviceData,
		TotalGameCount:             array.Map(this.AccTotalGameCounts, func(m int32, _ int) int32 { return m }),   //累计游戏次数
		TotalNewbieCount:           array.Map(this.AccTotalNewbieCounts, func(m int32, _ int) int32 { return m }), //累计新手次数
		TotalRankCount:             array.Map(this.AccTotalRankCounts, func(m int32, _ int) int32 { return m }),   //累计排位次数
		TotalFreeCount:             array.Map(this.AccTotalFreeCounts, func(m int32, _ int) int32 { return m }),   //累计自由次数
		GiveupCount:                int32(len(this.GiveupGames)),
		WheelCurrCount:             this.WheelCurrCount,
		WheelFreeCount:             freeCount,
		WheelTime:                  int32(wheelTime),
		HasWheelAward:              hasWheelAward,
		NotFinishOrders:            this.NotFinishOrderToPb(),
		CheckPraiseCount:           this.CheckPraiseCount,
		MaxLandCount:               this.MaxLandCount,
		MaxWheelMul:                this.MaxWheelMul,
		RankSeason:                 this.RankSeason,
		RankScore:                  this.RankScore,
		RankCoin:                   this.RankCoin,
		LogoutSurplusTime:          logoutSurplusTime,                  //注销账号剩余时间
		OfflineNotifyOpt:           array.Clone(this.OfflineNotifyOpt), //离线推送设置
		SubData:                    this.UserSubInfoToPb(),             //订阅信息
		Blacklists:                 this.ToBlacklistsPb(),              //黑名单
		FriendsList:                this.FriendsListToPb(),             //好友列表
		FriendsApplys:              this.FriendsApplyListToPb(),        //好友申请列表
		BattleForecastCount:        this.BattleForecastCount,           //战斗预测次数
		Portrayals:                 this.ToPortrayalsPb(),
		TodaySendTrumpetCount:      this.TodaySendTrumpetCount,
		BuyOptionalHeroSurplusTime: this.GetBuyOptionalHeroSurplusTime(),
		BuyFreeGoldSurplusTime:     this.GetBuyFreeGoldSurplusTime(),
		RechargeCountRecord:        rechargeCountRecord,
		ExpectPosition:             this.ExpectPosition,
		PassNewbieIndex:            this.PassNewbieIndex,
		AccLikeJwmCount:            this.AccLikeJwmCount,
		SessionId:                  this.SessionId,
		ActivityRecord:             activityMap,
		RankRewardRecord:           this.ToRankRewardRecordPb(),
		PlantData:                  this.ToPlantDataPb(),
	}
	userInfo.UnlockPawnSkinIds = this.ToUnlockPawnSkinsPb()
	userInfo.UnlockChatEmojiIds = this.EmojisToPb()
	userInfo.UnlockCitySkinIds = this.ToUnlockCitySkinsToPb()
	userInfo.UnlockBotanys = this.ToUnlockBotanysPb()
	userInfo.Titles = this.TitlesToPb()
	userInfo.InviteFriends = this.InveteFriendsToPb()
	this.GuidesLock.RLock()
	userInfo.Guides = array.Map(this.Guides, func(m *GuideInfo, _ int) *pb.GuideInfo { return &pb.GuideInfo{Id: int32(m.ID), Tag: m.Tag} })
	this.GuidesLock.RUnlock()
	userInfo.SkinItemList = this.ToSkinImtemsPb()
	return userInfo
}

func (this *User) GetOs() string {
	if this.DeviceOS == "" {
		return "none"
	}
	arr := strings.Split(this.DeviceOS, ";")
	if len(arr) >= 1 {
		return arr[0]
	}
	return "none"
}

func (this *User) GetOsAndVer() (deviceOS, deviceOSVersion string) {
	deviceOS = "none"
	deviceOSVersion = "0.0"
	if this.DeviceOS == "" {
		return
	}
	if DeviceOSArr := strings.Split(this.DeviceOS, ";"); len(DeviceOSArr) > 0 {
		deviceOS = DeviceOSArr[0]
		if len(DeviceOSArr) > 1 {
			deviceOSVersion = DeviceOSArr[1]
		}
	}
	return
}

func (this *User) ToBlacklistsPb() []*pb.BlacklistInfo {
	this.BlackListLock.RLock()
	defer this.BlackListLock.RUnlock()
	return array.Map(this.Blacklists, func(m *BlacklistInfo, _ int) *pb.BlacklistInfo {
		return &pb.BlacklistInfo{
			Uid:      m.UID,
			Nickname: m.Nickname,
			HeadIcon: m.HeadIcon,
			Time:     int64(m.Time),
		}
	})
}

func (this *User) Init(lobby *Lobby) {
	now := time.Now().UnixMilli()
	this.SetState(1)
	this.SetDbLastUpdateTime(int64(now))
	this.MaxLandCountFix()
	this.MaxWheelMulFix()
	this.MaxLandCountTaskFix()
	this.Compati(this.Version, lobby)
	this.CheckUpdateRankSeason(lobby)
	this.CheckUpdateNextToDayTime(now, true, nil)
	this.CheckUpdateGeneralTask()
	this.CheckUpdateAchieveTask()
	this.CheckUserSubInfos(now, nil)
	this.CheckUpdateTitle(now, lobby)
	this.CheckUpdatePlant(now)
	// this.InitRedisSub()
	this.UserInfoNotifyTick(lobby)
}

func (this *User) Offline(lobby *Lobby) {
	if this.Session != nil {
		this.Session.Set("lid", "")
		this.Session = nil
	}
	// this.redisSub.Close()
	this.SetState(0)
	lobby.InvokeChatRpcNR(slg.RPC_LEAVE, this.UID, this.TeamUid, this.Language)
	lobby.CheckDelTeam(this.UID, this.TeamUid)
	this.DhLogoutLog()
}

// 是否游客
func (this *User) IsGuest() bool {
	return this.LoginType == slg.LOGIN_TYPE_GUEST
}

// 是否新手 只要一局新手区都没玩并且未放弃过游戏 就是新手
func (this *User) IsNovicePlayer() bool {
	return (len(this.AccTotalNewbieCounts) == 0 || this.AccTotalNewbieCounts[1] < 1) && len(this.GiveupGames) == 0
}

// 总的游戏次数
func (this *User) GetTotalGameCount() int32 {
	return this.AccTotalGameCounts[1]
}

// 新手区次数
func (this *User) GetNewbieGameCount() int32 {
	return this.AccTotalNewbieCounts[1]
}

// 检测是否可以玩某个区
func (this *User) CheckCanPlayGameBySid(sid int32) bool {
	playSid := this.GetPlaySid()
	if playSid == sid {
		// 正在玩该区则可以进入
		return true
	}
	if this.TeamUid != "" && playSid == 0 {
		// 组队只能通过报名进入新区
		return false
	}
	roomType := sid / slg.ROOM_TYPE_FLAG
	if roomType == slg.ROOKIE_SERVER_TYPE {
		return this.CheckCanPlayGameNewbie()
	}
	// 其他区需要报名进入
	return false
}

// 检测是否可以玩新手区
func (this *User) CheckCanPlayGameNewbie() bool {
	totalGameCount := this.GetTotalGameCount()
	newbieCount := this.GetNewbieGameCount()
	if IsCloseApply() {
		// 已关闭报名 可以进入新手
		return true
	}
	if totalGameCount-newbieCount > 0 {
		// 已玩过其他区 不能进入新手区
		return false
	} else if this.PassNewbieIndex > 0 && newbieCount > this.PassNewbieIndex {
		// 新手区通关后只能再玩一次新手区
		return false
	}

	var newbieGiveupCount int32
	this.GiveupGameLock.RLock()
	for _, sid := range this.GiveupGames {
		roomType := sid / slg.ROOM_TYPE_FLAG
		if roomType != slg.ROOKIE_SERVER_TYPE {
			// 放弃过其他区服的对局 不能进入新手区
			this.GiveupGameLock.RUnlock()
			return false
		} else {
			newbieGiveupCount++
		}
	}
	this.GiveupGameLock.RUnlock()
	if newbieCount+newbieGiveupCount >= slg.NEWBIE_MAX_COUNT {
		// 新手区次数上限
		return false
	}
	return true
}

func (this *User) IsOnline() bool {
	return this.GetState() == 1
}

// 获取在线状态 0.在线 否则离线时间
func (this *User) GetOnlineState() int64 {
	if this.IsOnline() {
		return 0
	}
	return this.LastOfflineTime
}

// 获取禁言时间
func (this *User) GetBannedChatEndTime() int64 {
	if this.BannedChatEndTime <= time.Now().UnixMilli() {
		this.BannedChatEndTime = 0
	}
	return this.BannedChatEndTime
}

// 是否被禁言
func (this *User) IsBannedChat() bool {
	return this.GetBannedChatEndTime() > 0
}

// 获取剩余购买免费金币的剩余时间
func (this *User) GetBuyFreeGoldSurplusTime() int64 {
	if this.TodayBuyFreeGoldCount <= 0 {
		return 0
	}
	now := time.Now().UnixMilli()
	if this.NextToDayTime > now { //需要明天来了
		return ut.MaxInt64(0, this.NextToDayTime-now)
	}
	this.CheckUpdateNextToDayTime(now, false, nil)
	return this.GetBuyFreeGoldSurplusTime()
}

// 获取剩余购买自选英雄包的剩余时间
func (this *User) GetBuyOptionalHeroSurplusTime() int64 {
	if this.LastBuyOptionalHeroTime <= 0 {
		return 0
	} else if this.LastBuyOptionalHeroTime < ut.GetCurrentWeekDayTime(1) {
		return 0 //小于本周1 说明已经过了 可以再次购买
	}
	// 获取到下周1的时间
	return ut.MaxInt64(0, ut.GetNextWeekDayTime(1)-time.Now().UnixMilli())
}

func InitNextToDayTime() int64 {
	now := time.Now()
	nextRefresh := time.Date(now.Year(), now.Month(), now.Day(), 6, 0, 0, 0, time.Local)
	if now.After(nextRefresh) {
		nextRefresh = nextRefresh.Add(24 * time.Hour)
	}
	return nextRefresh.UnixMilli()
}

// 检测是否过天
func (this *User) CheckUpdateNextToDayTime(now int64, init bool, lobby *Lobby) {
	if this.NextToDayTime <= now {
		this.NextToDayTime = InitNextToDayTime()
		// 清空每日免费金币次数
		this.TodayBuyFreeGoldCount = 0
		// 清空发送喇叭次数
		this.TodaySendTrumpetCount = 0
		// 清空转盘次数
		this.WheelCurrCount = 0
		this.WheelDayFreeCount = 0
		this.WheelRandomAwardLock.Lock()
		this.WheelRandomAwardMap = map[int32][]*g.TypeObj{}
		this.WheelRandomAwardLock.Unlock()
		// 刷新下每日任务
		this.GeneralTasks.Lock()
		this.GeneralTasks.ToDayFinishs = []int32{} //清空每日完成任务
		if !init {
			this.CheckUpdateGeneralTask()
		}
		this.GeneralTasks.Unlock()
		if !init {
			// 订阅检测
			this.CheckUserSubInfos(now, lobby)
			// 称号检测
			this.CheckUpdateTitle(now, lobby)
		}
		if this.BattlePass != nil {
			this.BattlePass.NextDay()
		}
		this.FlagUpdateDB()
		// 暂时注释掉 由游戏服自己去维护NextToDayTime
		// if this.PlaySID != 0 && lobby != nil {
		// 	// 在对局中则通知游戏服
		// 	lobby.InvokeGameRpcNR(this.PlaySID, slg.RPC_CHECKUP_DATE_NEXT_TO_DAYTIME, this.UID)
		// }
		if !init {
			// 非初始化时调用 则通知玩家
			this.PutNotifyQueue(constant.NQ_UPDATE_GENERAL_TASKS, &pb.OnUpdatePlayerInfoNotify{
				Data_48: this.GeneralTasks.ToPb(),
			}) //常规任务列表
			this.PutNotifyQueue(constant.NQ_UPDATE_WHEEL_COUNT, &pb.OnUpdatePlayerInfoNotify{
				Data_51: this.WheelCurrCount,
			}) //转盘次数
			this.PutNotifyQueue(constant.NQ_TODAY_TRUMPET_COUNT, &pb.OnUpdatePlayerInfoNotify{
				Data_72: this.TodaySendTrumpetCount,
			}) //喇叭次数
			this.PutNotifyQueue(constant.NQ_TODAY_FREE_GOLD_TIME, &pb.OnUpdatePlayerInfoNotify{
				Data_73: int64(this.GetBuyFreeGoldSurplusTime()),
			}) //每日免费金币剩余时间
		}
	}
}

// 玩家聊天数据保存
func (this *User) CheckUpdateChatDB() int {
	this.FriendsLock.RLock()
	defer this.FriendsLock.RUnlock()
	count := 0
	for _, friendInfo := range this.FriendsList {
		if chats := friendInfo.chats; chats != nil && len(chats.List) > 0 && chats.NeedUpdateDB {
			chats.NeedUpdateDB = false
			channel := GetFriendChannel(this.UID, friendInfo.Uid)
			chats.RLock()
			friendChatDb.UpdateChats(channel, chats.List)
			chats.RUnlock()
			count += 1
		}
	}
	return count
}

// 标记
func (this *User) FlagUpdateDB() bool {
	this.SetDbUpdaateFlag(1)
	return this.IsOnline()
}

// 获取playsid
func (this *User) GetPlaySid() int32 {
	if this.PlaySID != 0 && IsRoomClose(this.PlaySID) {
		// 区服已关闭
		this.SetPlaySid(0)
	}
	return this.PlaySID
}

// 设置playsid
func (this *User) SetPlaySid(sid int32) {
	if this.PlaySID != sid {
		log.Warning("SetPlaySid uid: %v, playSid: %v, new: %v", this.UID, this.PlaySID, sid)
		this.PlaySID = sid
		// 刷新转盘记录
		for _, m := range this.WheelRecords {
			this.UpdateWheelNeedSid(m)
		}
	}
}

// 保存所有用户
func SaveAllUser() {
	users.Lock()
	saveCount = 0
	userCount = len(users.Map)
	log.Info("SaveAllUser start userCount: %v", userCount)
	for _, user := range users.Map {
		db.UpdateData(user.UID, user.ToDB())
		user.CheckUpdateChatDB()
		user.Kick(slg.KICK_NOTIFY_TYPE_NONE, 0)
		saveCount++
	}
	users.Map = map[string]*User{}
	users.Unlock()
	log.Info("SaveAllUser finish")
}

func GetUser(uid string) *User {
	if uid == "" {
		return nil
	}
	users.RLock()
	defer users.RUnlock()
	return users.Map[uid]
}

func GetUserByRpc(uid string) *User {
	user := GetUser(uid)
	// rpc获取在线玩家 获取之前已添加大厅服分配锁 这里需要移除
	rds.RemUserMallocLidLock(uid)
	return user
}

// 获取在线玩家
func GetUserByOnline(uid string) *User {
	user := GetUser(uid)
	if user != nil && user.IsOnline() {
		return user
	}
	return nil
}

func GetUserByNickname(name string) *User {
	users.RLock()
	defer users.RUnlock()
	for _, user := range users.Map {
		if user.Nickname == name {
			return user
		}
	}
	return nil
}

// 从数据库获取
func GetUserByDB(uid string, lid string) *User {
	user := GetUser(uid)
	if user != nil {
		user.SetDbUpdaateFlag(1)
		user.SetDbLastUpdateTime(int64(time.Now().UnixMilli()))
	} else if data, e := db.FindByUid(uid); e == "" {
		user = CreateUser(data)
		users.Lock()
		users.Map[uid] = user
		users.Unlock()
		rds.UpdateLobbyLoad(uid, lid, true, false)
	}
	rds.RemUserMallocLidLock(uid)
	return user
}

// 从数据库获取 根据昵称 不存到内存中
func GetUserByNicknmaeOrDB(name string) *User {
	user := GetUserByNickname(name)
	if user != nil {
		user.SetDbUpdaateFlag(1)
		user.SetDbLastUpdateTime(int64(time.Now().UnixMilli()))
	} else if data, e := db.FindUserByName(name); e == "" {
		user = CreateUser(data)
	}
	return user
}

// 从数据库获取 但是不存起来 仅用于获取但不修改数据
func GetUserByDBNotAdd(uid string) *User {
	user := GetUser(uid)
	if user != nil {
	} else if data, e := db.FindByUid(uid); e == "" {
		user = CreateUser(data)
	}
	return user
}

// 用户离线
func UserOffline(lobby *Lobby, uid, sessionId string) {
	user := GetUser(uid)
	if user == nil {
		log.Info("UserOffline uid: %v, user nil!", uid)
		return
	} else if user.Session == nil {
		user.Offline(lobby)
		// DelUser(uid)
		log.Info("UserOffline uid: %v, Session nil!", uid)
	} else if user.Session.GetSessionID() == sessionId { //相同sessionId 保存数据并移除玩家
		now := time.Now().UnixMilli()
		addTime := ut.MaxInt64(now-user.LastLoginTime, 0)
		user.SumOnlineTime += int64(addTime) //总在线时间
		user.LastOfflineTime = now
		user.SetUserOfflineTime(now)
		user.SetDbUpdaateFlag(1)
		user.SetDbLastUpdateTime(int64(now))
		user.Offline(lobby)
		user.FriendInfoNotify()
		UserLeaveTrack(user, now)
		log.Info("UserOffline uid: %v, done.", uid)
	} else {
		//不同sessionId 可能为前一个session异常断线导致 无需移除玩家
		log.Info("UserOffline uid: %v, lastSession: %v, curSession: %v", uid, sessionId, user.Session.GetSessionID())
	}
	// log.Info("3 UserOffline uid: %v, onlineCount: %v", uid, len(users.Map))
}

// 用户离线上报
func UserLeaveTrack(user *User, now int64) {
	if now-user.LastOfflineTaTrackTime >= ta.USER_OFFLINE_TRACK_INTERVAL {
		user.LastOfflineTaTrackTime = now
		ta.UserSet(0, user.UID, user.DistinctId, 0, map[string]interface{}{
			"version":          slg.CLIENT_VERSION,
			"ingot":            user.Ingot,
			"accTotalIngot":    user.AccTotalIngot,
			"gold":             user.Gold,
			"accTotalGold":     user.AccTotalGold,
			"warToken":         user.WarToken,
			"accTotalWarToken": user.AccTotalWarToken,
			"gameCount":        user.GetTotalGameCount(),
			"gameSumWinCount":  user.AccTotalGameCounts[0],
			"rank_score":       user.RankScore,
			"wheelSumCount":    user.WheelSumCount,
			"giveupCount":      len(user.GiveupGames),
			"common_emoji":     len(user.UnlockChatEmojiIds),
			"common_avatar":    len(user.UnlockHeadIcons),
			"common_skin":      len(user.UnlockPawnSkinIds),
		})
	}
}

// 删除账号信息
type DelUserInfo struct {
	Uid       string `bson:"uid"`
	Name      string `bson:"name"`
	Email     string `bson:"email"`
	Identity  string `bson:"identity"`
	LoginType string `bson:"loginType"`
	NickName  string `bson:"nickName"`
	OpenId    string `bson:"openid"` //第三方登录openId

	ApplyTime         int64 `bson:"applyTime"`
	BannedChatEndTime int64 `bson:"banned_chat_end_time"` //禁言结束时间
	BanAccountEndTime int64 `bson:"ban_account_end_time"` //封禁账号结束时间

	Gold            int32 `bson:"gold"`
	Ingot           int32 `bson:"ingot"`
	WarToken        int32 `bson:"war_token"`
	TotalGold       int32 `bson:"totalGold"`
	State           int32 `bson:"state"`
	MaxLandCount    int32 `bson:"max_land_count"`    //最大地块数
	BanAccountType  int32 `bson:"ban_account_type"`  //封禁账号类型
	NewbieCount     int32 `bson:"newbie_count"`      //新手区局数
	PassNewbieIndex int32 `bson:"pass_newbie_index"` //第X局通关了新手区
}

// 申请删除账号
func ApplyDelUser(user *User, name, email, identity, openId string) {
	log.Info("ApplyDelUser uid: %v", user.UID)
	newbieCount := user.AccTotalNewbieCounts[1]
	user.GiveupGameLock.RLock()
	for _, sid := range user.GiveupGames {
		roomType := sid / slg.ROOM_TYPE_FLAG
		if roomType == slg.ROOKIE_SERVER_TYPE {
			newbieCount++
		}
	}
	user.GiveupGameLock.RUnlock()
	delInfo := &DelUserInfo{
		Uid:               user.UID,
		Name:              name,
		Email:             email,
		Identity:          identity,
		LoginType:         user.LoginType,
		NickName:          user.Nickname,
		Gold:              user.Gold,
		Ingot:             user.Ingot,
		WarToken:          user.WarToken,
		TotalGold:         user.AccTotalGold,
		ApplyTime:         time.Now().UnixMilli(),
		State:             slg.ACCOUNT_DEL_STATE_APPLY,
		MaxLandCount:      user.MaxLandCount,
		BannedChatEndTime: user.BannedChatEndTime,
		BanAccountType:    user.BanAccountType,
		BanAccountEndTime: user.BanAccountEndTime,
		OpenId:            openId,
		NewbieCount:       newbieCount,
		PassNewbieIndex:   user.PassNewbieIndex,
	}
	if err := db.ApplyDelUser(delInfo); err == "" {
		delUserMap.Store(user.UID, delInfo)
	}
}

// 删除用户
func DelUserAndDB(uid string) {
	users.Lock()
	_, exist := users.Map[uid]
	delete(users.Map, uid)
	users.Unlock()
	if exist {
		// 从内存中移除则更新redis数据
		rds.UpdateLobbyLoad(uid, "", false, false)
	}
	db.DelUser(uid)
}

// 撤回删除用户
func (this *Lobby) CancelDelUser(uid string) {
	log.Info("CancelDelUser uid: %v", uid)
	delUserMap.Delete(uid)
	db.CancelDelUser(uid)
	// 通知其他大厅服撤回删除
	this.BroadCastOtherLobby(slg.RPC_CANCEL_DEL_USER, uid)
}

// 禁言
func (this *User) BanUserChat(banTime int64) {
	if banTime <= 0 {
		// 解封
		this.BannedChatEndTime = 0
		this.FlagUpdateDB()
		return
	}
	now := time.Now().UnixMilli()
	newBanEndTime := now + banTime
	if newBanEndTime <= this.BannedChatEndTime {
		// 新的禁言结束时间小于之前的 不处理
		return
	}
	this.AddBanRecord(slg.BAN_TYPE_CHAT, banTime, now) // 添加封禁记录
	this.BannedChatEndTime = newBanEndTime
	// 更新同一设备id账号的禁言时间
	db.UpdateBanChatByDistinct(this.DistinctId, this.BannedChatEndTime)
	this.FlagUpdateDB()
}

// 封禁用户
func (this *Lobby) BanUserAccount(user *User, banTime int64, reason int32, banDevice bool) {
	log.Warning("BanUserAccount uid: %v, banTime: %v, reason: %v", user.UID, banTime, reason)
	if banTime <= 0 {
		// 解封
		user.BanAccountEndTime = 0
		if reason == slg.BAN_TYPE_REFUND {
			user.BanUserSkinItemTrack(banTime > 0) // 封禁用户皮肤溯源处理
		}
		user.FlagUpdateDB()
		return
	}
	now := time.Now().UnixMilli()
	newBanEndTime := now + banTime
	if newBanEndTime <= user.BanAccountEndTime {
		// 新的封禁结束时间小于之前的 不处理
		return
	}
	user.AddBanRecord(reason, banTime, now) // 添加封禁记录
	user.BanAccountEndTime = newBanEndTime
	user.BanAccountType = reason
	if reason == slg.BAN_TYPE_REFUND {
		user.BanUserSkinItemTrack(banTime > 0) // 封禁用户皮肤溯源处理
	}
	// 强制下线
	this.UserForceOffline(user, now, slg.KICK_NOTIFY_TYPE_BAN, reason)
	if banDevice {
		// 更新同一设备id账号的封禁时间
		db.UpdateBanByDistinct(user.DistinctId, user.BanAccountEndTime, reason)
	}
}

// 用户强制离线
func (this *Lobby) UserForceOffline(user *User, now int64, kickType int8, banType int32) {
	this.InvokeGameRpcNR(user.GetPlaySid(), slg.RPC_KICK, user.UID)
	user.Kick(kickType, banType)
	time := ut.MaxInt64(now-user.LastLoginTime, 0)
	user.SumOnlineTime += int64(time) //总在线时间
	user.LastOfflineTime = now
	user.SetUserOfflineTime(now)
	user.SetDbUpdaateFlag(1)
	user.SetDbLastUpdateTime(int64(now))
	user.Offline(this)
	user.FriendInfoNotify()
	UserLeaveTrack(user, now)
}

// 改变防作弊数据
func (this *Lobby) ChangeUserAntiCheatData(user *User, rst bool, count int32) {
	if user.AntiCheatData == nil {
		user.AntiCheatData = []int32{0, 0}
	}
	if !rst {
		// 未通过检测 处理封禁
		user.AntiCheatData[0] += count
		user.AntiCheatData[1] = 0
		fail := user.AntiCheatData[0]
		if banTime, ok := constant.ANTI_CHEAT_BAN_TIME_MAP[fail]; ok {
			this.BanUserAccount(user, banTime, slg.BAN_TYPE_CHEAT, false)
		} else {
			var banTime int64
			for k, v := range constant.ANTI_CHEAT_BAN_TIME_MAP {
				if fail >= k && v > banTime {
					banTime = v
				}
			}
			if banTime > 0 {
				this.BanUserAccount(user, banTime, slg.BAN_TYPE_CHEAT, false)
			}
		}
	} else {
		// 通过检测 连续失败次数归零
		user.AntiCheatData[1] += count
		user.AntiCheatData[0] = 0
	}
	user.FlagUpdateDB()
}

// 改变元宝
func (this *User) ChangeIngot(val, reason int32) int32 {
	this.UserResLock.Lock()
	defer this.UserResLock.Unlock()
	if val < 0 && this.Ingot+val < 0 {
		return -1
	}
	this.Ingot += val
	if val > 0 {
		this.AccTotalIngot += val //记录累计获得
	}
	AddIngotRecord(this.UID, val, this.Ingot, reason)
	this.FlagUpdateDB()
	return this.Ingot
}

// 扣除元宝
func (this *Lobby) ReduceIngot(user *User, val, reason int32) int32 {
	user.UserResLock.Lock()
	defer user.UserResLock.Unlock()
	user.Ingot -= val
	// 如果小于0 就封号
	if user.Ingot < 0 {
		this.BanUserAccount(user, ut.TIME_DAY*3650, slg.BAN_TYPE_REFUND, false)
	}
	AddIngotRecord(user.UID, -val, user.Ingot, reason)
	user.FlagUpdateDB()
	return user.Ingot
}

// 改变金币
func (this *User) ChangeGold(val, reason int32) int32 {
	this.UserResLock.Lock()
	defer this.UserResLock.Unlock()
	if val < 0 && this.Gold+val < 0 {
		return -1
	}
	this.Gold += val
	if val > 0 {
		this.AccTotalGold += val //记录累计获得金币
	}
	AddGoldRecord(this.UID, val, this.Gold, reason)
	this.FlagUpdateDB()
	return this.Gold
}

// 扣除金币
func (this *Lobby) ReduceGold(user *User, val, reason int32) int32 {
	user.UserResLock.Lock()
	defer user.UserResLock.Unlock()
	user.Gold -= val
	AddIngotRecord(user.UID, -val, user.Gold, reason)
	user.FlagUpdateDB()
	return user.Gold
}

// 改变兵符
func (this *User) ChangeWarToken(val, reason int32) int32 {
	this.UserResLock.Lock()
	defer this.UserResLock.Unlock()
	if val < 0 && this.WarToken+val < 0 {
		return -1
	}
	this.WarToken += val
	if val > 0 {
		this.AccTotalWarToken += val //记录累计获得
	}
	AddItemRecord(this.UID, ctype.WAR_TOKEN, 0, val, this.WarToken, reason, "")
	this.FlagUpdateDB()
	return this.WarToken
}

// 改变段位积分
func (this *User) ChangeRankCoin(val int32) int32 {
	this.UserResLock.Lock()
	defer this.UserResLock.Unlock()
	if val < 0 && this.RankCoin+val < 0 {
		return -1
	}
	this.RankCoin += val
	if val > 0 {
		this.AccTotalRankCoin += val //记录累计获得
	}
	this.FlagUpdateDB()
	return this.RankCoin
}

// 改变物品
func (this *User) ChangeUserItems(items []*g.TypeObj, reason int32, output *pb.UpdateOutPut) *pb.UpdateOutPut {
	if output == nil {
		output = &pb.UpdateOutPut{}
	}
	for _, m := range items {
		if m.Type == ctype.GOLD {
			this.ChangeGold(m.Count, reason)
			output.Gold = this.Gold
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Gold))
		} else if m.Type == ctype.INGOT {
			this.ChangeIngot(m.Count, reason)
			output.Ingot = this.Ingot
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Ingot))
		} else if m.Type == ctype.WAR_TOKEN {
			this.ChangeWarToken(m.Count, reason)
			output.WarToken = this.WarToken
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_WarToken))
		} else if m.Type == ctype.TITLE { //称号
			this.AddTitleByGm(m.Id)
			output.Titles = this.TitlesToPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Title))
		} else if m.Type == ctype.PAWN_SKIN {
			this.AddPawnSkin(m.Id)
			output.PawnSkins = this.ToUnlockPawnSkinsPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_PawnSkin))
		} else if m.Type == ctype.RANK_SCORE {
			this.RankScore += m.Count
		} else if m.Type == ctype.HERO_DEBRIS { //英雄残卷
			this.AddPortrayalDebrisCount(m.Id, m.Count)
			output.Portrayals = this.ToPortrayalsPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Portrayal))
		} else if m.Type == ctype.HERO_OPT { //自选英雄
			this.AddPortrayalDebrisCount(lc.GetRandomHeroIdByLv(m.Id), lc.PORTRAYAL_COMP_NEED_COUNT)
			output.Portrayals = this.ToPortrayalsPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Portrayal))
			log.Warning("ChangeUserItems uid: %v, heroId: %v, reason: %v", this.UID, m.Id, reason)
		} else if m.Type == ctype.SKIN_ITEM { //皮肤物品
			this.AddSkinItem("", m.Id, false, false)
			output.SkinItems = this.ToSkinImtemsPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_SkinItemEnum))
		} else if m.Type == ctype.CITY_SKIN { //城市皮肤
			this.AddCitySkin(m.Id)
			output.CitySkins = this.ToUnlockCitySkinsToPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_CitySkin))
		} else if m.Type == ctype.BOTANY { //植物
			this.ChangeBotany(m.Id, m.Count, "")
			output.UnlockBotanys = this.ToUnlockBotanysPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_Botany))
		} else if m.Type == ctype.HEAD_ICON { //头像
			if json := config.GetJsonData("headIcon", m.Id); json != nil {
				this.AddHeadIcon(ut.String(json["icon"]))
			}
			output.UnlockHeadIcons = this.HeadIconsClone()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_HeadIcon))
		} else if m.Type == ctype.CHAT_EMOJI { //聊天表情
			this.AddEmoji(m.Id)
			output.UnlockChatEmojiIds = this.EmojisToPb()
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_ChatEmoji))
		} else if m.Type == ctype.RANK_COIN { //段位积分
			this.ChangeRankCoin(m.Count)
			output.RankCoin = this.RankCoin
			output.Flag |= pb.AddFlags(int64(pb.OutPutFlagEnum_RankCoin))
		} else if m.Type == ctype.BATTLE_PASS { //战令
			if this.BattlePass == nil {
				this.BattlePass = NewBattlePass()
			}
			this.BattlePass.BuyBattlePass(this.UID)
		} else if m.Type == ctype.BATTLE_PASS_SCORE { //战令积分
			if this.BattlePass == nil {
				this.BattlePass = NewBattlePass()
			}
			this.BattlePass.Lock()
			this.BattlePass.Score = ut.MinInt32(slg.BATTLE_PASS_SCORE_MAX, this.BattlePass.Score+m.Count)
			this.BattlePass.Unlock()
		} else if m.Type == ctype.FREE_AD { //免广告次数
			this.FreeAdCount += m.Count
		}
	}
	this.FlagUpdateDB()
	return output
}

// 是否拥有头像
func (this *User) HasHeadIcon(icon string) bool {
	this.HeadIconLock.RLock()
	defer this.HeadIconLock.RUnlock()
	return array.Has(this.UnlockHeadIcons, icon)
}

// 添加头像
func (this *User) AddHeadIcon(icon string) {
	this.HeadIconLock.Lock()
	if !array.Has(this.UnlockHeadIcons, icon) {
		this.UnlockHeadIcons = append(this.UnlockHeadIcons, icon)
		this.FlagUpdateDB()
	}
	this.HeadIconLock.Unlock()
}

// 头像列表克隆
func (this *User) HeadIconsClone() []string {
	arr := []string{}
	this.HeadIconLock.RLock()
	for _, v := range this.UnlockHeadIcons {
		arr = append(arr, v)
	}
	this.HeadIconLock.RUnlock()
	return arr
}

// 是否拥有士兵皮肤
func (this *User) HasPawnSkin(id int32) bool {
	this.PawnSkinLock.RLock()
	defer this.PawnSkinLock.RUnlock()
	return array.Has(this.UnlockPawnSkinIds, id)
}

// 解锁士兵皮肤
func (this *User) AddPawnSkin(id int32) {
	if config.GetJsonData("pawnSkin", id) == nil {
		return
	}
	this.PawnSkinLock.Lock()
	if !array.Has(this.UnlockPawnSkinIds, id) {
		this.UnlockPawnSkinIds = append(this.UnlockPawnSkinIds, id)
		this.FlagUpdateDB()
	}
	this.PawnSkinLock.Unlock()
}

// 士兵皮肤列表转为pb
func (this *User) ToUnlockPawnSkinsPb() []int32 {
	this.PawnSkinLock.RLock()
	defer this.PawnSkinLock.RUnlock()
	return pb.Int32Array(this.UnlockPawnSkinIds)
}

// 士兵皮肤列表克隆
func (this *User) PawnSkinsClone() []int32 {
	arr := []int32{}
	this.PawnSkinLock.RLock()
	for _, v := range this.UnlockPawnSkinIds {
		arr = append(arr, v)
	}
	this.PawnSkinLock.RUnlock()
	return arr
}

// 是否放弃游戏
func (this *User) IsGiveupGame(sid int32) bool {
	this.GiveupGameLock.RLock()
	defer this.GiveupGameLock.RUnlock()
	return array.Has(this.GiveupGames, sid)
}

// 添加放弃对局
func (this *User) AddGiveupGame(sid int32) {
	if !this.IsGiveupGame(sid) {
		this.GiveupGameLock.Lock()
		this.GiveupGames = append(this.GiveupGames, sid)
		this.GiveupGameLock.Unlock()
	}
}

// 最大地块数兼容
func (this *User) MaxLandCountFix() {
	if pb.HasFlagInt32(this.FixedFlag, pb.PFFE_MAXLAND_COUNT_1) {
		return
	}
	this.FixedFlag |= pb.AddFlagsInt32(pb.PFFE_MAXLAND_COUNT_1)
	// 遍历完成任务
	for _, id := range this.AchieveTasks.Finishs {
		var count int32
		if id == 10030001 { //100
			count = 100
		} else if id == 10030002 {
			count = 1000
		} else if id == 10030003 {
			count = 10000
		}
		if count > this.MaxLandCount {
			this.MaxLandCount = count
		}
	}
}

// 最大地块任务数据兼容
func (this *User) MaxLandCountTaskFix() {
	if pb.HasFlagInt32(this.FixedFlag, pb.PFFE_MAX_LAND_TASK) {
		return
	}
	this.FixedFlag |= pb.AddFlagsInt32(pb.PFFE_MAX_LAND_TASK)
	this.AchieveTasks.RLock()
	defer this.AchieveTasks.RUnlock()
	for _, t := range this.AchieveTasks.List {
		cond := t.GetCondInfo()
		if cond.Type == tctype.THOUSAND_MU {
			t.AddProgress(this.MaxLandCount)
		}
	}
}

// 历史最大转动倍数
func (this *User) MaxWheelMulFix() {
	if pb.HasFlagInt32(this.FixedFlag, pb.PFFE_MAX_WHEEL_MUL) {
		return
	}
	this.FixedFlag |= pb.AddFlagsInt32(pb.PFFE_MAX_WHEEL_MUL)
	// 遍历完成任务
	for _, id := range this.AchieveTasks.Finishs {
		var count int32
		if id == 10100001 {
			count = 9
		} else if id == 10100002 {
			count = 27
		}
		if count > this.MaxWheelMul {
			this.MaxWheelMul = count
		}
	}
	//
	for _, m := range this.AchieveTasks.List {
		var count int32
		if m.ID == 10100001 && m.Progress >= 1 {
			count = 9
		} else if m.ID == 10100002 && m.Progress >= 1 {
			count = 27
		}
		if count > this.MaxWheelMul {
			this.MaxWheelMul = count
		}
	}
}

// 消息推送
func (this *User) NotifyUser(topic string, m protoreflect.ProtoMessage) {
	bytes, err := pb.ProtoMarshal(m)
	if err != "" {
		log.Error("NotifyUser err: %v, topic: %v, data: %v", err, topic, m)
	} else if this.Session != nil {
		// log.Info("SendNR uid: %v, topic: %v", this.UID, topic)
		this.Session.SendNR(topic, bytes)
	}
}

// 是否拥有城市皮肤
func (this *User) HasCitySkin(id int32) bool {
	this.CitySkinLock.RLock()
	defer this.CitySkinLock.RUnlock()
	return array.Has(this.UnlockCitySkinIds, id)
}

// 解锁城市皮肤
func (this *User) AddCitySkin(id int32) {
	if config.GetJsonData("citySkin", id) == nil {
		return
	}
	this.CitySkinLock.Lock()
	if !array.Has(this.UnlockCitySkinIds, id) {
		this.UnlockCitySkinIds = append(this.UnlockCitySkinIds, id)
		this.FlagUpdateDB()
	}
}

// 城市皮肤克隆
func (this *User) CitySkinsClone() []int32 {
	this.CitySkinLock.RLock()
	arr := []int32{}
	for _, v := range this.UnlockCitySkinIds {
		arr = append(arr, v)
	}
	this.CitySkinLock.RUnlock()
	return arr
}

// 城市皮肤ToPb
func (this *User) ToUnlockCitySkinsToPb() []int32 {
	this.CitySkinLock.RLock()
	arr := []int32{}
	for _, v := range this.UnlockCitySkinIds {
		arr = append(arr, v)
	}
	this.CitySkinLock.RUnlock()
	return arr
}

// 离线消息通知
func (this *User) OfflineNotify(msgType int32, params ...string) {
	if !slg.IsOpenOfflineMsg() {
		return
	}
	if this.FCMToken != "" {
		open, typeOpen := false, false // 离线通知总开关和对应类型开关
		for _, v := range this.OfflineNotifyOpt {
			if v == 0 {
				open = true
			} else if v == constant.OFFLINE_MSG_OPT_MAP[msgType] {
				typeOpen = true
			}
		}
		if msgType == constant.OFFLINE_MSG_TYPE_GAME_OVER {
			// 对局奖励消息默认推送
			typeOpen = true
		}
		if !open || !typeOpen {
			return
		}
		data := map[string]string{}
		title, body := genOfflineMsg(this.Language, msgType, params...)
		if body != "" {
			sdk.SendOfflineMessge(this.FCMToken, title, body, data)
		}
	}
}

// 踢出玩家
func (this *User) Kick(kickType int8, banType int32) {
	if this.Session == nil {
		return
	}
	if kickType >= 0 {
		body, _ := pb.ProtoMarshal(&pb.GAME_ONKICK_NOTIFY{Type: int32(kickType), BanType: banType})
		this.Session.SendNR("game/OnKick", body)
	}
	this.Session.UnBind()
	this.Session.Set("sid", "")
	this.Session.Push()
	this.Session.Close()
	this.Session = nil
}

// 生成离线消息文本
func genOfflineMsg(language string, msgType int32, params ...string) (title, msg string) {
	jsonCfg := config.GetJsonData("messagePushText", msgType)
	if jsonCfg == nil {
		log.Error("genOfflineMsg cfg nil msgType: %v", msgType)
		return
	}
	textCfg := ut.MapInterface(jsonCfg["text"])
	if textCfg == nil {
		log.Error("genOfflineMsg textCfg nil msgType: %v", msgType)
		return
	}
	if language == "" {
		language = "cn"
	}
	text := ut.String(textCfg[language])
	if text == "" {
		log.Error("genOfflineMsg text nil msgType: %v, language: %v", msgType, language)
		return
	}
	titleCfg := ut.MapInterface(jsonCfg["title"])
	if titleCfg == nil {
		log.Error("genOfflineMsg titleCfg nil msgType: %v", msgType)
		return
	}
	title = ut.String(titleCfg[language])
	switch msgType {
	case constant.OFFLINE_MSG_TYPE_NEW_MSG: //好友消息
		if len(params) < 3 {
			log.Error("genOfflineMsg friend msg params: %v", params)
			return
		}
		textArr := strings.Split(text, "/")
		if len(textArr) < 2 {
			log.Error("genOfflineMsg text err: %v", text)
			return
		}
		title = strings.Replace(title, "{0}", params[0], -1)
		emojiId := ut.Int(params[2])
		if emojiId <= 0 {
			// 文字内容
			msg = strings.Replace(textArr[0], "{0}", params[1], -1)
		} else {
			// 表情
			msg = textArr[1]
		}
	case constant.OFFLINE_MSG_TYPE_GAME_OVER: //对局结束
		sid := ut.Int(params[0])
		serverName := slg.GetServerNoName(sid, language)
		msg = strings.Replace(text, "{0}", serverName, -1)
	case constant.OFFLINE_MSG_TYPE_TEAM_INVITE: //组队邀请
		msg = strings.Replace(text, "{0}", params[0], -1)
	case constant.OFFLINE_MSG_TYPE_MAIL: //新邮件
		msg = text
	}
	return
}

// 获取在线人数
func GetOnlineSum() (int, int) {
	sum := 0
	users.RLock()
	defer users.RUnlock()
	for _, v := range users.Map {
		if v.IsOnline() {
			sum++
		}
	}
	return sum, len(users.Map)
}

// 获取指定数量的按最近登录排序的用户
func GetOnlineSortedUsers(size, onlineType int, lastId string) []*User {
	arr := []*User{}
	offlineArr := []*User{}
	users.RLock()
	defer users.RUnlock()
	for _, v := range users.Map {
		if v.IsOnline() {
			arr = append(arr, v)
		} else {
			offlineArr = append(offlineArr, v)
		}
	}
	sort.Slice(arr, func(i, j int) bool {
		return arr[i].LastLoginTime > arr[j].LastLoginTime
	})
	sort.Slice(offlineArr, func(i, j int) bool {
		return offlineArr[i].LastLoginTime > offlineArr[j].LastLoginTime
	})
	switch onlineType {
	case 0: //全部
		arr = append(arr, offlineArr...)
	case 1: //在线
		break
	case 2: //离线且在内存
		arr = offlineArr
	}
	start := 0
	tail := ut.Min(size, len(arr))
	if lastId != "" {
		start := array.FindIndex(arr, func(v *User) bool { return v.UID == lastId })
		if start < 0 {
			return []*User{}
		}
	}
	return arr[start:tail]
}

// 禁言/封禁记录
type BanRecord struct {
	BanTime int64 `bson:"ban_time"` // 禁言/封禁到期时间
	Time    int64 `bson:"time"`     // 操作时间
	Type    int32 `bson:"type"`     // 类型 0禁言 1封禁
}

// 添加禁言/封禁记录
func (this *User) AddBanRecord(bType int32, banTime, now int64) {
	this.BanRecordList = append([]*BanRecord{{
		Type:    bType,
		BanTime: banTime,
		Time:    now,
	}}, this.BanRecordList...)
	recordDb := &BanRecordDb{
		Type:    bType,
		BanTime: banTime,
		Time:    now,
		Uid:     this.UID,
		Name:    this.Nickname,
	}
	go banRecordDb.InsertBanRecord(recordDb)
}

// 用户对局结算
func (this *User) GameOverSettle(sid, tp, gameScore, landScore int32, isWin bool, rank, S int32, MS float64, runTime float64) (addScore, addWarToken int32, giveup bool, gameCount int32, isPassNewbie bool, alliScore int32, newbieRepeat bool) {
	if array.Has(this.GiveupGames, sid) {
		giveup = true
		return
	}
	actGameScore := gameScore
	// 累计对局次数
	this.AccTotalGameCounts[1] += 1
	if isWin {
		this.AccTotalGameCounts[0] += 1
		actGameScore -= constant.GAME_EXTRA_SCORE
	}
	// 战斗积分
	alliScore = actGameScore - landScore
	// 计算兵符
	addWarToken = SettleUserWarToken(S, rank, actGameScore)
	if tp == slg.RANK_SERVER_TYPE {
		// 排位结算
		if isWin {
			this.AccTotalRankCounts[0] += 1
		}
		this.AccTotalRankCounts[1] += 1
		gameCount = this.AccTotalRankCounts[1]
		addScore = SettleUserRankScore(this, float64(rank), float64(S), MS)
	} else if tp == slg.NORMAL_SERVER_TYPE {
		if isWin {
			this.AccTotalFreeCounts[0] += 1
		}
		this.AccTotalFreeCounts[1] += 1
		gameCount = this.AccTotalFreeCounts[1]
	} else if tp == slg.ROOKIE_SERVER_TYPE {
		if isWin {
			this.AccTotalNewbieCounts[0] += 1
		}
		this.AccTotalNewbieCounts[1] += 1
		gameCount = this.AccTotalNewbieCounts[1]
		if this.PassNewbieIndex > 0 {
			// 玩家已通关新手区 且未关闭报名 则奖励减少
			if !IsCloseApply() {
				addWarToken = int32(ut.Ceil(float64(addWarToken) * slg.NEWBIE_PASSED_REWARD_REDUCE))
				newbieRepeat = true
			}
		} else {
			this.CheckPassNewBie(rank, actGameScore, false)
		}
		isPassNewbie = this.PassNewbieIndex > 0
	}
	this.FlagUpdateDB()
	return
}

// 判断是否通关新手区
func (this *User) CheckPassNewBie(rank, score int32, isGiveup bool) {
	if (rank >= 0 && rank <= slg.NEWBIE_PASS_RANK && score >= slg.NEWBIE_PASS_MIN_SCORE) ||
		score >= slg.NEWBIE_PASS_SCORE ||
		this.MaxLandCount >= slg.NEWBIE_PASS_LAND {
		// 记录新手区在第几局通关
		if isGiveup {
			this.PassNewbieIndex = this.AccTotalNewbieCounts[1] + 1
		} else {
			this.PassNewbieIndex = this.AccTotalNewbieCounts[1]
		}
	}
}

// S = 总人数
// Rank = 当前排名(从0开始)
// gameScore = 当前积分
// rate := 1 - Rank/S
// count += rate * 100 / 3.125 * 2//第一名固定64
// count += min(30,  gameScore * 0.01)//只保留个人积分的部分
func SettleUserWarToken(S, rank, gameScore int32) int32 {
	if rank < 0 || gameScore == 0 {
		return 0
	}
	rate := 1.0 - float64(rank)/float64(S)
	count := rate * 100 / 3.125 * 2
	count += math.Min(30, float64(gameScore)*0.01)
	return ut.RoundInt32(count)
}

// 检测是否拥有表情
func (this *User) CheckHasEmoji(emoji int32) bool {
	if emoji > 0 {
		json := config.GetJsonData("chatEmoji", emoji)
		if json == nil {
			return false
		} else if ut.Int(json["cond"]) != 0 {
			this.ChatEmojiLock.RLock()
			defer this.ChatEmojiLock.RUnlock()
			return array.Has(this.UnlockChatEmojiIds, emoji)
		}
	}
	return true
}

// 添加聊天表情
func (this *User) AddEmoji(emoji int32) {
	if config.GetJsonData("chatEmoji", emoji) == nil {
		return
	}
	this.ChatEmojiLock.Lock()
	if !array.Has(this.UnlockChatEmojiIds, emoji) {
		this.UnlockChatEmojiIds = append(this.UnlockChatEmojiIds, emoji)
		this.FlagUpdateDB()
	}
	this.ChatEmojiLock.Unlock()
}

// 聊天表情克隆
func (this *User) EmojisClone() []int32 {
	this.ChatEmojiLock.RLock()
	arr := []int32{}
	for _, v := range this.UnlockChatEmojiIds {
		arr = append(arr, v)
	}
	this.ChatEmojiLock.RUnlock()
	return arr
}

// 聊天表情ToPb
func (this *User) EmojisToPb() []int32 {
	this.ChatEmojiLock.RLock()
	arr := []int32{}
	for _, v := range this.UnlockChatEmojiIds {
		arr = append(arr, v)
	}
	this.ChatEmojiLock.RUnlock()
	return arr
}

// 检测是否有某个画像
func (this *User) CheckPortrayalByChat(id int32) (map[string]interface{}, string) {
	if id == 0 {
		return nil, ""
	} else if portrayal := this.GetPortrayalInfo(id); portrayal != nil {
		return portrayal.ToJson(), ""
	}
	return nil, ecode.HERO_NOT_EXIST.String()
}

// 直接發
func (this *User) SessionSendNR(topic string, body []byte) {
	if this.IsOnline() && this.Session != nil {
		this.Session.SendNR(topic, body)
	}
}

// 添加到用户信息通知队列
func (this *User) PutNotifyQueue(tp int32, data *pb.OnUpdatePlayerInfoNotify) {
	if data == nil || !this.IsOnline() || this.Session == nil || this.notifyQueue == nil {
		return
	}
	data.Type = tp
	this.notifyQueue <- data
}

// 用户信息通知Tick
func (this *User) UserInfoNotifyTick(lobby *Lobby) {
	if this.notifyQueue != nil {
		return
	}
	this.notifyQueue = make(chan *pb.OnUpdatePlayerInfoNotify, 1000)
	go func() {
		tiker := time.NewTicker(time.Millisecond * 100) //100毫秒通知一次
		defer func() {
			tiker.Stop()
			close(this.notifyQueue)
			this.notifyQueue = nil
		}()
		for this.IsOnline() {
			<-tiker.C
			now := time.Now().UnixMilli()
			// 跨天检测
			this.CheckUpdateNextToDayTime(now, false, lobby)
			// 种植
			this.CheckUpdatePlant(now)
			// 通知
			if len(this.notifyQueue) <= 0 {
				continue
			}
			list := []*pb.OnUpdatePlayerInfoNotify{}
			for len(this.notifyQueue) > 0 {
				msg := <-this.notifyQueue
				list = append(list, msg)
			}
			if body, err := pb.ProtoMarshal(&pb.LOBBY_ONUPDATEUSERINFO_NOTIFY{List: list}); err == "" {
				this.SessionSendNR(slg.LOBBY_UPDATE_USER_INFO, body)
			} else {
				log.Error("CheckNotify error!")
			}
		}
	}()
}

// 检测是否拥有皮肤
func (this *User) CheckHasSkin(id int32) bool {
	if this.HasPawnSkin(id) {
		return true
	}
	this.SkinItemListLock.RLock()
	defer this.SkinItemListLock.RUnlock()
	return array.Some(this.SkinItemList, func(m *SkinItem) bool { return m.Id == id && m.State >= 0 })
}

func (this *User) ToSeasonRankScoreHistoryDB() []map[string]interface{} {
	return array.Map(this.SeasonRankScoreHistory, func(m map[string]interface{}, _ int) map[string]interface{} {
		obj := map[string]interface{}{}
		for k, v := range m {
			obj[k] = v
		}
		return obj
	})
}

func (this *User) GuidesClone() []*GuideInfo {
	this.GuidesLock.RLock()
	defer this.GuidesLock.RUnlock()
	arr := []*GuideInfo{}
	for _, v := range this.Guides {
		arr = append(arr, v)
	}
	return arr
}

// 添加充值次数
func (this *User) AddRechargeProductCount(productId string, count int32) bool {
	rechargeCount := this.RechargeCountRecord.Get(productId)
	this.RechargeCountRecord.Set(productId, rechargeCount+count)
	return rechargeCount == this.RechargeRefundRecord.Get(productId)
}

// 添加退款次数
func (this *User) AddRechargeRefundProductCount(productId string, count int32) bool {
	refundCount := this.RechargeRefundRecord.Get(productId) + count
	this.RechargeRefundRecord.Set(productId, refundCount)
	return this.RechargeCountRecord.Get(productId) == refundCount
}

// 是否首次充值
func (this *User) IsFirstRechargeByProduct(productId string) bool {
	return this.RechargeCountRecord.Get(productId) == this.RechargeRefundRecord.Get(productId)
}

// 玩家登录dh上报
func (this *User) DhLoginLog() {
	dh.Track(dh.DH_EVENT_NAME_LOGIN, map[string]interface{}{
		"device_info": map[string]interface{}{ //可以直接使用SDK上报的内容
			"adid":         this.AdvertisingId, //广告ID,安卓是Google Advertising ID ;ios是IDFA
			"idfv":         "",                 //idfv（仅ios）
			"sm_id":        "",                 //数美id
			"imei":         "",                 //国际移动设备识别码（仅安卓）
			"android_id":   "",                 //安卓id，安卓设备号（仅安卓）
			"appsflyer_id": this.AppsflyerId,   //appsflyer sdk得到的id
			"device_token": this.FCMToken,      //推送消息用的token
			"mac_address":  "",                 //mac地址，可选
			"device_model": this.DeviceOS,      //设备型号
			"device_name":  "",                 //设备名字，可选
			"os_version":   this.DeviceOS,      //手机系统版本
			"language":     this.Language,      //玩家语言，前端根据需要自行填写，后端取当前玩家设置的游戏语言编码
			"network_type": "1",                //网络类型，1是数据 2是Wi-Fi
			"app_version":  slg.CLIENT_VERSION, //游戏版本
			"ip":           this.Ip,            //        ipv4地址
			"oaid":         "",                 //OAID 广告标识符 (Open Advertising Identifier)，仅安卓，可选
		},
		"user_info": map[string]interface{}{
			"bundle_id":   dh.APP_BUNDLE_ID,                   //游戏包名
			"sub_package": "",                                 //头条分包，没有的传空值
			"session_id":  this.SessionId,                     //由客户端初始化时生成（ 建议使用时间戳+设备信息or uuid  md5）一个唯一值，并传给后端
			"account":     this.UID,                           //游戏账户，用来标记一个用户的一组user_id
			"platform":    dh.DhPlatformSwitch(this.Platform), //平台，iOS android pc
			"server_id":   dh.DhLogServerId,                   //逻辑服ID， 大于等于10000的server_id会被当作测试环境数据处理
			"user_id":     this.UID,                           //用户游戏角色ID，为8位数字id，全服唯一（不接卓杭sdk可以不限制，但尽量短一些）
		},
	})
}

// 玩家离线dh上报
func (this *User) DhLogoutLog() {
	dh.Track(dh.DH_EVENT_NAME_LOGOUT, map[string]interface{}{
		"user_info": map[string]interface{}{
			"bundle_id":   dh.APP_BUNDLE_ID, //游戏包名
			"sub_package": "",               //头条分包，没有的传空值
			"session_id":  this.SessionId,   //由客户端初始化时生成（ 建议使用时间戳+设备信息or uuid  md5）一个唯一值，并传给后端
			"server_id":   dh.DhLogServerId, //逻辑服ID， 大于等于10000的server_id会被当作测试环境数据处理
			"user_id":     this.UID,         //用户游戏角色ID，为8位数字id，全服唯一（不接卓杭sdk可以不限制，但尽量短一些）
		},
	})
}

// 设置玩家离线时间 0为在线
func (this *User) SetUserOfflineTime(offlineTime int64) {
	go func() {
		pipe := rds.RdsPipeline()
		pipe.HSet(context.TODO(), this.UID, rds.RDS_USER_FIELD_LAST_OFFLINE_TIME, offlineTime)
		pipe.Expire(context.TODO(), this.UID, rds.RDS_USER_EXPIRE_TIME_DURATION)
		pipe.Exec(context.TODO())
	}()
}

// 获取好友离线时间
func (this *User) GetFriendsOfflineTime() {
	this.FriendsLock.RLock()
	arr := array.Clone(this.FriendsList)
	this.FriendsLock.RUnlock()
	pipe := rds.RdsPipeline()
	cmds := make([]*redis.StringCmd, len(arr))
	for i, v := range arr {
		cmds[i] = pipe.HGet(context.TODO(), v.Uid, rds.RDS_USER_FIELD_LAST_OFFLINE_TIME)
	}
	pipe.Exec(context.TODO())

	now := time.Now().UnixMilli()
	// 兼容到redis的map
	fixMap := map[string]int64{}
	for i, cmd := range cmds {
		value, err := cmd.Result()
		friendInfo := arr[i]
		if friendInfo == nil {
			continue
		}
		if err == redis.Nil {
			// 不存在
			if friendInfo.LastOfflineTime == 0 {
				friendInfo.LastOfflineTime = now
			}
			if now-friendInfo.LastOfflineTime < rds.RDS_USER_EXPIRE_TIME {
				// 离线时间未超过30天 兼容数据到redis
				fixMap[friendInfo.Uid] = friendInfo.LastOfflineTime
			}
			continue
		} else if err != nil {
			log.Warning("GetFriendsOfflineTime err: %v, friend: %v", err, friendInfo.Uid)
			continue
		} else {
			friendInfo.LastOfflineTime = ut.Int64(value)
		}
	}

	// 处理兼容存到redis
	if len(fixMap) > 0 {
		for k, v := range fixMap {
			pipe.HSet(context.TODO(), k, rds.RDS_USER_FIELD_LAST_OFFLINE_TIME, v)
			pipe.Expire(context.TODO(), k, rds.RDS_USER_EXPIRE_TIME_DURATION)
		}
		pipe.Exec(context.TODO())
	}
}

// 获取月卡每日奖励当天刷新时间
func GetMonthCardUpdateTime() int64 {
	return InitNextToDayTime() - ut.TIME_DAY
}
