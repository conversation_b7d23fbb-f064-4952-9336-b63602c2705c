package lobby

import (
	"context"
	"time"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	rds "slgsrv/utils/redis"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"go.mongodb.org/mongo-driver/bson"
)

// 生成唯一id
func genUID() string {
	uid := ut.UID8()
	for db.HasUid(uid) {
		uid = ut.UID8()
		log.Info("生成uid的时候发现一样的 uid=" + uid)
	}
	return uid
}

// 检测名字是否存在和合法
func checkNickname(nickname string) bool {
	if sensitive.CheckName(nickname) != 0 {
		return false // 是否合法
	}
	return !db.<PERSON><PERSON>ickname(nickname)
}

// 解析token并获取user
func (this *Lobby) DecodeToken(token, lid string) (user *User, err string) {
	if token == "" {
		err = ecode.NOT_ACCOUNT_TOKEN.String()
		return
	}
	// 解析token
	str := ut.AESDecrypt(token)
	uid := str[0:8]
	// redis查询token验证
	rdsToken, e := rds.RdsHGet(uid, rds.RDS_USER_FIELD_TOKEN)
	if e != nil || token != rdsToken {
		log.Info("token无效")
		err = ecode.TOKEN_INVALID.String()
		return
	}
	user = GetUser(uid)
	if user == nil {
		// 内存中没有 先判断大厅服是否到达负载上限
		if len(users.Map) >= rds.LOBBY_ASSIGN_MAX_NUM {
			err = ecode.CUR_LOBBY_FULL.String()
			// 移除玩家lid
			rds.RdsHDel(rds.RDS_USER_LID_MAP_KEY, uid)
			return
		}
		// 从数据库获取
		data, e := db.FindByUid(uid)
		if e != "" {
			log.Info("token无效")
			err = ecode.TOKEN_INVALID.String()
			return
		}
		user = CreateUser(data)
		users.Lock()
		users.Map[data.Uid] = user
		users.Unlock()
		// 从数据库获取添加到内存 则更新大厅服负载
		rds.UpdateLobbyLoad(uid, lid, true, true)
		log.Info("AddUser uid: %v, done.", data.Uid)
	} else if user.Session != nil {
		user.Kick(slg.KICK_NOTIFY_TYPE_OTHER, 0)
		log.Info("AddUser uid: %v, kick!", user.UID)
	} else {
		log.Info("AddUser uid: %v, memory!", user.UID)
	}
	rds.RemUserMallocLidLock(uid)
	user.Init(this)
	return
}

// 获取登录游戏的token
func GetGameToken(user *User, ip string) (accountToken string) {
	// 先更新登录时间
	lastLoginTime := time.Now().UnixMilli()
	user.LastLoginTime = lastLoginTime
	user.Ip = ip
	user.SetDbUpdaateFlag(1)
	// 重新生成token
	str := user.UID + ut.String(lastLoginTime)
	accountToken = ut.AESEncrypt(str)
	rds.RdsHSet(user.UID, rds.RDS_USER_FIELD_TOKEN, accountToken)
	return
}

var delUserMap = deadlock.Map{} // 账号删除map

// 从数据库读取账号删除数据
func DelUserInit() {
	var applyDelUsers []DelUserInfo
	if cur, err := accountDelCol.getCollection().Find(context.TODO(), bson.M{"state": slg.ACCOUNT_DEL_STATE_APPLY}); err == nil {
		err = cur.All(context.TODO(), &applyDelUsers)
		if err != nil {
			log.Error("DelUserInit parse err: %v", err)
			return
		}
		for _, info := range applyDelUsers {
			infoCopy := info
			delUserMap.Store(info.Uid, &infoCopy)
		}
	} else {
		log.Error("DelUserInit err: %v", err)
	}
}

// 账号删除检测
func DelUserCheck() {
	go func() {
		tiker := time.NewTicker(time.Hour * 1)
		defer tiker.Stop()
		for isRunning {
			now := time.Now().UnixMilli()
			delArr := []string{}
			delUserMap.Range(func(key, value any) bool {
				info := value.(*DelUserInfo)
				if GetUserByOnline(info.Uid) != nil {
					// 玩家还在线 不处理删除
					return true
				}
				if now >= info.ApplyTime+slg.LOGOUT_APPLY_TIME {
					// 已过冷静期 删除账号
					DelUserAndDB(info.Uid)
					delArr = append(delArr, info.Uid)
					log.Info("DelUserCheck del user: %v", info.Uid)
				}
				return true
			})
			for _, uid := range delArr {
				delUserMap.Delete(uid)
			}
			<-tiker.C
		}
	}()
}

// 已过冷静期 删除账号
func DelUserAndDBAndMap(uid string) {
	DelUserAndDB(uid)
	delUserMap.Delete(uid)
	log.Info("DelUserAndDBAndMap del user: %v", uid)
}

// 定时保存玩家信息
func CheckUpdateUserDB() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		defer tiker.Stop()
		for isRunning {
			<-tiker.C
			if !isRunning {
				break
			}
			now, updateInterval := int64(time.Now().UnixMilli()), int64(ut.TIME_MINUTE*2) // 1分钟检测3000个玩家
			users.RLock()
			for _, m := range users.Map {
				dbLastUpdateTime := m.GetDbLastUpdateTime()
				if dbLastUpdateTime <= 0 {
					m.SetDbLastUpdateTime(now)
					continue
				} else if now-dbLastUpdateTime < updateInterval {
					continue
				} else if m.GetDbUpdaateFlag() == 2 {
					continue
				}
				m.SetDbLastUpdateTime(now)
				if m.IsOnline() && m.GetDbUpdaateFlag() == 0 {
					continue
				} else if count := len(updateUserDBChan); count < 50 {
					m.SetDbUpdaateFlag(2)
					updateUserDBChan <- m
					if count >= 49 {
						break
					}
				}
			}
			users.RUnlock()
		}
	}()
}

// 标记用户离线
func SetUserOffline(user *User) {
	user.SetState(0)
	user.SetDbLastUpdateTime(int64(time.Now().UnixMilli()))
}

// 执行保存
func ExecuteUpdateUserDB() {
	go func() {
		for isRunning {
			user, ok := <-updateUserDBChan
			if !ok {
				continue
			}
			beginTime, dbLastUpdateTime := time.Now().UnixMilli(), user.GetDbLastUpdateTime()
			// 保存玩家数据到db
			chatCount, del, now, _ := SaveUserDb(user)
			log.Info("ExecuteUpdateUserDB uid: %v, updateChatCount: %v, del: %v, lut: %vms, db: %vms", user.UID, chatCount, del, int64(now)-dbLastUpdateTime, now-beginTime)
		}
	}()
}

// 保存玩家数据到db
func SaveUserDb(user *User) (chatCount int, del bool, now int64, err string) {
	// 先保存
	userDb := user.ToDB()
	err = db.UpdateData(user.UID, userDb)
	if err != "" {
		log.Error("SaveUserDb uid: %v, err: %v", user.UID, err)
		// 尝试保存每个字段
		for k, v := range userDb {
			db.UpdateOne(user.UID, k, v)
		}
	}
	// 是否有聊天信息需要保存
	chatCount = user.CheckUpdateChatDB()
	now = time.Now().UnixMilli()
	// 如果没在线 并且可以更新 并且大厅服分配锁不生效 就直接删除
	users.Lock()
	if !user.IsOnline() && user.GetDbUpdaateFlag() == 2 && !rds.GetUserMallocLidLock(user.UID) {
		_, exist := users.Map[user.UID]
		delete(users.Map, user.UID)
		users.Unlock()
		if exist {
			// 从内存中移除则更新redis数据
			rds.UpdateLobbyLoad(user.UID, "", false, false)
		}
		del = true
	} else {
		users.Unlock()
		user.SetDbLastUpdateTime(int64(now))
		user.SetDbUpdaateFlag(0)
	}
	return
}

// 通知好友信息更新Tick
func (this *Lobby) NotifyFriendUpdateTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		defer tiker.Stop()
		for isRunning {
			<-tiker.C
			if !isRunning {
				break
			}
			sum := len(notifyFriendUpdateChan)
			if sum == 0 {
				continue
			}
			beginTime := time.Now().UnixMilli()
			updateMap := map[string]*User{}
			for len(notifyFriendUpdateChan) > 0 {
				user, ok := <-notifyFriendUpdateChan
				if !ok {
					break
				}
				if updateMap[user.UID] != nil {
					continue
				}
				updateMap[user.UID] = user
			}
			for _, user := range updateMap {
				this.NotifyFriendUpdate(user)
			}
			log.Info("NotifyFriendUpdate updateChatCount: %v, costTime: %vms", len(updateMap), time.Now().UnixMilli()-beginTime)
		}
	}()
}

// 通知好友信息更新
func (this *Lobby) NotifyFriendUpdate(user *User) {
	now := time.Now().UnixMilli()
	onlineState := user.GetOnlineState()
	// 更新对方内存中的好友信息
	user.FriendsLock.RLock()
	friendUidList := array.Map(user.FriendsList, func(m *Friend, i int) string { return m.Uid })
	user.FriendsLock.RUnlock()
	for _, friendUid := range friendUidList {
		m := user.GetFriend(friendUid)
		// 通知在线好友更新
		rst, err := ut.RpcBytes(this.InvokeOnlineUserFunc(m.Uid, slg.RPC_USER_UPDATE_FRIEND_INFO, user.UID, user.Nickname, user.HeadIcon, user.SID, user.GetPlaySid(), onlineState))
		if err != "" {
			if err == ecode.NOT_FRIENDS.String() {
				// 不是好友则删除好友
				user.DelFriend(m.Uid)
			} else if err == ecode.NOT_BIND_UID.String() {
				// 好友不在线
				if m.LastOfflineTime == 0 {
					m.LastOfflineTime = time.Now().UnixMilli()
					user.NotifyUser(slg.LOBBY_FRIEND_UPDATE, m.ToUpdateNotifyPb(now))
				}
			}
			continue
		}
		needNotify := false
		friendInfo := &pb.FriendInfo{}
		if e := pb.ProtoUnMarshal(rst, friendInfo); e == nil {
			// 更新自己内存中的好友信息
			oldSid, oldOfflineTime := m.PlaySid, m.LastOfflineTime
			m.Nickname = friendInfo.Nickname
			m.HeadIcon = friendInfo.HeadIcon
			m.PlaySid = friendInfo.PlaySid
			m.LastOfflineTime = friendInfo.OfflineTime
			// 如果不一样就通知
			if oldSid != m.PlaySid || oldOfflineTime != m.LastOfflineTime {
				needNotify = true
			}
		}
		if m.PlaySid != 0 && IsRoomClose(m.PlaySid) {
			m.PlaySid = 0
			needNotify = true
		}
		if needNotify {
			user.NotifyUser(slg.LOBBY_FRIEND_UPDATE, m.ToUpdateNotifyPb(now))
		}
	}
}

// 大厅服redis负载均衡人数兼容检测
func LobbyRdsLoadCheck() {
	go func() {
		tiker := time.NewTicker(time.Minute * 1)
		defer tiker.Stop()
		for isRunning {
			<-tiker.C
			userNum := len(users.Map)
			rds.RdsHSet(rds.RDS_LOBBY_LOAD_MAP_KEY, lobbyModule.LID, userNum)
		}
	}()
}
