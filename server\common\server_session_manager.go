package common

import (
	"fmt"
	"reflect"
	"unsafe"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	ut "slgsrv/utils"
)

// ServerSessionManager 服务器会话管理器
type ServerSessionManager struct{}

// NewServerSessionManager 创建服务器会话管理器
func NewServerSessionManager() *ServerSessionManager {
	return &ServerSessionManager{}
}

// GetAllServerNodes 获取所有服务器节点信息
func (ssm *ServerSessionManager) GetAllServerNodes(app module.App) map[string]interface{} {
	defer func() {
		if err := recover(); err != nil {
			log.Error("GetAllServerNodes panic: %v", err)
		}
	}()

	if app == nil {
		return map[string]interface{}{
			"error": "应用实例为空",
		}
	}

	// 收集所有节点信息
	nodeInfo := map[string]interface{}{
		"timestamp": ut.Now(),
		"nodes":     map[string]interface{}{},
	}

	// 通过反射获取 serverList (这是私有字段，需要小心处理)
	serverListInfo := map[string]interface{}{}

	// 尝试获取不同类型的服务器
	serverTypes := []string{"game", "lobby", "gate", "login", "http", "chat", "mail", "match"}

	for _, serverType := range serverTypes {
		servers := app.GetServersByType(serverType)
		if len(servers) > 0 {
			typeInfo := []map[string]interface{}{}
			for _, server := range servers {
				if server != nil {
					serverInfo := map[string]interface{}{
						"id":   server.GetID(),
						"type": serverType, // 使用循环中的 serverType
					}

					// 尝试获取服务器的其他信息
					serverInfo["server_session"] = fmt.Sprintf("%p", server)

					typeInfo = append(typeInfo, serverInfo)
				}
			}
			if len(typeInfo) > 0 {
				serverListInfo[serverType] = typeInfo
			}
		}
	}

	nodeInfo["nodes"] = serverListInfo

	// 添加应用基本信息
	appInfo := map[string]interface{}{
		"process_id": app.GetProcessID(),
		"settings":   app.GetSettings(),
	}
	nodeInfo["app_info"] = appInfo

	return nodeInfo
}

// ForceRemoveServerSession 强制删除指定节点的session
func (ssm *ServerSessionManager) ForceRemoveServerSession(app module.App, serverID string) (map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			log.Error("ForceRemoveServerSession panic: %v", err)
		}
	}()

	if serverID == "" {
		return nil, fmt.Errorf("serverID不能为空")
	}

	if app == nil {
		return nil, fmt.Errorf("应用实例为空")
	}

	log.Info("尝试强制删除服务器会话: %s", serverID)

	// 尝试通过反射访问 app.serverList 来删除指定的 serverID
	success := false
	var removedInfo map[string]interface{}

	// 方法1：尝试通过 GetServerByID 检查是否存在
	server, err := app.GetServerByID(serverID)
	if err == nil && server != nil {
		removedInfo = map[string]interface{}{
			"server_id": serverID,
			"found":     true,
			"session":   fmt.Sprintf("%p", server),
		}

		// 这里我们无法直接删除，但可以记录信息
		log.Info("找到服务器会话: %s, session: %p", serverID, server)

		// 尝试通过反射删除（危险操作，需要谨慎）
		if removeErr := ssm.forceRemoveServerFromList(app, serverID); removeErr != nil {
			log.Error("强制删除服务器会话失败: %v", removeErr)
			return nil, fmt.Errorf("删除失败: %v", removeErr)
		}

		success = true
		log.Info("成功删除服务器会话: %s", serverID)
	} else {
		removedInfo = map[string]interface{}{
			"server_id": serverID,
			"found":     false,
			"error":     fmt.Sprintf("%v", err),
		}
		log.Warning("未找到服务器会话: %s, error: %v", serverID, err)
	}

	// 验证删除结果
	afterRemoval, _ := app.GetServerByID(serverID)
	removedInfo["removed_successfully"] = (afterRemoval == nil)
	removedInfo["success"] = success

	return removedInfo, nil
}

// forceRemoveServerFromList 通过反射强制删除服务器会话（危险操作）
func (ssm *ServerSessionManager) forceRemoveServerFromList(app module.App, serverID string) error {
	defer func() {
		if err := recover(); err != nil {
			log.Error("forceRemoveServerFromList panic: %v", err)
		}
	}()

	// 使用反射访问 app 的私有字段 serverList
	// 注意：这是一个危险操作，依赖于 vmqant 框架的内部实现

	appValue := reflect.ValueOf(app)
	if appValue.Kind() == reflect.Ptr {
		appValue = appValue.Elem()
	}

	// 尝试找到 serverList 字段
	serverListField := appValue.FieldByName("serverList")
	if !serverListField.IsValid() {
		return fmt.Errorf("无法找到 serverList 字段")
	}

	// 检查字段是否可以设置
	if !serverListField.CanSet() {
		// 尝试通过 unsafe 包来修改私有字段
		if serverListField.CanAddr() {
			// 使用 reflect 的 unsafe 操作
			serverListField = reflect.NewAt(serverListField.Type(), unsafe.Pointer(serverListField.UnsafeAddr())).Elem()
		} else {
			return fmt.Errorf("无法访问 serverList 字段")
		}
	}

	// 检查 serverList 的类型
	log.Info("serverList 类型: %v", serverListField.Type())

	// 假设 serverList 是一个 sync.Map 或类似的结构
	if serverListField.Type().String() == "sync.Map" {
		// 调用 Delete 方法
		deleteMethod := serverListField.MethodByName("Delete")
		if deleteMethod.IsValid() {
			deleteMethod.Call([]reflect.Value{reflect.ValueOf(serverID)})
			log.Info("通过 sync.Map.Delete 删除了服务器: %s", serverID)
			return nil
		}
	}

	// 如果是其他类型的 map，尝试其他方法
	return fmt.Errorf("不支持的 serverList 类型: %v", serverListField.Type())
}

// GetServerSessionInfo 获取指定服务器的会话信息
func (ssm *ServerSessionManager) GetServerSessionInfo(app module.App, serverID string) map[string]interface{} {
	defer func() {
		if err := recover(); err != nil {
			log.Error("GetServerSessionInfo panic: %v", err)
		}
	}()

	info := map[string]interface{}{
		"server_id": serverID,
		"timestamp": ut.Now(),
	}

	if app == nil {
		info["error"] = "应用实例为空"
		return info
	}

	server, err := app.GetServerByID(serverID)
	if err != nil {
		info["found"] = false
		info["error"] = fmt.Sprintf("%v", err)
	} else if server != nil {
		info["found"] = true
		info["session"] = fmt.Sprintf("%p", server)
	} else {
		info["found"] = false
		info["error"] = "服务器不存在"
	}

	return info
}

// CheckServerHealth 检查服务器健康状态
func (ssm *ServerSessionManager) CheckServerHealth(app module.App, serverType string) map[string]interface{} {
	defer func() {
		if err := recover(); err != nil {
			log.Error("CheckServerHealth panic: %v", err)
		}
	}()

	healthInfo := map[string]interface{}{
		"server_type": serverType,
		"timestamp":   ut.Now(),
		"servers":     []map[string]interface{}{},
		"total_count": 0,
		"healthy_count": 0,
	}

	if app == nil {
		healthInfo["error"] = "应用实例为空"
		return healthInfo
	}

	servers := app.GetServersByType(serverType)
	healthInfo["total_count"] = len(servers)

	serverList := []map[string]interface{}{}
	healthyCount := 0

	for _, server := range servers {
		if server != nil {
			serverInfo := map[string]interface{}{
				"id":      server.GetID(),
				"session": fmt.Sprintf("%p", server),
				"healthy": true, // 简单判断：存在即健康
			}
			healthyCount++
			serverList = append(serverList, serverInfo)
		}
	}

	healthInfo["servers"] = serverList
	healthInfo["healthy_count"] = healthyCount
	healthInfo["health_ratio"] = float64(healthyCount) / float64(len(servers))

	return healthInfo
}
