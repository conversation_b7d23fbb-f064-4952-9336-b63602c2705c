package game

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/common/sensitive"
	"slgsrv/server/game/common/config"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/effect"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	"slgsrv/server/game/world"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
)

type TempPawnMoveInfo struct {
	Pawn  g.Pawn
	Point *ut.Vec2
}

func (this *Game) InitHDArea() {
	this.GetServer().RegisterGO("HD_CheckArmyName", this.checkArmyName)                 //检测军队名字
	this.GetServer().RegisterGO("HD_DrillPawn", this.drillPawn)                         //招募士兵
	this.GetServer().RegisterGO("HD_CancelDrillPawn", this.cancelDrillPawn)             //取消招募士兵
	this.GetServer().RegisterGO("HD_PawnLving", this.pawnLving)                         //士兵练级
	this.GetServer().RegisterGO("HD_CancelPawnLving", this.cancelPawnLving)             //取消士兵练级
	this.GetServer().RegisterGO("HD_MoveAreaPawns", this.moveAreaPawns)                 //移动士兵
	this.GetServer().RegisterGO("HD_ChangePawnAttr", this.changePawnAttr)               //改变士兵属性
	this.GetServer().RegisterGO("HD_ChangePawnArmy", this.changePawnArmy)               //改变所属军队
	this.GetServer().RegisterGO("HD_ModifyAmryName", this.modifyAmryName)               //修改军队名字
	this.GetServer().RegisterGO("HD_UseUpScrollUpPawnLv", this.useUpScrollUpPawnLv)     //升级士兵
	this.GetServer().RegisterGO("HD_ChangeFortAutoSupport", this.changeFortAutoSupport) //改变要塞是否自动支援
	this.GetServer().RegisterGO("HD_SetArmySpeed", this.setArmySpeed)                   //设置军队行军速度
	this.GetServer().RegisterGO("HD_LeaveArea", this.leaveArea)                         //离开区域 删除记录的玩家
	this.GetServer().RegisterGO("HD_WatchArea", this.watchArea)                         //观战区域 删除记录的玩家 实时
	this.GetServer().RegisterGO("HD_AreaSendChat", this.areaSendChat)                   //设置军队行军速度
	this.GetServer().RegisterGO("HD_CureInjuryPawn", this.cureInjuryPawn)               //治疗士兵
	this.GetServer().RegisterGO("HD_CancelCurePawn", this.cancelCurePawn)               //取消治疗士兵
	this.GetServer().RegisterGO("HD_GiveupInjuryPawn", this.giveupInjuryPawn)           //放弃治疗受伤士兵
	this.GetServer().RegisterGO("HD_SpeedUpCuringPawn", this.speedUpCuringPawn)         //加速治疗士兵
	this.GetServer().RegisterGO("HD_SetHospitalNoticeOp", this.setHospitalNoticeOp)     //设置医馆通知开关
}

// 检测名字
func (this *Game) checkArmyName(session gate.Session, msg *pb.GAME_HD_CHECKARMYNAME_C2S) (ret map[string]interface{}, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	name := msg.GetName()
	if ut.GetStringLen(name) > 12 {
		err = ecode.TEXT_LEN_LIMIT.String()
	} else if wld.CheckArmyNameEqual(plr.Uid, name) {
		err = ecode.NAME_EXIST.String()
	} else if sta := sensitive.CheckName(name); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	}
	return
}

// 招募士兵
func (this *Game) drillPawn(session gate.Session, msg *pb.GAME_HD_DRILLPAWN_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, buildUid, id, armyUid := msg.GetIndex(), msg.GetBuildUid(), msg.GetId(), msg.GetArmyUid()
	json := config.GetJsonData("pawnBase", id)
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	build := area.GetBuildByUID(buildUid)
	if build == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if ut.Int32(json["spawn_build_id"]) != build.Id {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if ut.Int(json["need_unlock"]) == 1 && !plr.IsUnlockPawn(id) { //是否需要解锁
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if wld.IsDrillPawnQueueFull(index, buildUid) {
		return nil, ecode.DRILL_QUEUE_FULL.String()
	} else if !plr.CheckCTypesNeedArea(g.StringToTypeObjs(json["drill_cond"]), area.GetIndex()) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	} else if !plr.AntiCheatScoreCheck() { //防作弊检测
		return nil, ecode.ANTI_CHEAT.String()
	}
	// 扣除资源
	var cost []*g.TypeObj = nil
	checkAndDeductCostFunc := func() bool {
		// 检测是否有季节的费用提高
		cost = room.GetSeason().ChangeBaseResCost(g.StringToTypeObjs(json["drill_cost"]), effect.RECRUIT_COST)
		return plr.CheckAndDeductCostByTypeObjs(cost)
	}
	army := area.GetArmyByUid(armyUid)
	if army == nil { //这里如果没有 就说明要创建一个
		if armyUid != "" {
			return nil, ecode.ARMY_NOT_EXIST.String()
		}
		armyName := msg.GetArmyName()
		if armyName == "" {
			armyName = "???"
		}
		if wld.GetPlayerArmyCount(plr.Uid) >= plr.GetArmyMaxCount() {
			return nil, ecode.PLAYER_FULL_ARMY.String() //军队上限
		} else if err, _ = wld.IsAreaFullArmyAndAddTimesByMove(area, plr.Uid, 1); err != "" {
			return //军队是否满了 包含行军和增援
		} else if ut.GetStringLen(armyName) > 12 {
			return nil, ecode.TEXT_LEN_LIMIT.String()
		} else if sta := sensitive.CheckName(armyName); sta != 0 {
			return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
		} else if !plr.CheckCerealConsume(ut.Float64(json["cereal_cost"])) { //检测粮耗
			return nil, ecode.CEREAL_COST_NOT_ENOUGH.String()
		} else if !checkAndDeductCostFunc() { //扣除资源
			return nil, ecode.RES_NOT_ENOUGH.String()
		}
		army = area.CreateEmptyArmy(armyName, plr.Uid)
		wld.AddAreaArmy(area, army, -1)
		wld.NotifyPlayerArmyDistInfo(plr.Uid)
	} else if army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if army.IsFull() {
		return nil, ecode.ARMY_PAWN_FULL.String()
	} else if wld.CheckArmyMarchingByUID(army.Uid) || army.AIndex != index {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if !plr.CheckCerealConsume(ut.Float64(json["cereal_cost"])) { //检测粮耗
		return nil, ecode.CEREAL_COST_NOT_ENOUGH.String()
	} else if !checkAndDeductCostFunc() { //扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	if cost == nil {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 添加到军队 表示这个军队有训练的士兵
	army.AddDrillPawn(id)
	// 添加到训练队列
	needTime := ut.Int32(json["drill_time"])
	if plr.UpRecruitPawnCount > 0 { //是否有加速
		plr.UpRecruitPawnCount -= 1
		needTime = int32(float64(needTime) * constant.UP_RECRUIT_PAWN_MUL)
	}
	wld.PutDrillPawnQueue(index, build.Uid, army.Uid, id, 1, needTime, plr.GetPolicyEffectFloat(effect.XL_CD), cost)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_DRILLPAWN_S2C{
		Output:             plr.ToOutputInfoPb(),
		Queues:             wld.ToDrillPawnQueuePb(index),
		Army:               army.ToPb(area.ToArmyState(army)),
		UpRecruitPawnCount: plr.UpRecruitPawnCount,
	})
}

// 取消招募士兵
func (this *Game) cancelDrillPawn(session gate.Session, msg *pb.GAME_HD_CANCELDRILLPAWN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, buildUid, uid := msg.GetIndex(), msg.GetBuildUid(), msg.GetUid()
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	info := wld.GetDrillPawnQueueInfo(index, buildUid, uid)
	if info == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	json := config.GetJsonData("pawnBase", info.Id)
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	army := area.GetArmyByUid(info.AUid)
	if army == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	// 删除
	dInfo := wld.CancelDrillPawnQueue(index, buildUid, uid)
	if dInfo == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	rst := &pb.GAME_HD_CANCELDRILLPAWN_S2C{}
	if dInfo.StartTime == 0 { //返还资源 招募中的不返回资源
		cost := dInfo.Cost
		if cost == nil {
			cost = g.StringToTypeObjs(json["drill_cost"])
		}
		plr.ChangeCostByTypeObjs(cost, 1)
		rst.NeedCost = g.ToTypeObjsPb(cost)
		rst.Output = plr.ToOutputInfoPb()
	}
	// 删除军队里面的
	army.RemoveDrillPawn(info.Id)
	// 如果没有了 就删除
	if army.GetActPawnCount() == 0 {
		if area.IsBattle() && !army.IsFighting() {
			// 未加入战斗的军队因取消招募删除 则减少增援数量
			area.GetFspModel().DecDefenderAcc(army.Uid)
		}
		area.RemoveArmy(army.Uid)
		wld.NotifyPlayerArmyDistInfo(plr.Uid)
	}
	// 返回
	rst.Queues = wld.ToDrillPawnQueuePb(index)
	rst.Army = army.ToPb(area.ToArmyState(army))
	return pb.ProtoMarshal(rst)
}

// 士兵练级
func (this *Game) pawnLving(session gate.Session, msg *pb.GAME_HD_PAWNLVING_C2S) (ret []byte, err string) {
	e, room, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, auid, puid := msg.GetIndex(), msg.GetAuid(), msg.GetPuid()
	area, army := wld.GetAreaAndArmy(index, auid)
	if area == nil || army == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if wld.CheckArmyMarchingByUID(army.Uid) || army.AIndex != index {
		return nil, ecode.ARMY_NOT_EXIST.String()
	}
	pawn := army.GetPawnByUID(puid)
	if pawn == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if pawn.GetPawnType() >= constant.PAWN_TYPE_MACHINE { //器械不能训练
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if pawn.IsMaxLv() {
		return nil, ecode.YET_MAXLV.String()
	} else if wld.IsPawnLvingQueueFull(index) {
		return nil, ecode.LVING_QUEUE_FULL.String()
	} else if wld.IsInLvingQueue(index, puid) {
		return nil, ecode.YET_IN_LVINGQUEUE.String()
	} else if !plr.AntiCheatScoreCheck() { //防作弊检测
		return nil, ecode.ANTI_CHEAT.String()
	}
	json := pawn.GetAttrJson()
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if !plr.CheckCTypesNeedArea(g.StringToTypeObjs(json["lv_cond"]), area.GetIndex()) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	}
	// 检测是否有季节的费用提高
	cost := room.GetSeason().ChangeBaseResCost(pawn.GetUpCost(), effect.UPLVING_COST)
	if !plr.CheckAndDeductCostByTypeObjs(cost) { //扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 获取减少比列
	cd := wld.GetPlayerPolicyEffectFloatByUid(plr.Uid, effect.UPLVING_CD) * 0.01
	cd += room.GetSeason().GetEffectFloat(effect.UPLVING_CD)
	cd += wld.GetAncientEffectFloatByPlayer(plr.Uid, effect.UPLVING_CD) * 0.01
	// 添加到训练队列
	wld.PutPawnLvingQueue(index, army.Uid, pawn.Uid, pawn.Id, pawn.Lv+1, ut.Int32(json["lv_time"]), cd, cost)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_PAWNLVING_S2C{
		Cost:   plr.ToItemByTypeObjsPb(cost),
		Queues: wld.ToPawnLvingQueuePb(index),
	})
}

// 取消士兵训练
func (this *Game) cancelPawnLving(session gate.Session, msg *pb.GAME_HD_CANCELPAWNLVING_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, uid := ut.Int32(msg.GetIndex()), msg.GetUid()
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	}
	info := wld.GetPawnLvingQueueInfo(index, uid)
	if info == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	army := area.GetArmyByUid(info.AUID)
	if army == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	pawn := army.GetPawnByUID(info.PUID)
	if pawn == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	json := pawn.GetAttrJson()
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	lInfo := wld.CancelPawnLvingQueue(index, uid)
	if lInfo == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	rst := &pb.GAME_HD_CANCELPAWNLVING_S2C{
		Queues: wld.ToPawnLvingQueuePb(index),
	}
	if lInfo.StartTime == 0 { //返还资源 训练中的不返回资源
		cost := lInfo.Cost
		if cost == nil {
			cost = pawn.GetUpCost()
		}
		plr.ChangeCostByTypeObjs(cost, 1)
		rst.NeedCost = g.ToTypeObjsPb(cost)
		rst.Cost = plr.ToItemByTypeObjsPb(cost)
	}
	// 返回
	return pb.ProtoMarshal(rst)
}

// 移动士兵
func (this *Game) moveAreaPawns(session gate.Session, msg *pb.GAME_HD_MOVEAREAPAWNS_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, pawns := msg.GetIndex(), msg.GetArmyUid(), msg.GetPawns()
	area, army := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || army == nil || army.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	}
	arr := []TempPawnMoveInfo{}
	ignores := map[string]bool{}
	for i := len(pawns) - 1; i >= 0; i-- {
		data := pawns[i]
		pointPb := data.GetPoint()
		if pawn := army.GetPawnByUID(data.GetUid()); pawn != nil {
			ignores[pawn.Uid] = true
			arr = append(arr, TempPawnMoveInfo{Pawn: pawn, Point: &ut.Vec2{X: pointPb.GetX(), Y: pointPb.GetY()}})
		} else {
			pawns = append(pawns[:i], pawns[i+1:]...)
		}
	}
	for i := len(arr) - 1; i >= 0; i-- {
		data := arr[i]
		if area.CheckPawnPointIgnore(data.Point, ignores) {
			data.Pawn.SetPoint(data.Point)
		} else {
			arr = append(arr[:i], arr[i+1:]...)
		}
	}
	wld.TagUpdateDBByIndex(index)
	wld.NotifyAreaUpdateInfo(index, constant.NQ_MOVE_PAWN, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_11: &pb.MovePawnNotifyInfo{
			ArmyUid: armyUid,
			Pawns: array.Map(arr, func(m TempPawnMoveInfo, _ int) *pb.MovePawnInfo {
				return &pb.MovePawnInfo{Uid: m.Pawn.GetUID(), Point: pb.NewVec2(m.Pawn.GetPoint())}
			}),
		},
	}, plr.Uid) //通知
	return pb.ProtoMarshal(&pb.GAME_HD_MOVEAREAPAWNS_S2C{})
}

// 改变士兵属性
func (this *Game) changePawnAttr(session gate.Session, msg *pb.GAME_HD_CHANGEPAWNATTR_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, uid := msg.GetIndex(), msg.GetArmyUid(), msg.GetUid()
	attackSpeed, equipId, skinId, petId := msg.GetAttackSpeed(), msg.GetEquipId(), msg.GetSkinId(), msg.GetPetId()
	area, pawn := wld.GetAreaAndPawn(index, armyUid, uid)
	if area == nil || pawn == nil || pawn.Owner != plr.Uid {
		log.Error("changePawnAttr error? index: %v, armyUid: %v, uid: %v, area: %v, pawn: %v", index, armyUid, uid, area == nil, pawn == nil)
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		log.Error("changePawnAttr error? BATTLEING, equipId: %v", equipId)
		return nil, ecode.BATTLEING.String() //当前战斗中
	}
	pawns := []g.Pawn{}
	// 出手速度
	if pawn.AttackSpeed != attackSpeed {
		pawn.AttackSpeed = attackSpeed
		pawns = append(pawns, pawn)
	}
	// 皮肤
	if skinId == pawn.SkinId {
	} else if skinId == 0 {
		pawns = area.ChangePawnSkin(pawn, skinId, msg.GetSyncSkin(), pawns)
	} else if skinId/1000 != pawn.Id {
		log.Error("changePawnAttr error? PAWN_SKIN_ERROR, pawnId: %v, skinId: %v", pawn.Id, skinId)
	} else if _, err = this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHECK_HAS_PAWN_SKIN, plr.Uid, skinId); err == "" {
		pawns = area.ChangePawnSkin(pawn, skinId, msg.GetSyncSkin(), pawns)
	} else {
		log.Error("changePawnAttr error? PAWN_SKIN_NOT_EXIST, pawnId: %v, skinId: %v", pawn.Id, skinId)
	}
	// 装备
	if pawn.Equip.ID == equipId {
	} else if equip := wld.GetPlayerEquip(plr.Uid, equipId); equip != nil && equip.CheckExclusivePawn(pawn.Id) {
		if arr := area.ChangePawnEquip(pawn, equip, msg.GetSyncEquip(), pawns); len(arr) > 0 {
			pawns = arr
			// 添加本局使用过的装备
			wld.AddEquipUseMap(plr.Uid, equipId)
		}
	} else if equipId != 0 {
		log.Error("changePawnAttr error? EQUIP_NOT_EXIST, equipId=" + ut.Itoa(equipId))
	}
	// 宠物
	if pawn.PetId == petId {
	} else if killCount := wld.GetPlayerKillCount(plr.Uid, petId); killCount > 0 || petId == constant.DEFAULT_PET_ID {
		pawn.PetId = petId
		pawns = append(pawns, pawn)
	} else if petId != 0 {
		log.Error("changePawnAttr error? KillCount, petId: %v, count: %v", petId, killCount)
	}
	// 标记
	wld.TagUpdateDBByIndex(index)
	datas, pawnUIDMap := []*pb.AreaPawnInfo{}, map[string]bool{}
	for _, m := range pawns {
		uid := m.GetUID()
		if !pawnUIDMap[uid] {
			pawnUIDMap[uid] = true
			datas = append(datas, m.ToNotifyPb())
		}
	}
	// 通知
	wld.NotifyAreaUpdateInfo(index, constant.NQ_CHANGE_PAWN_ATTR, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_25: datas,
	})
	return
}

// 改变士兵所属军队
func (this *Game) changePawnArmy(session gate.Session, msg *pb.GAME_HD_CHANGEPAWNARMY_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, uid := msg.GetIndex(), msg.GetArmyUid(), msg.GetUid()
	newArmyUid, isNewCreate := msg.GetNewArmyUid(), msg.GetIsNewCreate()
	attackSpeed, equipId, skinId, petId := msg.GetAttackSpeed(), msg.GetEquipId(), msg.GetSkinId(), msg.GetPetId()
	area, preArmy := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || preArmy == nil || preArmy.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if wld.CheckArmyMarchingByUID(preArmy.Uid) {
		return nil, ecode.MARCHING.String() //行军中
	}
	pawn := preArmy.GetPawnByUID(uid)
	if pawn == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	newArmy := area.GetArmyByUid(newArmyUid)
	if isNewCreate { //新建
		if newArmyUid == "" {
			return nil, ecode.ARMY_NOT_EXIST.String()
		} else if wld.GetPlayerArmyCount(plr.Uid) >= plr.GetArmyMaxCount() {
			return nil, ecode.PLAYER_FULL_ARMY.String() //军队上限
		} else if wld.IsAreaFullArmyAct(area, plr.Uid) {
			return nil, ecode.AREA_FULL_ARMY.String() //军队已满
		} else if ut.GetStringLen(newArmyUid) > 12 {
			return nil, ecode.TEXT_LEN_LIMIT.String()
		} else if sta := sensitive.CheckName(newArmyUid); sta != 0 {
			return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
		}
		newArmy = area.CreateEmptyArmy(newArmyUid, plr.Uid)
		wld.AddAreaArmy(area, newArmy, -1)
	}
	if newArmy == nil || newArmy.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if newArmy.IsFull() {
		return nil, ecode.ARMY_PAWN_FULL.String() //军队士兵已满
	} else if pawn.IsHero() && newArmy.HasHero() {
		return nil, ecode.ARMY_ONLY_AVATAR_ONE.String() //军队中已经有英雄了
	}
	log.Info("changePawnArmy before index: %v, owner: %v, armyUid: %v, armyName: %v, pawnUid: %v, pawnTreasursCount: %v", index, pawn.Owner, preArmy.Uid, preArmy.Name, pawn.Uid, len(pawn.Treasures))
	// 删除前必须先暂停训练
	wld.PausePawnLving(pawn.AIndex, time.Now().UnixMilli())
	// 先从前一个军队中删除
	if preArmy.RemovePawn(uid) != nil && preArmy.GetActPawnCount() == 0 { //如果一个士兵都没有了
		area.RemoveArmy(preArmy.Uid)
	} else {
		wld.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_19: preArmy.ToPb(area.ToArmyState(preArmy)),
		})
	}
	// 出手速度
	pawn.AttackSpeed = attackSpeed
	// 皮肤
	if skinId == pawn.SkinId {
	} else if skinId == 0 {
		pawn.SetSkinId(skinId)
	} else if skinId/1000 != pawn.Id {
		log.Error("changePawnArmy error? PAWN_SKIN_ERROR, pawnId: %v, skinId: %v", pawn.Id, skinId)
	} else if _, err = this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHECK_HAS_PAWN_SKIN, plr.Uid, skinId); err == "" {
		pawn.SetSkinId(skinId)
	}
	// 装备
	if equip := wld.GetPlayerEquip(plr.Uid, equipId); equip != nil && equip.CheckExclusivePawn(pawn.Id) {
		pawn.ChangeEquip(equip.ID, equip.Attrs, area.IsRecoverPawnHP())
	}
	// 宠物
	if pawn.PetId != petId && wld.GetPlayerKillCount(plr.Uid, petId) > 0 || petId == constant.DEFAULT_PET_ID {
		pawn.PetId = petId
	}
	// 添加到新的军队中
	newArmy.AddPawn(pawn)
	// 改变练级队列里面的士兵
	wld.ChangePawnLvingInfo(pawn)
	// 加入新队伍后再恢复训练
	wld.PausePawnLving(pawn.AIndex, 0)
	wld.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_19: newArmy.ToPb(area.ToArmyState(newArmy)),
	})
	// 通知军队分布信息
	wld.NotifyPlayerArmyDistInfo(plr.Uid)
	wld.TagUpdateDBByIndex(index) //标记一下
	mostPawnCount := wld.GetMaxPawnCountInArmy(plr.Uid)
	wld.TriggerTask(plr.Uid, tctype.ARMY_PAWN_COUNT, mostPawnCount, 0)
	log.Info("changePawnArmy after armyUid: %v, armyName: %v, pawnTreasursCount: %v", newArmy.Uid, newArmy.Name, len(pawn.Treasures))
	return pb.ProtoMarshal(&pb.GAME_HD_CHANGEPAWNARMY_S2C{
		Uid:  newArmy.Uid,
		Name: newArmy.Name,
	})
}

// 修改军队名字
func (this *Game) modifyAmryName(session gate.Session, msg *pb.GAME_HD_MODIFYAMRYNAME_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, name := msg.GetIndex(), msg.GetArmyUid(), msg.GetName()
	area, army := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || army == nil || army.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if army.Name == name {
		return nil, ""
	} else if ut.GetStringLen(name) > 12 {
		return nil, ecode.TEXT_LEN_LIMIT.String()
	} else if wld.CheckArmyNameEqual(plr.Uid, name) {
		return nil, ecode.NAME_EXIST.String()
	} else if sta := sensitive.CheckName(name); sta != 0 {
		return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
	}
	army.Name = name
	wld.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_19: army.ToPb(area.ToArmyState(army)),
	})
	wld.NotifyPlayerArmyDistInfo(plr.Uid)
	wld.TagUpdateDBByIndex(index) //标记一下
	return
}

// 升级士兵
func (this *Game) useUpScrollUpPawnLv(session gate.Session, msg *pb.GAME_HD_USEUPSCROLLUPPAWNLV_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, puid := msg.GetIndex(), msg.GetArmyUid(), msg.GetUid()
	area, army := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || army == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	pawn := army.GetPawnByUID(puid)
	if pawn == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if pawn.GetPawnType() >= constant.PAWN_TYPE_MACHINE {
		return nil, ecode.PAWN_NOT_EXIST.String() //器械不可升级
	} else if pawn.IsMaxLv() {
		return nil, ecode.YET_MAXLV.String() //已经满级
	} else if wld.CheckPawnLving(pawn.Uid) {
		return nil, ecode.YET_IN_LVINGQUEUE.String() //已经在练级了
	}
	json := pawn.GetAttrJson()
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if !plr.CheckCTypesNeedArea(g.StringToTypeObjs(json["lv_cond"]), plr.MainCityIndex) {
		return nil, ecode.COND_NOT_ENOUGH.String()
	}
	cost := []*g.TypeObj{g.NewTypeObj(ctype.UP_SCROLL, 0, 1)}
	if !plr.CheckAndDeductCostByTypeObjs(cost) { //扣除费用
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 直接升级
	pawn.UpdateLv(pawn.Lv + 1)
	// 触发任务
	wld.TriggerTask(army.Owner, tctype.UPLV_PAWN_APPOINT, pawn.Lv, pawn.Id)
	wld.TriggerTask(army.Owner, tctype.UPLV_PAWN, pawn.Lv, 0)
	if pawn.Lv >= 6 {
		wld.AddPlayerMaxLvPawnCount(army.Owner, 1)
	}
	// 标记一下
	wld.TagUpdateDBByIndex(index)
	// 通知
	wld.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
		Data_19: army.ToPb(area.ToArmyState(army)),
	})
	// 上报
	wld.TaTrack(army.Owner, 0, "ta_drillPawn", map[string]interface{}{
		"role_id":    pawn.Id,
		"role_lv":    pawn.Lv,
		"use_scroll": true,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_USEUPSCROLLUPPAWNLV_S2C{
		Cost: plr.ToItemByTypeObjsPb(cost),
		Lv:   pawn.Lv,
	})
}

// 改变要塞是否自动支援
func (this *Game) changeFortAutoSupport(session gate.Session, msg *pb.GAME_HD_CHANGEFORTAUTOSUPPORT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, isAuto := msg.GetIndex(), msg.GetIsAuto()
	area := wld.GetArea(index)
	if area == nil {
		return nil, ecode.BUILD_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if area.Owner != plr.Uid {
		return nil, ecode.ONLY_OWNER_OPERATED.String() //只能由拥有者操作
	}
	wld.UpdateFortAutoSupport(plr.Uid, index, isAuto)
	return
}

// 设置军队行军速度
func (this *Game) setArmySpeed(session gate.Session, msg *pb.GAME_HD_SETARMYSPEED_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, speed := msg.GetIndex(), msg.GetUid(), msg.GetMarchSpeed()
	area, army := wld.GetAreaAndArmy(index, armyUid)
	if area == nil || army == nil || army.Owner != plr.Uid {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if area.IsBattle() {
		return nil, ecode.BATTLEING.String() //当前战斗中
	} else if wld.CheckArmyMarchingByUID(army.Uid) {
		return nil, ecode.MARCHING.String()
	} else if army.GetPawnCount() == 0 {
		return nil, ecode.NEED_PAWN_SET_MARCHSPEED.String()
	}
	perMarchSpeed := army.MarchSpeed
	// 获取最慢士兵的速度
	lowestSpeed, _ := army.GetPawnByMinMarchSpeed()
	army.MarchSpeed = ut.ClampInt32(speed, constant.CAN_MIN_MARCH_SPEED, lowestSpeed)
	army.ModifiedMarchSpeed = lowestSpeed != army.MarchSpeed
	// 不一样才通知
	if perMarchSpeed != army.MarchSpeed {
		wld.NotifyAreaUpdateInfo(index, constant.NQ_UPDATE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{
			Data_19: army.ToPb(area.ToArmyState(army)),
		})
		wld.TagUpdateDBByIndex(index) //标记一下
	}
	return pb.ProtoMarshal(&pb.GAME_HD_SETARMYSPEED_S2C{MarchSpeed: army.MarchSpeed})
}

// 离开区域
func (this *Game) leaveArea(session gate.Session, msg *pb.GAME_HD_LEAVEAREA_C2S) (ret map[string]interface{}, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	wld.RemoveReqAreaPlayer(msg.GetIndex(), plr.Uid)
	return
}

// 观战区域
func (this *Game) watchArea(session gate.Session, msg *pb.GAME_HD_WATCHAREA_C2S) (ret map[string]interface{}, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	} else if msg.GetState() {
		wld.RecordAreaWatchPlayer(msg.GetIndex(), plr.Uid)
	} else {
		wld.RemoveAreaWatchPlayer(plr.Uid)
	}
	return
}

// 发送聊天
func (this *Game) areaSendChat(session gate.Session, msg *pb.GAME_HD_AREASENDCHAT_C2S) (ret map[string]interface{}, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, emoji := msg.GetIndex(), msg.GetEmoji()
	if !wld.CheckAreaWatchPlayer(index, plr.Uid) {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	json := config.GetJsonData("chatEmoji", emoji)
	if json == nil {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	} else if ut.Int(json["cond"]) == 0 {
	} else if _, e := this.InvokeLobbyRpc(plr.GetLid(), slg.RPC_CHECK_HAS_CHAT_EMOJI, plr.Uid, emoji); e != "" {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	}
	// 直接转发
	wld.NotifyWatchPlayer(msg.GetIndex(), constant.NQ_AREA_CHAT, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_68: &pb.AreaChatInfo{
		Uid:   plr.Uid,
		Emoji: msg.GetEmoji(),
	}})
	return
}

// 治疗士兵
func (this *Game) cureInjuryPawn(session gate.Session, msg *pb.GAME_HD_CUREINJURYPAWN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, armyUid, pawnUid := msg.GetIndex(), msg.GetArmyUid(), msg.GetPawnUid()
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if wld.IsPawnCuringQueueFull(index) { // 治疗队列已满
		return nil, ecode.CURING_QUEUE_FULL.String()
	}
	hospital := area.GetAreaBuildById(constant.HOSPITAL_BUILD_ID)
	if hospital == nil || hospital.Lv <= 0 {
		// 未修建医馆
		return nil, ecode.BUILD_NOT_EXIST.String()
	}
	// else if !plr.AntiCheatScoreCheck() { //防作弊检测
	// 	return nil, ecode.ANTI_CHEAT.String()
	// }

	injuryPawn := wld.GetInjuryPawn(pawnUid, plr.Uid)
	if injuryPawn == nil { // 受伤士兵不存在
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if injuryPawn.Curing {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	json := config.GetJsonData("pawnBase", injuryPawn.Id)
	if json == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}

	// 获取招募和升级消耗的总资源和时间
	costRes, needTime := world.GetPawnResCost(injuryPawn.Id, injuryPawn.Lv)
	checkAndDeductCostFunc := func() bool {
		// // 检测是否有季节的费用提高
		// costRes = room.GetSeason().ChangeBaseResCost(costRes, effect.CURE_CD)
		// 消耗三资 = (招募三资 + 训练三资) * (1.0 + 治疗次数 * 0.2)
		for i := len(costRes) - 1; i >= 0; i-- {
			resObj := costRes[i]
			if resObj.Type != ctype.CEREAL && resObj.Type != ctype.TIMBER && resObj.Type != ctype.STONE {
				// 治疗只消耗三资
				costRes = append(costRes[:i], costRes[i+1:]...)
			} else {
				resObj.Count = int32(float64(resObj.Count) * (constant.CURE_RES_INIT_PARAM + float64(injuryPawn.CureCount)*constant.CURE_RES_COUNT_PARAM))
			}
		}
		return plr.CheckAndDeductCostByTypeObjs(costRes)
	}
	army := area.GetArmyByUid(armyUid)
	if army == nil { //这里如果没有 就说明要创建一个
		if armyUid != "" {
			return nil, ecode.ARMY_NOT_EXIST.String()
		}
		armyName := msg.GetArmyName()
		if armyName == "" {
			armyName = "???"
		}
		if wld.GetPlayerArmyCount(plr.Uid) >= plr.GetArmyMaxCount() {
			return nil, ecode.PLAYER_FULL_ARMY.String() //军队上限
		} else if err, _ = wld.IsAreaFullArmyAndAddTimesByMove(area, plr.Uid, 1); err != "" {
			return //军队是否满了 包含行军和增援
		} else if ut.GetStringLen(armyName) > 12 {
			return nil, ecode.TEXT_LEN_LIMIT.String()
		} else if sta := sensitive.CheckName(armyName); sta != 0 {
			return nil, ut.If(sta == 1, ecode.TEXT_HAS_SENSITIVE.String(), ecode.TEXT_HAS_SPECIAL_SYMBOL.String())
		} else if !plr.CheckCerealConsume(ut.Float64(json["cereal_cost"])) { //检测粮耗
			return nil, ecode.CEREAL_COST_NOT_ENOUGH.String()
		} else if !checkAndDeductCostFunc() { //扣除资源
			return nil, ecode.RES_NOT_ENOUGH.String()
		}
		army = area.CreateEmptyArmy(armyName, plr.Uid)
		wld.AddAreaArmy(area, army, -1)
		wld.NotifyPlayerArmyDistInfo(plr.Uid)
	} else if army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	} else if army.IsFull() {
		return nil, ecode.ARMY_PAWN_FULL.String()
	} else if wld.CheckArmyMarchingByUID(army.Uid) || army.AIndex != index {
		return nil, ecode.ARMY_NOT_EXIST.String()
	} else if !plr.CheckCerealConsume(ut.Float64(json["cereal_cost"])) { //检测粮耗
		return nil, ecode.CEREAL_COST_NOT_ENOUGH.String()
	} else if !checkAndDeductCostFunc() { //扣除资源
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	if costRes == nil {
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	// 更新受伤的士兵的状态
	if !wld.ChangeInjuryPawnState(injuryPawn.Uid, plr.Uid, true) {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	// 添加到军队 表示这个军队有训练的士兵
	army.AddCuringPawn(injuryPawn)
	// 治疗时间 = (招募时间 + 训练时间) * (0.5 + 治疗次数 * 0.1) * 建筑等级加速
	needTime = int32(float64(needTime) * (constant.CURE_TIME_INIT_PARAM + constant.CURE_TIME_COUNT_PARAM*float64(injuryPawn.CureCount)))
	if eff := hospital.GetEffectFloat(effect.CURE_CD); eff > 0 {
		needTime = int32(float64(needTime) * (1.0 - eff*0.01))
	}
	// 添加到治疗队列
	wld.PutPawnCuringQueue(index, injuryPawn.Id, injuryPawn.Lv, army.Uid, pawnUid, needTime, plr.GetPolicyEffectFloat(effect.CURE_CD), costRes)
	// 返回
	return pb.ProtoMarshal(&pb.GAME_HD_CUREINJURYPAWN_S2C{
		Output: plr.ToOutputInfoPb(),
		Queues: wld.ToPawnCuringQueuePb(index),
		Army:   army.ToPb(area.ToArmyState(army)),
	})
}

// 取消治疗士兵
func (this *Game) cancelCurePawn(session gate.Session, msg *pb.GAME_HD_CANCELCUREPAWN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, uid := msg.GetIndex(), msg.GetUid()
	area := wld.GetArea(index)
	if area == nil || area.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	info := wld.GetPawnCuringInfo(index, uid)
	if info == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	army := area.GetArmyByUid(info.AUID)
	if army == nil || army.Owner != plr.Uid {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}

	// 删除
	dInfo := wld.CancelPawnCuringQueue(index, uid)
	if dInfo == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	// 更新受伤的士兵的状态
	injuryPawn := wld.GetInjuryPawn(uid, plr.Uid)
	if injuryPawn != nil {
		// 更新受伤的士兵的状态
		wld.ChangeInjuryPawnState(injuryPawn.Uid, plr.Uid, false)
	}
	rst := &pb.GAME_HD_CANCELCUREPAWN_S2C{}
	if dInfo.StartTime == 0 { //返还资源 招募中的不返回资源
		cost := dInfo.Cost
		if cost != nil {
			plr.ChangeCostByTypeObjs(cost, 1)
		}
		rst.NeedCost = g.ToTypeObjsPb(cost)
		rst.Output = plr.ToOutputInfoPb()
	}
	// 删除军队里面的
	army.RemoveCuringPawn(uid)
	// 如果没有了 就删除
	if army.GetActPawnCount() == 0 {
		if area.IsBattle() && !army.IsFighting() {
			// 未加入战斗的军队因取消治疗删除 则减少增援数量
			area.GetFspModel().DecDefenderAcc(army.Uid)
		}
		area.RemoveArmy(army.Uid)
		wld.NotifyPlayerArmyDistInfo(plr.Uid)
	}
	// 返回
	rst.Queues = wld.ToPawnCuringQueuePb(index)
	rst.Army = army.ToPb(area.ToArmyState(army))
	return pb.ProtoMarshal(rst)
}

// 放弃治疗受伤士兵
func (this *Game) giveupInjuryPawn(session gate.Session, msg *pb.GAME_HD_GIVEUPINJURYPAWN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	uid := msg.GetUid()
	if curingInfo := wld.GetCuringInfo(plr.MainCityIndex, uid); curingInfo != nil {
		return nil, ecode.PAWN_NOT_EXIST.String() // 治疗中不能放弃
	}
	injuryPawn := wld.RemoveInjuryPawn(uid, plr.Uid)
	if injuryPawn == nil {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(&pb.GAME_HD_GIVEUPINJURYPAWN_S2C{InjuryPawns: wld.InjuryPawnListClone(plr.Uid)})
}

// 加速治疗士兵
func (this *Game) speedUpCuringPawn(session gate.Session, msg *pb.GAME_HD_SPEEDUPCURINGPAWN_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	index, costBook := msg.GetIndex(), msg.GetCostBook()
	if costBook <= 0 {
		return nil, ecode.UNKNOWN.String()
	}
	curingQueue := wld.PawnCuringQueues.GetClone(index)
	if curingQueue == nil || len(curingQueue) == 0 {
		return nil, ecode.PAWN_NOT_EXIST.String()
	}

	// 获取队列士兵消耗书总数
	var totalCostBook, totalNeedTime, totalBaseNeedTime int32
	for _, curingInfo := range curingQueue {
		costRes, baseNeedTime := world.GetPawnResCost(curingInfo.Id, curingInfo.Lv)
		for _, v := range costRes {
			if v.Type == ctype.EXP_BOOK {
				totalCostBook += v.Count
			}
		}
		totalNeedTime += curingInfo.OriginNeedTime
		totalBaseNeedTime += baseNeedTime * 1000
	}
	now := time.Now().UnixMilli()
	totalCostBook *= int32(constant.CURE_SPEED_UP_BOOK_PARAM)
	// 每本书减少的时间 = (招募时间 + 训练时间) / (当前治疗队列士兵消耗书的总和*3)
	speedUpEach := totalBaseNeedTime / totalCostBook
	// 实际剩余的时间 = 总时间 - 队列第一个已经花费的时间
	leftTime := totalNeedTime - int32(now-curingQueue[0].StartTime)
	speedUpTime := speedUpEach * costBook
	if extraTime := speedUpTime - leftTime; extraTime > 0 {
		// 加速的时间大于实际剩余时间
		if extraTime >= speedUpEach {
			// 超过的时间大于每本书加速时间 则实际消耗的书更少
			costBook = leftTime / speedUpEach
			if leftTime%speedUpEach > 0 {
				costBook++
			}
		}
		speedUpTime = leftTime
	}
	if plr.ExpBook < costBook { // 经验书不足
		return nil, ecode.RES_NOT_ENOUGH.String()
	}
	plr.ChangeExpBook(-costBook) // 扣除资源
	wld.SpeedUpPawnCuringQueue(index, speedUpTime)

	return pb.ProtoMarshal(&pb.GAME_HD_SPEEDUPCURINGPAWN_S2C{
		ExpBook: plr.ExpBook,
		Queues:  wld.ToPawnCuringQueuePb(index),
	})
}

// 放弃治疗受伤士兵
func (this *Game) setHospitalNoticeOp(session gate.Session, msg *pb.GAME_HD_SETHOSPITALNOTICEOP_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	wld.SetHospitalNoticeClose(plr.Uid, msg.GetIsClose())
	return pb.ProtoMarshal(&pb.GAME_HD_SETHOSPITALNOTICEOP_S2C{})
}
