package bdtype

// 战斗数据类型
const (
	NONE                 = iota
	SUM_KILL             //击杀 1
	KILL_PAWN            //击杀士兵 2
	SUM_DAMAGE           //输出伤害 3
	TO_PAWN_DAMGE        //对士兵造成的伤害 4
	HEAL_HP              //回复生命 5
	HIT_DAMAGE_MITIGATED //实际承受伤害 6
	DIE_COUNT            //沦陷次数 7
	KILL_MAIN            //攻陷次数 8
	ASSIST_KILL_MAIN     //协助攻陷次数 9
	BUILD_DAMAGE         //建筑造成伤害 10
	DAMAGE_TO_BUILD      //对玩家建筑造成的伤害 11
	DAMAGE_TO_MONSTER    //对野怪造成的伤害 12
	KILL_MONSTER         //击杀野怪数量 13
	PAWN_DEAD            //士兵阵亡数量 14
	HIT_DAMAGE_TAKEN     //减伤前承受伤害 15

	// 下面用于客户端显示用 暂时放这里 主要是怕和上面的类型冲突
	ACC_RECRUIT_PAWN_COUNT    = 1001 //累计招募士兵数量 *10000 + 士兵id 为对应士兵数据
	RECRUIT_PAWN_MAX_COUNT    = 1002 //招募最多的士兵数量 弃用
	RECRUIT_PAWN_MAX_COUNT_ID = 1003 //招募最多的士兵数量ID 弃用
	MAXLV_PAWN_COUNT          = 1004 //满级士兵数量 *10000 + 士兵id 为对应士兵数据
	LAND_COUNT                = 1005 //领地数量
	MAX_LAND_COUNT            = 1006 //最大领地数量
	FIGHT_COUNT               = 1007 //参战次数
)

// 联盟分计算参数比例
var ALLI_SCORE_PARAM = map[int32]float64{
	TO_PAWN_DAMGE:        1,    //对士兵造成的伤害 4
	HIT_DAMAGE_MITIGATED: 0.75, //实际承受伤害 6
	BUILD_DAMAGE:         0.25, //建筑造成伤害 10
	DAMAGE_TO_BUILD:      1,    //对建筑造成的伤害 11
	HIT_DAMAGE_TAKEN:     0.25, //减伤前承受伤害 15
}

// 联盟分计算参数比例 (无效战斗的额外比例)
var ALLI_SCORE_INVALID_BATTLE_PARAM = map[int32]float64{
	HIT_DAMAGE_MITIGATED: 0.25, //承受伤害 6
	BUILD_DAMAGE:         0.25, //建筑造成伤害 10
	DAMAGE_TO_BUILD:      1,    //对建筑造成的伤害 11
}

// 士兵死亡对应计算联盟分相关参数
const (
	ALLI_SCORE_PAWN_DEAD_PARAM     = 0.0002 //总参数
	ALLI_SCORE_PAWN_DEAD_COST_RES  = 1.0    //士兵的死亡的等级对应消耗粮食，木头，石头比例参数
	ALLI_SCORE_PAWN_DEAD_COST_BOOK = 300.0  //士兵的死亡的等级对应消耗经验书比例参数
)

// 士兵战损计算联盟分相关参数
const (
	ALLI_SCORE_PAWN_PARAM     = 0.0006 //总参数
	ALLI_SCORE_PAWN_COST_RES  = 1.0    //士兵的死亡的等级对应消耗粮食，木头，石头比例参数
	ALLI_SCORE_PAWN_COST_BOOK = 300.0  //士兵的死亡的等级对应消耗经验书比例参数
	ALLI_SCORE_BUILD_PARAM    = 0.003  //建筑死亡的比例参数
)
