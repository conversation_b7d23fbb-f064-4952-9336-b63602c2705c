package lobby

import (
	"sort"
	"strings"

	slg "slgsrv/server/common"
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"
	"slgsrv/server/game/common/enums/tctype"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"

	"github.com/huyangv/vmqant/log"
)

func (this *Lobby) InitRpc() {
	//
	this.GetServer().RegisterGO(slg.RPC_LOBYY_NOTIFY_ALL_USER, this.lobyyNotifyAllUser)
	this.GetServer().RegisterGO(slg.RPC_LOBBY_PUT_USER_NOTIFY, this.lobyyPutUserNotify)
	this.GetServer().RegisterGO(slg.RPC_LOBBY_PUT_ALL_USER_NOTIFY, this.lobyyPutAllUserNotify)

	// 用户
	this.GetServer().RegisterGO(slg.RPC_LEAVE, this.leave)
	this.GetServer().RegisterGO(slg.RPC_GET_ONLINE_USER_INFO, this.getOnlineUserInfo)
	this.GetServer().RegisterGO(slg.RPC_CHANGE_PAWN_SKIN, this.changePawnSkin)
	this.GetServer().RegisterGO(slg.RPC_GET_GOLD, this.getGold)
	this.GetServer().RegisterGO(slg.RPC_CHANGEIGOLD, this.changeGold)
	this.GetServer().RegisterGO(slg.RPC_CHECK_HAS_PAWN_SKIN, this.checkHasPawnSkin)
	this.GetServer().RegisterGO(slg.RPC_CHECK_HAS_CHAT_EMOJI, this.checkHasChatEmoji)
	this.GetServer().RegisterGO(slg.RPC_CHECK_USE_CITY_SKIN, this.checkUseCitySkin)
	this.GetServer().RegisterGO(slg.RPC_TRIGGER_TASK_BY_GAME, this.triggerTaskByGame)
	this.GetServer().RegisterGO(slg.RPC_MAX_LAND_COUNT_CHANGE, this.maxLandCountChange)
	this.GetServer().RegisterGO(slg.RPC_GET_OFFLINE_REPORT_INFO, this.getOfflineReportInfo)
	this.GetServer().RegisterGO(slg.RPC_GET_USER_CHECK_CHAT_INFO, this.getUserCheckChatInfo)
	this.GetServer().RegisterGO(slg.RPC_GET_USER_BANNED_CHAT_ENDTIME, this.getUserBannedChatEndTime)
	this.GetServer().RegisterGO(slg.RPC_GET_USER_BY_CREATE_PLAYER, this.getUserByCreatePlayer)
	this.GetServer().RegisterGO(slg.RPC_SET_USER_PLAYSID, this.setUserPlaySid)
	this.GetServer().RegisterGO(slg.RPC_GET_TEAM_USERS_BY_CREATEPLAYER, this.getTeamUsersByCreatePlayer)
	this.GetServer().RegisterGO(slg.RPC_ADD_USER_ITEMS, this.addUserItems)
	this.GetServer().RegisterGO(slg.RPC_CANCEL_DEL_USER, this.rpcCancelDelUser)
	this.GetServer().RegisterGO(slg.RPC_ADD_BATTLE_PASS_SCORE, this.rpcAddBattlePassScore)
	this.GetServer().RegisterGO(slg.RPC_UPDATE_USER_INFO, this.updateUserInfo)
	this.GetServer().RegisterGO(slg.RPC_GET_USER_HERO_INFO, this.getUserHeroInfo)

	// 结算
	this.GetServer().RegisterGO(slg.RPC_GAMEOVER_SETTLE, this.gameOverSettle)

	// 邮件
	this.GetServer().RegisterGO(slg.RPC_SEND_MAIL_ITEM_ONE, this.sendMailItemOne)
	this.GetServer().RegisterGO(slg.RPC_SEND_MAIL_MANY, this.sendMailMany)

	// 支付相关
	this.GetServer().RegisterGO(slg.RPC_PAY_ORDER_BY_NOTIFY, this.payOrderByNotify)
	this.GetServer().RegisterGO(slg.RPC_ORDER_REFUND_BY_NOTIFY, this.orderRefundByNotify)
	this.GetServer().RegisterGO(slg.RPC_SUB_PAY_ORDER_BY_NOTIFY, this.subPayOrderByNotify)
	this.GetServer().RegisterGO(slg.RPC_DID_RENEW_BY_NOTIFY, this.didRenewByNotify)
	this.GetServer().RegisterGO(slg.RPC_RENEW_STATE_CHANGE_BY_NOTIFY, this.renewStateChangeByNotify)
	this.GetServer().RegisterGO(slg.RPC_SUB_REFUND_BY_NOTIFY, this.subRefundByNotify)

	// 停服更新相关
	this.GetServer().RegisterGO(slg.RPC_STOP_SERVER, this.stopServer)
	this.GetServer().RegisterGO(slg.RPC_GET_LOBBY_INFO, this.getLobbyInfo)

	// 后台相关
	this.GetServer().RegisterGO(slg.RPC_SET_TA_PARAMS, this.setTaParams)
	this.GetServer().RegisterGO(slg.RPC_GET_USER_BY_WEB, this.getUserByWeb)
	this.GetServer().RegisterGO(slg.RPC_SET_FRIEND_PARAMS, this.setFriendParams)
	this.GetServer().RegisterGO(slg.RPC_SET_LOGIN_LIMIT, this.setLoginLimit)
	this.GetServer().RegisterGO(slg.RPC_SET_TEAM_PARAMS, this.setTeamParams)
	this.GetServer().RegisterGO(slg.RPC_SET_APPLY_CLOSE, this.setApplyClose)
	this.GetServer().RegisterGO(slg.RPC_GET_ONLINE_SUM, this.getOnlineSum)
	this.GetServer().RegisterGO(slg.RPC_SET_PORTRAYAL_ODDS, this.setPortayalOdds)
	this.GetServer().RegisterGO(slg.RPC_SET_OFFLINE_MSG_PARAMS, this.setOfflineMsgParams)
}

// 通知所有用户
func (this *Lobby) lobyyNotifyAllUser(msgMap map[int32][]byte) (result interface{}, err string) {
	this.NotifyAllUser(msgMap)
	return
}

// 添加消息到通知队列
func (this *Lobby) lobyyPutUserNotify(nType int32, receivers []string, msgBytes []byte) (result interface{}, err string) {
	msg := &pb.OnUpdatePlayerInfoNotify{}
	e := pb.ProtoUnMarshal(msgBytes, msg)
	if e != nil {
		log.Warning("lobyyPutUserNotify err nType: %v receivers: %v e: %v", nType, receivers, e)
		return
	}
	for _, uid := range receivers {
		this.PutUserNotifyQueue(nType, uid, msg) // 通知玩家
	}
	return
}

// 添加消息到所有用户通知队列
func (this *Lobby) lobyyPutAllUserNotify(nType int32, msgBytes []byte) (result interface{}, err string) {
	msg := &pb.OnUpdatePlayerInfoNotify{}
	e := pb.ProtoUnMarshal(msgBytes, msg)
	if e != nil {
		log.Warning("lobyyPutAllUserNotify err nType: %v, e: %v", nType, e)
		return
	}
	this.PutAllUserNotifyQueue(nType, msg)
	return
}

// 有玩家离开
func (this *Lobby) leave(uid, sessionId string) (result interface{}, err string) {
	UserOffline(this, uid, sessionId)
	return
}

// 获取用户信息
func (this *Lobby) getOnlineUserInfo(uid string) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	// 主城皮肤
	mainCitySkin := array.Find(user.LastCitySkins, func(m int32) bool { return m/1000 == constant.MAIN_CITY_ID })
	return map[string]interface{}{
		"nickname":     user.Nickname,
		"headIcon":     user.HeadIcon,
		"registerTime": user.CreateTime,
		"rankScore":    user.RankScore,
		"platform":     user.Platform,
		"mainCitySkin": mainCitySkin,
	}, ""
}

// 改变士兵皮肤
func (this *Lobby) changePawnSkin(uid string, id, pawnId int32) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if id != 0 && !user.CheckHasSkin(id) {
		return nil, ecode.SKIN_NOT_EXIST.String()
	}
	user.LastPawnSkins = array.Delete(user.LastPawnSkins, func(m int32) bool { return m/1000 == pawnId })
	if id != 0 && !array.Has(user.LastPawnSkins, id) {
		user.LastPawnSkins = append(user.LastPawnSkins, id)
	}
	user.FlagUpdateDB()
	return
}

// 检测是否有某个士兵皮肤
func (this *Lobby) checkHasPawnSkin(uid string, id int32) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if !user.CheckHasSkin(id) {
		return nil, ecode.SKIN_NOT_EXIST.String()
	}
	return
}

// 获取金币
func (this *Lobby) getGold(uid string) (result map[string]interface{}, err string) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return map[string]interface{}{"gold": 0, "accTotalGold": 0}, ""
	}
	return map[string]interface{}{
		"gold":         user.Gold,
		"accTotalGold": user.AccTotalGold,
	}, ""
}

// 改变金币
func (this *Lobby) changeGold(uid string, val, reason int32) (result map[string]interface{}, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	} else if user.ChangeGold(val, reason) == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() // 金币不足
	}
	user.FlagUpdateDB()
	return map[string]interface{}{
		"gold": user.Gold,
	}, ""
}

// 对局结算
func (this *Lobby) gameOverSettle(sid, tp int32, time []int64, players []map[string]interface{}, pers int32, alliMap map[string]interface{}) (result map[string]interface{}, err string) {
	S := pers
	runTime := float64(time[1] - time[0])
	day := int32(ut.Ceil(runTime / float64(ut.TIME_DAY)))
	rescords := []interface{}{}
	arr := []UserRankScoreInfo{}
	serverType := sid / slg.ROOM_TYPE_FLAG
	for _, m := range players {
		uid, rank := ut.String(m["uid"]), ut.Int32(m["rank"])
		if tempUser := GetUserByDBNotAdd(uid); tempUser != nil {
			arr = append(arr, UserRankScoreInfo{user: tempUser, rank: rank, data: m})
		} else {
			log.Info("gameOverByRank user not exist, sid: %v, pers:%v/%v, uid: %v", sid, len(players), pers, uid)
		}
	}
	sort.Slice(arr, func(i, j int) bool { return arr[i].rank < arr[j].rank })

	// 计算平均分
	var sumScore int32
	for _, m := range arr {
		sumScore += m.user.RankScore
	}
	MS := float64(sumScore) / float64(len(arr))
	// 已处理的队伍Map
	handleTeamMap := map[string]bool{}

	for i, m := range arr {
		uid := m.user.UID
		win, gameScore, landScore, alliUid := ut.Bool(m.data["win"]), ut.Int32(m.data["score"]), ut.Int32(m.data["land_score"]), ut.String(m.data["alli_uid"])
		rst, err := ut.RpcInterfaceMap(this.InvokeUserFunc(uid, slg.RPC_USER_GAMEOVER_SETTLE, sid, tp, gameScore, landScore, win, m.rank, S, MS, runTime))
		if err != "" {
			log.Error("gameOverByRank uid: %v, sid: %v, err: %v", uid, sid, err)
			continue
		} else if ut.Bool(rst["giveUp"]) {
			continue
		}
		addScore, rankScore, addWarToken, teamUid := ut.Int32(rst["addScore"]), ut.Int32(rst["rankScore"]), ut.Int32(rst["addWarToken"]), ut.String(rst["teamUid"])
		if serverType == slg.CUSTOM_SERVER_TYPE {
			// 自定义区服无奖励
			addWarToken = 0
		} else {
			this.GameSettleAward(uid, sid, day, S, m.rank, addWarToken, false)
		}
		pawnStatistic := GetPlayerPawnStatistics(uid, ut.String(sid))
		rescords = append(rescords, CreateGameRecord(sid, time, m.data, addScore, rankScore, addWarToken, pawnStatistic, alliMap[alliUid]))
		if i%100 == 0 || i == len(arr)-1 {
			// 每100人批量保存一次对局记录
			gameRecordDB.InsertMany(rescords)
			rescords = []interface{}{}
		}

		// 队伍处理
		if teamUid != "" {
			if len(arr) == 1 {
				// 单人结算 退出队伍
				this.InvokeTeamFuncNR(teamUid, slg.RPC_GAMEOVER_LEAVE_TEAM, []string{uid}, false)
			} else if !handleTeamMap[teamUid] {
				handleTeamMap[teamUid] = true
				// 多人结算 重置每个队伍的playSid
				this.InvokeTeamFuncNR(teamUid, slg.RPC_TEAM_RESET_STATE, int32(-1), int32(0))
			}
		}
	}
	return
}

// 检测是否有某个表情
func (this *Lobby) checkHasChatEmoji(uid string, id int32) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if !user.CheckHasEmoji(id) {
		return nil, ecode.CHAT_EMOJI_NOT_EXIST.String()
	}
	return
}

// 检测使用城市皮肤
func (this *Lobby) checkUseCitySkin(uid string, cityId, skinId int32) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if skinId > 0 && !user.HasCitySkin(skinId) {
		return nil, ecode.SKIN_NOT_EXIST.String()
	}
	user.LastCitySkins = array.Delete(user.LastCitySkins, func(m int32) bool { return m/1000 == cityId })
	if skinId > 0 && !array.Has(user.LastCitySkins, skinId) {
		user.LastCitySkins = append(user.LastCitySkins, skinId)
	}
	user.FlagUpdateDB()
	return
}

// 游戏服触发任务
func (this *Lobby) triggerTaskByGame(uid string, condType, count, param int32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.UNKNOWN.String()
	}
	notifyGeneralTasks, notifyAchieveTasks := user.TriggerLoginTask(condType, count, param)
	notifyTasks := &pb.TaskUpdateNotify{
		GeneralTasks: notifyGeneralTasks,
		AchieveTasks: notifyAchieveTasks,
	}
	user.FlagUpdateDB()
	return pb.ProtoMarshal(notifyTasks)
}

// 最大地块数改变
func (this *Lobby) maxLandCountChange(uid string, maxLandCount, onlineHour int32, active10kLand bool) (result map[string]interface{}, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.UNKNOWN.String()
	}
	if maxLandCount > user.MaxLandCount {
		user.MaxLandCount = maxLandCount
	}
	// 触发任务
	user.TriggerTask(this, tctype.LIVER_EMPEROR, maxLandCount, onlineHour)
	if maxLandCount >= 10000 && !active10kLand {
		// 万亩任务触发限制
		maxLandCount = 9999
	}
	user.TriggerTask(this, tctype.THOUSAND_MU, maxLandCount, 0)
	user.TriggerTask(this, tctype.LAND_10K_COUNT, maxLandCount, 0)
	user.FlagUpdateDB()
	return
}

// 离线获取上报信息
func (this *Lobby) getOfflineReportInfo(uid string) (result map[string]interface{}, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.UNKNOWN.String()
	}
	result = map[string]interface{}{
		"gameCount":    user.AccTotalGameCounts[1],
		"rankScore":    user.RankScore,
		"gold":         user.Gold,
		"accTotalGold": user.AccTotalGold,
	}
	return
}

// 获取用户聊天信息相关
func (this *Lobby) getUserCheckChatInfo(uid string, emoji, portrayalId int32) (ret map[string]interface{}, err string) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return
	} else if !user.CheckHasEmoji(emoji) {
		err = ecode.CHAT_EMOJI_NOT_EXIST.String()
		return
	}
	portrayalInfo, err := user.CheckPortrayalByChat(portrayalId)
	if err != "" {
		return
	}
	user.BlackListLock.RLock()
	defer user.BlackListLock.RUnlock()
	return map[string]interface{}{
		"bannedChatEndTime": user.GetBannedChatEndTime(),
		"blacklists":        array.Map(user.Blacklists, func(m *BlacklistInfo, _ int) string { return m.UID }),
		"portrayalInfo":     portrayalInfo,
	}, ""
}

// 获取用户的禁言结束时间
func (this *Lobby) getUserBannedChatEndTime(uid string) (ret map[string]interface{}, err error) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return
	}
	return map[string]interface{}{
		"bannedChatEndTime": user.GetBannedChatEndTime(),
	}, nil
}

// 获取用户信息 创建角色的时候
func (this *Lobby) getUserByCreatePlayer(uid string) (result map[string]interface{}, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	result = GetCreatePlayerInfo(user)
	return
}

// 设置用户playsid
func (this *Lobby) setUserPlaySid(uid string, sid int32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	} else if user.GetPlaySid() != sid {
		user.SetPlaySid(sid)
		user.FriendInfoNotify()
		user.FlagUpdateDB()
	}
	return
}

// 获取队伍中的用户信息 开服创建角色的时候
func (this *Lobby) getTeamUsersByCreatePlayer(teamUid string, sid int32) (result map[string]interface{}, err string) {
	rst, err := ut.RpcBytes(this.InvokeTeamFunc(teamUid, slg.RPC_GET_TEAM_INFO))
	if err != "" {
		log.Warning("getTeamUsersByCreatePlayer team nil, teamUid: %v", teamUid)
		return
	}
	teamPb := &pb.TeamInfo{}
	pb.ProtoUnMarshal(rst, teamPb)
	userMap := map[string]map[string]interface{}{}
	for _, v := range teamPb.UserList {
		rst, e := ut.RpcInterfaceMap(this.InvokeUserFunc(v.Uid, slg.RPC_GET_APPLY_USER_BY_CREATEPLAYER, sid))
		if e != "" {
			log.Warning("getTeamUsersByCreatePlayer get user nil uid: %v, err: %v", e)
			continue
		}
		userInfo := ut.MapInterface(rst["user"])
		userMap[ut.String(userInfo["uid"])] = userInfo
	}
	// 通知队伍进入游戏
	this.InvokeTeamFuncNR(teamUid, slg.RPC_TEAM_ENTER_GAME, sid)
	result = map[string]interface{}{
		"userMap":     userMap,
		"independent": teamPb.Independent,
	}
	return
}

// 添加玩家物品到用户服和游戏服
func (this *Lobby) addUserItems(uid string, items []*g.TypeObj, reason int32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	output := this.ChangeUserItems(user, items, reason)
	return pb.ProtoMarshal(output)
}

// 撤回删除用户
func (this *Lobby) rpcCancelDelUser(uid string) (result []byte, err string) {
	// 从待删除的内存表中移除
	delUserMap.Delete(uid)
	return
}

// 添加战令积分
func (this *Lobby) rpcAddBattlePassScore(uid string, score int32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	if user.BattlePass == nil {
		user.BattlePass = NewBattlePass()
	}
	user.BattlePass.AddScore(score)
	// 检测是否可以领取奖励
	if user.BattlePass.CheckCanClaimAward() {
		this.PutUserNotifyToLobby(constant.NQ_BATTLE_PASS_HAS_AWARD, []string{user.UID}, &pb.OnUpdatePlayerInfoNotify{Data_78: true}) // 通知玩家
	}
	return
}

// 获取用户服信息
func (this *Lobby) getLobbyInfo() (result map[string]interface{}, err string) {
	result = map[string]interface{}{
		"ip":        slg.SERVER_IP,
		"userNum":   len(users.Map),
		"isRunning": isRunning,
		"userCount": userCount,
		"saveCount": saveCount,
	}
	return
}

// 后台获取内存中用户信息 (其他lobby与http通信 该lobby通过rpc转发)
func (this *Lobby) getUserByWeb(uidList []string) (result map[string]interface{}, err string) {
	userInfoMap := map[string]map[string]interface{}{}
	for _, uid := range uidList {
		if user := GetUser(uid); user != nil {
			userData := user.ToWeb("")
			userData["incache"] = true
			userData["online"] = user.IsOnline()
			userInfoMap[uid] = userData
		}
	}
	onlineSum, total := GetOnlineSum()
	result = map[string]interface{}{
		"map":        userInfoMap,
		"onlineSum":  onlineSum,
		"cacheTotal": total,
	}
	return
}

// 设置数数参数
func (this *Lobby) setTaParams(taOpen, taDebug string) (ret map[string]interface{}, err error) {
	slg.TA_OPEN = ut.If(taOpen == "1", true, false)
	slg.TA_DEBUG = ut.If(taDebug == "1", true, false)
	return
}

// 设置好友相关参数
func (this *Lobby) setFriendParams(maxNum, applyNum, landCount, blackListMax string) (ret map[string]interface{}, err error) {
	_maxNum := ut.Int(maxNum)
	_applyNum := ut.Int(applyNum)
	_landCount := ut.Int(landCount)
	_blackListMax := ut.Int(blackListMax)

	if _maxNum > 0 {
		slg.FRIENDS_MAX_NUM = _maxNum
	}
	if _applyNum > 0 {
		slg.FRIENDS_APPLY_MAX_NUM = _applyNum
	}
	if _landCount > 0 {
		slg.FRIENDS_MIN_LAND_COUNT = _landCount
	}
	if _blackListMax > 0 {
		slg.BLACK_LIST_MAX = _blackListMax
	}
	return
}

// 设置组队相关参数
func (this *Lobby) setTeamParams(userNumMax, chatNumMax int) (ret map[string]interface{}, err error) {
	slg.TEAM_USER_NUM_MAX = userNumMax
	slg.TEAM_CHAT_NUM_MAX = chatNumMax
	return
}

// 设置报名是否关闭
func (this *Lobby) setApplyClose(roomType int32, close bool) (ret map[string]interface{}, err error) {
	applyCloseMap.Set(roomType, close)
	return
}

// 设置登录限制类型
func (this *Lobby) setLoginLimit(limitType int) (ret map[string]interface{}, err error) {
	this.loginLimitType = limitType
	return
}

// 设置天选画像概率
func (this *Lobby) setPortayalOdds(odds int) (ret map[string]interface{}, err error) {
	g.PORTRAYAL_CHOSEN_ONE_ODD_MAX = odds
	return
}

// 设置离线通知参数
func (this *Lobby) setOfflineMsgParams(open bool) (ret map[string]interface{}, err error) {
	slg.OfflineMsgNotify = open
	return
}

// 获取在线人数
func (this *Lobby) getOnlineSum() (ret map[string]interface{}, err error) {
	onlineSum, _ := GetOnlineSum()
	ret = map[string]interface{}{
		"onlineSum": onlineSum,
	}
	return
}

// 登录服停机
func (this *Lobby) stopServer() (result map[string]interface{}, err string) {
	if isRunning {
		StopTick()
		SaveAllUser()
	}
	return
}

// 用户信息更新通知
func (this *Lobby) updateUserInfo(uid string, data *pb.OnUpdatePlayerInfoNotify) (result []byte, err string) {
	user := GetUserByOnline(uid)
	if user == nil || user.Session == nil {
		return
	}
	user.PutNotifyQueue(data.Type, data)
	return
}

// 获取玩家的英雄信息
func (this *Lobby) getUserHeroInfo(uid string, id int32) (result []byte, err string) {
	user := GetUserByOnline(uid)
	if user == nil {
		return nil, ecode.NOT_BIND_UID.String()
	}
	info := user.GetPortrayalInfo(id)
	if !info.IsUnlock() {
		return nil, ecode.HERO_NOT_EXIST.String()
	}
	return pb.ProtoMarshal(info.ToPbForPawn())
}

// 获取玩家信息
func (this *Lobby) getUserInfo(uid string) (result map[string]interface{}, err string) {
	user := GetUserByDBNotAdd(uid)
	if user == nil {
		return nil, ecode.PLAYER_NOT_EXIST.String()
	}
	result = map[string]interface{}{
		"playSid":        user.GetPlaySid(),
		"nickname":       user.Nickname,
		"ip":             user.Ip,
		"expectPosition": user.ExpectPosition,
		"farmType":       user.FarmType,
		"teamUid":        user.TeamUid,
	}
	return
}

// 发送邮件
func (this *Lobby) sendMailItemOne(sid int, contentId int, title, content, sender, receiver string, items []*g.TypeObj) (result []byte, err string) {
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_ITEM_ONE, sid, contentId, title, content, sender, receiver, items)
	return
}

// 发送邮件
func (this *Lobby) sendMailMany(sid int, contentId int, title, content, sender string, uids []string) (result []byte, err string) {
	this.InvokeMailRpcNR(slg.RPC_SEND_MAIL_MANY, sid, contentId, title, content, sender, uids)
	return
}

// 物品购买处理
func (this *Lobby) payOrderByNotify(cpOrderId, orderId, userId, productId, price, platform string, quantity int32) (result []byte, err string) {
	user := this.GetUserByDB(userId)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.handleVerifyPayOrder(user, cpOrderId, orderId, productId, platform, quantity)
	return
}

// 处理退款
func (this *Lobby) orderRefundByNotify(uid, orderUid, productId, currencyType string, payAmount float32, quantity, oldState int) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.userRefund(user, orderUid, productId, currencyType, payAmount, quantity, oldState)
	return
}

// 处理购买订阅
func (this *Lobby) subPayOrderByNotify(cpOrderId, orderId, userId, productId, platform, offerType string, endTime int64) (result []byte, err string) {
	user := this.GetUserByDB(userId)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	// 处理购买
	this.handleBuySubOrder(user, cpOrderId, orderId, productId, platform, offerType)
	// 默认货币类型是美元
	currencyType := "USD"
	payAmount := slg.RECHARGE_PRICE_USD_CONFIG[productId]
	// 更新玩家订阅信息
	user.UpdateUserSubInfo(cpOrderId, productId, endTime, 0, true, currencyType, payAmount, false, this)
	user.FlagUpdateDB()
	return
}

// 处理续订
func (this *Lobby) didRenewByNotify(uid, orderUid, orderId, productId, subType, currencyType string, payAmount float32, endTime int64, offerType string) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.userDidRenew(user, orderUid, orderId, productId, subType, currencyType, payAmount, endTime, true, offerType)
	return
}

// 处理续订状态更新
func (this *Lobby) renewStateChangeByNotify(uid, orderUid, productId, subType, currencyType string, payAmount float32, endTime int64, auto bool) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.userRenewStateChange(user, orderUid, productId, subType, currencyType, payAmount, endTime, auto)
	return
}

// 处理订阅退款
func (this *Lobby) subRefundByNotify(uid, orderUid, subType, currencyType string, payAmount float32) (result []byte, err string) {
	user := this.GetUserByDB(uid)
	if user == nil {
		err = ecode.PLAYER_NOT_EXIST.String()
		return
	}
	this.userSubRefund(user, orderUid, subType, currencyType, payAmount)
	return
}

// 游戏结算奖励
func (this *Lobby) GameSettleAward(uid string, sid, day, S, rank, addWarToken int32, newbieRepeat bool) {
	if addWarToken > 0 {
		mailId := ut.If(newbieRepeat, slg.MAIL_GAME_SETTLEMENT_NEWBIE_REPEAT_ID, slg.MAIL_GAME_SETTLEMENT_ID)
		// 历经{0}天鏖战，{1}终于落下了帷幕，感谢你的积极参与！本次对局总人数为{2}人，你获得了第{3}名的战绩，以下是你所赢得的奖励，请查收。
		parames := ut.Itoa(day) + "|" + slg.GetServerNoNameKey(sid) + "|" + ut.Itoa(S) + "|" + ut.Itoa(rank+1)
		this.sendMailItemOne(0, mailId, "", parames, "-1", uid, []*g.TypeObj{g.NewTypeObj(ctype.WAR_TOKEN, 0, addWarToken)})
	} else if rank >= 0 {
		// 没有奖励的邮件 历经{0}天鏖战，{1}终于落下了帷幕，感谢你的积极参与！本次对局总人数为{2}人，你获得第{3}名，请再接再厉。
		parames := ut.Itoa(day) + "|" + slg.GetServerNoNameKey(sid) + "|" + ut.Itoa(S) + "|" + ut.Itoa(rank+1)
		this.sendMailItemOne(0, slg.MAIL_GAME_SETTLEMENT_NO_REWARD_ID, "", parames, "-1", uid, nil)
	}
}

func GetOsAndVer(os string) (deviceOS, deviceOSVersion string) {
	deviceOS = "none"
	deviceOSVersion = "0.0"
	if os == "" {
		return
	}
	if DeviceOSArr := strings.Split(os, ";"); len(DeviceOSArr) > 0 {
		deviceOS = DeviceOSArr[0]
		if len(DeviceOSArr) > 1 {
			deviceOSVersion = DeviceOSArr[1]
		}
	}
	return
}

// 从用户信息中获取创建游戏服玩家信息
func GetCreatePlayerInfo(user *User) map[string]interface{} {
	// 携带数据 创建过后就不在是新手了
	user.CarryNoviceData = false
	// 获取玩家的操作系统
	deviceOS, deviceOSVersion := GetOsAndVer(user.DeviceOS)
	// 主城皮肤
	mainCitySkin := array.Find(user.LastCitySkins, func(m int32) bool { return m/1000 == constant.MAIN_CITY_ID })
	// 返回
	return map[string]interface{}{
		"uid":               user.UID,
		"nickname":          user.Nickname,
		"headIcon":          user.HeadIcon,
		"personalDesc":      user.PersonalDesc,
		"title":             user.LastTitle,
		"pawnSkins":         array.Map(user.LastPawnSkins, func(m int32, _ int) int32 { return m }),
		"registerTime":      user.CreateTime,
		"deviceOS":          deviceOS,
		"deviceOSVersion":   deviceOSVersion,
		"platform":          user.Platform,
		"lang":              user.Language,
		"FCMToken":          user.FCMToken,
		"offlineNotifyOpt":  user.OfflineNotifyOpt,
		"distinctId":        user.DistinctId,
		"pos":               user.ExpectPosition - 1,
		"playSid":           user.GetPlaySid(),
		"mainCitySkin":      mainCitySkin,
		"treasureLostCount": user.TreasureLostCount,
		"antiCheat":         user.AntiCheatData,
		"isOnline":          user.IsOnline(),
		"farmType":          user.FarmType,
		"rankScore":         user.RankScore,
	}
}

// 从用户数据库数据中获取创建游戏服玩家信息 弃用
func GetCreatePlayerInfoByTableData(data TableData) map[string]interface{} {
	// 目前报名启动的服务器创建玩家不支持携带新手村数据 所以无需修改CarryNoviceData
	// user.CarryNoviceData = false
	// 获取玩家的操作系统
	deviceOS, deviceOSVersion := GetOsAndVer(data.DeviceOS)
	// 返回
	return map[string]interface{}{
		"uid":              data.Uid,
		"nickname":         data.Nickname,
		"headIcon":         data.HeadIcon,
		"personalDesc":     data.PersonalDesc,
		"title":            data.LastTitle,
		"pawnSkins":        array.Map(data.LastPawnSkins, func(m int32, _ int) int32 { return m }),
		"registerTime":     data.CreateTime,
		"deviceOS":         deviceOS,
		"deviceOSVersion":  deviceOSVersion,
		"platform":         data.Platform,
		"lang":             data.Language,
		"FCMToken":         data.FCMToken,
		"offlineNotifyOpt": data.OfflineNotifyOpt,
		"distinctId":       data.DistinctId,
	}
}
