package helper

import (
	ut "slgsrv/utils"

	"github.com/huyangv/vmqant/log"
)

const (
	CIRCLE_COUNT        = 5  //主城周围的多少圈数内没有被占领 就可以
	MIN_CELL_COUNT      = 80 //最小单元格数量 至少这么多就可以
	RANGE_NOT_USE_COUNT = 10 //周围多少格不会占用
)

type FinderMainCity struct {
	opened       []*ut.Vec2                         //开放列表
	closed       []*ut.Vec2                         //关闭列表
	sp           map[string]int32                   //记录每个格子的移动力
	checkHasPass func(x, y int32) bool              //检测方法
	hasCell      func(index int32, uid string) bool //是否有被占领的地块
	ignorePlayer string                             //忽略检查的玩家
	mapSize      *ut.Vec2
}

// 检查节点
func (this *FinderMainCity) findNode(point *ut.Vec2, tx, ty int32) bool {
	step := this.sp[point.Join("_")] - 1
	if step < 0 {
		return true //如果已经没有移动力了
	}
	// 检测是否找过
	id := ut.Itoa(tx) + "_" + ut.Itoa(ty)
	i := arrayHasPoint(this.closed, tx, ty)
	if i >= 0 && this.sp[id] >= step {
		return true //如果已经找过 则检查这个点的行动力是否小于当前这个行动力
	} else if !this.checkHasPass(tx, ty) {
		return step < CIRCLE_COUNT-1 //第一圈不能有障碍
	} else if this.hasCell(PointToIndex(tx, ty, this.mapSize), this.ignorePlayer) { //如果被占领了
		return false
	}
	// 如果都不是，则代表是个可以拓展的节点
	this.sp[id] = step //把新的节点的移动力继承并减1
	var p *ut.Vec2
	if i >= 0 {
		p = this.closed[i]
	} else { //新的点
		p = ut.NewVec2(tx, ty)
		this.closed = append(this.closed, p)
	}
	// 如果这个点还有移动点 那么就继续扩展
	if step > 0 {
		this.opened = append(this.opened, p)
	}
	return true
}

// 检测是否可以修建主城
func (this *FinderMainCity) checkBuildCanMainCity(x, y, step int32) bool {
	this.opened = []*ut.Vec2{}
	this.closed = []*ut.Vec2{}
	this.sp = map[string]int32{}
	// 左下角为原点 大小2x2
	points := []*ut.Vec2{ut.NewVec2(x, y), ut.NewVec2(x+1, y), ut.NewVec2(x, y+1), ut.NewVec2(x+1, y+1)}
	for _, point := range points {
		if !this.checkHasPass(point.X, point.Y) || this.hasCell(PointToIndex(point.X, point.Y, this.mapSize), this.ignorePlayer) {
			return false
		}
		this.sp[point.Join("_")] = step          //将初始点的移动力设置为最高移动力
		this.opened = append(this.opened, point) //放进待处理队列
		this.closed = append(this.closed, point) //把起点放进已处理的点中
	}
	// 开始搜寻
	openedLen := len(this.opened)
	for openedLen > 0 {
		p := this.opened[openedLen-1]
		this.opened = this.opened[:openedLen-1]
		if !this.findNode(p, p.X+1, p.Y) { //右
			return false
		} else if !this.findNode(p, p.X-1, p.Y) { //左
			return false
		} else if !this.findNode(p, p.X, p.Y+1) { //上
			return false
		} else if !this.findNode(p, p.X, p.Y-1) { //下
			return false
		} else if !this.findNode(p, p.X-1, p.Y-1) { //左下
			return false
		} else if !this.findNode(p, p.X+1, p.Y-1) { //右下
			return false
		} else if !this.findNode(p, p.X+1, p.Y+1) { //右上
			return false
		} else if !this.findNode(p, p.X-1, p.Y+1) { //左上
			return false
		}
		openedLen = len(this.opened)
	}
	// fmt.Println(this.closed)
	// fmt.Println(len(this.closed))
	return len(this.closed) >= MIN_CELL_COUNT
}

func (this *FinderMainCity) Filter(indexs []int32, mapSize *ut.Vec2, chp func(x, y int32) bool, hc func(index int32, uid string) bool) []int32 {
	now := ut.Now()
	cnt := len(indexs)
	this.mapSize = mapSize
	this.checkHasPass = chp
	this.hasCell = hc
	for i := cnt - 1; i >= 0; i-- {
		index := indexs[i]
		point := IndexToPoint(index, mapSize)
		if !this.checkBuildCanMainCity(point.X, point.Y, CIRCLE_COUNT) { //周围多少圈范围内都没有其他玩家
			indexs = append(indexs[:i], indexs[i+1:]...)
		}
	}
	log.Info("FilterMainCityIndexs plen=" + ut.Itoa(cnt) + ", clen=" + ut.Itoa(len(indexs)) + ", time=" + ut.Itoa(ut.Now()-now) + "ms")
	return indexs
}

// 检测单个位置
func (this *FinderMainCity) CheckIndex(index int32, uid string, mapSize *ut.Vec2, chp func(x, y int32) bool, hc func(index int32, uid string) bool) bool {
	this.checkHasPass = chp
	this.hasCell = hc
	this.ignorePlayer = uid
	this.mapSize = mapSize
	point := IndexToPoint(index, mapSize)
	return this.checkBuildCanMainCity(point.X, point.Y, CIRCLE_COUNT)
}

func CheckMainCityIndex(index int32, uid string, mapSize *ut.Vec2, chp func(x, y int32) bool, hc func(index int32, uid string) bool) bool {
	return new(FinderMainCity).CheckIndex(index, uid, mapSize, chp, hc)
}

func FilterMainCityIndexs(indexs []int32, mapSize *ut.Vec2, chp func(x, y int32) bool, hc func(index int32, uid string) bool) []int32 {
	if len(indexs) == 0 {
		return indexs
	}
	return new(FinderMainCity).Filter(indexs, mapSize, chp, hc)
}
