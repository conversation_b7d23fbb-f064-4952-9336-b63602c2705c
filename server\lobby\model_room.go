package lobby

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"time"

	"github.com/huyangv/vmqant/log"
)

var (
	rooms         = ut.NewMapLock[int32, *pb.ServerInfo]() // 游戏服map
	initFinish    = false                                  // 是否已获取游戏服信息
	applyCloseMap = ut.NewMapLock[int32, bool]()           // 报名关闭map
)

func GetRoomById(sid int32) *pb.ServerInfo {
	if sid == 0 {
		return nil
	}
	return rooms.Get(sid)
}

// 获取运行天数
func GetRoomRunDay(room *pb.ServerInfo) int32 {
	if room.CreateTime == 0 {
		return 0
	}
	return int32(ut.Ceil(float64(ut.Now()-room.CreateTime) / float64(ut.TIME_DAY)))
}

// 获取用于转盘的运行天数
func GetRoomRunDayByWheel(sid int32) int32 {
	var runDay int32 = 1
	if room := GetRoomById(sid); room != nil {
		runDay = ut.MaxInt32(1, GetRoomRunDay(room))
	}
	return runDay
}

// 获取指定类型可以进的区服
func GetCanPlayRookieRoom(user *User) *pb.ServerInfo {
	now, ROOKIE_CAP_MIN := time.Now().UnixMilli(), slg.ROOKIE_CAP_MIN
	var maxPersCap int32 = 0
	var ret *pb.ServerInfo = nil
	giveupMap := map[int32]bool{}
	for _, v := range user.GiveupGames {
		giveupMap[v] = true
	}
	rooms.ForEach(func(m *pb.ServerInfo, k int32) bool {
		if !giveupMap[k] && m.Type == slg.ROOKIE_SERVER_TYPE && !IsGameOver(m) && m.PersCap > ROOKIE_CAP_MIN && now < slg.GetSummerStartTime(m.CreateTime) {
			if m.PersCap > maxPersCap {
				maxPersCap = m.PersCap
				ret = m
			}
		}
		return true
	})
	return ret
}

// 从工具服获取区服信息Tick
func (this *Lobby) GetRoomsByRpcTick() {
	go func() {
		tiker := time.NewTicker(time.Second * 1)
		for isRunning {
			this.UpdateRooms()
			<-tiker.C
		}
	}()
}

// 重新获取服务器
func (this *Lobby) UpdateRooms() {
	bytes, err := ut.RpcBytes(this.InvokeMatchRpc(slg.RPC_GET_ROOMS))
	if err == "" {
		if bytes != nil {
			msg := &pb.ServerList{}
			if e := pb.ProtoUnMarshal(bytes, msg); e == nil {
				rooms.Lock()
				flagMap := map[int32]bool{}
				for _, v := range msg.List {
					rooms.Map[v.GetId()] = v
					flagMap[v.GetId()] = true
				}
				for sid := range rooms.Map {
					// 删除匹配服中没有的房间
					if !flagMap[sid] {
						delete(rooms.Map, sid)
					}
				}
				rooms.Unlock()
			}
		}
		initFinish = true
	}
}

// 是否结束
func IsGameOver(room *pb.ServerInfo) bool {
	return room.GetWinType() != 0
}

// 游戏服是否关闭
func IsRoomClose(sid int32) bool {
	room := GetRoomById(sid)
	if room == nil {
		return true
	}
	return room.IsClose
}

// 获取服务器状态
func GetRoomState(sid int32) int32 {
	room := GetRoomById(sid)
	if room == nil || room.IsClose {
		return slg.SERVER_STATUS_CLOSE
	}
	return room.State
}

// 数据库获取报名信息 大厅服只查询不修改
func InitApplyInfo() {
	datas, err := applyDb.FindAllApplyInfo()
	if err != nil {
		log.Error("InitApplyInfo FindAllApplyInfo err: %v", err)
		return
	}
	for _, info := range datas {
		applyCloseMap.Set(info.RoomType, info.Close)
	}
}

// 获取是否关闭报名
func IsCloseApply() bool {
	rst := false
	applyCloseMap.ForEach(func(v bool, k int32) bool {
		if v {
			rst = true
			return false
		}
		return true
	})
	return rst
}
