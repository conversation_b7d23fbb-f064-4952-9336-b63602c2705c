package mail

import (
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

// 邮件基础信息
type MailBaseInfo struct {
	UID        string `bson:"uid"`
	Sender     string `bson:"sender"`   //发送人
	Receiver   string `bson:"receiver"` //接受人 -1.系统 0.所有人
	Title      string `bson:"title"`    //标题
	Content    string `bson:"content"`  //内容
	senderName string //发送人名字

	ReceiverMap   map[string]bool `bson:"receiver_map"`    //接受人map 多人接收者
	NoReceiverMap map[string]bool `bson:"no_receiver_map"` //不会接收的人map
	Items         []*g.TypeObj    `bson:"items"`           //奖励物品

	CreateTime int64 `bson:"create_time"` //创建时间
	SID        int32 `bson:"sid"`
	ContentId  int32 `bson:"content_id"` //内容类型 0.字符串 1.json内容
}

// 玩家邮件信息
type UserMailInfo struct {
	deadlock.RWMutex `bson:"-"`
	UID              string  `bson:"uid"`        //邮件uid + "_" + 玩家uid
	MailUid          string  `bson:"mail_uid"`   //邮件uid
	UserId           string  `bson:"user_id"`    //玩家uid
	ClaimList        []int32 `bson:"claim_list"` //领取奖励的下标列表
	IsRead           bool    `bson:"is_read"`    //是否已读
	IsClaim          bool    `bson:"is_claim"`   //是否已领奖
	IsDelete         bool    `bson:"is_delete"`  //是否已删除
}

// 玩家邮件信息map
type UserMailMap struct {
	datas map[string]*UserMailInfo
	deadlock.RWMutex
}

// 初始化玩家邮件map
func (this *UserMailMap) InitUserMailMap(userId string) {
	// 从数据库获取玩家邮件数据
	datas, err := userMailDb.FindUserMailInfo(userId)
	this.Lock()
	if err == nil && datas != nil {
		for _, v := range datas {
			this.datas[v.MailUid] = v
		}
	}
	this.Unlock()
}

func NewUserMailInfo(userId, uid string) *UserMailInfo {
	return &UserMailInfo{
		UID:       uid + "_" + userId,
		MailUid:   uid,
		UserId:    userId,
		ClaimList: []int32{},
	}
}

// 获取玩家指定邮件信息
func (this *UserMailMap) GetUserMailInfo(uid string) *UserMailInfo {
	this.RLock()
	defer this.RUnlock()
	return this.datas[uid]
}

// 更新玩家指定邮件信息
func (this *UserMailMap) UpdateUserMailInfo(mailInfo *UserMailInfo) {
	this.Lock()
	this.datas[mailInfo.MailUid] = mailInfo
	this.Unlock()
	userMailUpdateMap.Set(mailInfo.UID, mailInfo)
	dbChan <- mailInfo.UID
}

// 删除玩家邮件信息
func (this *UserMailMap) DeleteUserMailInfo(uid string) {
	this.Lock()
	delete(this.datas, uid)
	this.Unlock()
	userMailUpdateMap.Del(uid)
	dbChan <- uid
}

func NewMail(uid string, sid, contentId int32, title, content, sender, receiver string) *MailBaseInfo {
	return &MailBaseInfo{
		UID:        uid,
		SID:        sid,
		Sender:     sender,
		Receiver:   receiver,
		ContentId:  contentId,
		Title:      title,
		Content:    content,
		CreateTime: time.Now().UnixMilli(),
	}
}

func (this *MailBaseInfo) ToPb(userMail *UserMailInfo) *pb.MailInfo {
	state := 2 //已读
	if userMail == nil || !userMail.IsRead {
		state = 0 //未读
	} else if len(this.Items) > 0 && !userMail.IsClaim {
		state = 1 //读了但是没领取
	}
	// 自动删除时间
	var autoDelSurplusMaxTime int64 = slg.NORMAL_MAIL_EXPIR_TIME
	if len(this.Items) > 0 {
		autoDelSurplusMaxTime = slg.ITEM_MAIL_EXPIR_TIME
	}
	rst := &pb.MailInfo{
		Uid:                this.UID,
		Sender:             this.Sender,
		SenderName:         this.senderName,
		ContentId:          int32(this.ContentId),
		Title:              this.Title,
		Content:            this.Content,
		CreateTime:         int64(this.CreateTime),
		AutoDelSurplusTime: int64(ut.MaxInt64(0, (this.CreateTime+autoDelSurplusMaxTime)-time.Now().UnixMilli())),
		State:              int32(state),
	}
	if this.Items != nil {
		rst.Items = array.Map(this.Items, func(m *g.TypeObj, _ int) *pb.TypeObj { return m.ToPb() })
	}
	if userMail != nil && userMail.ClaimList != nil {
		rst.OneClaims = pb.Int32Array(userMail.ClaimList)
	}
	return rst
}

// GetAllMails 获取单个区服所有邮件 -1则是全部区服
func (this *Mail) GetAllMails(sid, page, pageSize int, sender, receiver string) []map[string]interface{} {
	limit := int64(pageSize)
	skip := int64(page * pageSize)
	arr, err := mailDb.GetMail(sid, sender, receiver, limit, skip)
	if err != nil {
		log.Info("err:%s", err.Error())
		return nil
	}
	var data []map[string]interface{}
	for _, mail := range arr {
		isRead := false
		if len(mail.ReceiverMap) > 0 || receiver == "0" {
			// 群发不获取已读状态
		} else if userMap := allUserMailMap.Get(receiver); userMap != nil {
			isRead = userMap.GetUserMailInfo(mail.UID) != nil
		}
		data = append(data, mail.Strip2(isRead))
	}
	return data
}

// Strip2 给web使用
func (this MailBaseInfo) Strip2(isRead bool) map[string]interface{} {
	state, isSystem, mult := 0, false, false
	if len(this.ReceiverMap) > 1 {
		mult = true // 多人邮件
		state = 0   // 多个接收者的邮件无法整理状态 标记为未知
	} else {
		if isRead {
			state = 1 // 已读 包括系统邮件
			if this.Receiver == "-1" {
				isSystem = true
				state = 2 // 发给系统的邮件 已读
			}
		} else {
			state = 3 //未读
			if this.Receiver == "-1" {
				isSystem = true
				state = 4 // 发给系统的邮件 未读
			}
		}
	}
	msg := map[string]interface{}{
		"sid":        this.SID,
		"uid":        this.UID,
		"sender":     this.Sender,
		"receiver":   this.Receiver,
		"title":      this.Title,
		"contentId":  this.ContentId,
		"content":    this.Content,
		"items":      this.Items,
		"createTime": this.CreateTime,
		"state":      state,
		"isSystem":   isSystem,
		"mult":       mult,
	}
	return msg
}
