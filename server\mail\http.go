package mail

import (
	"encoding/json"
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"sort"
	"strings"
)

func (this *Mail) InitHttp() {
	// 发送邮件
	this.GetServer().RegisterGO("/http/sendMailToLogin", this.sendMail)
	// 拉取邮件列表
	this.GetServer().RegisterGO("/http/mails", this.httpGetMailsToWeb)
	// 设置系统邮件已读
	this.GetServer().RegisterGO("/http/tagMail", this.httpTagMail)
}

// 发送邮件
func (this *Mail) sendMail(roomId, contentId string, title, content, receiver, items string) (map[string]interface{}, error) {
	sid := ut.Int32(roomId)
	var item []*g.TypeObj
	if items == "" {
		item = nil
	} else {
		err := json.Unmarshal([]byte(items), &item)
		if err != nil {
			return slg.HttpResponseErrorNoDataWithDesc("处理奖励数据失败!!"), nil
		}
	}
	var e string
	if receiver == "-1" {
		// 发送所有人
		e = this.SendMailAll(sid, ut.Int32(contentId), title, content, "-1", item)
	} else if receiverArr := strings.Split(receiver, "|"); len(receiverArr) > 1 {
		// 多人
		e = this.SendMailMany(sid, ut.Int32(contentId), title, content, "-1", receiverArr, item)
	} else {
		// 单人
		e = this.SendMailItemOne(sid, ut.Int32(contentId), title, content, "-1", receiver, item)
	}
	if e != "" {
		return slg.HttpResponseErrorNoDataWithDesc("发送失败!"), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("发送成功!"), nil
}

func (this *Mail) httpGetMailsToWeb(sid, sortType, sender, receiver, page, size string) (map[string]interface{}, error) {
	_sid := ut.Int(sid)
	_sortType := ut.Int(sortType)
	_page := ut.Int(page)
	_size := ut.Int(size)

	if _sid < 0 {
		_sid = 0
	}
	mails := this.GetAllMails(_sid, _page, _size, sender, receiver)
	if _sortType == 1 {
		sort.Slice(mails, func(i, j int) bool {
			return ut.Int(mails[i]["state"]) > ut.Int(mails[j]["state"])
		})
	}
	if _sortType == 2 {
		sort.Slice(mails, func(i, j int) bool {
			return ut.Int(mails[i]["state"]) < ut.Int(mails[j]["state"])
		})
	}
	total := len(mails)
	start := _page * _size
	end := int(math.Min(float64((_page+1)*_size), float64(total)))
	if end < start {
		start, _page = 0, 0
	}
	data := make(map[string]interface{})
	data["total"] = total
	data["page"] = _page
	data["size"] = _size
	data["sortType"] = _sortType
	data["docs"] = mails[start:end]
	return slg.HttpResponseSuccessWithDataWithDesc(data, "邮件列表获取成功!"), nil
}

func (this *Mail) httpTagMail(sid, uid string) (map[string]interface{}, error) {
	ret := this.ReadMail("-1", uid)
	if ret != "" {
		return slg.HttpResponseErrorNoDataWithDesc(ret), nil
	}
	return slg.HttpResponseSuccessNoDataWithDesc("修改成功!"), nil
}
