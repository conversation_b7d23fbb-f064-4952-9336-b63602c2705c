[{"id": 100001, "type": 0, "cond": "1017,0,2", "prev_id": -1, "next_id": 100002, "reward": "2,0,90|3,0,90", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1011", "params": 2, "tip": "升级主城可以拥有更多的军队"}, {"id": 100002, "type": 0, "cond": "12,1,3", "prev_id": 100001, "next_id": 100003, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 3, "tip": ""}, {"id": 100003, "type": 0, "cond": "4,2004,2", "prev_id": 100002, "next_id": 100004, "reward": "2,0,90|3,0,90", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2004|2", "tip": ""}, {"id": 100004, "type": 0, "cond": "1022,2,2", "prev_id": 100003, "next_id": 100005, "reward": "2,0,90|3,0,90", "recreate_reward": "", "show_progress": "", "desc": "taskText.1016", "params": "ui.ceri_type_name_2", "tip": ""}, {"id": 100005, "type": 0, "cond": "1031,2,2", "prev_id": 100004, "next_id": 100006, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1029", "params": 2, "tip": ""}, {"id": 100006, "type": 0, "cond": "3,0,1", "prev_id": 100005, "next_id": 100007, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "攻占更远的1级土地", "params": "", "tip": "土地离主城越远，难度越高，奖励也更好"}, {"id": 100007, "type": 0, "cond": "4,2001,3", "prev_id": 100006, "next_id": 100008, "reward": "9,0,2", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|3", "tip": ""}, {"id": 100008, "type": 0, "cond": "1022,1,1", "prev_id": 100007, "next_id": 100009, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1016", "params": "ui.ceri_type_name_1", "tip": ""}, {"id": 100009, "type": 0, "cond": "4,2008,1", "prev_id": 100008, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2008", "tip": ""}, {"id": 100010, "type": 0, "cond": "4,2002,1", "prev_id": -1, "next_id": 100011, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2002", "tip": ""}, {"id": 100011, "type": 0, "cond": "4,2003,1", "prev_id": 100010, "next_id": 100012, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2003", "tip": ""}, {"id": 100012, "type": 0, "cond": "3,0,1", "prev_id": 100011, "next_id": 100013, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "领取1次宝箱奖励", "params": "", "tip": ""}, {"id": 100013, "type": 0, "cond": "1001,1,3", "prev_id": 100012, "next_id": 100014, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "3|1", "tip": ""}, {"id": 100014, "type": 0, "cond": "1005,3,1", "prev_id": 100013, "next_id": 100015, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "占领1块粮食地", "params": "", "tip": ""}, {"id": 100015, "type": 0, "cond": "1005,5,2", "prev_id": 100014, "next_id": 100016, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "攻占2块石头地", "params": "", "tip": ""}, {"id": 100016, "type": 0, "cond": "1005,4,3", "prev_id": 100015, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "攻占3块木头地", "params": "", "tip": ""}, {"id": 100017, "type": 0, "cond": "3,0,1", "prev_id": -1, "next_id": 100018, "reward": "1,0,100|2,0,100|3,0,100|9,0,2", "recreate_reward": "", "show_progress": "", "desc": "给所有士兵穿戴装备", "params": "", "tip": ""}, {"id": 100018, "type": 0, "cond": "1001,2,3", "prev_id": 100017, "next_id": 100019, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "3|2", "tip": ""}, {"id": 100019, "type": 0, "cond": "3,0,1", "prev_id": 100018, "next_id": 100020, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "给兵营里士兵选择默认装备", "params": "", "tip": ""}, {"id": 100020, "type": 0, "cond": "1031,2,4", "prev_id": 100019, "next_id": 100021, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1029", "params": 4, "tip": ""}, {"id": 100021, "type": 0, "cond": "4,2008,3", "prev_id": 100020, "next_id": 100022, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1001", "params": "buildText.name_2008|3", "tip": ""}, {"id": 100022, "type": 0, "cond": "3,0,2", "prev_id": 100021, "next_id": 100023, "reward": "1,0,100|2,0,100|3,0,100|9,0,2", "recreate_reward": "", "show_progress": "", "desc": "研究新的装备", "params": "", "tip": ""}, {"id": 100023, "type": 0, "cond": "3,0,1", "prev_id": 100022, "next_id": 100024, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "给刀盾兵换上新的装备", "params": "", "tip": "防具通常更适合盾兵"}, {"id": 100024, "type": 0, "cond": "1001,2,5", "prev_id": 100023, "next_id": 100025, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1003", "params": "5|2", "tip": ""}, {"id": 100025, "type": 0, "cond": "3,0,9", "prev_id": 100024, "next_id": 100026, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "军队人数达到9", "params": "", "tip": ""}, {"id": 100026, "type": 0, "cond": "1001,3,1", "prev_id": 100025, "next_id": 100027, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|3", "tip": ""}, {"id": 100027, "type": 0, "cond": "4,2015,1", "prev_id": 100026, "next_id": 100028, "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2015", "tip": "奖励自选英雄礼包"}, {"id": 100028, "type": 0, "cond": "3,0,1", "prev_id": 100027, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "使用英雄占领任意一块土地", "params": "", "tip": ""}, {"id": 100029, "type": 0, "cond": "3,0,1", "prev_id": -1, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "重铸一次装备", "params": "", "tip": ""}, {"id": 100999, "type": 0, "cond": "3,0,1", "prev_id": -1, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "招募一个'{0}'", "params": "", "tip": ""}, {"id": 10102201, "type": 1, "cond": "1022,2,1", "prev_id": 0, "next_id": "", "reward": "1,0,30|2,0,30|3,0,30", "recreate_reward": "", "show_progress": "", "desc": "taskText.1016", "params": "ui.ceri_type_name_2", "tip": ""}, {"id": 10101701, "type": 1, "cond": "1017,0,2", "prev_id": 10102201, "next_id": "", "reward": "1,0,50|2,0,50|3,0,50", "recreate_reward": "", "show_progress": "", "desc": "taskText.1011", "params": 2, "tip": ""}, {"id": 10102202, "type": 1, "cond": "1022,1,1", "prev_id": 10101701, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": "", "desc": "taskText.1016", "params": "ui.ceri_type_name_1", "tip": ""}, {"id": 10000402, "type": 1, "cond": "4,2002,1", "prev_id": 10102202, "next_id": "", "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2002", "tip": ""}, {"id": 10000403, "type": 1, "cond": "4,2003,1", "prev_id": 10000402, "next_id": "", "reward": "1,0,125|2,0,125|3,0,125", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2003", "tip": ""}, {"id": 10000404, "type": 1, "cond": "4,2001,2", "prev_id": 10000403, "next_id": "", "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|2", "tip": ""}, {"id": 10000405, "type": 1, "cond": "4,2004,2", "prev_id": 10000404, "next_id": "", "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2004|2", "tip": ""}, {"id": 10103001, "type": 1, "cond": "1031,2,3", "prev_id": 10000405, "next_id": "", "reward": "1,0,250|2,0,250|3,0,200", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1029", "params": 3, "tip": ""}, {"id": 10000407, "type": 1, "cond": "4,2008,1", "prev_id": 10103001, "next_id": "", "reward": "9,0,2", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2008", "tip": ""}, {"id": 10102101, "type": 1, "cond": "1039,0,1", "prev_id": 10000407, "next_id": "", "reward": "1,0,200|2,0,200|3,0,200", "recreate_reward": "", "show_progress": "", "desc": "taskText.1037", "params": 1, "tip": ""}, {"id": 10000409, "type": 1, "cond": "4,2001,5", "prev_id": 10102101, "next_id": "", "reward": "1,0,300|2,0,300|3,0,300", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|5", "tip": ""}, {"id": 10000406, "type": 1, "cond": "4,2011,1", "prev_id": 10000409, "next_id": "", "reward": "7,0,1", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2011", "tip": ""}, {"id": 10102001, "type": 1, "cond": "1027,0,2", "prev_id": 10000406, "next_id": "", "reward": "1,0,400|2,0,400|3,0,400", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1020", "params": 2, "tip": ""}, {"id": 10000410, "type": 1, "cond": "4,2001,10", "prev_id": 10102001, "next_id": "", "reward": "1,0,500|2,0,500|3,0,500", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|10", "tip": ""}, {"id": 10000412, "type": 1, "cond": "4,2015,1", "prev_id": 10000410, "next_id": "", "reward": "1,0,600|2,0,600|3,0,600", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "buildText.name_2015", "tip": ""}, {"id": 10103401, "type": 1, "cond": "1034,0,1", "prev_id": 10000412, "next_id": "", "reward": "7,0,3", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1033", "params": 1, "tip": ""}, {"id": 10000413, "type": 1, "cond": "4,2008,10", "prev_id": 10103401, "next_id": "", "reward": "9,0,5", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2008|10", "tip": ""}, {"id": 10103501, "type": 1, "cond": "1035,0,1", "prev_id": 10000413, "next_id": "", "reward": "14,0,1", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1034", "params": 1, "tip": ""}, {"id": 10000414, "type": 1, "cond": "4,2001,15", "prev_id": 10103501, "next_id": "", "reward": "13,0,1|23,0,15", "recreate_reward": "13,0,1", "show_progress": 1, "desc": "taskText.1001", "params": "buildText.name_2001|15", "tip": ""}, {"id": 20100101, "type": 2, "cond": "12,1,1", "prev_id": 0, "next_id": "", "reward": "1,0,50|2,0,50|3,0,50", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 1, "tip": ""}, {"id": 20001205, "type": 2, "cond": "12,1,3", "prev_id": 20100101, "next_id": "", "reward": "1,0,75|2,0,75|3,0,75", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 3, "tip": ""}, {"id": 20100102, "type": 2, "cond": "1001,2,1", "prev_id": 20001205, "next_id": "", "reward": "1,0,100|2,0,100|3,0,100", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|2", "tip": ""}, {"id": 20001206, "type": 2, "cond": "12,1,10", "prev_id": 20100102, "next_id": "", "reward": "1,0,150|2,0,150|3,0,150", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 10, "tip": ""}, {"id": 20100103, "type": 2, "cond": "1001,3,1", "prev_id": 20001206, "next_id": "", "reward": "1,0,200|2,0,200|3,0,200", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|3", "tip": ""}, {"id": 20001201, "type": 2, "cond": "12,1,25", "prev_id": 20100103, "next_id": "", "reward": "1,0,250|2,0,250|3,0,250", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 25, "tip": ""}, {"id": 20103601, "type": 2, "cond": "1036,0,30", "prev_id": 20001201, "next_id": "", "reward": "1,0,300|2,0,300|3,0,300", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1035", "params": 30, "tip": ""}, {"id": 20001202, "type": 2, "cond": "12,1,50", "prev_id": 20103601, "next_id": "", "reward": "23,0,5", "recreate_reward": "1,0,350|2,0,350|3,0,350", "show_progress": 1, "desc": "taskText.1010", "params": 50, "tip": ""}, {"id": 20100104, "type": 2, "cond": "1001,4,1", "prev_id": 20001202, "next_id": "", "reward": "1,0,400|2,0,400|3,0,400", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|4", "tip": ""}, {"id": 20102501, "type": 2, "cond": "1025,0,1", "prev_id": 20100104, "next_id": "", "reward": "1,0,500|2,0,500|3,0,500", "recreate_reward": "", "show_progress": "", "desc": "taskText.1019", "params": 1, "tip": ""}, {"id": 20001203, "type": 2, "cond": "12,1,75", "prev_id": 20102501, "next_id": "", "reward": "1,0,600|2,0,600|3,0,600", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1010", "params": 75, "tip": ""}, {"id": 20102601, "type": 2, "cond": "1026,2102,1", "prev_id": 20001203, "next_id": "", "reward": "1,0,700|2,0,700|3,0,700", "recreate_reward": "", "show_progress": "", "desc": "taskText.1002", "params": "cityText.name_2102", "tip": ""}, {"id": 20001204, "type": 2, "cond": "12,1,100", "prev_id": 20102601, "next_id": "", "reward": "23,0,10", "recreate_reward": "1,0,800|2,0,800|3,0,800", "show_progress": 1, "desc": "taskText.1010", "params": 100, "tip": ""}, {"id": 20100105, "type": 2, "cond": "1001,5,1", "prev_id": 20001204, "next_id": "", "reward": "1,0,1000|2,0,1000|3,0,1000", "recreate_reward": "", "show_progress": 1, "desc": "taskText.1003", "params": "1|5", "tip": ""}]