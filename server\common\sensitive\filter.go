package sensitive

import (
	"errors"
	"regexp"
	slg "slgsrv/server/common"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
)

// 特殊字符
const REG = "^[A-Za-z][A-Za-z0-9]+$"

// 内部特殊字
var LOCAL_REG_STR = []string{
	"九万亩", "九萬畝",
	"大自然", "nature", "NATURE", "Nature",
	"系统", "系統", "system", "System", "SYSTEM",
	"无", "無", "None", "none", "NONE", "nil", "NIL", "Nil", "null", "Null", "NULL",
	"免战", "免戰", "Truce", "truce", "TRUCE",
}

// 定义不可见字符的Unicode码点
var INVISIBLE_RUNES = []rune{
	// '\u0020', // 空格
	'\u00A0', // 不间断空格
	'\u1680', // Ogham空格标记
	'\u2000', // 各种空格
	'\u2001',
	'\u2002',
	'\u2003',
	'\u2004',
	'\u2005',
	'\u2006',
	'\u2007',
	'\u2008',
	'\u2009',
	'\u200A',
	'\u202F', // 窄不间断空格
	'\u205F', // 数学空格
	'\u3000', // 全角空格
	'\u200B', // 零宽度空格
	'\u200C', // 零宽度非连接符
	'\u200D', // 零宽度连接符
	'\u2060', // 单词连接符
	'\uFEFF', // 零宽度非断空格
	'\u200E', // 左到右标记
	'\u200F', // 右到左标记
	'\u202A', // 左到右嵌入
	'\u202B', // 右到左嵌入
	'\u202C', // 嵌入结束
	'\u202D', // 左到右覆盖
	'\u202E', // 右到左覆盖
	// 其他控制字符
}

// 泰语辅音
var THAI_CONSONANTS = []rune{
	'\u0E3A',
	'\u0E31',
	'\u0E34',
	'\u0E35',
	'\u0E36',
	'\u0E37',
	'\u0E38',
	'\u0E39',
	'\u0E47',
	'\u0E48',
	'\u0E49',
	'\u0E4A',
	'\u0E4B',
	'\u0E4C',
	'\u0E4D',
	'\u0E4E',
}

// 检查字符串是否包含不可见字符
func containsInvisibleChars(s string) bool {
	for _, r := range INVISIBLE_RUNES {
		if strings.Contains(s, string(r)) {
			return true
		}
	}
	return false
}

// 检测是否泰语辅音
func isThaiConsonants(s string) bool {
	s = strings.ReplaceAll(s, " ", "")
	for _, r := range THAI_CONSONANTS {
		if s == string(r) {
			return true
		}
	}
	return false
}

// true表示有 敏感字
func Validate(text string) bool {
	ok, _ := textModeration(text)
	return ok
}

// 检测名字 1.有敏感字 2.有特殊字符 0.可用
func CheckName(text string) int {
	if text == "" || strings.ReplaceAll(text, " ", "") == "" {
		return 2
	} else if containsInvisibleChars(text) || isThaiConsonants(text) {
		return 2
	} else if strings.Contains(text, "\n") || strings.Contains(text, "\t") {
		return 2
	} else if array.Some(LOCAL_REG_STR, func(m string) bool { return m == text }) {
		return 1
	} else if ok, _ := textModeration(text); ok {
		return 1
	} else if matched, _ := regexp.MatchString(`[\p{P}\p{S}]`, text); matched {
		return 2
	}
	return 0
}

// 敏感字检测
func textModeration(text string) (bool, string) {
	if slg.IsChinaArea() {
		return GlobalGetTencentMod().TextModeration(text)
	}
	return false, text
}

// 把敏感字替换为*号
func Replace(text string) string {
	if text == "" {
		return text
	}
	_, str := textModeration(text)
	return str
}

// 正则表达式替换字符
func ReplaceStringByRegex(str, rule, replace string) (string, error) {
	reg, err := regexp.Compile(rule)
	if reg == nil || err != nil {
		return "", errors.New("正则表达式错误:" + err.Error())
	}
	return reg.ReplaceAllString(str, replace), nil
}

// CheckAccount 检测账号是否有效
// 6到15位由字母数字组成 必现以字母开头
// 1.必须由字母开头 2.必须由字母数字组成的6-15位字符 0.成功
func CheckAccount(text string) int {
	text = ut.Trim(text)
	length := len(text)
	// 长度是否满足
	if length > 15 || length < 6 {
		return 2
	}
	// 必须由字母开头,不区分大小写
	if !ut.InitialIsChar(text, false) {
		return 1
	}
	// 只能包含字母和数字
	if matched, _ := regexp.Match(REG, []byte(text)); !matched {
		return 2
	}
	return 0
}
