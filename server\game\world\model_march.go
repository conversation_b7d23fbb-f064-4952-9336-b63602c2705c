package world

import (
	"math"
	slg "slgsrv/server/common"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	ut "slgsrv/utils"
	"time"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

/**
行军相关
*/

type MarchList struct {
	deadlock.RWMutex
	Map        map[string]*March
	ArmyUIDMap map[string]*March
	RbTree     *ut.RbTreeContainer[*March]
}

func (this *Model) ToMarchs() []map[string]interface{} {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	arr := []map[string]interface{}{}
	for _, m := range this.Marchs.Map {
		arr = append(arr, m.Strip())
	}
	return arr
}

func (this *Model) ToMarchsPb(userId string) []*pb.MarchInfo {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	arr := []*pb.MarchInfo{}
	it := this.Marchs.RbTree.Iterator()
	for it.Next() {
		marchList := it.Value().([]*March)
		if marchList != nil {
			for _, v := range marchList {
				if this.IsShowMarch(v, userId) {
					arr = append(arr, v.ToPb())
				}
			}
		}
	}
	return arr
}

// 是否显示行军
func (this *Model) IsShowMarch(march *March, userId string) bool {
	if this.CheckIsOneAlliance(march.Owner, userId) {
		// 军队是同联盟的显示
		return true
	}
	tArea := this.GetArea(march.TargetIndex)
	if tArea != nil && this.CheckIsOneAlliance(tArea.Owner, userId) {
		// 目标位置是同联盟的显示
		return true
	}
	return false
}

func (this *Model) GetMarchsClone() []*March {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	arr := []*March{}
	it := this.Marchs.RbTree.Iterator()
	for it.Next() {
		marchList := it.Value().([]*March)
		if marchList != nil {
			for _, v := range marchList {
				arr = append(arr, v)
			}
		}
	}
	return arr
}

// 获取行军信息
func (this *Model) GetMarchByUID(uid string) *March {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	return this.Marchs.Map[uid]
}

// 获取行军信息
func (this *Model) GetMarchByArmyUID(uid string) *March {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	return this.Marchs.ArmyUIDMap[uid]
}

// 军队是否行军中
func (this *Model) CheckArmyMarchingByUID(uid string) bool {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	return this.Marchs.ArmyUIDMap[uid] != nil
}

// 添加行军军队
func (this *Model) AddMarchArmy(index int32, army *AreaArmy, target int32, timeState float64, auto, forceRevoke, cellTonden bool) {
	if index == target {
		return
	}
	// 行军速度最慢的士兵
	marchSpeed, roleId := army.GetMarchSpeedAndRoleID()
	// 获取时间
	time, cd := 0.0, 0.0
	if timeState > 0 {
		time = timeState
	} else {
		dis := this.GetToMapCellDis(index, target)
		time = float64(dis) * (float64(ut.TIME_HOUR) / float64(marchSpeed))
		cd = this.GetPlayerMarchCd(army.Owner)
		time *= 1.0 - cd
		if timeState < 0 { //是否加速
			time = math.Max(time/math.Abs(timeState), 1000)
		}
		// GM加速
		time = math.Max(time/float64(slg.GetMarchUpSpeed()), 1000)
	}
	march := NewMarch(ut.ID()).Init(army, index, target, int32(time), roleId)
	march.AutoRevoke = auto
	march.ForceRevoke = forceRevoke
	march.CellTonden = cellTonden
	this.Marchs.Lock()
	this.Marchs.Map[march.Uid] = march
	this.Marchs.ArmyUIDMap[march.ArmyUid] = march
	this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
	this.Marchs.Unlock()
	// 通知行军
	this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
	// 通知删除战场中的军队 因为进入行军中就需要从战场中删除掉
	this.NotifyAreaUpdateInfo(index, constant.NQ_REMOVE_ARMY, &pb.GAME_ONUPDATEAREAINFO_NOTIFY{Data_12: army.Uid})
	// 通知军队分布情况
	this.NotifyPlayerArmyDistInfo(army.Owner)
	//
	printParams := append([]interface{}{marchSpeed, timeState, cd}, march.ToPrintParams()...)
	log.Info("AddMarchArmy marchSpeed: %v, timeState: %v, cd: %v, owner: %v, name: %v, armyIndex: %v, startIndex: %v, targetIndex: %v, autoRevoke: %v, needTime: %v", printParams...)
}

// 撤回行军
func (this *Model) CancelMarchArmy(march *March, auto bool) {
	now := time.Now().UnixMilli()
	march.AutoRevoke = auto
	// 将目标变成起始位置
	march.StartIndex = march.TargetIndex
	march.TargetIndex = march.ArmyIndex
	// 如果是自动遣返
	if auto {
		// 加下速
		march.NeedTime = ut.MaxInt32(march.NeedTime/(constant.MARC_AUTO_REVOKE_SPEED*int32(slg.GetMarchUpSpeed())), 1000)
		march.StartTime = now
		this.Marchs.Lock()
		this.Marchs.Map[march.Uid] = march
		this.Marchs.ArmyUIDMap[march.ArmyUid] = march
		this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
		this.Marchs.Unlock()
	} else {
		// 已经走的时间
		elapsed := now - march.StartTime
		lastTime := march.StartTime + int64(march.NeedTime)
		this.Marchs.Lock()
		// 开始时间等于 当前时间减去剩余时间
		march.StartTime = now - ut.MaxInt64(int64(march.NeedTime)-elapsed, 0)
		this.Marchs.RbTree.RemoveElement(int(lastTime), march)
		this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
		this.Marchs.Unlock()
	}
	march.CellTonden = false // 撤回的行军不屯田
	// 通知行军
	this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
	//
	log.Info("CancelMarchArmy owner: %v, name: %v, armyIndex: %v, startIndex: %v, targetIndex: %v, autoRevoke: %v, needTime: %v", march.ToPrintParams()...)
}

// 强行遣返 行军中
func (this *Model) ForceCancelMarchArmy(march *March) {
	area, army := this.GetAreaAndArmy(march.ArmyIndex, march.ArmyUid)
	if area == nil || army == nil {
		return
	}
	now := time.Now().UnixMilli()
	march.AutoRevoke = true
	if sArea := this.GetArea(march.StartIndex); march.ForceRevoke || (sArea != nil && !this.CheckIsOneAlliance(sArea.Owner, march.Owner)) {
		var ta *Area
		if march.ForceRevoke {
			// 强制撤离只找自己的地
			ta = this.GetPlayerNotFullArmyArea(march.Owner, march.ArmyIndex)
		} else {
			// 如果开始位置不是友方 就找一个自己或盟友的地返回
			ta = this.GetPlayerAndAlliNotFullArmyArea(march.Owner, march.ArmyIndex)
		}
		if ta != nil {
			march.StartIndex = march.ArmyIndex
			march.TargetIndex = ta.index
			// 行军速度
			marchSpeed, _ := army.GetMarchSpeedAndRoleID()
			// 获取时间
			dis := this.GetToMapCellDis(march.StartIndex, march.TargetIndex)
			needTime := float64(dis) * (float64(ut.TIME_HOUR) / float64(marchSpeed))
			needTime *= 1.0 - this.GetPlayerMarchCd(march.Owner)
			lastTime := march.StartTime + int64(march.NeedTime)
			march.NeedTime = int32(math.Max(needTime/(3*float64(slg.GetMarchUpSpeed())), 1000))
			march.StartTime = now

			this.Marchs.Lock()
			this.Marchs.RbTree.RemoveElement(int(lastTime), march)
			this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
			this.Marchs.Unlock()
		} else {
			area.RemoveArmy(march.ArmyUid) //直接删除士兵
			this.NotifyPlayerArmyDistInfo(march.Owner)
			// 删除行军
			this.RemoveMarchByArmyUid(march.ArmyUid)
			// 记录解散
			this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_REMOVE_ARMY, march.Owner, march.ArmyUid, march.armyName, march.ArmyIndex, 0)
			return
		}
	} else {
		lastTime := march.StartTime + int64(march.NeedTime)
		// 将目标变成起始位置
		march.StartIndex, march.TargetIndex = march.TargetIndex, march.StartIndex
		// 已经走的时间
		elapsed := now - march.StartTime
		// 开始时间等于 当前时间减去剩余时间
		march.StartTime = now - ut.MaxInt64(int64(march.NeedTime)-elapsed, 0)

		this.Marchs.Lock()
		this.Marchs.RbTree.RemoveElement(int(lastTime), march)
		this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
		this.Marchs.Unlock()
	}
	// 遣返记录
	this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_MOVE_REVOKE, march.Owner, march.ArmyUid, march.armyName, march.TargetIndex, march.StartIndex)
	// 通知行军
	this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
}

// 强制撤回 行军中
func (this *Model) ForceRevokeMarchArmy(march *March, mainIndex int32) {
	area, army := this.GetAreaAndArmy(march.ArmyIndex, march.ArmyUid)
	if area == nil || army == nil {
		return
	}
	now := time.Now().UnixMilli()
	march.ForceRevoke = true
	// 如果起始位置是强制撤回的主城位置 则添加标记并加速
	if march.StartIndex == mainIndex {
		// 同一方向 不加速
	} else if march.TargetIndex == mainIndex {
		lastTime := march.StartTime + int64(march.NeedTime)
		// 目标位置是该主城 则强制撤回 将目标变成起始位置
		march.StartIndex, march.TargetIndex = march.TargetIndex, march.StartIndex
		// 已经走的时间
		elapsed := now - march.StartTime
		// 开始时间等于 当前时间减去加速后的剩余时间
		march.StartTime = now - ut.MaxInt64((int64(march.NeedTime)-elapsed)/constant.MARC_FORCE_REVOKE_SPEED, 0)
		// 需要的时间加速
		march.NeedTime = ut.MaxInt32(march.NeedTime/constant.MARC_FORCE_REVOKE_SPEED, 1000)

		this.Marchs.Lock()
		this.Marchs.RbTree.RemoveElement(int(lastTime), march)
		this.Marchs.RbTree.AddElement(int(march.StartTime+int64(march.NeedTime)), march)
		this.Marchs.Unlock()
	} else {
		// 行军的起始位置和目标位置都不是该主城 不做处理
		log.Warning("ForceRevokeMarchArmy mainIndex: %v, start: %v, target: %v", mainIndex, march.StartIndex, march.TargetIndex)
		return
	}
	// 遣返记录
	this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_MOVE_FORECE_REVOKE, march.Owner, march.ArmyUid, march.armyName, march.TargetIndex, march.StartIndex)
	// 通知行军
	this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
}

// 删除行军
func (this *Model) RemoveMarchByArmyUid(auid string) {
	this.Marchs.Lock()
	defer this.Marchs.Unlock()
	march := this.Marchs.ArmyUIDMap[auid]
	if march != nil {
		delete(this.Marchs.Map, march.Uid)
		delete(this.Marchs.ArmyUIDMap, march.ArmyUid)
		this.Marchs.RbTree.RemoveElement(int(march.StartTime+int64(march.NeedTime)), march)
		this.PutNotifyQueue(constant.NQ_REMOVE_MARCH, &pb.OnUpdateWorldInfoNotify{Data_14: march.ToPb()})
		log.Info("RemoveMarchByArmyUid owner: %v, name: %v, armyIndex: %v, startIndex: %v, targetIndex: %v, autoRevoke: %v, needTime: %v", march.ToPrintParams()...)
	}
}

// 检测更新行军信息
func (this *Model) CheckUpdateMarch() {
	now := time.Now().UnixMilli()
	this.Marchs.RLock()
	firstTreeNode := this.Marchs.RbTree.FindMinElements()
	if firstTreeNode == nil { //没有军队
		this.Marchs.RUnlock()
		return
	} else {
		march := firstTreeNode.Value.([]*March)[0]
		if now-march.StartTime < int64(march.NeedTime) { //时间最短的都没到
			this.Marchs.RUnlock()
			return
		}
		this.Marchs.RUnlock()
	}
	list := []*March{}
	this.Marchs.Lock()
	for m := this.Marchs.RbTree.FindMinElements(); m != nil; m = this.Marchs.RbTree.FindMinElements() {
		marchsList := m.Value.([]*March)
		if now-marchsList[0].StartTime < int64(marchsList[0].NeedTime) { //当前时间最短的都没到
			break
		}
		for _, m := range marchsList {
			delete(this.Marchs.Map, m.Uid)
			delete(this.Marchs.ArmyUIDMap, m.ArmyUid)
			this.PutNotifyQueue(constant.NQ_REMOVE_MARCH, &pb.OnUpdateWorldInfoNotify{Data_14: m.ToPb()})
			list = append(list, m)
		}
		this.Marchs.RbTree.Remove(m.Key)
	}
	this.Marchs.Unlock()
	for _, m := range list {
		area, tArea := this.GetArea(m.ArmyIndex), this.GetArea(m.TargetIndex)
		if area == nil || tArea == nil {
			continue
		}
		army := area.GetArmyByUid(m.ArmyUid)
		if army == nil {
			continue
		}
		isRevoke := area.index == tArea.index
		isCanAddAreaArmy := this.CheckCanAddAreaArmy(tArea, army.Owner, isRevoke)
		// 如果是撤回 或者可以进入
		if isRevoke || isCanAddAreaArmy {
			if !isRevoke {
				area.RemoveArmy(army.Uid) // 只要不是撤回 就从当前区域删除
			}
			// 如果是撤回 但是不能进入当前区域 就找一个可以进入的继续行军
			if isRevoke && !isCanAddAreaArmy {
				this.findAreaGoonMarch(area, m, army, isRevoke)
			} else {
				// 添加到目标区域
				this.AddAreaArmy(tArea, army, this.GetAddArmyDir(m.StartIndex, m.TargetIndex))
				// 通知军队分布情况
				this.NotifyPlayerArmyDistInfo(army.Owner)
				// 添加记录
				this.AddArmyMarchRecord(m.AutoRevoke, isRevoke, army, m.StartIndex, m.TargetIndex)
				// 离线通知
				this.OfflineNotify(army.Owner, constant.OFFLINE_MSG_TYPE_MARCH, army.Name)
				if m.CellTonden && !m.AutoRevoke && !m.ForceRevoke {
					// 屯田的行军到达
					this.AddCellTonden(m.TargetIndex, m.Owner, m.ArmyUid)
				}
			}
		} else if m.AutoRevoke || !this.CheckIsOneAlliance(tArea.Owner, army.Owner) { //本身就是遣返 是撤回后的遣返  或者是遣返的地方不是自己盟友的地
			this.findAreaGoonMarch(area, m, army, isRevoke)
		} else { //否则 强行遣返
			this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_MOVE_REVOKE, army.Owner, army.Uid, army.Name, m.StartIndex, m.TargetIndex)
			army.AutoBackIndex = -1
			this.CancelMarchArmy(m, true)
		}
	}
}

// 找一个可以进入的继续行军
func (this *Model) findAreaGoonMarch(area *Area, march *March, army *AreaArmy, isRevoke bool) {
	this.AddArmyMarchRecord(march.AutoRevoke, isRevoke, army, march.StartIndex, march.TargetIndex)
	if ta := this.GetPlayerAndAlliNotFullArmyArea(army.Owner, march.ArmyIndex); ta != nil {
		this.AddMarchArmy(march.ArmyIndex, army, ta.index, 0, true, false, false)
	} else {
		area.RemoveArmy(army.Uid) //直接删除士兵
		this.NotifyPlayerArmyDistInfo(army.Owner)
		// 记录解散
		this.room.GetRecord().AddArmyMarchRecord(constant.MARCH_TYPE_REMOVE_ARMY, army.Owner, army.Uid, army.Name, army.AIndex, 0)
	}
}

// 添加行军记录
func (this *Model) AddArmyMarchRecord(autoRevoke, isRevoke bool, army *AreaArmy, armyIndex, targetIndex int32) {
	var tp int32 = constant.MARCH_TYPE_MOVE //移动
	if autoRevoke {
		tp = constant.MARCH_TYPE_AUTO_REVOKE //遣返
	} else if isRevoke {
		tp = constant.MARCH_TYPE_CANCEL                 //撤回
		targetIndex, armyIndex = armyIndex, targetIndex //如果是撤回交互下位置
	}
	this.room.GetRecord().AddArmyMarchRecord(tp, army.Owner, army.Uid, army.Name, armyIndex, targetIndex)
}

// 是否可以添加军队到区域
func (this *Model) CheckCanAddAreaArmy(area *Area, owner string, isRevoke bool) bool {
	if !isRevoke && this.IsAreaFullArmy(area, owner) {
		return false //如果当前区域没有这个军队且满了 就不能进
	} else if this.CheckIsOneAlliance(area.Owner, owner) {
		return true //如果是联盟或者自己的土地就可以进入
	}
	// 如果在攻击范围内且不是免战的 就可以进入
	return this.IsCanOccupyArea(area.index, area, owner) && this.CheckCanOccupyCell(area.index, owner)
}

// 删除玩家所有行军
func (this *Model) RemovePlayerAllMarch(owner string) {
	needRemoveList := []*March{}
	this.Marchs.Lock()
	for _, march := range this.Marchs.Map {
		if march.Owner == owner {
			needRemoveList = append(needRemoveList, march)
			delete(this.Marchs.ArmyUIDMap, march.ArmyUid)
			this.Marchs.RbTree.RemoveElement(int(march.StartTime+int64(march.NeedTime)), march)
			this.PutNotifyQueue(constant.NQ_REMOVE_MARCH, &pb.OnUpdateWorldInfoNotify{Data_14: march.ToPb()})
		}
	}
	// 删除Map
	for _, march := range needRemoveList {
		delete(this.Marchs.Map, march.Uid)
	}
	this.Marchs.Unlock()
	// 这里还要删除区域里面的军队
	for _, march := range needRemoveList {
		if area := this.GetArea(march.ArmyIndex); area != nil {
			area.RemoveArmy(march.ArmyUid)
		}
	}
}

// 获取前往某个地方的行军数量
func (this *Model) GetGoToIndexMarchCount(index int32) int32 {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	var count int32
	for _, m := range this.Marchs.Map {
		if m.TargetIndex == index {
			count += 1
		}
	}
	return count
}

// 获取前往某个地方的行军数量 如果是撤回 不会算前往
func (this *Model) GetGoToAreaMarchCount(area *Area, uid string) int32 {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	isFriend := this.CheckIsOneAlliance(area.Owner, uid)
	var index, count int32 = area.index, 0
	for _, m := range this.Marchs.Map {
		if m.TargetIndex != index || m.ArmyIndex == index {
			continue //撤回的不算
		} else if this.CheckIsOneAlliance(area.Owner, m.Owner) == isFriend {
			count += 1
		}
	}
	return count
}

// 获取前往某个地方的同盟行军数量
func (this *Model) GetGoToAreaMarchCountAndAll(area *Area, uid string) (int32, int32, bool) {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	isFriend := this.CheckIsOneAlliance(area.Owner, uid)
	var index, count, countAll int32 = area.index, 0, 0
	for _, m := range this.Marchs.Map {
		if m.TargetIndex != index {
			continue
		} else if this.CheckIsOneAlliance(area.Owner, m.Owner) == isFriend {
			countAll++
			if m.ArmyIndex != index {
				count++
			}
		}
	}
	return count, countAll, isFriend
}

// 获取某个玩家的所有行军
func (this *Model) GetMarchsByPlayer(owner string) []*March {
	this.Marchs.RLock()
	defer this.Marchs.RUnlock()
	arr := []*March{}
	for _, march := range this.Marchs.Map {
		if march.Owner == owner {
			arr = append(arr, march)
		}
	}
	return arr
}

// 检测所有行军的目的地是否不可攻占和进入
func (this *Model) CheckAllMarchTargetCanAddArmy(isBattleEnd bool, index int32) {
	list := this.GetMarchsClone()
	for _, march := range list {
		if isBattleEnd && march.StartIndex == index {
			// 如果是战斗结束时 检测并通知从index出发的行军
			if sArea := this.GetArea(march.StartIndex); sArea != nil && sArea.Owner != "" {
				this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
			}
		}
		area := this.GetArea(march.TargetIndex)
		if area == nil {
			continue
		} else if this.CheckIsOneAlliance(area.Owner, march.Owner) {
			continue //如果是联盟或者自己的土地就可以进入
		} else if this.IsCanOccupyArea(march.StartIndex, area, march.Owner) {
			if isBattleEnd && march.TargetIndex == index && area.Owner != "" {
				// 进入的不是野地且为战斗结束时检测 则需要通知行军
				this.PutNotifyQueue(constant.NQ_ADD_MARCH, &pb.OnUpdateWorldInfoNotify{Data_13: march.ToPb()})
			}
			continue //如果在攻击范围内且不是免战的 就可以进入
		}
		log.Info("强制遣返行军 owner=%v, armyUid=%v, armyIndex=%v, sindex=%v, tindex=%v", march.Owner, march.ArmyUid, march.ArmyIndex, march.StartIndex, march.TargetIndex)
		this.ForceCancelMarchArmy(march)
	}
}
