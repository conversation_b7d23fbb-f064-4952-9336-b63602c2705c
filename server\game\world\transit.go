package world

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"time"
)

// 运送信息
type Transit struct {
	Uid   string `json:"uid"`
	Owner string `json:"owner"` //谁的商队

	StartTime      int64 `json:"start_time"`     //开始时间
	MerchantCount  int32 `json:"merchant_count"` //商人数量
	GoodsType      int32 `json:"goods_type"`     //携带出去
	GoodsCount     int32 `json:"goods_count"`
	BackGoodsType  int32 `json:"back_goods_type"` //需要带回来的
	BackGoodsCount int32 `json:"back_goods_count"`
	StartIndex     int32 `json:"start_index"`  //起点位置
	TargetIndex    int32 `json:"target_index"` //目标位置
	NeedTime       int32 `json:"need_time"`    //行军时间
	Type           int8  `json:"type"`         //运送类型 1.购买 2.赠送
}

func NewTransit(uid string) *Transit {
	return &Transit{Uid: uid}
}

func (this *Transit) Init(owner string, goodsType, goodsCount, backGoodsType, backGoodsCount, sindex, target, needTime int32) *Transit {
	this.Owner = owner
	this.GoodsType = goodsType
	this.GoodsCount = goodsCount
	this.BackGoodsType = backGoodsType
	this.BackGoodsCount = backGoodsCount
	this.StartIndex = sindex
	this.TargetIndex = target
	this.NeedTime = needTime
	this.StartTime = time.Now().UnixMilli()
	return this
}

func (this *Transit) Strip() map[string]interface{} {
	return map[string]interface{}{
		"uid":            this.Uid,
		"owner":          this.Owner,
		"merchantCount":  this.MerchantCount,
		"goodsType":      this.GoodsType,
		"goodsCount":     this.GoodsCount,
		"backGoodsType":  this.BackGoodsType,
		"backGoodsCount": this.BackGoodsCount,
		"startIndex":     this.StartIndex,
		"targetIndex":    this.TargetIndex,
		"needTime":       this.NeedTime,
		"surplusTime":    ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0),
	}
}

func (this *Transit) ToPb() *pb.TransitInfo {
	return &pb.TransitInfo{
		Uid:            this.Uid,
		Owner:          this.Owner,
		MerchantCount:  this.MerchantCount,
		GoodsType:      this.GoodsType,
		GoodsCount:     this.GoodsCount,
		BackGoodsType:  this.BackGoodsType,
		BackGoodsCount: this.BackGoodsCount,
		StartIndex:     this.StartIndex,
		TargetIndex:    this.TargetIndex,
		NeedTime:       this.NeedTime,
		SurplusTime:    int32(ut.MaxInt64(this.StartTime+int64(this.NeedTime)-time.Now().UnixMilli(), 0)),
	}
}
