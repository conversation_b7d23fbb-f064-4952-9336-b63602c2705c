package game

import (
	"slgsrv/server/common/ecode"
	"slgsrv/server/common/pb"
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/ctype"

	"github.com/huyangv/vmqant/gate"
)

func (this *Game) InitHDShop() {
	this.GetServer().RegisterGO("HD_BuyAddOutput", this.buyAddOutput) //购买添加产量
}

// 购买添加产量
func (this *Game) buyAddOutput(session gate.Session, msg *pb.GAME_HD_BUYADDOUTPUT_C2S) (ret []byte, err string) {
	e, _, plr, wld := checkError(session)
	if e != "" {
		return nil, e
	}
	t := msg.GetType()
	if t != ctype.CEREAL && t != ctype.TIMBER && t != ctype.STONE {
		return nil, ecode.UNKNOWN.String()
	}
	gold := this.ChangePlayerGold(plr, -constant.ADD_OUTPUT_GOLD, constant.GOLD_CHANGE_BUY_ADD_OUTPUT_GOLD)
	if gold == -1 {
		return nil, ecode.GOLD_NOT_ENOUGH.String() //金币不足
	}
	// 直接添加时间
	plr.AddAddOutputTime(t)
	// 上报
	wld.TaTrack(plr.Uid, plr.ReCreateMainCityCount, "ta_shopByAddOp", map[string]interface{}{
		"res_type":  t,
		"gold_cost": constant.ADD_OUTPUT_GOLD,
	})
	return pb.ProtoMarshal(&pb.GAME_HD_BUYADDOUTPUT_S2C{
		AddOutputSurplusTime: plr.ToAddOutputSurplusTimePb(),
		Output:               plr.ToOutputInfoPb(),
		Gold:                 gold,
	})
}
