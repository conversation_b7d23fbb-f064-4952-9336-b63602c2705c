package player

import (
	"slgsrv/server/common/pb"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
)

// 个人标记信息
type MapMarkInfo struct {
	Name  string   `bson:"name"`
	Point *ut.Vec2 `bson:"point"`
}

func (this *Model) ToMapMarksPb() []*pb.MapMarkInfo {
	return array.Map(this.MapMarks, func(m *MapMarkInfo, _ int) *pb.MapMarkInfo {
		return &pb.MapMarkInfo{Name: m.Name, Point: pb.NewVec2(m.Point)}
	})
}

func (this *Model) GetMapMarkCount() int {
	return len(this.MapMarks)
}

func (this *Model) FindMapMark(point *ut.Vec2) *MapMarkInfo {
	return array.Find(this.MapMarks, func(m *MapMarkInfo) bool { return m.Point.Equals(point) })
}

func (this *Model) AddMapMark(name string, point *ut.Vec2) {
	this.MapMarks = append(this.MapMarks, &MapMarkInfo{Name: name, Point: point})
}

func (this *Model) RemoveMapMark(point *ut.Vec2) {
	this.MapMarks = array.RemoveItem(this.MapMarks, func(m *MapMarkInfo) bool { return m.Point.Equals(point) })
}
