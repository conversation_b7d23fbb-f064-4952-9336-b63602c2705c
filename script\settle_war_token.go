package main

// import (
// 	"context"
// 	slg "slgsrv/server/common"
// 	"slgsrv/server/game/common/enums/ctype"
// 	"slgsrv/server/game/common/g"
// 	"slgsrv/server/game/player"
// 	r "slgsrv/server/game/room"
// 	"slgsrv/server/lobby"
// 	"slgsrv/server/mail"
// 	ut "slgsrv/utils"
// 	"time"

// 	"github.com/huyangv/vmqant/log"
// 	"go.mongodb.org/mongo-driver/bson"
// 	"go.mongodb.org/mongo-driver/mongo"
// 	"go.mongodb.org/mongo-driver/mongo/options"
// )

// const (
// 	lobbyDbUri  = "mongodb://localhost:27017" //大厅服数据库地址
// 	lobbyDbName = "slgsrv"                    //大厅服数据库名

// 	gameDbUri  = "mongodb://localhost:27017" //游戏服数据库地址
// 	gameDbName = "slgsrv"                    //游戏服数据库名

// 	sid = 2000152
// )

// type ScoreInfo struct {
// 	Uid        string
// 	Score      int
// 	TotalScore int
// }

// func main() {
// 	log.Info("settle war token start!")
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()
// 	// 连接大厅服数据库
// 	clientLobby, err := mongo.NewClient(options.Client().ApplyURI(lobbyDbUri))
// 	if err != nil {
// 		log.Error("parse lobbyDbUri err: %v", err)
// 	}
// 	err = clientLobby.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect lobbyDb err: %v", err)
// 	}
// 	defer clientLobby.Disconnect(ctx)
// 	lobbyDb := clientLobby.Database(lobbyDbName)

// 	// 连接游戏服数据库
// 	clientGame, err := mongo.NewClient(options.Client().ApplyURI(gameDbUri))
// 	if err != nil {
// 		log.Error("parse gameDbUri err: %v", err)
// 	}
// 	defer cancel()
// 	err = clientGame.Connect(ctx)
// 	if err != nil {
// 		log.Error("connect gameDb err: %v", err)
// 	}
// 	defer clientGame.Disconnect(ctx)
// 	gameDb := clientGame.Database(gameDbName)

// 	// 获取游戏服数据
// 	var roomInfo r.RoomTableData
// 	e := gameDb.Collection("room").FindOne(context.TODO(), bson.M{"id": sid}).Decode(&roomInfo)
// 	if e != nil {
// 		log.Error("get roomInfo err: %v", err)
// 		return
// 	}
// 	if roomInfo.GameOverInfo == nil {
// 		log.Error("roomInfo not game over")
// 		return
// 	}

// 	// 遍历游戏服玩家表
// 	cur, e := gameDb.Collection("player_"+ut.String(sid)).Find(context.TODO(), bson.D{})
// 	defer func() {
// 		_ = cur.Close(context.TODO())
// 	}()
// 	if e == nil {
// 		var list []player.TableData
// 		cur.All(context.TODO(), &list)
// 		playerNum := 0
// 		runTime := float64(roomInfo.GameOverInfo.EndTime - roomInfo.CreateTime)
// 		day := ut.Ceil(runTime / float64(ut.TIME_DAY))
// 		for _, v := range list {
// 			if v.IsGiveupGame {
// 				continue
// 			}
// 			var gameRecord lobby.GameRecordData
// 			// 从对局记录中获取兵符数量
// 			e = lobbyDb.Collection(slg.DB_COLLECTION_NAME_GAME_RECORD).FindOne(context.TODO(), bson.M{
// 				"sid": sid,
// 				"uid": v.Uid,
// 			}).Decode(&gameRecord)
// 			if e != nil {
// 				log.Info("get game record err: %v, uid: %v", e, v.Uid)
// 				continue
// 			}
// 			addWarToken := gameRecord.AddWarToken
// 			// 历经{0}天鏖战，{1}终于落下了帷幕，感谢你的积极参与！本次对局总人数为{2}人，你获得了第{3}名的战绩，以下是你所赢得的奖励，请查收。
// 			parames := ut.Itoa(day) + "|" + slg.GetServerNoNameKey(sid) + "|" + ut.Itoa(playerNum) + "|" + ut.String(gameRecord.Rank)
// 			mail := &mail.MailBaseInfo{
// 				UID:        ut.ID(),
// 				Receiver:   v.Uid,
// 				Title:      "",
// 				ContentId:  slg.MAIL_GAME_SETTLEMENT_ID,
// 				Content:    parames,
// 				Sender:     "-1",
// 				SID:        0,
// 				Items:      []*g.TypeObj{g.NewTypeObj(ctype.WAR_TOKEN, 0, addWarToken)},
// 				CreateTime: ut.Now(),
// 			}
// 			lobbyDb.Collection(slg.DB_COLLECTION_NAME_MAIL).InsertOne(context.TODO(), mail)
// 		}
// 	}
// 	log.Info("settle war token end!")
// }
