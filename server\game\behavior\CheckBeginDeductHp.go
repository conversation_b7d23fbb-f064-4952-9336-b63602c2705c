package behavior

import (
	"slgsrv/server/game/common/constant"
	"slgsrv/server/game/common/enums/bufftype"
	"slgsrv/server/game/common/enums/eeffect"
	"slgsrv/server/game/common/g"
	ut "slgsrv/utils"
	"slgsrv/utils/array"
	"strings"
)

// 检测回合开始扣血
type CheckBeginDeductHp struct {
	BaseAction

	needTime  int32 //回血需要的时间
	isHasFire bool
}

func (this *CheckBeginDeductHp) OnInit(conf map[string]interface{}) {
	this.needTime = ut.Int32(conf["parameters"])
}

func (this *CheckBeginDeductHp) OnOpen() {
	point := this.target.GetPoint()
	isHasBuff := this.target.IsHasBuffs(
		bufftype.POISONING_MAX_HP,
		bufftype.POISONING_CUR_HP,
		bufftype.BLEED, bufftype.DELAY_DEDUCT_HP,
		bufftype.IGNITION,
		bufftype.INFECTION_PLAGUE,
	)
	this.isHasFire = this.GetCtrl().CheckHasFighterById(point.X, point.Y, constant.FIRE_PAWN_ID)
	this.SetBlackboardData("isBeginDeductHp", !isHasBuff && !this.isHasFire)
	this.SetTreeBlackboardData("isDeductHpAction", false)
}

func (this *CheckBeginDeductHp) OnLeave(state Status) {
	this.SetBlackboardData("isBeginDeductHp", state == SUCCESS)
}

func (this *CheckBeginDeductHp) OnTick(dt int32) Status {
	if ut.Bool(this.GetTreeBlackboardData("isBatter")) || ut.Bool(this.GetBlackboardData("isBeginDeductHp")) {
		return SUCCESS
	}
	currTime := ut.Int32(this.GetBlackboardData("currTime"))
	if currTime >= this.needTime {
		return SUCCESS
	} else if currTime == 0 {
		ctrl := this.GetCtrl()
		providerMap, lastDamage := map[string]int32{}, int32(0)
		trueDamage, buffs := int32(0), this.target.GetBuffs()
		removes := []int32{}
		for _, m := range buffs {
			if m.Type == bufftype.POISONING_MAX_HP { //目标最大生命值的真实伤害
				trueDamage += ut.MaxInt32(1, ut.RoundInt32(float64(this.target.GetMaxHp())*m.Value))
			} else if m.Type == bufftype.POISONING_CUR_HP { //造成目标当前生命值的伤害
				trueDamage += ut.MaxInt32(1, ut.RoundInt32(float64(this.target.GetCurHp())*m.Value))
			} else if m.Type == bufftype.BLEED { //流血
				trueDamage += m.GetValueInt()
			} else if m.Type == bufftype.IGNITION { //点燃
				trueDamage += m.GetValueInt()
			} else if m.Type == bufftype.INFECTION_PLAGUE { //瘟疫
				trueDamage += ut.RoundInt32(float64(this.target.GetMaxHp()) * m.Value * 0.01)
			} else if m.Type == bufftype.DELAY_DEDUCT_HP { //金丝软甲 延迟扣血
				if effect := this.target.GetEquipEffectByType(eeffect.FIXED_DAMAGE); effect != nil {
					if m.Value <= effect.Value { //低于固定伤害 直接扣除 并删除buff
						trueDamage += int32(m.Value)
						removes = append(removes, m.Type)
					} else { //否在扣除50%
						v := ut.RoundInt32(m.Value * 0.5)
						trueDamage += v
						m.Value -= float64(v)
					}
				}
			} else {
				continue
			}
			providerMap[m.Provider] += trueDamage - lastDamage
			lastDamage = trueDamage
		}
		// 删除buff
		this.target.RemoveMultiBuff(removes...)
		// 检测火
		var fire g.IFighter = nil
		if this.isHasFire {
			point, camp := this.target.GetPoint(), this.target.GetCamp()
			fire = array.Find(ctrl.GetFighters(), func(m g.IFighter) bool {
				return m.GetID() == constant.FIRE_PAWN_ID && m.GetPoint().Equals(point)
			})
			if fire != nil {
				lv := fire.GetLV()
				if fire.GetCamp() == camp { //如果是友方就增涨1怒气
					this.target.AddAnger(1)
				} else {
					trueDamage += lv * fire.GetCurHp()
				}
				// 记录伤害
				if arr := strings.Split(fire.GetUID(), "_"); len(arr) >= 3 {
					providerMap[arr[2]] += trueDamage - lastDamage
				}
				lastDamage = trueDamage
				// 触发火势 加等级到最高之后减等级
				fire.AddAttackCount(1)
				maxLv := fire.GetMaxHp()
				attackCount := fire.GetAttackCount()
				if attackCount < maxLv {
					lv = attackCount + 1
				} else {
					lv = maxLv - (attackCount - maxLv + 1)
				}
				if lv > 0 {
					fire.SetLV(lv)
					fire = nil
				}
			}
		}
		if trueDamage <= 0 {
			return SUCCESS
		}
		_, trueDamage = this.target.HitPrepDamageHandle(0, trueDamage)
		val, _, _ := this.target.OnHit(trueDamage, trueDamage)
		this.target.ChangeState(constant.PAWN_STATE_HIT)
		this.SetTreeBlackboardData("isDeductHpAction", true)
		// 记录
		if val > 0 {
			trueDamage = 0
			for uid, damage := range providerMap {
				if damage == 0 {
				} else if trueDamage+damage >= val {
					ctrl.AddFighterBattleDamageInfo(uid, this.target, val-trueDamage)
					break
				} else {
					trueDamage += damage
					ctrl.AddFighterBattleDamageInfo(uid, this.target, damage)
				}
			}
		}
		// 在后面删除
		if fire != nil {
			ctrl.RemoveNoncombat(fire.GetUID())
		}
	}
	this.SetBlackboardData("currTime", currTime+dt)
	return RUNNING
}
