package behavior

const (
	COMPOSITE = "composite"
	DECORATOR = "decorator"
	ACTION    = "action"
	CONDITION = "condition"
)

type Status uint8

const (
	SUCCESS Status = 1
	FAILURE Status = 2
	RUNNING Status = 3
	ERROR   Status = 4
)

var SKIN_BUFF_CONF = map[int32]map[int32]int32{
	320501: { //剑盾 曹操
		26: 26001,
	},
	3205102: { //剑盾buff
		26: 26102,
	},
	320301: { //枪盾 张飞
		11: 11001,
	},
	3203102: { //枪盾buff
		11: 11102,
	},
}

func GetBuffIDBySkin(id, skinId int32) int32 {
	if skinBuff := SKIN_BUFF_CONF[skinId]; skinBuff != nil {
		if s := skinBuff[id]; s > 0 {
			id = s
		}
	}
	return id
}
